<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Portal - AuthTracker</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .patient-header {
            background: linear-gradient(135deg, var(--mint-color), var(--secondary-color));
            padding: 30px 0;
            border-bottom: 3px solid var(--primary-color);
        }
        
        .patient-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .patient-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            font-weight: 700;
        }
        
        .patient-details h2 {
            margin: 0 0 5px 0;
            color: var(--dark-gray);
        }
        
        .patient-meta {
            color: #666;
            font-size: 1rem;
        }
        
        .security-badge {
            background: var(--success-green);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-left: auto;
        }
        
        .patient-nav {
            background: white;
            border-bottom: 2px solid var(--mint-color);
            padding: 0;
        }
        
        .patient-nav .nav-content {
            display: flex;
            gap: 0;
            justify-content: center;
        }
        
        .patient-nav .nav-item {
            padding: 15px 30px;
            text-decoration: none;
            color: var(--dark-gray);
            font-weight: 500;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .patient-nav .nav-item:hover, 
        .patient-nav .nav-item.active {
            background: var(--mint-color);
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
        }
        
        .prior-auth-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
            border-left: 5px solid var(--primary-color);
        }
        
        .prior-auth-card.approved {
            border-left-color: var(--success-green);
        }
        
        .prior-auth-card.pending {
            border-left-color: var(--warning-orange);
        }
        
        .prior-auth-card.denied {
            border-left-color: var(--danger-red);
        }
        
        .auth-card-header {
            background: var(--light-gray);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .auth-card-title {
            font-weight: 500;
            margin: 0;
        }
        
        .auth-card-content {
            padding: 20px;
        }
        
        .auth-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .auth-detail-item {
            display: flex;
            flex-direction: column;
        }
        
        .auth-detail-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 3px;
        }
        
        .auth-detail-value {
            font-weight: 500;
            color: var(--dark-gray);
        }
        
        .progress-bar {
            background: var(--light-gray);
            border-radius: 10px;
            height: 8px;
            margin: 15px 0;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        
        .progress-fill.submitted {
            width: 25%;
            background: var(--primary-color);
        }
        
        .progress-fill.review {
            width: 50%;
            background: var(--warning-orange);
        }
        
        .progress-fill.approved {
            width: 100%;
            background: var(--success-green);
        }
        
        .progress-fill.denied {
            width: 100%;
            background: var(--danger-red);
        }
        
        .progress-steps {
            display: flex;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #666;
            margin-top: 5px;
        }
        
        .philhealth-card {
            background: linear-gradient(135deg, var(--philhealth-blue), var(--philhealth-green));
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .philhealth-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        

        
        .philhealth-status {
            flex: 1;
        }
        
        .philhealth-member-id {
            font-size: 1.1rem;
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .philhealth-validity {
            opacity: 0.9;
            font-size: 0.9rem;
        }
        
        .help-section {
            background: var(--mint-color);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .help-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }
        
        .contact-info {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 10px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .contact-item:last-child {
            border-bottom: none;
        }
        
        .contact-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
        }
        
        .contact-content {
            flex: 1;
        }
        
        .contact-title {
            font-weight: 500;
            margin-bottom: 3px;
        }
        
        .contact-detail {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Patient Header -->
    <header class="patient-header">
        <div class="container">
            <div class="patient-info">
                <div class="patient-avatar">JD</div>
                <div class="patient-details">
                    <h2>Juan Dela Cruz</h2>
                    <div class="patient-meta">
                        Patient ID: 12345 | DOB: January 15, 1980 | PhilHealth Member
                    </div>
                </div>
                <div class="security-badge">🔒 Secure Session</div>
            </div>
        </div>
    </header>

    <!-- Patient Navigation -->
    <nav class="patient-nav">
        <div class="container">
            <div class="nav-content">
                <a href="#" class="nav-item active">📋 My Prior Authorizations</a>
                <a href="#" class="nav-item">👤 Personal Information</a>
                <a href="#" class="nav-item">💬 Communication History</a>
                <a href="#" class="nav-item">❓ Help & Support</a>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <main class="dashboard">
        <div class="container">
            <!-- Welcome Section -->
            <div class="mb-20">
                <h2>Welcome back, Juan</h2>
                <p style="color: #666; font-size: 1.1rem;">Track your prior authorization requests and manage your healthcare information</p>
            </div>

            <!-- PhilHealth Status -->
            <div class="philhealth-card">
                <div class="philhealth-header">
                    <div class="philhealth-logo-small"></div>
                    <div class="philhealth-status">
                        <div class="philhealth-member-id">Member ID: 12-***********</div>
                        <div class="philhealth-validity">Active Member • Valid until December 2025</div>
                    </div>
                    <div>
                        <button class="btn btn-primary">View Benefits</button>
                    </div>
                </div>
                <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px;">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div>
                            <div style="font-weight: 500;">Current Coverage Status</div>
                            <div style="opacity: 0.9; font-size: 0.9rem;">Premium payments up to date</div>
                        </div>
                        <div style="font-size: 1.5rem;">✅</div>
                    </div>
                </div>
            </div>

            <!-- Prior Authorization Overview -->
            <div class="stats-grid">
                <div class="stat-card approved">
                    <div class="stat-number">3</div>
                    <div class="stat-label">✅ Approved Requests</div>
                    <div class="stat-change positive">This year</div>
                </div>
                
                <div class="stat-card pending">
                    <div class="stat-number">1</div>
                    <div class="stat-label">📋 Pending Review</div>
                    <div class="stat-change">In progress</div>
                </div>
                
                <div class="stat-card philhealth">
                    <div class="stat-number">₱45,000</div>
                    <div class="stat-label">💰 Total Coverage</div>
                    <div class="stat-change positive">PhilHealth benefits</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-number">2.3</div>
                    <div class="stat-label">⏱️ Avg Processing Days</div>
                    <div class="stat-change">Your requests</div>
                </div>
            </div>

            <!-- Prior Authorization Requests -->
            <div class="content-card mt-20">
                <div class="card-header">
                    <h3>📋 My Prior Authorization Requests</h3>
                </div>
                <div class="card-content">
                    <!-- Active Request -->
                    <div class="prior-auth-card pending">
                        <div class="auth-card-header">
                            <h4 class="auth-card-title">Cardiac MRI Scan</h4>
                            <span class="status-badge status-review">Under Review</span>
                        </div>
                        <div class="auth-card-content">
                            <div class="auth-details">
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Tracking ID</div>
                                    <div class="auth-detail-value">PA240804001</div>
                                </div>
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Requested Date</div>
                                    <div class="auth-detail-value">August 1, 2025</div>
                                </div>
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Provider</div>
                                    <div class="auth-detail-value">Dr. Maria Santos</div>
                                </div>
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Insurance</div>
                                    <div class="auth-detail-value">PhilHealth + Maxicare</div>
                                </div>
                            </div>
                            
                            <div class="progress-bar">
                                <div class="progress-fill review"></div>
                            </div>
                            <div class="progress-steps">
                                <span>✅ Submitted</span>
                                <span style="color: var(--warning-orange); font-weight: 500;">📋 Under Review</span>
                                <span>Pending Decision</span>
                                <span>Complete</span>
                            </div>
                            
                            <div style="margin-top: 15px; padding: 15px; background: #fff3cd; border-radius: 8px;">
                                <div style="font-weight: 500; color: var(--warning-orange); margin-bottom: 5px;">
                                    📋 Status Update
                                </div>
                                <div style="color: #666; font-size: 0.9rem;">
                                    Your request is currently being reviewed by the insurance company. 
                                    Expected decision by August 6, 2025.
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Approved Request -->
                    <div class="prior-auth-card approved">
                        <div class="auth-card-header">
                            <h4 class="auth-card-title">Echocardiogram</h4>
                            <span class="status-badge status-approved">Approved</span>
                        </div>
                        <div class="auth-card-content">
                            <div class="auth-details">
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Tracking ID</div>
                                    <div class="auth-detail-value">PA240715002</div>
                                </div>
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Approved Date</div>
                                    <div class="auth-detail-value">July 18, 2025</div>
                                </div>
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Approval Number</div>
                                    <div class="auth-detail-value">APV-***********</div>
                                </div>
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Coverage Amount</div>
                                    <div class="auth-detail-value">₱8,500 (PhilHealth)</div>
                                </div>
                            </div>
                            
                            <div class="progress-bar">
                                <div class="progress-fill approved"></div>
                            </div>
                            <div class="progress-steps">
                                <span>✅ Submitted</span>
                                <span>✅ Reviewed</span>
                                <span>✅ Approved</span>
                                <span style="color: var(--success-green); font-weight: 500;">✅ Complete</span>
                            </div>
                            
                            <div style="margin-top: 15px;">
                                <button class="btn btn-primary" style="margin-right: 10px;">Download Approval Letter</button>
                                <button class="btn btn-secondary">View Details</button>
                            </div>
                        </div>
                    </div>

                    <!-- Previous Request -->
                    <div class="prior-auth-card approved">
                        <div class="auth-card-header">
                            <h4 class="auth-card-title">Blood Work Panel</h4>
                            <span class="status-badge status-approved">Approved</span>
                        </div>
                        <div class="auth-card-content">
                            <div class="auth-details">
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Tracking ID</div>
                                    <div class="auth-detail-value">PA240601003</div>
                                </div>
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Completed Date</div>
                                    <div class="auth-detail-value">June 5, 2025</div>
                                </div>
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Coverage Amount</div>
                                    <div class="auth-detail-value">₱2,800 (PhilHealth)</div>
                                </div>
                                <div class="auth-detail-item">
                                    <div class="auth-detail-label">Status</div>
                                    <div class="auth-detail-value">Procedure Completed</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid mt-20">
                <!-- Help & Support -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>❓ Help & Support</h3>
                    </div>
                    <div class="card-content">
                        <div class="help-section">
                            <div class="help-icon">🤝</div>
                            <h4>Need Help?</h4>
                            <p style="margin: 15px 0; color: #666;">
                                Our patient support team is here to help you understand your prior authorization status and benefits.
                            </p>
                            <button class="btn btn-primary">Contact Support</button>
                        </div>
                        
                        <div style="margin-top: 20px;">
                            <h5 style="margin-bottom: 15px;">Frequently Asked Questions</h5>
                            <div style="text-align: left;">
                                <div style="margin-bottom: 10px;">
                                    <a href="#" style="color: var(--primary-color); text-decoration: none;">
                                        📋 How long does prior authorization take?
                                    </a>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <a href="#" style="color: var(--primary-color); text-decoration: none;">
                                        💰 What does PhilHealth cover?
                                    </a>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <a href="#" style="color: var(--primary-color); text-decoration: none;">
                                        📞 How to contact my insurance?
                                    </a>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <a href="#" style="color: var(--primary-color); text-decoration: none;">
                                        📄 Understanding my benefits
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="contact-info">
                    <h3 style="margin-bottom: 20px;">📞 Contact Information</h3>
                    
                    <div class="contact-item">
                        <div class="contact-icon">🏥</div>
                        <div class="contact-content">
                            <div class="contact-title">Your Healthcare Provider</div>
                            <div class="contact-detail">Cardiology Associates<br>Dr. Maria Santos</div>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📞</div>
                        <div class="contact-content">
                            <div class="contact-title">Clinic Phone</div>
                            <div class="contact-detail">(02) 8123-4567</div>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">🏢</div>
                        <div class="contact-content">
                            <div class="contact-title">PhilHealth Hotline</div>
                            <div class="contact-detail">917-7777 (Metro Manila)<br>1-800-1-888-1777 (Toll-free)</div>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">💬</div>
                        <div class="contact-content">
                            <div class="contact-title">Patient Support</div>
                            <div class="contact-detail"><EMAIL><br>Available 24/7</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
