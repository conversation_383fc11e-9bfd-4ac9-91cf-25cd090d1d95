<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Provider Dashboard - AuthTracker Mobile</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        body {
            font-size: 16px;
            background: var(--light-gray);
        }
        
        .mobile-container {
            max-width: 414px;
            margin: 0 auto;
            background: var(--light-gray);
            min-height: 100vh;
        }
        
        .mobile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--philhealth-blue));
            color: white;
            padding: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .header-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }
        
        .header-user {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .notification-icon {
            background: var(--accent-color);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .welcome-section {
            padding: 20px 15px;
            background: white;
            margin-bottom: 15px;
        }
        
        .welcome-section h2 {
            font-size: 1.4rem;
            margin: 0 0 5px 0;
        }
        
        .welcome-section p {
            font-size: 0.9rem;
            color: #666;
            margin: 0;
        }
        
        .stats-mobile {
            padding: 0 15px 15px;
        }
        
        .stats-grid-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .stat-card-mobile {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-card-mobile.urgent {
            border-left-color: var(--danger-red);
        }
        
        .stat-card-mobile.pending {
            border-left-color: var(--warning-orange);
        }
        
        .stat-card-mobile.approved {
            border-left-color: var(--success-green);
        }
        
        .stat-card-mobile.philhealth {
            border-left-color: var(--philhealth-blue);
        }
        
        .stat-number-mobile {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-label-mobile {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 3px;
        }
        
        .stat-change-mobile {
            font-size: 0.7rem;
            color: var(--success-green);
        }
        
        .quick-actions-mobile {
            padding: 0 15px 15px;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .action-btn-mobile {
            background: white;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 15px 10px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            min-height: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .action-btn-mobile:active {
            transform: scale(0.98);
            background: var(--primary-color);
            color: white;
        }
        
        .action-icon {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .philhealth-status-mobile {
            background: linear-gradient(135deg, var(--philhealth-blue), var(--philhealth-green));
            color: white;
            margin: 0 15px 15px;
            padding: 15px;
            border-radius: 10px;
        }
        
        .philhealth-header-mobile {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .philhealth-logo-mobile {
            width: 40px;
            height: 20px;
            background: white;
            border-radius: 4px;
            background-image: url('../philhealth-vector-logo-seeklogo/philhealth-seeklogo.svg');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .philhealth-status-text {
            flex: 1;
        }
        
        .philhealth-title {
            font-size: 1rem;
            font-weight: 600;
            margin: 0 0 3px 0;
        }
        
        .philhealth-subtitle {
            font-size: 0.8rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .content-section {
            background: white;
            margin: 0 15px 15px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-header {
            background: var(--light-gray);
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            color: var(--dark-gray);
        }
        
        .section-content {
            padding: 15px;
        }
        
        .schedule-item-mobile {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .schedule-item-mobile:last-child {
            border-bottom: none;
        }
        
        .schedule-time-mobile {
            width: 60px;
            font-size: 0.8rem;
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .schedule-details {
            flex: 1;
            margin-left: 10px;
        }
        
        .patient-name-mobile {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .appointment-type-mobile {
            font-size: 0.8rem;
            color: #666;
        }
        
        .philhealth-badge-mobile {
            background: var(--philhealth-green);
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 500;
        }
        
        .priority-badge-mobile {
            background: var(--danger-red);
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 500;
        }
        
        .activity-item-mobile {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .activity-item-mobile:last-child {
            border-bottom: none;
        }
        
        .activity-icon-mobile {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 10px;
            font-size: 0.8rem;
            font-weight: 600;
            color: white;
        }
        
        .activity-icon-mobile.approved { background: var(--success-green); }
        .activity-icon-mobile.pending { background: var(--warning-orange); }
        .activity-icon-mobile.philhealth { background: var(--philhealth-blue); }
        
        .activity-content-mobile {
            flex: 1;
        }
        
        .activity-title-mobile {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 2px;
        }
        
        .activity-meta-mobile {
            font-size: 0.8rem;
            color: #666;
        }
        
        .action-items-mobile {
            padding: 0 15px 20px;
        }
        
        .action-item-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 4px solid var(--warning-orange);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .action-item-card.urgent {
            border-left-color: var(--danger-red);
            background: #fff5f5;
        }
        
        .action-item-card.philhealth {
            border-left-color: var(--philhealth-blue);
            background: #f0f8ff;
        }
        
        .action-item-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--dark-gray);
        }
        
        .action-item-desc {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
        }
        
        .action-item-btn {
            background: var(--primary-color);
            color: white;
            padding: 8px 15px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }
        
        .action-item-btn.urgent {
            background: var(--danger-red);
        }
        
        .action-item-btn.philhealth {
            background: var(--philhealth-blue);
        }
        
        /* Bottom navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid var(--light-gray);
            padding: 10px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .nav-item-mobile {
            text-align: center;
            text-decoration: none;
            color: #666;
            font-size: 0.7rem;
            padding: 5px;
            min-width: 60px;
        }
        
        .nav-item-mobile.active {
            color: var(--primary-color);
        }
        
        .nav-icon {
            font-size: 1.2rem;
            margin-bottom: 2px;
            display: block;
        }
        
        /* Add bottom padding to prevent content being hidden behind nav */
        .mobile-container {
            padding-bottom: 70px;
        }

        /* Prevent text selection on touch */
        .action-btn-mobile, .nav-item-mobile {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- Mobile Header -->
        <header class="mobile-header">
            <div class="header-top">
                <div>
                    <div class="header-title">AuthTracker</div>
                    <div class="header-user">Dr. Maria Santos</div>
                </div>
                <div class="notification-icon">5</div>
            </div>
        </header>

        <!-- Welcome Section -->
        <section class="welcome-section">
            <h2>Good morning, Dr. Santos</h2>
            <p>Your practice overview for today</p>
        </section>

        <!-- Stats Grid -->
        <section class="stats-mobile">
            <div class="stats-grid-mobile">
                <div class="stat-card-mobile urgent">
                    <div class="stat-number-mobile">3</div>
                    <div class="stat-label-mobile">🚨 URGENT</div>
                    <div class="stat-change-mobile">STAT Required</div>
                </div>
                
                <div class="stat-card-mobile pending">
                    <div class="stat-number-mobile">12</div>
                    <div class="stat-label-mobile">📋 Pending</div>
                    <div class="stat-change-mobile">+2 today</div>
                </div>
                
                <div class="stat-card-mobile approved">
                    <div class="stat-number-mobile">8</div>
                    <div class="stat-label-mobile">✅ Approved</div>
                    <div class="stat-change-mobile">This week</div>
                </div>
                
                <div class="stat-card-mobile philhealth">
                    <div class="stat-number-mobile">₱45K</div>
                    <div class="stat-label-mobile">💰 Claims</div>
                    <div class="stat-change-mobile">This month</div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions-mobile">
            <div class="actions-grid">
                <a href="#" class="action-btn-mobile">
                    <div class="action-icon">➕</div>
                    <div>New Prior Auth</div>
                </a>
                <a href="#" class="action-btn-mobile">
                    <div class="action-icon">🔍</div>
                    <div>Search Patient</div>
                </a>
                <a href="#" class="action-btn-mobile">
                    <div class="action-icon">📞</div>
                    <div>Call Insurance</div>
                </a>
                <a href="#" class="action-btn-mobile">
                    <div class="action-icon">📄</div>
                    <div>Upload Doc</div>
                </a>
            </div>
        </section>

        <!-- PhilHealth Status -->
        <section class="philhealth-status-mobile">
            <div class="philhealth-header-mobile">
                <div class="philhealth-logo-mobile"></div>
                <div class="philhealth-status-text">
                    <div class="philhealth-title">System Connected ✅</div>
                    <div class="philhealth-subtitle">Last sync: 2 minutes ago</div>
                </div>
            </div>
        </section>

        <!-- Today's Schedule -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">📅 Today's Schedule</h3>
            </div>
            <div class="section-content">
                <div class="schedule-item-mobile">
                    <div class="schedule-time-mobile">9:00</div>
                    <div class="schedule-details">
                        <div class="patient-name-mobile">John Dela Cruz</div>
                        <div class="appointment-type-mobile">Follow-up Consultation</div>
                    </div>
                    <span class="philhealth-badge-mobile">PhilHealth</span>
                </div>
                
                <div class="schedule-item-mobile">
                    <div class="schedule-time-mobile">10:30</div>
                    <div class="schedule-details">
                        <div class="patient-name-mobile">Maria Gonzales</div>
                        <div class="appointment-type-mobile">New Patient</div>
                    </div>
                    <span class="philhealth-badge-mobile">PhilHealth</span>
                </div>
                
                <div class="schedule-item-mobile">
                    <div class="schedule-time-mobile">14:00</div>
                    <div class="schedule-details">
                        <div class="patient-name-mobile">Roberto Silva</div>
                        <div class="appointment-type-mobile">Prior Auth Review</div>
                    </div>
                    <span class="priority-badge-mobile">URGENT</span>
                </div>
            </div>
        </section>

        <!-- Recent Activity -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">🔔 Recent Activity</h3>
            </div>
            <div class="section-content">
                <div class="activity-item-mobile">
                    <div class="activity-icon-mobile approved">✓</div>
                    <div class="activity-content-mobile">
                        <div class="activity-title-mobile">Prior Auth Approved</div>
                        <div class="activity-meta-mobile">MRI Brain - John Dela Cruz</div>
                    </div>
                </div>
                
                <div class="activity-item-mobile">
                    <div class="activity-icon-mobile philhealth">₱</div>
                    <div class="activity-content-mobile">
                        <div class="activity-title-mobile">PhilHealth Claim Processed</div>
                        <div class="activity-meta-mobile">₱8,500 approved</div>
                    </div>
                </div>
                
                <div class="activity-item-mobile">
                    <div class="activity-icon-mobile pending">?</div>
                    <div class="activity-content-mobile">
                        <div class="activity-title-mobile">Insurance Inquiry</div>
                        <div class="activity-meta-mobile">Additional docs requested</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Action Items -->
        <section class="action-items-mobile">
            <div class="action-item-card urgent">
                <div class="action-item-title">🚨 2 STAT Priorities</div>
                <div class="action-item-desc">Emergency procedures requiring immediate attention</div>
                <a href="#" class="action-item-btn urgent">Handle STAT</a>
            </div>
            
            <div class="action-item-card">
                <div class="action-item-title">📋 3 Prior Auths Need Review</div>
                <div class="action-item-desc">Pending your clinical review and approval</div>
                <a href="#" class="action-item-btn">Review Now</a>
            </div>
            
            <div class="action-item-card philhealth">
                <div class="action-item-title">💰 PhilHealth Updates</div>
                <div class="action-item-desc">5 new benefit updates available</div>
                <a href="#" class="action-item-btn philhealth">View Updates</a>
            </div>
        </section>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <a href="#" class="nav-item-mobile active">
                <span class="nav-icon">🏠</span>
                Dashboard
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">👥</span>
                Patients
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">📋</span>
                Prior Auth
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">📄</span>
                Records
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">📊</span>
                Reports
            </a>
        </nav>
    </div>
</body>
</html>
