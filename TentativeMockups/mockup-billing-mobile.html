<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Billing Dashboard - AuthTracker Mobile</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        body {
            font-size: 16px;
            background: var(--light-gray);
        }
        
        .mobile-container {
            max-width: 414px;
            margin: 0 auto;
            background: var(--light-gray);
            min-height: 100vh;
            padding-bottom: 70px;
        }
        
        .mobile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--philhealth-blue));
            color: white;
            padding: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }
        
        .header-user {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .notification-icon {
            background: var(--accent-color);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .welcome-section {
            padding: 20px 15px;
            background: white;
            margin-bottom: 15px;
        }
        
        .welcome-section h2 {
            font-size: 1.4rem;
            margin: 0 0 5px 0;
        }
        
        .welcome-section p {
            font-size: 0.9rem;
            color: #666;
            margin: 0;
        }
        
        .financial-overview-mobile {
            background: linear-gradient(135deg, var(--primary-color), var(--philhealth-blue));
            color: white;
            margin: 0 15px 15px;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
        }
        
        .financial-amount-mobile {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 8px;
        }
        
        .financial-label-mobile {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 10px;
        }
        
        .financial-change-mobile {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
        }
        
        .stats-mobile {
            padding: 0 15px 15px;
        }
        
        .stats-grid-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .stat-card-mobile {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-card-mobile.approved {
            border-left-color: var(--success-green);
        }
        
        .stat-card-mobile.pending {
            border-left-color: var(--warning-orange);
        }
        
        .stat-card-mobile.philhealth {
            border-left-color: var(--philhealth-blue);
        }
        
        .stat-number-mobile {
            font-size: 1.6rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-label-mobile {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 3px;
        }
        
        .stat-change-mobile {
            font-size: 0.7rem;
            color: var(--success-green);
        }
        
        .philhealth-dashboard-mobile {
            background: linear-gradient(135deg, var(--philhealth-blue), var(--philhealth-green));
            color: white;
            margin: 0 15px 15px;
            padding: 15px;
            border-radius: 10px;
        }
        
        .philhealth-header-mobile {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
        }
        
        .philhealth-logo-mobile {
            width: 40px;
            height: 20px;
            background: white;
            border-radius: 4px;
            background-image: url('../philhealth-vector-logo-seeklogo/philhealth-seeklogo.svg');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .philhealth-status-text {
            flex: 1;
        }
        
        .philhealth-title {
            font-size: 1rem;
            font-weight: 600;
            margin: 0 0 3px 0;
        }
        
        .philhealth-subtitle {
            font-size: 0.8rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .philhealth-stats-mobile {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
        }
        
        .philhealth-stat-mobile {
            text-align: center;
        }
        
        .philhealth-stat-number {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 2px;
        }
        
        .philhealth-stat-label {
            font-size: 0.7rem;
            opacity: 0.9;
        }
        
        .content-section {
            background: white;
            margin: 0 15px 15px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-header {
            background: var(--light-gray);
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            color: var(--dark-gray);
        }
        
        .section-content {
            padding: 15px;
        }
        
        .approval-rate-mobile {
            text-align: center;
            padding: 20px;
        }
        
        .approval-circle-mobile {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: conic-gradient(var(--success-green) 0deg 313deg, var(--light-gray) 313deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            position: relative;
        }
        
        .approval-circle-mobile::before {
            content: '';
            width: 70px;
            height: 70px;
            background: white;
            border-radius: 50%;
            position: absolute;
        }
        
        .approval-rate-text {
            position: relative;
            z-index: 1;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--success-green);
        }
        
        .approval-stats-mobile {
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
        }
        
        .approval-stat-mobile {
            text-align: center;
        }
        
        .approval-stat-number {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 3px;
        }
        
        .approval-stat-number.approved {
            color: var(--success-green);
        }
        
        .approval-stat-number.denied {
            color: var(--danger-red);
        }
        
        .approval-stat-label {
            font-size: 0.8rem;
            color: #666;
        }
        
        .insurance-item-mobile {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .insurance-item-mobile.philhealth {
            border-left-color: var(--philhealth-blue);
        }
        
        .insurance-item-mobile.private {
            border-left-color: var(--accent-color);
        }
        
        .insurance-info-mobile {
            flex: 1;
        }
        
        .insurance-name-mobile {
            font-weight: 600;
            margin-bottom: 3px;
            font-size: 0.9rem;
        }
        
        .insurance-details-mobile {
            font-size: 0.8rem;
            color: #666;
        }
        
        .insurance-amount-mobile {
            text-align: right;
        }
        
        .amount-value-mobile {
            font-size: 1rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .amount-label-mobile {
            font-size: 0.7rem;
            color: #666;
        }
        
        .action-items-mobile {
            padding: 0 15px 15px;
        }
        
        .action-item-card-mobile {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 4px solid var(--warning-orange);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .action-item-card-mobile.philhealth {
            border-left-color: var(--philhealth-blue);
            background: #f0f8ff;
        }
        
        .action-item-card-mobile.urgent {
            border-left-color: var(--danger-red);
            background: #fff5f5;
        }
        
        .action-item-title-mobile {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--dark-gray);
        }
        
        .action-item-desc-mobile {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 10px;
        }
        
        .action-item-btn-mobile {
            background: var(--primary-color);
            color: white;
            padding: 8px 15px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }
        
        .action-item-btn-mobile.philhealth {
            background: var(--philhealth-blue);
        }
        
        .action-item-btn-mobile.urgent {
            background: var(--danger-red);
        }

        .action-item-btn-mobile:active {
            transform: scale(0.98);
        }
        
        /* Bottom navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid var(--light-gray);
            padding: 10px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .nav-item-mobile {
            text-align: center;
            text-decoration: none;
            color: #666;
            font-size: 0.7rem;
            padding: 5px;
            min-width: 60px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        .nav-item-mobile.active {
            color: var(--primary-color);
        }
        
        .nav-icon {
            font-size: 1.2rem;
            margin-bottom: 2px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- Mobile Header -->
        <header class="mobile-header">
            <div class="header-top">
                <div>
                    <div class="header-title">AuthTracker</div>
                    <div class="header-user">Carmen Rodriguez (Billing)</div>
                </div>
                <div class="notification-icon">7</div>
            </div>
        </header>

        <!-- Welcome Section -->
        <section class="welcome-section">
            <h2>Good morning, Carmen</h2>
            <p>Your financial overview and billing metrics</p>
        </section>

        <!-- Financial Overview -->
        <section class="financial-overview-mobile">
            <div class="financial-amount-mobile">₱2.5M</div>
            <div class="financial-label-mobile">Monthly Claims Processed</div>
            <div class="financial-change-mobile">+15% vs last month</div>
        </section>

        <!-- Stats Grid -->
        <section class="stats-mobile">
            <div class="stats-grid-mobile">
                <div class="stat-card-mobile approved">
                    <div class="stat-number-mobile">87%</div>
                    <div class="stat-label-mobile">📈 Approval Rate</div>
                    <div class="stat-change-mobile">+3% this month</div>
                </div>
                
                <div class="stat-card-mobile pending">
                    <div class="stat-number-mobile">₱450K</div>
                    <div class="stat-label-mobile">💰 Pending</div>
                    <div class="stat-change-mobile">23 claims</div>
                </div>
                
                <div class="stat-card-mobile philhealth">
                    <div class="stat-number-mobile">₱1.8M</div>
                    <div class="stat-label-mobile">🏥 PhilHealth</div>
                    <div class="stat-change-mobile">72% of total</div>
                </div>
                
                <div class="stat-card-mobile">
                    <div class="stat-number-mobile">156</div>
                    <div class="stat-label-mobile">📋 Claims</div>
                    <div class="stat-change-mobile">This month</div>
                </div>
            </div>
        </section>

        <!-- PhilHealth Dashboard -->
        <section class="philhealth-dashboard-mobile">
            <div class="philhealth-header-mobile">
                <div class="philhealth-logo-mobile"></div>
                <div class="philhealth-status-text">
                    <div class="philhealth-title">Real-time Claims Processing</div>
                    <div class="philhealth-subtitle">156 claims • 89% approval rate</div>
                </div>
            </div>
            <div class="philhealth-stats-mobile">
                <div class="philhealth-stat-mobile">
                    <div class="philhealth-stat-number">₱1.8M</div>
                    <div class="philhealth-stat-label">Processed</div>
                </div>
                <div class="philhealth-stat-mobile">
                    <div class="philhealth-stat-number">156</div>
                    <div class="philhealth-stat-label">Claims</div>
                </div>
                <div class="philhealth-stat-mobile">
                    <div class="philhealth-stat-number">89%</div>
                    <div class="philhealth-stat-label">Approval</div>
                </div>
            </div>
        </section>

        <!-- Approval Rate -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">✅ Approval Metrics</h3>
            </div>
            <div class="section-content">
                <div class="approval-rate-mobile">
                    <div class="approval-circle-mobile">
                        <div class="approval-rate-text">87%</div>
                    </div>
                    <h4 style="margin: 0 0 15px 0;">Current Approval Rate</h4>
                    
                    <div class="approval-stats-mobile">
                        <div class="approval-stat-mobile">
                            <div class="approval-stat-number approved">156</div>
                            <div class="approval-stat-label">Approved</div>
                        </div>
                        <div class="approval-stat-mobile">
                            <div class="approval-stat-number denied">23</div>
                            <div class="approval-stat-label">Denied</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Insurance Companies -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">🏢 Insurance Overview</h3>
            </div>
            <div class="section-content">
                <div class="insurance-item-mobile philhealth">
                    <div class="insurance-info-mobile">
                        <div class="insurance-name-mobile">PhilHealth</div>
                        <div class="insurance-details-mobile">156 claims • 89% approval</div>
                    </div>
                    <div class="insurance-amount-mobile">
                        <div class="amount-value-mobile">₱1.8M</div>
                        <div class="amount-label-mobile">This month</div>
                    </div>
                </div>
                
                <div class="insurance-item-mobile private">
                    <div class="insurance-info-mobile">
                        <div class="insurance-name-mobile">Maxicare</div>
                        <div class="insurance-details-mobile">45 claims • 92% approval</div>
                    </div>
                    <div class="insurance-amount-mobile">
                        <div class="amount-value-mobile">₱420K</div>
                        <div class="amount-label-mobile">This month</div>
                    </div>
                </div>
                
                <div class="insurance-item-mobile private">
                    <div class="insurance-info-mobile">
                        <div class="insurance-name-mobile">Intellicare</div>
                        <div class="insurance-details-mobile">32 claims • 85% approval</div>
                    </div>
                    <div class="insurance-amount-mobile">
                        <div class="amount-value-mobile">₱280K</div>
                        <div class="amount-label-mobile">This month</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Action Items -->
        <section class="action-items-mobile">
            <div class="action-item-card-mobile">
                <div class="action-item-title-mobile">💰 23 Claims Pending</div>
                <div class="action-item-desc-mobile">₱450,000 awaiting approval</div>
                <a href="#" class="action-item-btn-mobile">Review Claims</a>
            </div>
            
            <div class="action-item-card-mobile philhealth">
                <div class="action-item-title-mobile">🏥 PhilHealth Batch Ready</div>
                <div class="action-item-desc-mobile">12 claims ready for submission</div>
                <a href="#" class="action-item-btn-mobile philhealth">Submit Batch</a>
            </div>
            
            <div class="action-item-card-mobile urgent">
                <div class="action-item-title-mobile">🚨 5 Denied Claims</div>
                <div class="action-item-desc-mobile">Require appeal or resubmission</div>
                <a href="#" class="action-item-btn-mobile urgent">Handle Denials</a>
            </div>
        </section>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <a href="#" class="nav-item-mobile active">
                <span class="nav-icon">🏠</span>
                Dashboard
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">💳</span>
                Insurance
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">📋</span>
                Billing
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">📊</span>
                Reports
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">🏥</span>
                PhilHealth
            </a>
        </nav>
    </div>
</body>
</html>
