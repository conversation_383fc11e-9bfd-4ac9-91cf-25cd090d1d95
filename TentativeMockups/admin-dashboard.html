<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - AuthTracker</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .admin-header {
            background: linear-gradient(135deg, var(--danger-red), var(--primary-color));
            color: white;
            padding: 20px 0;
        }
        
        .admin-badge {
            background: var(--danger-red);
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .system-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .status-indicator {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 5px solid var(--success-green);
        }
        
        .status-indicator.warning {
            border-left-color: var(--warning-orange);
        }
        
        .status-indicator.error {
            border-left-color: var(--danger-red);
        }
        
        .status-icon {
            font-size: 2rem;
            margin-bottom: 10px;
        }
        
        .status-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 1.2rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .user-management-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .user-management-table th {
            background: var(--primary-color);
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 500;
        }
        
        .user-management-table td {
            padding: 12px 15px;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .user-management-table tr:hover {
            background: var(--mint-color);
        }
        
        .role-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .role-admin {
            background: var(--danger-red);
            color: white;
        }
        
        .role-provider {
            background: var(--primary-color);
            color: white;
        }
        
        .role-staff {
            background: var(--accent-color);
            color: white;
        }
        
        .role-billing {
            background: var(--warning-orange);
            color: white;
        }
        
        .activity-log {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .log-item:last-child {
            border-bottom: none;
        }
        
        .log-timestamp {
            width: 100px;
            font-size: 0.8rem;
            color: #666;
        }
        
        .log-user {
            width: 150px;
            font-weight: 500;
        }
        
        .log-action {
            flex: 1;
            color: var(--dark-gray);
        }
        
        .log-severity {
            width: 80px;
            text-align: center;
        }
        
        .severity-info {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }
        
        .severity-warning {
            background: var(--warning-orange);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }
        
        .severity-error {
            background: var(--danger-red);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
        }
        
        .compliance-meter {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .compliance-circle {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            background: conic-gradient(var(--success-green) 0deg 342deg, var(--light-gray) 342deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            position: relative;
        }
        
        .compliance-circle::before {
            content: '';
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 50%;
            position: absolute;
        }
        
        .compliance-score {
            position: relative;
            z-index: 1;
            font-size: 2rem;
            font-weight: 700;
            color: var(--success-green);
        }
        
        .philhealth-integration-status {
            background: linear-gradient(135deg, var(--philhealth-blue), var(--philhealth-green));
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .integration-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .integration-item {
            background: rgba(255,255,255,0.2);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .integration-status {
            font-size: 1.5rem;
            margin-bottom: 5px;
        }
        
        .integration-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Admin Header -->
    <header class="admin-header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1>AuthTracker</h1>
                    <span class="admin-badge">System Administrator</span>
                </div>
                <div class="user-info">
                    <span>Administrator</span>
                    <span>|</span>
                    <span>System Control Panel</span>
                    <div class="notification-badge">12</div>
                    <span>⚙️</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav-bar">
        <div class="container">
            <div class="nav-content">
                <a href="#" class="nav-item active">🏠 Dashboard</a>
                <a href="#" class="nav-item">👥 User Management</a>
                <a href="#" class="nav-item">🏢 Practice Management</a>
                <a href="#" class="nav-item">📊 System Analytics</a>
                <a href="#" class="nav-item">🔒 Security & Compliance</a>
                <a href="#" class="nav-item">⚙️ System Configuration</a>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <main class="dashboard">
        <div class="container">
            <!-- Welcome Section -->
            <div class="mb-20">
                <h2>System Administration Dashboard</h2>
                <p style="color: #666; font-size: 1.1rem;">Monitor and manage the AuthTracker platform</p>
            </div>

            <!-- System Status -->
            <div class="system-status">
                <div class="status-indicator">
                    <div class="status-icon">🟢</div>
                    <div class="status-title">System Health</div>
                    <div class="status-value">Operational</div>
                </div>
                
                <div class="status-indicator">
                    <div class="status-icon">🔗</div>
                    <div class="status-title">PhilHealth API</div>
                    <div class="status-value">Connected</div>
                </div>
                
                <div class="status-indicator warning">
                    <div class="status-icon">⚠️</div>
                    <div class="status-title">Database Load</div>
                    <div class="status-value">78%</div>
                </div>
                
                <div class="status-indicator">
                    <div class="status-icon">🔒</div>
                    <div class="status-title">Security Status</div>
                    <div class="status-value">Secure</div>
                </div>
                
                <div class="status-indicator">
                    <div class="status-icon">👥</div>
                    <div class="status-title">Active Users</div>
                    <div class="status-value">1,247</div>
                </div>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">500+</div>
                    <div class="stat-label">🏢 Healthcare Facilities</div>
                    <div class="stat-change positive">+12 this month</div>
                </div>
                
                <div class="stat-card philhealth">
                    <div class="stat-number">₱2.5M</div>
                    <div class="stat-label">💰 Daily Claims Volume</div>
                    <div class="stat-change positive">+8% vs yesterday</div>
                </div>
                
                <div class="stat-card approved">
                    <div class="stat-number">99.2%</div>
                    <div class="stat-label">⚡ System Uptime</div>
                    <div class="stat-change positive">This month</div>
                </div>
                
                <div class="stat-card pending">
                    <div class="stat-number">1,247</div>
                    <div class="stat-label">👥 Active Users</div>
                    <div class="stat-change positive">Peak: 1,350</div>
                </div>
            </div>

            <!-- PhilHealth Integration Status -->
            <div class="philhealth-integration-status">
                <div style="display: flex; align-items: center; gap: 20px; margin-bottom: 20px;">
                    <div class="philhealth-logo-small"></div>
                    <div style="flex: 1;">
                        <h3 style="margin: 0; color: white;">Real-time Integration Active</h3>
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">
                            All PhilHealth services operational • Last sync: 30 seconds ago
                        </p>
                    </div>
                </div>
                
                <div class="integration-grid">
                    <div class="integration-item">
                        <div class="integration-status">✅</div>
                        <div class="integration-label">Eligibility API</div>
                    </div>
                    <div class="integration-item">
                        <div class="integration-status">✅</div>
                        <div class="integration-label">Claims Submission</div>
                    </div>
                    <div class="integration-item">
                        <div class="integration-status">✅</div>
                        <div class="integration-label">Benefit Verification</div>
                    </div>
                    <div class="integration-item">
                        <div class="integration-status">✅</div>
                        <div class="integration-label">Provider Directory</div>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- User Management -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>👥 Recent User Activity</h3>
                    </div>
                    <div class="card-content">
                        <table class="user-management-table">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Role</th>
                                    <th>Practice</th>
                                    <th>Last Active</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Dr. Maria Santos</td>
                                    <td><span class="role-badge role-provider">Provider</span></td>
                                    <td>Cardiology Associates</td>
                                    <td>2 min ago</td>
                                    <td><span class="status-badge status-approved">Online</span></td>
                                </tr>
                                <tr>
                                    <td>Sarah Mendoza</td>
                                    <td><span class="role-badge role-staff">Staff</span></td>
                                    <td>Cardiology Associates</td>
                                    <td>5 min ago</td>
                                    <td><span class="status-badge status-approved">Online</span></td>
                                </tr>
                                <tr>
                                    <td>Carmen Rodriguez</td>
                                    <td><span class="role-badge role-billing">Billing</span></td>
                                    <td>Cardiology Associates</td>
                                    <td>15 min ago</td>
                                    <td><span class="status-badge status-review">Away</span></td>
                                </tr>
                                <tr>
                                    <td>Dr. Roberto Silva</td>
                                    <td><span class="role-badge role-provider">Provider</span></td>
                                    <td>Metro Medical Center</td>
                                    <td>1 hour ago</td>
                                    <td><span class="status-badge status-expired">Offline</span></td>
                                </tr>
                                <tr>
                                    <td>Ana Reyes</td>
                                    <td><span class="role-badge role-admin">Admin</span></td>
                                    <td>System Administrator</td>
                                    <td>2 hours ago</td>
                                    <td><span class="status-badge status-approved">Online</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Compliance Monitoring -->
                <div class="compliance-meter">
                    <h3 style="margin-bottom: 20px;">🔒 HIPAA Compliance Score</h3>
                    <div class="compliance-circle">
                        <div class="compliance-score">95%</div>
                    </div>
                    <div style="margin-bottom: 20px;">
                        <div style="font-weight: 500; color: var(--success-green);">Excellent Compliance</div>
                        <div style="font-size: 0.9rem; color: #666;">All critical requirements met</div>
                    </div>
                    
                    <div style="text-align: left;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>Access Controls</span>
                            <span style="color: var(--success-green);">✅ 100%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>Audit Logging</span>
                            <span style="color: var(--success-green);">✅ 98%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>Data Encryption</span>
                            <span style="color: var(--success-green);">✅ 100%</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                            <span>PHI Protection</span>
                            <span style="color: var(--warning-orange);">⚠️ 85%</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Activity Log -->
            <div class="content-card mt-20">
                <div class="card-header">
                    <h3>📋 System Activity Log</h3>
                </div>
                <div class="card-content">
                    <div class="activity-log">
                        <div class="log-item">
                            <div class="log-timestamp">10:45 AM</div>
                            <div class="log-user">Dr. Santos</div>
                            <div class="log-action">Created new prior authorization PA240804001</div>
                            <div class="log-severity"><span class="severity-info">INFO</span></div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-timestamp">10:42 AM</div>
                            <div class="log-user">System</div>
                            <div class="log-action">PhilHealth API sync completed successfully</div>
                            <div class="log-severity"><span class="severity-info">INFO</span></div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-timestamp">10:38 AM</div>
                            <div class="log-user">Sarah M.</div>
                            <div class="log-action">Updated patient contact information</div>
                            <div class="log-severity"><span class="severity-info">INFO</span></div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-timestamp">10:35 AM</div>
                            <div class="log-user">System</div>
                            <div class="log-action">Database backup completed</div>
                            <div class="log-severity"><span class="severity-info">INFO</span></div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-timestamp">10:30 AM</div>
                            <div class="log-user">Carmen R.</div>
                            <div class="log-action">Submitted PhilHealth claims batch (12 claims)</div>
                            <div class="log-severity"><span class="severity-info">INFO</span></div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-timestamp">10:25 AM</div>
                            <div class="log-user">System</div>
                            <div class="log-action">Failed login attempt detected from IP *************</div>
                            <div class="log-severity"><span class="severity-warning">WARN</span></div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-timestamp">10:20 AM</div>
                            <div class="log-user">Dr. Silva</div>
                            <div class="log-action">Accessed patient medical records</div>
                            <div class="log-severity"><span class="severity-info">INFO</span></div>
                        </div>
                        
                        <div class="log-item">
                            <div class="log-timestamp">10:15 AM</div>
                            <div class="log-user">System</div>
                            <div class="log-action">Semantic protection system activated</div>
                            <div class="log-severity"><span class="severity-info">INFO</span></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="content-card mt-20">
                <div class="card-header">
                    <h3>⚡ System Administration Actions</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <button class="btn btn-primary">👥 Manage Users</button>
                        <button class="btn btn-philhealth">🏥 PhilHealth Settings</button>
                        <button class="btn btn-warning">⚠️ View Alerts</button>
                        <button class="btn btn-success">📊 Generate Reports</button>
                        <button class="btn btn-secondary">🔧 System Configuration</button>
                        <button class="btn btn-danger">🔒 Security Audit</button>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
