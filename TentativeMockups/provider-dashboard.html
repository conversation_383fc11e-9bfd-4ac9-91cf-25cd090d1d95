<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Provider Dashboard - AuthTracker</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .quick-action-btn {
            background: white;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            padding: 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .quick-action-btn:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-3px);
        }
        
        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: 700;
            color: white;
        }
        
        .activity-icon.approved { background: var(--success-green); }
        .activity-icon.pending { background: var(--warning-orange); }
        .activity-icon.urgent { background: var(--danger-red); }
        .activity-icon.philhealth { background: var(--philhealth-blue); }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .activity-meta {
            font-size: 0.9rem;
            color: #666;
        }
        
        .schedule-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .schedule-time {
            width: 80px;
            font-weight: 500;
            color: var(--primary-color);
        }
        
        .schedule-content {
            flex: 1;
            margin-left: 15px;
        }
        
        .patient-name {
            font-weight: 500;
            margin-bottom: 3px;
        }
        
        .appointment-type {
            font-size: 0.9rem;
            color: #666;
        }
        
        .philhealth-status {
            background: var(--philhealth-green);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 0.8rem;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1>AuthTracker</h1>
                    <span class="philhealth-badge">PhilHealth Integrated</span>
                </div>
                <div class="user-info">
                    <span>Dr. Maria Santos</span>
                    <span>|</span>
                    <span>Cardiology Associates</span>
                    <div class="notification-badge">5</div>
                    <span>⚙️</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav-bar">
        <div class="container">
            <div class="nav-content">
                <a href="#" class="nav-item active">🏠 Dashboard</a>
                <a href="#" class="nav-item">👥 Patients</a>
                <a href="#" class="nav-item">📋 Prior Authorizations</a>
                <a href="#" class="nav-item">📄 Medical Records</a>
                <a href="#" class="nav-item">💬 Communications</a>
                <a href="#" class="nav-item">📊 Reports</a>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <main class="dashboard">
        <div class="container">
            <!-- Welcome Section -->
            <div class="mb-20">
                <h2>Good morning, Dr. Santos</h2>
                <p style="color: #666; font-size: 1.1rem;">Here's your practice overview for today</p>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <a href="#" class="quick-action-btn">
                    <div style="font-size: 1.5rem; margin-bottom: 10px;">➕</div>
                    <div>New Prior Auth</div>
                </a>
                <a href="#" class="quick-action-btn">
                    <div style="font-size: 1.5rem; margin-bottom: 10px;">🔍</div>
                    <div>Search Patient</div>
                </a>
                <a href="#" class="quick-action-btn">
                    <div style="font-size: 1.5rem; margin-bottom: 10px;">📞</div>
                    <div>Call Insurance</div>
                </a>
                <a href="#" class="quick-action-btn">
                    <div style="font-size: 1.5rem; margin-bottom: 10px;">📄</div>
                    <div>Upload Document</div>
                </a>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card urgent">
                    <div class="stat-number">3</div>
                    <div class="stat-label">🚨 URGENT Priority</div>
                    <div class="stat-change positive">STAT Processing Required</div>
                </div>
                
                <div class="stat-card pending">
                    <div class="stat-number">12</div>
                    <div class="stat-label">📋 Pending Review</div>
                    <div class="stat-change positive">+2 since yesterday</div>
                </div>
                
                <div class="stat-card approved">
                    <div class="stat-number">8</div>
                    <div class="stat-label">✅ Approved This Week</div>
                    <div class="stat-change positive">+25% vs last week</div>
                </div>
                
                <div class="stat-card philhealth">
                    <div class="stat-number">₱45K</div>
                    <div class="stat-label">💰 PhilHealth Claims</div>
                    <div class="stat-change positive">This month</div>
                </div>
            </div>

            <!-- PhilHealth Integration Status -->
            <div class="philhealth-section">
                <div style="display: flex; align-items: center; gap: 20px;">
                    <div class="philhealth-logo-small"></div>
                    <div style="flex: 1;">
                        <h3 style="margin: 0; color: white;">System Connected ✅</h3>
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">
                            Real-time eligibility verification active • Last sync: 2 minutes ago
                        </p>
                    </div>
                    <div>
                        <button class="btn btn-primary">View PhilHealth Dashboard</button>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Today's Schedule -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>📅 Today's Schedule</h3>
                    </div>
                    <div class="card-content">
                        <div class="schedule-item">
                            <div class="schedule-time">9:00 AM</div>
                            <div class="schedule-content">
                                <div class="patient-name">John Dela Cruz</div>
                                <div class="appointment-type">Follow-up Consultation</div>
                            </div>
                            <span class="philhealth-status">PhilHealth</span>
                        </div>
                        
                        <div class="schedule-item">
                            <div class="schedule-time">10:30 AM</div>
                            <div class="schedule-content">
                                <div class="patient-name">Maria Gonzales</div>
                                <div class="appointment-type">New Patient - Cardiology</div>
                            </div>
                            <span class="philhealth-status">PhilHealth</span>
                        </div>
                        
                        <div class="schedule-item">
                            <div class="schedule-time">2:00 PM</div>
                            <div class="schedule-content">
                                <div class="patient-name">Roberto Silva</div>
                                <div class="appointment-type">Prior Auth Review</div>
                            </div>
                            <span class="priority-badge priority-urgent">URGENT</span>
                        </div>
                        
                        <div class="schedule-item">
                            <div class="schedule-time">3:30 PM</div>
                            <div class="schedule-content">
                                <div class="patient-name">Ana Reyes</div>
                                <div class="appointment-type">Post-procedure Check</div>
                            </div>
                            <span class="philhealth-status">PhilHealth</span>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>🔔 Recent Activity</h3>
                    </div>
                    <div class="card-content">
                        <div class="activity-item">
                            <div class="activity-icon approved">✓</div>
                            <div class="activity-content">
                                <div class="activity-title">Prior Auth PA240804001 Approved</div>
                                <div class="activity-meta">MRI Brain - John Dela Cruz • 15 minutes ago</div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon philhealth">₱</div>
                            <div class="activity-content">
                                <div class="activity-title">PhilHealth Claim Processed</div>
                                <div class="activity-meta">₱8,500 approved for Patient #12345 • 1 hour ago</div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon pending">?</div>
                            <div class="activity-content">
                                <div class="activity-title">Insurance Inquiry Received</div>
                                <div class="activity-meta">Additional documentation requested • 2 hours ago</div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon urgent">!</div>
                            <div class="activity-content">
                                <div class="activity-title">STAT Priority Assignment</div>
                                <div class="activity-meta">Emergency procedure authorization • 3 hours ago</div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-icon approved">📄</div>
                            <div class="activity-content">
                                <div class="activity-title">Document Uploaded</div>
                                <div class="activity-meta">Lab results for PA240804002 • 4 hours ago</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Items -->
            <div class="content-card mt-20">
                <div class="card-header">
                    <h3>⚡ Action Items Requiring Your Attention</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border-left: 4px solid var(--warning-orange);">
                            <h4 style="color: var(--warning-orange); margin-bottom: 10px;">📋 3 Prior Auths Need Review</h4>
                            <p style="margin-bottom: 15px; color: #666;">Pending your clinical review and approval</p>
                            <button class="btn btn-warning">Review Now</button>
                        </div>
                        
                        <div style="background: #f8d7da; padding: 15px; border-radius: 8px; border-left: 4px solid var(--danger-red);">
                            <h4 style="color: var(--danger-red); margin-bottom: 10px;">🚨 2 STAT Priorities</h4>
                            <p style="margin-bottom: 15px; color: #666;">Emergency procedures requiring immediate attention</p>
                            <button class="btn btn-danger">Handle STAT</button>
                        </div>
                        
                        <div style="background: #d1ecf1; padding: 15px; border-radius: 8px; border-left: 4px solid var(--philhealth-blue);">
                            <h4 style="color: var(--philhealth-blue); margin-bottom: 10px;">💰 PhilHealth Updates</h4>
                            <p style="margin-bottom: 15px; color: #666;">5 new benefit updates available</p>
                            <button class="btn btn-philhealth">View Updates</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
