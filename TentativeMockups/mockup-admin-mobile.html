<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Admin Dashboard - AuthTracker Mobile</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        body {
            font-size: 16px;
            background: var(--light-gray);
        }
        
        .mobile-container {
            max-width: 414px;
            margin: 0 auto;
            background: var(--light-gray);
            min-height: 100vh;
            padding-bottom: 70px;
        }
        
        .admin-header-mobile {
            background: linear-gradient(135deg, var(--danger-red), var(--primary-color));
            color: white;
            padding: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }
        
        .header-user {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .admin-badge-mobile {
            background: var(--danger-red);
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
            margin-top: 3px;
        }
        
        .notification-icon {
            background: var(--accent-color);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .welcome-section {
            padding: 20px 15px;
            background: white;
            margin-bottom: 15px;
        }
        
        .welcome-section h2 {
            font-size: 1.4rem;
            margin: 0 0 5px 0;
        }
        
        .welcome-section p {
            font-size: 0.9rem;
            color: #666;
            margin: 0;
        }
        
        .system-status-mobile {
            padding: 0 15px 15px;
        }
        
        .status-grid-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .status-indicator-mobile {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid var(--success-green);
        }
        
        .status-indicator-mobile.warning {
            border-left-color: var(--warning-orange);
        }
        
        .status-indicator-mobile.error {
            border-left-color: var(--danger-red);
        }
        
        .status-icon-mobile {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }
        
        .status-title-mobile {
            font-weight: 600;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .status-value-mobile {
            font-size: 1rem;
            font-weight: 700;
            color: var(--primary-color);
        }
        
        .stats-mobile {
            padding: 0 15px 15px;
        }
        
        .stats-grid-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .stat-card-mobile {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-card-mobile.philhealth {
            border-left-color: var(--philhealth-blue);
        }
        
        .stat-card-mobile.approved {
            border-left-color: var(--success-green);
        }
        
        .stat-card-mobile.pending {
            border-left-color: var(--warning-orange);
        }
        
        .stat-number-mobile {
            font-size: 1.6rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-label-mobile {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 3px;
        }
        
        .stat-change-mobile {
            font-size: 0.7rem;
            color: var(--success-green);
        }
        
        .philhealth-integration-mobile {
            background: linear-gradient(135deg, var(--philhealth-blue), var(--philhealth-green));
            color: white;
            margin: 0 15px 15px;
            padding: 15px;
            border-radius: 10px;
        }
        
        .philhealth-header-mobile {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 12px;
        }
        
        .philhealth-logo-mobile {
            width: 40px;
            height: 20px;
            background: white;
            border-radius: 4px;
            background-image: url('../philhealth-vector-logo-seeklogo/philhealth-seeklogo.svg');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .philhealth-status-text {
            flex: 1;
        }
        
        .philhealth-title {
            font-size: 1rem;
            font-weight: 600;
            margin: 0 0 3px 0;
        }
        
        .philhealth-subtitle {
            font-size: 0.8rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .integration-grid-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 12px;
        }
        
        .integration-item-mobile {
            background: rgba(255,255,255,0.2);
            padding: 10px;
            border-radius: 6px;
            text-align: center;
        }
        
        .integration-status-mobile {
            font-size: 1.2rem;
            margin-bottom: 3px;
        }
        
        .integration-label-mobile {
            font-size: 0.7rem;
            opacity: 0.9;
        }
        
        .content-section {
            background: white;
            margin: 0 15px 15px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-header {
            background: var(--light-gray);
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            color: var(--dark-gray);
        }
        
        .section-content {
            padding: 15px;
        }
        
        .compliance-meter-mobile {
            text-align: center;
            padding: 20px;
        }
        
        .compliance-circle-mobile {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: conic-gradient(var(--success-green) 0deg 342deg, var(--light-gray) 342deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            position: relative;
        }
        
        .compliance-circle-mobile::before {
            content: '';
            width: 70px;
            height: 70px;
            background: white;
            border-radius: 50%;
            position: absolute;
        }
        
        .compliance-score-mobile {
            position: relative;
            z-index: 1;
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--success-green);
        }
        
        .compliance-details-mobile {
            text-align: left;
            margin-top: 15px;
        }
        
        .compliance-item-mobile {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .compliance-status-mobile {
            font-weight: 600;
        }
        
        .compliance-status-mobile.good {
            color: var(--success-green);
        }
        
        .compliance-status-mobile.warning {
            color: var(--warning-orange);
        }
        
        .user-activity-mobile {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .user-item-mobile {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .user-item-mobile:last-child {
            border-bottom: none;
        }
        
        .user-info-mobile {
            flex: 1;
        }
        
        .user-name-mobile {
            font-weight: 600;
            margin-bottom: 2px;
            font-size: 0.9rem;
        }
        
        .user-role-mobile {
            font-size: 0.8rem;
            color: #666;
        }
        
        .user-status-mobile {
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .user-status-mobile.online {
            background: var(--success-green);
            color: white;
        }
        
        .user-status-mobile.away {
            background: var(--warning-orange);
            color: white;
        }
        
        .user-status-mobile.offline {
            background: var(--light-gray);
            color: var(--dark-gray);
        }
        
        .activity-log-mobile {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .log-item-mobile {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .log-item-mobile:last-child {
            border-bottom: none;
        }
        
        .log-time-mobile {
            width: 50px;
            font-size: 0.7rem;
            color: #666;
        }
        
        .log-content-mobile {
            flex: 1;
            margin-left: 8px;
        }
        
        .log-action-mobile {
            font-size: 0.8rem;
            color: var(--dark-gray);
        }
        
        .log-user-mobile {
            font-size: 0.7rem;
            color: #666;
        }
        
        .log-severity-mobile {
            width: 40px;
            text-align: center;
        }
        
        .severity-mobile {
            padding: 1px 4px;
            border-radius: 6px;
            font-size: 0.6rem;
            font-weight: 600;
        }
        
        .severity-mobile.info {
            background: var(--primary-color);
            color: white;
        }
        
        .severity-mobile.warning {
            background: var(--warning-orange);
            color: white;
        }
        
        .severity-mobile.error {
            background: var(--danger-red);
            color: white;
        }
        
        .admin-actions-mobile {
            padding: 0 15px 15px;
        }
        
        .actions-grid-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .admin-action-btn-mobile {
            background: var(--primary-color);
            color: white;
            padding: 12px 8px;
            border-radius: 8px;
            text-decoration: none;
            text-align: center;
            font-weight: 600;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            min-height: 50px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .admin-action-btn-mobile.philhealth {
            background: var(--philhealth-blue);
        }
        
        .admin-action-btn-mobile.warning {
            background: var(--warning-orange);
        }
        
        .admin-action-btn-mobile.success {
            background: var(--success-green);
        }
        
        .admin-action-btn-mobile.secondary {
            background: var(--light-gray);
            color: var(--dark-gray);
        }
        
        .admin-action-btn-mobile.danger {
            background: var(--danger-red);
        }
        
        .admin-action-btn-mobile:active {
            transform: scale(0.98);
        }
        
        .action-icon {
            font-size: 1rem;
            margin-bottom: 3px;
        }
        
        /* Bottom navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid var(--light-gray);
            padding: 10px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .nav-item-mobile {
            text-align: center;
            text-decoration: none;
            color: #666;
            font-size: 0.7rem;
            padding: 5px;
            min-width: 60px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        .nav-item-mobile.active {
            color: var(--primary-color);
        }
        
        .nav-icon {
            font-size: 1.2rem;
            margin-bottom: 2px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- Admin Header -->
        <header class="admin-header-mobile">
            <div class="header-top">
                <div>
                    <div class="header-title">AuthTracker</div>
                    <div class="header-user">Administrator</div>
                    <div class="admin-badge-mobile">System Admin</div>
                </div>
                <div class="notification-icon">12</div>
            </div>
        </header>

        <!-- Welcome Section -->
        <section class="welcome-section">
            <h2>System Administration</h2>
            <p>Monitor and manage the AuthTracker platform</p>
        </section>

        <!-- System Status -->
        <section class="system-status-mobile">
            <div class="status-grid-mobile">
                <div class="status-indicator-mobile">
                    <div class="status-icon-mobile">🟢</div>
                    <div class="status-title-mobile">System Health</div>
                    <div class="status-value-mobile">Operational</div>
                </div>
                
                <div class="status-indicator-mobile">
                    <div class="status-icon-mobile">🔗</div>
                    <div class="status-title-mobile">PhilHealth API</div>
                    <div class="status-value-mobile">Connected</div>
                </div>
                
                <div class="status-indicator-mobile warning">
                    <div class="status-icon-mobile">⚠️</div>
                    <div class="status-title-mobile">Database Load</div>
                    <div class="status-value-mobile">78%</div>
                </div>
                
                <div class="status-indicator-mobile">
                    <div class="status-icon-mobile">🔒</div>
                    <div class="status-title-mobile">Security</div>
                    <div class="status-value-mobile">Secure</div>
                </div>
            </div>
        </section>

        <!-- Stats Grid -->
        <section class="stats-mobile">
            <div class="stats-grid-mobile">
                <div class="stat-card-mobile">
                    <div class="stat-number-mobile">500+</div>
                    <div class="stat-label-mobile">🏢 Facilities</div>
                    <div class="stat-change-mobile">+12 this month</div>
                </div>
                
                <div class="stat-card-mobile philhealth">
                    <div class="stat-number-mobile">₱2.5M</div>
                    <div class="stat-label-mobile">💰 Daily Claims</div>
                    <div class="stat-change-mobile">+8% vs yesterday</div>
                </div>
                
                <div class="stat-card-mobile approved">
                    <div class="stat-number-mobile">99.2%</div>
                    <div class="stat-label-mobile">⚡ Uptime</div>
                    <div class="stat-change-mobile">This month</div>
                </div>
                
                <div class="stat-card-mobile pending">
                    <div class="stat-number-mobile">1,247</div>
                    <div class="stat-label-mobile">👥 Active Users</div>
                    <div class="stat-change-mobile">Peak: 1,350</div>
                </div>
            </div>
        </section>

        <!-- PhilHealth Integration -->
        <section class="philhealth-integration-mobile">
            <div class="philhealth-header-mobile">
                <div class="philhealth-logo-mobile"></div>
                <div class="philhealth-status-text">
                    <div class="philhealth-title">Real-time Integration Active</div>
                    <div class="philhealth-subtitle">All services operational • Last sync: 30s ago</div>
                </div>
            </div>
            <div class="integration-grid-mobile">
                <div class="integration-item-mobile">
                    <div class="integration-status-mobile">✅</div>
                    <div class="integration-label-mobile">Eligibility API</div>
                </div>
                <div class="integration-item-mobile">
                    <div class="integration-status-mobile">✅</div>
                    <div class="integration-label-mobile">Claims Submit</div>
                </div>
                <div class="integration-item-mobile">
                    <div class="integration-status-mobile">✅</div>
                    <div class="integration-label-mobile">Benefit Verify</div>
                </div>
                <div class="integration-item-mobile">
                    <div class="integration-status-mobile">✅</div>
                    <div class="integration-label-mobile">Provider Dir</div>
                </div>
            </div>
        </section>

        <!-- HIPAA Compliance -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">🔒 HIPAA Compliance</h3>
            </div>
            <div class="section-content">
                <div class="compliance-meter-mobile">
                    <div class="compliance-circle-mobile">
                        <div class="compliance-score-mobile">95%</div>
                    </div>
                    <h4 style="margin: 0 0 15px 0;">Excellent Compliance</h4>
                    
                    <div class="compliance-details-mobile">
                        <div class="compliance-item-mobile">
                            <span>Access Controls</span>
                            <span class="compliance-status-mobile good">✅ 100%</span>
                        </div>
                        <div class="compliance-item-mobile">
                            <span>Audit Logging</span>
                            <span class="compliance-status-mobile good">✅ 98%</span>
                        </div>
                        <div class="compliance-item-mobile">
                            <span>Data Encryption</span>
                            <span class="compliance-status-mobile good">✅ 100%</span>
                        </div>
                        <div class="compliance-item-mobile">
                            <span>PHI Protection</span>
                            <span class="compliance-status-mobile warning">⚠️ 85%</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- User Activity -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">👥 Recent User Activity</h3>
            </div>
            <div class="section-content">
                <div class="user-activity-mobile">
                    <div class="user-item-mobile">
                        <div class="user-info-mobile">
                            <div class="user-name-mobile">Dr. Maria Santos</div>
                            <div class="user-role-mobile">Provider • Cardiology Associates</div>
                        </div>
                        <div class="user-status-mobile online">Online</div>
                    </div>
                    
                    <div class="user-item-mobile">
                        <div class="user-info-mobile">
                            <div class="user-name-mobile">Sarah Mendoza</div>
                            <div class="user-role-mobile">Staff • Cardiology Associates</div>
                        </div>
                        <div class="user-status-mobile online">Online</div>
                    </div>
                    
                    <div class="user-item-mobile">
                        <div class="user-info-mobile">
                            <div class="user-name-mobile">Carmen Rodriguez</div>
                            <div class="user-role-mobile">Billing • Cardiology Associates</div>
                        </div>
                        <div class="user-status-mobile away">Away</div>
                    </div>
                    
                    <div class="user-item-mobile">
                        <div class="user-info-mobile">
                            <div class="user-name-mobile">Dr. Roberto Silva</div>
                            <div class="user-role-mobile">Provider • Metro Medical</div>
                        </div>
                        <div class="user-status-mobile offline">Offline</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- System Activity Log -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">📋 System Activity Log</h3>
            </div>
            <div class="section-content">
                <div class="activity-log-mobile">
                    <div class="log-item-mobile">
                        <div class="log-time-mobile">10:45</div>
                        <div class="log-content-mobile">
                            <div class="log-action-mobile">New prior auth created</div>
                            <div class="log-user-mobile">Dr. Santos</div>
                        </div>
                        <div class="log-severity-mobile">
                            <span class="severity-mobile info">INFO</span>
                        </div>
                    </div>
                    
                    <div class="log-item-mobile">
                        <div class="log-time-mobile">10:42</div>
                        <div class="log-content-mobile">
                            <div class="log-action-mobile">PhilHealth sync completed</div>
                            <div class="log-user-mobile">System</div>
                        </div>
                        <div class="log-severity-mobile">
                            <span class="severity-mobile info">INFO</span>
                        </div>
                    </div>
                    
                    <div class="log-item-mobile">
                        <div class="log-time-mobile">10:25</div>
                        <div class="log-content-mobile">
                            <div class="log-action-mobile">Failed login attempt</div>
                            <div class="log-user-mobile">IP: *************</div>
                        </div>
                        <div class="log-severity-mobile">
                            <span class="severity-mobile warning">WARN</span>
                        </div>
                    </div>
                    
                    <div class="log-item-mobile">
                        <div class="log-time-mobile">10:15</div>
                        <div class="log-content-mobile">
                            <div class="log-action-mobile">Semantic protection activated</div>
                            <div class="log-user-mobile">System</div>
                        </div>
                        <div class="log-severity-mobile">
                            <span class="severity-mobile info">INFO</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Admin Actions -->
        <section class="admin-actions-mobile">
            <div class="actions-grid-mobile">
                <a href="#" class="admin-action-btn-mobile">
                    <div class="action-icon">👥</div>
                    <div>Users</div>
                </a>
                <a href="#" class="admin-action-btn-mobile philhealth">
                    <div class="action-icon">🏥</div>
                    <div>PhilHealth</div>
                </a>
                <a href="#" class="admin-action-btn-mobile warning">
                    <div class="action-icon">⚠️</div>
                    <div>Alerts</div>
                </a>
                <a href="#" class="admin-action-btn-mobile success">
                    <div class="action-icon">📊</div>
                    <div>Reports</div>
                </a>
                <a href="#" class="admin-action-btn-mobile secondary">
                    <div class="action-icon">🔧</div>
                    <div>Config</div>
                </a>
                <a href="#" class="admin-action-btn-mobile danger">
                    <div class="action-icon">🔒</div>
                    <div>Security</div>
                </a>
            </div>
        </section>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <a href="#" class="nav-item-mobile active">
                <span class="nav-icon">🏠</span>
                Dashboard
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">👥</span>
                Users
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">🏢</span>
                Practices
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">📊</span>
                Analytics
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">🔒</span>
                Security
            </a>
        </nav>
    </div>
</body>
</html>
