<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Staff Dashboard - AuthTracker Mobile</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        body {
            font-size: 16px;
            background: var(--light-gray);
        }
        
        .mobile-container {
            max-width: 414px;
            margin: 0 auto;
            background: var(--light-gray);
            min-height: 100vh;
            padding-bottom: 70px;
        }
        
        .mobile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--philhealth-blue));
            color: white;
            padding: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
        }
        
        .header-user {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .notification-icon {
            background: var(--accent-color);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .welcome-section {
            padding: 20px 15px;
            background: white;
            margin-bottom: 15px;
        }
        
        .welcome-section h2 {
            font-size: 1.4rem;
            margin: 0 0 5px 0;
        }
        
        .welcome-section p {
            font-size: 0.9rem;
            color: #666;
            margin: 0;
        }
        
        .stats-mobile {
            padding: 0 15px 15px;
        }
        
        .stats-grid-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .stat-card-mobile {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-card-mobile.pending {
            border-left-color: var(--warning-orange);
        }
        
        .stat-card-mobile.urgent {
            border-left-color: var(--danger-red);
        }
        
        .stat-card-mobile.approved {
            border-left-color: var(--success-green);
        }
        
        .stat-card-mobile.philhealth {
            border-left-color: var(--philhealth-blue);
        }
        
        .stat-number-mobile {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-label-mobile {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 3px;
        }
        
        .stat-change-mobile {
            font-size: 0.7rem;
            color: var(--success-green);
        }
        
        .stat-change-mobile.negative {
            color: var(--danger-red);
        }
        
        .philhealth-status-mobile {
            background: linear-gradient(135deg, var(--philhealth-blue), var(--philhealth-green));
            color: white;
            margin: 0 15px 15px;
            padding: 15px;
            border-radius: 10px;
        }
        
        .philhealth-header-mobile {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .philhealth-logo-mobile {
            width: 40px;
            height: 20px;
            background: white;
            border-radius: 4px;
            background-image: url('../philhealth-vector-logo-seeklogo/philhealth-seeklogo.svg');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .philhealth-status-text {
            flex: 1;
        }
        
        .philhealth-title {
            font-size: 1rem;
            font-weight: 600;
            margin: 0 0 3px 0;
        }
        
        .philhealth-subtitle {
            font-size: 0.8rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .content-section {
            background: white;
            margin: 0 15px 15px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-header {
            background: var(--light-gray);
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            color: var(--dark-gray);
        }
        
        .section-content {
            padding: 15px;
        }
        
        .task-item-mobile {
            display: flex;
            align-items: center;
            padding: 12px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .task-item-mobile.overdue {
            border-left-color: var(--danger-red);
            background: #fff5f5;
        }
        
        .task-item-mobile.urgent {
            border-left-color: var(--warning-orange);
            background: #fffbf0;
        }
        
        .task-checkbox-mobile {
            width: 18px;
            height: 18px;
            margin-right: 12px;
            cursor: pointer;
        }
        
        .task-content-mobile {
            flex: 1;
        }
        
        .task-title-mobile {
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 3px;
        }
        
        .task-meta-mobile {
            font-size: 0.8rem;
            color: #666;
        }
        
        .task-action-mobile {
            background: var(--primary-color);
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .task-action-mobile.urgent {
            background: var(--danger-red);
        }
        
        .task-action-mobile.warning {
            background: var(--warning-orange);
        }
        
        .reminder-item-mobile {
            background: var(--mint-color);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid var(--primary-color);
        }
        
        .reminder-item-mobile.due-today {
            background: #fff3cd;
            border-left-color: var(--warning-orange);
        }
        
        .reminder-time-mobile {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .reminder-text-mobile {
            margin-bottom: 8px;
            font-size: 0.9rem;
        }
        
        .reminder-actions-mobile {
            display: flex;
            gap: 8px;
        }
        
        .reminder-btn-mobile {
            padding: 4px 8px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .reminder-btn-primary {
            background: var(--primary-color);
            color: white;
        }
        
        .reminder-btn-secondary {
            background: var(--light-gray);
            color: var(--dark-gray);
        }
        
        .call-log-item-mobile {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .call-log-item-mobile:last-child {
            border-bottom: none;
        }
        
        .call-status-mobile {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 12px;
        }
        
        .call-status-mobile.completed { background: var(--success-green); }
        .call-status-mobile.pending { background: var(--warning-orange); }
        .call-status-mobile.failed { background: var(--danger-red); }
        
        .call-content-mobile {
            flex: 1;
        }
        
        .call-company-mobile {
            font-weight: 600;
            margin-bottom: 2px;
            font-size: 0.9rem;
        }
        
        .call-purpose-mobile {
            font-size: 0.8rem;
            color: #666;
        }
        
        .call-time-mobile {
            font-size: 0.7rem;
            color: #999;
        }
        
        .quick-actions-mobile {
            padding: 0 15px 15px;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
        }
        
        .action-btn-mobile {
            background: var(--primary-color);
            color: white;
            padding: 12px 8px;
            border-radius: 8px;
            text-decoration: none;
            text-align: center;
            font-weight: 600;
            font-size: 0.8rem;
            transition: all 0.3s ease;
            min-height: 50px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .action-btn-mobile.philhealth {
            background: var(--philhealth-blue);
        }
        
        .action-btn-mobile.secondary {
            background: var(--light-gray);
            color: var(--dark-gray);
        }
        
        .action-btn-mobile.accent {
            background: var(--accent-color);
        }
        
        .action-btn-mobile.success {
            background: var(--success-green);
        }
        
        .action-btn-mobile.warning {
            background: var(--warning-orange);
        }
        
        .action-btn-mobile:active {
            transform: scale(0.98);
        }
        
        .action-icon {
            font-size: 1rem;
            margin-bottom: 3px;
        }
        
        /* Bottom navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid var(--light-gray);
            padding: 10px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .nav-item-mobile {
            text-align: center;
            text-decoration: none;
            color: #666;
            font-size: 0.7rem;
            padding: 5px;
            min-width: 60px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        .nav-item-mobile.active {
            color: var(--primary-color);
        }
        
        .nav-icon {
            font-size: 1.2rem;
            margin-bottom: 2px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- Mobile Header -->
        <header class="mobile-header">
            <div class="header-top">
                <div>
                    <div class="header-title">AuthTracker</div>
                    <div class="header-user">Sarah Mendoza (Staff)</div>
                </div>
                <div class="notification-icon">3</div>
            </div>
        </header>

        <!-- Welcome Section -->
        <section class="welcome-section">
            <h2>Good morning, Sarah</h2>
            <p>Your task overview for today</p>
        </section>

        <!-- Stats Grid -->
        <section class="stats-mobile">
            <div class="stats-grid-mobile">
                <div class="stat-card-mobile pending">
                    <div class="stat-number-mobile">8</div>
                    <div class="stat-label-mobile">📞 Calls Today</div>
                    <div class="stat-change-mobile">Target: 10</div>
                </div>
                
                <div class="stat-card-mobile urgent">
                    <div class="stat-number-mobile">5</div>
                    <div class="stat-label-mobile">📝 Tasks</div>
                    <div class="stat-change-mobile negative">2 overdue</div>
                </div>
                
                <div class="stat-card-mobile approved">
                    <div class="stat-number-mobile">3</div>
                    <div class="stat-label-mobile">⏰ Reminders</div>
                    <div class="stat-change-mobile">Due today</div>
                </div>
                
                <div class="stat-card-mobile philhealth">
                    <div class="stat-number-mobile">12</div>
                    <div class="stat-label-mobile">📄 Documents</div>
                    <div class="stat-change-mobile">This week</div>
                </div>
            </div>
        </section>

        <!-- PhilHealth Status -->
        <section class="philhealth-status-mobile">
            <div class="philhealth-header-mobile">
                <div class="philhealth-logo-mobile"></div>
                <div class="philhealth-status-text">
                    <div class="philhealth-title">Eligibility Checker Available</div>
                    <div class="philhealth-subtitle">24/7 access • Verify instantly</div>
                </div>
            </div>
        </section>

        <!-- My Tasks -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">📝 My Tasks</h3>
            </div>
            <div class="section-content">
                <div class="task-item-mobile overdue">
                    <input type="checkbox" class="task-checkbox-mobile">
                    <div class="task-content-mobile">
                        <div class="task-title-mobile">Call insurance for PA240804001</div>
                        <div class="task-meta-mobile">Overdue by 2 hours</div>
                    </div>
                    <a href="#" class="task-action-mobile urgent">Call</a>
                </div>
                
                <div class="task-item-mobile urgent">
                    <input type="checkbox" class="task-checkbox-mobile">
                    <div class="task-content-mobile">
                        <div class="task-title-mobile">Follow up PA240804002</div>
                        <div class="task-meta-mobile">Due in 1 hour</div>
                    </div>
                    <a href="#" class="task-action-mobile warning">Follow</a>
                </div>
                
                <div class="task-item-mobile">
                    <input type="checkbox" class="task-checkbox-mobile">
                    <div class="task-content-mobile">
                        <div class="task-title-mobile">Upload lab results</div>
                        <div class="task-meta-mobile">Due today</div>
                    </div>
                    <a href="#" class="task-action-mobile">Upload</a>
                </div>
            </div>
        </section>

        <!-- Today's Reminders -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">⏰ Today's Reminders</h3>
            </div>
            <div class="section-content">
                <div class="reminder-item-mobile due-today">
                    <div class="reminder-time-mobile">11:00 AM</div>
                    <div class="reminder-text-mobile">Call PhilHealth for claim status - PA240804001</div>
                    <div class="reminder-actions-mobile">
                        <a href="#" class="reminder-btn-mobile reminder-btn-primary">Call</a>
                        <a href="#" class="reminder-btn-mobile reminder-btn-secondary">Snooze</a>
                    </div>
                </div>
                
                <div class="reminder-item-mobile">
                    <div class="reminder-time-mobile">2:00 PM</div>
                    <div class="reminder-text-mobile">Follow up with Dr. Santos</div>
                    <div class="reminder-actions-mobile">
                        <a href="#" class="reminder-btn-mobile reminder-btn-primary">Follow</a>
                        <a href="#" class="reminder-btn-mobile reminder-btn-secondary">Later</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Communication Log -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">📞 Today's Calls</h3>
            </div>
            <div class="section-content">
                <div class="call-log-item-mobile">
                    <div class="call-status-mobile completed"></div>
                    <div class="call-content-mobile">
                        <div class="call-company-mobile">PhilHealth Regional</div>
                        <div class="call-purpose-mobile">Claim status inquiry</div>
                    </div>
                    <div class="call-time-mobile">9:30 AM</div>
                </div>
                
                <div class="call-log-item-mobile">
                    <div class="call-status-mobile completed"></div>
                    <div class="call-content-mobile">
                        <div class="call-company-mobile">Maxicare Healthcare</div>
                        <div class="call-purpose-mobile">Prior auth follow-up</div>
                    </div>
                    <div class="call-time-mobile">10:15 AM</div>
                </div>
                
                <div class="call-log-item-mobile">
                    <div class="call-status-mobile pending"></div>
                    <div class="call-content-mobile">
                        <div class="call-company-mobile">Intellicare</div>
                        <div class="call-purpose-mobile">Document confirmation</div>
                    </div>
                    <div class="call-time-mobile">11:30 AM</div>
                </div>
            </div>
        </section>

        <!-- Quick Actions -->
        <section class="quick-actions-mobile">
            <div class="actions-grid">
                <a href="#" class="action-btn-mobile">
                    <div class="action-icon">📞</div>
                    <div>Call</div>
                </a>
                <a href="#" class="action-btn-mobile philhealth">
                    <div class="action-icon">🏥</div>
                    <div>PhilHealth</div>
                </a>
                <a href="#" class="action-btn-mobile secondary">
                    <div class="action-icon">📄</div>
                    <div>Upload</div>
                </a>
                <a href="#" class="action-btn-mobile accent">
                    <div class="action-icon">📝</div>
                    <div>Update</div>
                </a>
                <a href="#" class="action-btn-mobile success">
                    <div class="action-icon">✅</div>
                    <div>Complete</div>
                </a>
                <a href="#" class="action-btn-mobile warning">
                    <div class="action-icon">⏰</div>
                    <div>Remind</div>
                </a>
            </div>
        </section>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <a href="#" class="nav-item-mobile active">
                <span class="nav-icon">🏠</span>
                Dashboard
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">👥</span>
                Patients
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">📋</span>
                Status
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">📞</span>
                Calls
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">📝</span>
                Tasks
            </a>
        </nav>
    </div>
</body>
</html>
