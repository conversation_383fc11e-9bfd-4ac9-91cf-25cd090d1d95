/* AuthTracker Shared Styles */
/* Color Palette: https://colorhunt.co/palette/3aa6b9ffd0d0ff9eaac1ece4 */

/* Font Face Declarations for Museo Slab Rounded */
@font-face {
  font-family: 'Museo Slab';
  src: url('../museo-slab/Museo_Slab_100.otf') format('opentype');
  font-weight: 100;
  font-style: normal;
}

@font-face {
  font-family: 'Museo Slab';
  src: url('../museo-slab/Museo_Slab_300.otf') format('opentype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Museo Slab';
  src: url('../museo-slab/Museo_Slab_500.otf') format('opentype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Museo Slab';
  src: url('../museo-slab/Museo_Slab_700.otf') format('opentype');
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Museo Slab';
  src: url('../museo-slab/Museo_Slab_900.otf') format('opentype');
  font-weight: 900;
  font-style: normal;
}

/* Quicksand from Google Fonts */
@font-face {
  font-family: 'Quicksand';
  src: url('../quicksand/Quicksand-VariableFont_wght.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

:root {
  --primary-color: #3AA6B9;    /* Teal Blue */
  --secondary-color: #FFD0D0;  /* Light Pink */
  --accent-color: #FF9EAA;     /* Pink */
  --mint-color: #C1ECE4;       /* Mint Green */
  --white: #FFFFFF;
  --dark-gray: #2C3E50;
  --light-gray: #ECF0F1;
  --success-green: #27AE60;
  --warning-orange: #F39C12;
  --danger-red: #E74C3C;
  --philhealth-blue: #0066CC;
  --philhealth-green: #009639;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Quicksand', sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: var(--dark-gray);
  background-color: var(--mint-color);
}

/* Typography */
h1 {
  font-family: 'Museo Slab', serif;
  font-weight: 700;
  font-size: 2.5rem;
  color: var(--primary-color);
}
h2 {
  font-family: 'Museo Slab', serif;
  font-weight: 500;
  font-size: 2rem;
  color: var(--primary-color);
}
h3 {
  font-family: 'Quicksand', sans-serif;
  font-weight: 600;
  font-size: 1.5rem;
  color: var(--dark-gray);
}
h4 {
  font-family: 'Quicksand', sans-serif;
  font-weight: 600;
  font-size: 1.25rem;
  color: var(--dark-gray);
}
h5 {
  font-family: 'Quicksand', sans-serif;
  font-weight: 500;
  font-size: 1.1rem;
  color: var(--dark-gray);
}

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Layout Components */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.header {
  background: linear-gradient(135deg, var(--primary-color), var(--philhealth-blue));
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo h1 {
  color: white;
  font-weight: 700;
  margin: 0;
}

.philhealth-badge {
  background: var(--philhealth-green);
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
  color: white;
}

.notification-badge {
  background: var(--accent-color);
  color: white;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Navigation */
.nav-bar {
  background: white;
  border-bottom: 3px solid var(--mint-color);
  padding: 0;
}

.nav-content {
  display: flex;
  gap: 0;
}

.nav-item {
  padding: 15px 25px;
  text-decoration: none;
  color: var(--dark-gray);
  font-weight: 500;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.nav-item:hover, .nav-item.active {
  background: var(--mint-color);
  border-bottom-color: var(--primary-color);
  color: var(--primary-color);
}

/* Dashboard Grid */
.dashboard {
  padding: 30px 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  border-left: 5px solid var(--primary-color);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card.urgent {
  border-left-color: var(--danger-red);
}

.stat-card.pending {
  border-left-color: var(--warning-orange);
}

.stat-card.approved {
  border-left-color: var(--success-green);
}

.stat-card.philhealth {
  border-left-color: var(--philhealth-blue);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 5px;
}

.stat-label {
  color: var(--dark-gray);
  font-weight: 500;
  margin-bottom: 10px;
}

.stat-change {
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-change.positive {
  color: var(--success-green);
}

.stat-change.negative {
  color: var(--danger-red);
}

/* Content Sections */
.content-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  margin-top: 30px;
}

.content-card {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  overflow: hidden;
}

.card-header {
  background: var(--light-gray);
  padding: 20px;
  border-bottom: 1px solid #ddd;
}

.card-header h3 {
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-content {
  padding: 20px;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-family: 'Quicksand', sans-serif;
  font-weight: 600;
  text-decoration: none;
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: #2E8B9A;
  transform: translateY(-2px);
}

.btn-secondary {
  background: var(--secondary-color);
  color: var(--dark-gray);
}

.btn-accent {
  background: var(--accent-color);
  color: white;
}

.btn-philhealth {
  background: var(--philhealth-blue);
  color: white;
}

.btn-success {
  background: var(--success-green);
  color: white;
}

.btn-warning {
  background: var(--warning-orange);
  color: white;
}

.btn-danger {
  background: var(--danger-red);
  color: white;
}

/* Priority Indicators */
.priority-badge {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.priority-normal {
  background: var(--mint-color);
  color: var(--dark-gray);
}

.priority-urgent {
  background: var(--warning-orange);
  color: white;
}

.priority-stat {
  background: var(--danger-red);
  color: white;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

/* Status Indicators */
.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-submitted { background: var(--mint-color); color: var(--dark-gray); }
.status-review { background: var(--warning-orange); color: white; }
.status-approved { background: var(--success-green); color: white; }
.status-denied { background: var(--danger-red); color: white; }
.status-expired { background: var(--light-gray); color: var(--dark-gray); }

/* PhilHealth Integration */
.philhealth-section {
  background: linear-gradient(135deg, var(--philhealth-blue), var(--philhealth-green));
  color: white;
  padding: 20px;
  border-radius: 10px;
  margin: 20px 0;
}

.philhealth-logo {
  width: 120px;
  height: 60px;
  margin-bottom: 15px;
  background: white;
  padding: 10px;
  border-radius: 8px;
  background-image: url('../philhealth-vector-logo-seeklogo/philhealth-seeklogo.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}

.philhealth-logo-large {
  width: 200px;
  height: 100px;
  background: white;
  padding: 20px;
  border-radius: 15px;
  background-image: url('../philhealth-vector-logo-seeklogo/philhealth-seeklogo.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}

.philhealth-logo-small {
  width: 80px;
  height: 40px;
  background: white;
  padding: 8px;
  border-radius: 8px;
  background-image: url('../philhealth-vector-logo-seeklogo/philhealth-seeklogo.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
  }
  
  .nav-content {
    flex-wrap: wrap;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .content-grid {
    grid-template-columns: 1fr;
  }
  
  .nav-item {
    padding: 10px 15px;
  }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-right { text-align: right; }
.mb-20 { margin-bottom: 20px; }
.mt-20 { margin-top: 20px; }
.p-20 { padding: 20px; }
.hidden { display: none; }
.flex { display: flex; }
.flex-center { display: flex; align-items: center; justify-content: center; }
.gap-10 { gap: 10px; }
.gap-20 { gap: 20px; }

/* Print Styles for PDF Generation */
@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    body {
        background: white !important;
        font-size: 12pt;
        line-height: 1.4;
    }

    .mobile-container {
        max-width: none !important;
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        box-shadow: none !important;
    }

    /* Hide elements that don't make sense in print */
    .bottom-nav,
    .notification-icon,
    input[type="checkbox"],
    button:not(.btn),
    .mobile-btn:not(.btn),
    .action-btn-mobile:not(.btn) {
        display: none !important;
    }

    /* Ensure backgrounds and colors print */
    .mobile-header,
    .admin-header-mobile,
    .patient-header-mobile,
    .philhealth-status-mobile,
    .philhealth-card-mobile,
    .philhealth-dashboard-mobile,
    .philhealth-integration-mobile,
    .financial-overview-mobile {
        background: var(--primary-color) !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    /* Adjust spacing for print */
    .content-section,
    .stat-card-mobile,
    .feature-card-mobile,
    .prior-auth-card-mobile {
        margin-bottom: 15px !important;
        page-break-inside: avoid;
    }

    /* Ensure text is readable */
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
        color: var(--dark-gray) !important;
    }

    /* PhilHealth logos should print */
    .philhealth-logo,
    .philhealth-logo-small,
    .philhealth-logo-large,
    .philhealth-logo-mobile {
        background-image: url('../philhealth-vector-logo-seeklogo/philhealth-seeklogo.svg') !important;
        -webkit-print-color-adjust: exact !important;
    }

    /* Prevent page breaks in important sections */
    .stats-grid-mobile,
    .auth-details-mobile,
    .philhealth-header-mobile {
        page-break-inside: avoid;
    }

    /* Adjust font sizes for print readability */
    .stat-number-mobile {
        font-size: 18pt !important;
    }

    .section-title {
        font-size: 14pt !important;
    }

    .mobile-header .header-title {
        font-size: 16pt !important;
    }
}
