<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Patient Portal - AuthTracker Mobile</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        body {
            font-size: 16px;
            background: var(--light-gray);
        }
        
        .mobile-container {
            max-width: 414px;
            margin: 0 auto;
            background: var(--light-gray);
            min-height: 100vh;
            padding-bottom: 70px;
        }
        
        .patient-header-mobile {
            background: linear-gradient(135deg, var(--mint-color), var(--secondary-color));
            padding: 20px 15px;
            border-bottom: 3px solid var(--primary-color);
        }
        
        .patient-info-mobile {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .patient-avatar-mobile {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .patient-details-mobile {
            flex: 1;
        }
        
        .patient-name-mobile {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0 0 3px 0;
            color: var(--dark-gray);
        }
        
        .patient-meta-mobile {
            color: #666;
            font-size: 0.8rem;
            margin-bottom: 5px;
        }
        
        .security-badge-mobile {
            background: var(--success-green);
            color: white;
            padding: 3px 8px;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 600;
        }
        
        .philhealth-card-mobile {
            background: linear-gradient(135deg, var(--philhealth-blue), var(--philhealth-green));
            color: white;
            margin: 15px;
            padding: 15px;
            border-radius: 12px;
        }
        
        .philhealth-header-mobile {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 12px;
        }
        
        .philhealth-logo-mobile {
            width: 40px;
            height: 20px;
            background: white;
            border-radius: 4px;
            background-image: url('../philhealth-vector-logo-seeklogo/philhealth-seeklogo.svg');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }
        
        .philhealth-status-mobile {
            flex: 1;
        }
        
        .philhealth-member-id-mobile {
            font-size: 1rem;
            font-weight: 600;
            margin: 0 0 3px 0;
        }
        
        .philhealth-validity-mobile {
            opacity: 0.9;
            font-size: 0.8rem;
            margin: 0;
        }
        
        .philhealth-coverage-mobile {
            background: rgba(255,255,255,0.2);
            padding: 12px;
            border-radius: 8px;
            margin-top: 12px;
        }
        
        .coverage-header-mobile {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .coverage-title-mobile {
            font-weight: 600;
            margin: 0;
        }
        
        .coverage-status-mobile {
            font-size: 1.2rem;
        }
        
        .coverage-subtitle-mobile {
            opacity: 0.9;
            font-size: 0.8rem;
            margin: 3px 0 0 0;
        }
        
        .stats-mobile {
            padding: 0 15px 15px;
        }
        
        .stats-grid-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        
        .stat-card-mobile {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .stat-card-mobile.approved {
            border-left-color: var(--success-green);
        }
        
        .stat-card-mobile.pending {
            border-left-color: var(--warning-orange);
        }
        
        .stat-card-mobile.philhealth {
            border-left-color: var(--philhealth-blue);
        }
        
        .stat-number-mobile {
            font-size: 1.6rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .stat-label-mobile {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 3px;
        }
        
        .stat-change-mobile {
            font-size: 0.7rem;
            color: var(--success-green);
        }
        
        .content-section {
            background: white;
            margin: 0 15px 15px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-header {
            background: var(--light-gray);
            padding: 12px 15px;
            border-bottom: 1px solid #ddd;
        }
        
        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            color: var(--dark-gray);
        }
        
        .section-content {
            padding: 15px;
        }
        
        .prior-auth-card-mobile {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            overflow: hidden;
            border-left: 5px solid var(--primary-color);
        }
        
        .prior-auth-card-mobile.approved {
            border-left-color: var(--success-green);
        }
        
        .prior-auth-card-mobile.pending {
            border-left-color: var(--warning-orange);
        }
        
        .auth-card-header-mobile {
            background: var(--light-gray);
            padding: 12px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .auth-card-title-mobile {
            font-weight: 600;
            margin: 0;
            font-size: 1rem;
        }
        
        .auth-card-content-mobile {
            padding: 15px;
        }
        
        .auth-details-mobile {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 15px;
        }
        
        .auth-detail-item-mobile {
            display: flex;
            flex-direction: column;
        }
        
        .auth-detail-label-mobile {
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 3px;
        }
        
        .auth-detail-value-mobile {
            font-weight: 600;
            color: var(--dark-gray);
            font-size: 0.9rem;
        }
        
        .progress-bar-mobile {
            background: var(--light-gray);
            border-radius: 8px;
            height: 6px;
            margin: 12px 0;
            overflow: hidden;
        }
        
        .progress-fill-mobile {
            height: 100%;
            border-radius: 8px;
            transition: width 0.3s ease;
        }
        
        .progress-fill-mobile.submitted {
            width: 25%;
            background: var(--primary-color);
        }
        
        .progress-fill-mobile.review {
            width: 50%;
            background: var(--warning-orange);
        }
        
        .progress-fill-mobile.approved {
            width: 100%;
            background: var(--success-green);
        }
        
        .progress-steps-mobile {
            display: flex;
            justify-content: space-between;
            font-size: 0.7rem;
            color: #666;
            margin-top: 5px;
        }
        
        .status-update-mobile {
            margin-top: 12px;
            padding: 12px;
            background: #fff3cd;
            border-radius: 8px;
        }
        
        .status-update-title-mobile {
            font-weight: 600;
            color: var(--warning-orange);
            margin-bottom: 5px;
            font-size: 0.9rem;
        }
        
        .status-update-text-mobile {
            color: #666;
            font-size: 0.8rem;
            line-height: 1.4;
        }
        
        .auth-actions-mobile {
            margin-top: 12px;
            display: flex;
            gap: 8px;
        }
        
        .auth-btn-mobile {
            background: var(--primary-color);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 0.8rem;
            font-weight: 600;
            flex: 1;
            text-align: center;
        }
        
        .auth-btn-mobile.secondary {
            background: var(--light-gray);
            color: var(--dark-gray);
        }

        .auth-btn-mobile:active {
            transform: scale(0.98);
        }
        
        .help-section-mobile {
            background: var(--mint-color);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .help-icon-mobile {
            font-size: 2.5rem;
            margin-bottom: 12px;
        }
        
        .help-title-mobile {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .help-text-mobile {
            margin: 12px 0;
            color: #666;
            font-size: 0.9rem;
        }
        
        .help-btn-mobile {
            background: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            display: inline-block;
        }
        
        .contact-item-mobile {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 10px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .contact-item-mobile:last-child {
            border-bottom: none;
        }
        
        .contact-icon-mobile {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            font-weight: 600;
        }
        
        .contact-content-mobile {
            flex: 1;
        }
        
        .contact-title-mobile {
            font-weight: 600;
            margin-bottom: 2px;
            font-size: 0.9rem;
        }
        
        .contact-detail-mobile {
            color: #666;
            font-size: 0.8rem;
        }
        
        /* Bottom navigation */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid var(--light-gray);
            padding: 10px 0;
            display: flex;
            justify-content: space-around;
        }
        
        .nav-item-mobile {
            text-align: center;
            text-decoration: none;
            color: #666;
            font-size: 0.7rem;
            padding: 5px;
            min-width: 60px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }
        
        .nav-item-mobile.active {
            color: var(--primary-color);
        }
        
        .nav-icon {
            font-size: 1.2rem;
            margin-bottom: 2px;
            display: block;
        }
    </style>
</head>
<body>
    <div class="mobile-container">
        <!-- Patient Header -->
        <header class="patient-header-mobile">
            <div class="patient-info-mobile">
                <div class="patient-avatar-mobile">JD</div>
                <div class="patient-details-mobile">
                    <div class="patient-name-mobile">Juan Dela Cruz</div>
                    <div class="patient-meta-mobile">Patient ID: 12345 | DOB: Jan 15, 1980</div>
                    <div class="security-badge-mobile">🔒 Secure Session</div>
                </div>
            </div>
        </header>

        <!-- PhilHealth Status -->
        <section class="philhealth-card-mobile">
            <div class="philhealth-header-mobile">
                <div class="philhealth-logo-mobile"></div>
                <div class="philhealth-status-mobile">
                    <div class="philhealth-member-id-mobile">Member ID: 12-***********</div>
                    <div class="philhealth-validity-mobile">Active Member • Valid until Dec 2025</div>
                </div>
            </div>
            <div class="philhealth-coverage-mobile">
                <div class="coverage-header-mobile">
                    <div>
                        <div class="coverage-title-mobile">Current Coverage Status</div>
                        <div class="coverage-subtitle-mobile">Premium payments up to date</div>
                    </div>
                    <div class="coverage-status-mobile">✅</div>
                </div>
            </div>
        </section>

        <!-- Stats Overview -->
        <section class="stats-mobile">
            <div class="stats-grid-mobile">
                <div class="stat-card-mobile approved">
                    <div class="stat-number-mobile">3</div>
                    <div class="stat-label-mobile">✅ Approved</div>
                    <div class="stat-change-mobile">This year</div>
                </div>
                
                <div class="stat-card-mobile pending">
                    <div class="stat-number-mobile">1</div>
                    <div class="stat-label-mobile">📋 Pending</div>
                    <div class="stat-change-mobile">In progress</div>
                </div>
                
                <div class="stat-card-mobile philhealth">
                    <div class="stat-number-mobile">₱45K</div>
                    <div class="stat-label-mobile">💰 Coverage</div>
                    <div class="stat-change-mobile">PhilHealth</div>
                </div>
                
                <div class="stat-card-mobile">
                    <div class="stat-number-mobile">2.3</div>
                    <div class="stat-label-mobile">⏱️ Avg Days</div>
                    <div class="stat-change-mobile">Processing</div>
                </div>
            </div>
        </section>

        <!-- Prior Authorization Requests -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">📋 My Prior Authorization Requests</h3>
            </div>
            <div class="section-content">
                <!-- Active Request -->
                <div class="prior-auth-card-mobile pending">
                    <div class="auth-card-header-mobile">
                        <h4 class="auth-card-title-mobile">Cardiac MRI Scan</h4>
                        <span class="status-badge status-review">Under Review</span>
                    </div>
                    <div class="auth-card-content-mobile">
                        <div class="auth-details-mobile">
                            <div class="auth-detail-item-mobile">
                                <div class="auth-detail-label-mobile">Tracking ID</div>
                                <div class="auth-detail-value-mobile">PA240804001</div>
                            </div>
                            <div class="auth-detail-item-mobile">
                                <div class="auth-detail-label-mobile">Requested</div>
                                <div class="auth-detail-value-mobile">Aug 1, 2025</div>
                            </div>
                            <div class="auth-detail-item-mobile">
                                <div class="auth-detail-label-mobile">Provider</div>
                                <div class="auth-detail-value-mobile">Dr. Maria Santos</div>
                            </div>
                            <div class="auth-detail-item-mobile">
                                <div class="auth-detail-label-mobile">Insurance</div>
                                <div class="auth-detail-value-mobile">PhilHealth</div>
                            </div>
                        </div>
                        
                        <div class="progress-bar-mobile">
                            <div class="progress-fill-mobile review"></div>
                        </div>
                        <div class="progress-steps-mobile">
                            <span>✅ Submitted</span>
                            <span style="color: var(--warning-orange); font-weight: 600;">📋 Review</span>
                            <span>Decision</span>
                            <span>Complete</span>
                        </div>
                        
                        <div class="status-update-mobile">
                            <div class="status-update-title-mobile">📋 Status Update</div>
                            <div class="status-update-text-mobile">
                                Your request is being reviewed. Expected decision by August 6, 2025.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Approved Request -->
                <div class="prior-auth-card-mobile approved">
                    <div class="auth-card-header-mobile">
                        <h4 class="auth-card-title-mobile">Echocardiogram</h4>
                        <span class="status-badge status-approved">Approved</span>
                    </div>
                    <div class="auth-card-content-mobile">
                        <div class="auth-details-mobile">
                            <div class="auth-detail-item-mobile">
                                <div class="auth-detail-label-mobile">Tracking ID</div>
                                <div class="auth-detail-value-mobile">PA240715002</div>
                            </div>
                            <div class="auth-detail-item-mobile">
                                <div class="auth-detail-label-mobile">Approved</div>
                                <div class="auth-detail-value-mobile">Jul 18, 2025</div>
                            </div>
                            <div class="auth-detail-item-mobile">
                                <div class="auth-detail-label-mobile">Coverage</div>
                                <div class="auth-detail-value-mobile">₱8,500</div>
                            </div>
                            <div class="auth-detail-item-mobile">
                                <div class="auth-detail-label-mobile">Approval #</div>
                                <div class="auth-detail-value-mobile">APV-789123</div>
                            </div>
                        </div>
                        
                        <div class="progress-bar-mobile">
                            <div class="progress-fill-mobile approved"></div>
                        </div>
                        <div class="progress-steps-mobile">
                            <span>✅ Submitted</span>
                            <span>✅ Reviewed</span>
                            <span>✅ Approved</span>
                            <span style="color: var(--success-green); font-weight: 600;">✅ Complete</span>
                        </div>
                        
                        <div class="auth-actions-mobile">
                            <a href="#" class="auth-btn-mobile">Download Letter</a>
                            <a href="#" class="auth-btn-mobile secondary">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Help & Support -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">❓ Help & Support</h3>
            </div>
            <div class="section-content">
                <div class="help-section-mobile">
                    <div class="help-icon-mobile">🤝</div>
                    <div class="help-title-mobile">Need Help?</div>
                    <div class="help-text-mobile">
                        Our support team is here to help you understand your prior authorization status.
                    </div>
                    <a href="#" class="help-btn-mobile">Contact Support</a>
                </div>
            </div>
        </section>

        <!-- Contact Information -->
        <section class="content-section">
            <div class="section-header">
                <h3 class="section-title">📞 Contact Information</h3>
            </div>
            <div class="section-content">
                <div class="contact-item-mobile">
                    <div class="contact-icon-mobile">🏥</div>
                    <div class="contact-content-mobile">
                        <div class="contact-title-mobile">Healthcare Provider</div>
                        <div class="contact-detail-mobile">Dr. Maria Santos<br>Cardiology Associates</div>
                    </div>
                </div>
                
                <div class="contact-item-mobile">
                    <div class="contact-icon-mobile">📞</div>
                    <div class="contact-content-mobile">
                        <div class="contact-title-mobile">Clinic Phone</div>
                        <div class="contact-detail-mobile">(02) 8123-4567</div>
                    </div>
                </div>
                
                <div class="contact-item-mobile">
                    <div class="contact-icon-mobile">🏢</div>
                    <div class="contact-content-mobile">
                        <div class="contact-title-mobile">PhilHealth Hotline</div>
                        <div class="contact-detail-mobile">917-7777 (Metro Manila)</div>
                    </div>
                </div>
                
                <div class="contact-item-mobile">
                    <div class="contact-icon-mobile">💬</div>
                    <div class="contact-content-mobile">
                        <div class="contact-title-mobile">Patient Support</div>
                        <div class="contact-detail-mobile"><EMAIL></div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Bottom Navigation -->
        <nav class="bottom-nav">
            <a href="#" class="nav-item-mobile active">
                <span class="nav-icon">📋</span>
                Prior Auth
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">👤</span>
                Profile
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">💬</span>
                Messages
            </a>
            <a href="#" class="nav-item-mobile">
                <span class="nav-icon">❓</span>
                Help
            </a>
        </nav>
    </div>
</body>
</html>
