<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Dashboard - AuthTracker</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .task-item {
            display: flex;
            align-items: center;
            padding: 15px;
            background: white;
            border-radius: 8px;
            margin-bottom: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid var(--primary-color);
        }
        
        .task-item.overdue {
            border-left-color: var(--danger-red);
            background: #fff5f5;
        }
        
        .task-item.urgent {
            border-left-color: var(--warning-orange);
            background: #fffbf0;
        }
        
        .task-checkbox {
            width: 20px;
            height: 20px;
            margin-right: 15px;
            cursor: pointer;
        }
        
        .task-content {
            flex: 1;
        }
        
        .task-title {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .task-meta {
            font-size: 0.9rem;
            color: #666;
        }
        
        .task-actions {
            display: flex;
            gap: 10px;
        }
        
        .call-log-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid var(--light-gray);
        }
        
        .call-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 15px;
        }
        
        .call-status.completed { background: var(--success-green); }
        .call-status.pending { background: var(--warning-orange); }
        .call-status.failed { background: var(--danger-red); }
        
        .call-content {
            flex: 1;
        }
        
        .call-company {
            font-weight: 500;
            margin-bottom: 3px;
        }
        
        .call-purpose {
            font-size: 0.9rem;
            color: #666;
        }
        
        .call-time {
            font-size: 0.8rem;
            color: #999;
            margin-left: 15px;
        }
        
        .reminder-item {
            background: var(--mint-color);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid var(--primary-color);
        }
        
        .reminder-item.due-today {
            background: #fff3cd;
            border-left-color: var(--warning-orange);
        }
        
        .reminder-time {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .reminder-text {
            margin-bottom: 10px;
        }
        
        .reminder-actions {
            display: flex;
            gap: 10px;
        }
        
        .simplified-nav {
            background: white;
            border-bottom: 3px solid var(--mint-color);
            padding: 0;
        }
        
        .simplified-nav .nav-content {
            display: flex;
            gap: 0;
        }
        
        .simplified-nav .nav-item {
            padding: 15px 25px;
            text-decoration: none;
            color: var(--dark-gray);
            font-weight: 500;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .simplified-nav .nav-item:hover, 
        .simplified-nav .nav-item.active {
            background: var(--mint-color);
            border-bottom-color: var(--primary-color);
            color: var(--primary-color);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <h1>AuthTracker</h1>
                    <span class="philhealth-badge">PhilHealth Integrated</span>
                </div>
                <div class="user-info">
                    <span>Sarah Mendoza (Staff)</span>
                    <span>|</span>
                    <span>Cardiology Associates</span>
                    <div class="notification-badge">3</div>
                    <span>⚙️</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Simplified Navigation -->
    <nav class="simplified-nav">
        <div class="container">
            <div class="nav-content">
                <a href="#" class="nav-item active">🏠 Dashboard</a>
                <a href="#" class="nav-item">👥 Patients</a>
                <a href="#" class="nav-item">📋 Status Updates</a>
                <a href="#" class="nav-item">📞 Communications</a>
                <a href="#" class="nav-item">📝 Tasks & Reminders</a>
            </div>
        </div>
    </nav>

    <!-- Dashboard Content -->
    <main class="dashboard">
        <div class="container">
            <!-- Welcome Section -->
            <div class="mb-20">
                <h2>Good morning, Sarah</h2>
                <p style="color: #666; font-size: 1.1rem;">Your task overview for today</p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card pending">
                    <div class="stat-number">8</div>
                    <div class="stat-label">📞 Calls Completed Today</div>
                    <div class="stat-change positive">Target: 10 calls</div>
                </div>
                
                <div class="stat-card urgent">
                    <div class="stat-number">5</div>
                    <div class="stat-label">📝 Active Tasks</div>
                    <div class="stat-change negative">2 overdue</div>
                </div>
                
                <div class="stat-card approved">
                    <div class="stat-number">3</div>
                    <div class="stat-label">⏰ Reminders Due Today</div>
                    <div class="stat-change">All scheduled</div>
                </div>
                
                <div class="stat-card philhealth">
                    <div class="stat-number">12</div>
                    <div class="stat-label">📄 Documents Processed</div>
                    <div class="stat-change positive">This week</div>
                </div>
            </div>

            <!-- PhilHealth Quick Status -->
            <div class="philhealth-section">
                <div style="display: flex; align-items: center; gap: 20px;">
                    <div class="philhealth-logo-small"></div>
                    <div style="flex: 1;">
                        <h3 style="margin: 0; color: white;">Eligibility Checker Available</h3>
                        <p style="margin: 5px 0 0 0; opacity: 0.9;">
                            Verify patient eligibility instantly • 24/7 access
                        </p>
                    </div>
                    <div>
                        <button class="btn btn-primary">Check Eligibility</button>
                    </div>
                </div>
            </div>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- My Tasks -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>📝 My Tasks</h3>
                    </div>
                    <div class="card-content">
                        <div class="task-item overdue">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-content">
                                <div class="task-title">Call insurance for PA240804001</div>
                                <div class="task-meta">Overdue by 2 hours • Priority: High</div>
                            </div>
                            <div class="task-actions">
                                <button class="btn btn-danger" style="padding: 5px 10px; font-size: 0.8rem;">Call Now</button>
                            </div>
                        </div>
                        
                        <div class="task-item urgent">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-content">
                                <div class="task-title">Follow up on PA240804002 status</div>
                                <div class="task-meta">Due in 1 hour • Patient: Maria Gonzales</div>
                            </div>
                            <div class="task-actions">
                                <button class="btn btn-warning" style="padding: 5px 10px; font-size: 0.8rem;">Follow Up</button>
                            </div>
                        </div>
                        
                        <div class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-content">
                                <div class="task-title">Upload lab results for PA240804003</div>
                                <div class="task-meta">Due today • Patient: Roberto Silva</div>
                            </div>
                            <div class="task-actions">
                                <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem;">Upload</button>
                            </div>
                        </div>
                        
                        <div class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-content">
                                <div class="task-title">Update patient contact information</div>
                                <div class="task-meta">Due tomorrow • Patient: Ana Reyes</div>
                            </div>
                            <div class="task-actions">
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;">Update</button>
                            </div>
                        </div>
                        
                        <div class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-content">
                                <div class="task-title">Schedule follow-up appointment</div>
                                <div class="task-meta">Due tomorrow • Patient: John Dela Cruz</div>
                            </div>
                            <div class="task-actions">
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;">Schedule</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Today's Reminders -->
                <div class="content-card">
                    <div class="card-header">
                        <h3>⏰ Today's Reminders</h3>
                    </div>
                    <div class="card-content">
                        <div class="reminder-item due-today">
                            <div class="reminder-time">11:00 AM</div>
                            <div class="reminder-text">Call PhilHealth for claim status update - PA240804001</div>
                            <div class="reminder-actions">
                                <button class="btn btn-warning" style="padding: 5px 10px; font-size: 0.8rem;">Call Now</button>
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;">Snooze</button>
                            </div>
                        </div>
                        
                        <div class="reminder-item">
                            <div class="reminder-time">2:00 PM</div>
                            <div class="reminder-text">Follow up with Dr. Santos on urgent prior auth</div>
                            <div class="reminder-actions">
                                <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem;">Follow Up</button>
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;">Reschedule</button>
                            </div>
                        </div>
                        
                        <div class="reminder-item">
                            <div class="reminder-time">4:00 PM</div>
                            <div class="reminder-text">Submit weekly PhilHealth claims report</div>
                            <div class="reminder-actions">
                                <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem;">Submit</button>
                                <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;">Postpone</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Communication Log -->
            <div class="content-card mt-20">
                <div class="card-header">
                    <h3>📞 Today's Communication Log</h3>
                </div>
                <div class="card-content">
                    <div class="call-log-item">
                        <div class="call-status completed"></div>
                        <div class="call-content">
                            <div class="call-company">PhilHealth Regional Office</div>
                            <div class="call-purpose">Claim status inquiry - PA240804001</div>
                        </div>
                        <div class="call-time">9:30 AM</div>
                    </div>
                    
                    <div class="call-log-item">
                        <div class="call-status completed"></div>
                        <div class="call-content">
                            <div class="call-company">Maxicare Healthcare</div>
                            <div class="call-purpose">Prior authorization follow-up</div>
                        </div>
                        <div class="call-time">10:15 AM</div>
                    </div>
                    
                    <div class="call-log-item">
                        <div class="call-status pending"></div>
                        <div class="call-content">
                            <div class="call-company">Intellicare</div>
                            <div class="call-purpose">Document submission confirmation</div>
                        </div>
                        <div class="call-time">Scheduled: 11:30 AM</div>
                    </div>
                    
                    <div class="call-log-item">
                        <div class="call-status pending"></div>
                        <div class="call-content">
                            <div class="call-company">Patient: Maria Gonzales</div>
                            <div class="call-purpose">Insurance verification update</div>
                        </div>
                        <div class="call-time">Scheduled: 2:00 PM</div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="content-card mt-20">
                <div class="card-header">
                    <h3>⚡ Quick Actions</h3>
                </div>
                <div class="card-content">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <button class="btn btn-primary">📞 Make Insurance Call</button>
                        <button class="btn btn-philhealth">🏥 Check PhilHealth Status</button>
                        <button class="btn btn-secondary">📄 Upload Document</button>
                        <button class="btn btn-accent">📝 Update Patient Info</button>
                        <button class="btn btn-success">✅ Mark Task Complete</button>
                        <button class="btn btn-warning">⏰ Set Reminder</button>
                    </div>
                </div>
            </div>
        </div>
    </main>
</body>
</html>
