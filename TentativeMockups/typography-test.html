<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Typography & Logo Test - AuthTracker</title>
    <link rel="stylesheet" href="shared-styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 40px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
        }
        
        .typography-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid var(--mint-color);
            border-radius: 10px;
        }
        
        .logo-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px solid var(--philhealth-blue);
            border-radius: 10px;
            text-align: center;
        }
        
        .logo-test {
            display: flex;
            justify-content: space-around;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        
        .font-info {
            background: var(--mint-color);
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9rem;
            margin-bottom: 10px;
            color: var(--dark-gray);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>AuthTracker Typography & Logo Test</h1>
        <p>This page tests the implementation of Museo Slab Rounded, Quicksand fonts, and PhilHealth logo integration.</p>
        
        <!-- Typography Test -->
        <div class="typography-section">
            <h2>Typography Test</h2>
            
            <div class="font-info">H1 & H2: Museo Slab Rounded</div>
            <h1>H1 Heading - Museo Slab Rounded 700</h1>
            <h2>H2 Heading - Museo Slab Rounded 500</h2>
            
            <div class="font-info">H3, H4, H5 & Body: Quicksand</div>
            <h3>H3 Heading - Quicksand 600</h3>
            <h4>H4 Heading - Quicksand 600</h4>
            <h5>H5 Heading - Quicksand 500</h5>
            
            <p>Body text using Quicksand 400. This paragraph demonstrates the body text styling with proper line height and color. The Quicksand font provides excellent readability for healthcare applications.</p>
            
            <p><strong>Bold text using Quicksand 700</strong> and <em>italic text</em> for emphasis.</p>
            
            <div style="margin-top: 20px;">
                <button class="btn btn-primary">Primary Button - Quicksand 600</button>
                <button class="btn btn-secondary">Secondary Button - Quicksand 600</button>
            </div>
        </div>
        
        <!-- PhilHealth Logo Test -->
        <div class="logo-section">
            <h2>PhilHealth Logo Integration Test</h2>
            <p>Testing the official PhilHealth vector logo at different sizes:</p>
            
            <div class="logo-test">
                <div>
                    <h4>Small Logo</h4>
                    <div class="philhealth-logo-small"></div>
                    <p style="font-size: 0.8rem; margin-top: 10px;">80px × 40px</p>
                </div>
                
                <div>
                    <h4>Medium Logo</h4>
                    <div class="philhealth-logo"></div>
                    <p style="font-size: 0.8rem; margin-top: 10px;">120px × 60px</p>
                </div>
                
                <div>
                    <h4>Large Logo</h4>
                    <div class="philhealth-logo-large"></div>
                    <p style="font-size: 0.8rem; margin-top: 10px;">200px × 100px</p>
                </div>
            </div>
            
            <div style="margin-top: 30px; padding: 20px; background: var(--philhealth-blue); color: white; border-radius: 10px;">
                <h4 style="color: white; margin-bottom: 15px;">PhilHealth Integration Status</h4>
                <div style="display: flex; align-items: center; gap: 15px;">
                    <div class="philhealth-logo-small"></div>
                    <div>
                        <p style="margin: 0; font-weight: 600;">Official PhilHealth Partner</p>
                        <p style="margin: 5px 0 0 0; opacity: 0.9; font-size: 0.9rem;">Vector logo loaded from: ../philhealth-vector-logo-seeklogo/philhealth-seeklogo.svg</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Color Palette Test -->
        <div class="typography-section">
            <h2>Color Palette Test</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 20px;">
                <div style="background: var(--primary-color); color: white; padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>Primary</strong><br>#3AA6B9
                </div>
                <div style="background: var(--secondary-color); color: var(--dark-gray); padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>Secondary</strong><br>#FFD0D0
                </div>
                <div style="background: var(--accent-color); color: white; padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>Accent</strong><br>#FF9EAA
                </div>
                <div style="background: var(--mint-color); color: var(--dark-gray); padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>Mint</strong><br>#C1ECE4
                </div>
                <div style="background: var(--philhealth-blue); color: white; padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>PhilHealth Blue</strong><br>#0066CC
                </div>
                <div style="background: var(--philhealth-green); color: white; padding: 20px; border-radius: 8px; text-align: center;">
                    <strong>PhilHealth Green</strong><br>#009639
                </div>
            </div>
        </div>
        
        <!-- Status Indicators Test -->
        <div class="typography-section">
            <h2>Status Indicators & Components Test</h2>
            
            <h4>Priority Badges</h4>
            <div style="display: flex; gap: 10px; margin-bottom: 20px; flex-wrap: wrap;">
                <span class="priority-badge priority-normal">Normal</span>
                <span class="priority-badge priority-urgent">Urgent</span>
                <span class="priority-badge priority-stat">STAT</span>
            </div>
            
            <h4>Status Badges</h4>
            <div style="display: flex; gap: 10px; margin-bottom: 20px; flex-wrap: wrap;">
                <span class="status-badge status-submitted">Submitted</span>
                <span class="status-badge status-review">Under Review</span>
                <span class="status-badge status-approved">Approved</span>
                <span class="status-badge status-denied">Denied</span>
                <span class="status-badge status-expired">Expired</span>
            </div>
            
            <h4>Buttons</h4>
            <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-primary">Primary</button>
                <button class="btn btn-secondary">Secondary</button>
                <button class="btn btn-accent">Accent</button>
                <button class="btn btn-philhealth">PhilHealth</button>
                <button class="btn btn-success">Success</button>
                <button class="btn btn-warning">Warning</button>
                <button class="btn btn-danger">Danger</button>
            </div>
        </div>
    </div>
</body>
</html>
