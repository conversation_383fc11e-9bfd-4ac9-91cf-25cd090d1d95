//! Security tests for OCTAVE Healthcare System
//! 
//! Comprehensive security testing covering:
//! - Vulnerability scanning and penetration testing
//! - HIPAA compliance validation
//! - Authentication and authorization security
//! - Data encryption validation
//! - Input validation and sanitization
//! - Audit logging security

use std::collections::HashMap;
use tokio::test;
use uuid::Uuid;
use chrono::{DateTime, Utc, Duration};
use serde_json::Value;
use octave_core::{OctaveResult, OctaveError};
use octave_healthcare::*;
use octave_semantic::*;
use octave_compliance::*;
use octave_auth::*;

mod common;
use common::{TestDataFactory, HipaaAssertions};

/// Security vulnerability scanning tests
#[cfg(test)]
mod vulnerability_scanning_tests {
    use super::*;

    #[tokio::test]
    async fn test_sql_injection_protection() {
        let vulnerability_scanner = VulnerabilityScanner::new();
        
        let sql_injection_payloads = vec![
            "'; DROP TABLE patients; --",
            "1' OR '1'='1",
            "admin'/**/OR/**/1=1",
            "UNION SELECT * FROM users",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "1'; EXEC xp_cmdshell('dir'); --",
        ];
        
        for payload in sql_injection_payloads {
            let scan_result = vulnerability_scanner.scan_for_sql_injection(payload).await.unwrap();
            
            // Should detect SQL injection attempts
            assert!(scan_result.is_vulnerable);
            assert_eq!(scan_result.vulnerability_type, VulnerabilityType::SqlInjection);
            assert!(scan_result.risk_level >= RiskLevel::High);
            
            // Verify payload is blocked by semantic protection
            let immune_system = ImmuneSystem::new();
            let detection_result = immune_system.analyze_input(payload).unwrap();
            assert!(detection_result.is_threat);
        }
    }

    #[tokio::test]
    async fn test_xss_protection() {
        let vulnerability_scanner = VulnerabilityScanner::new();
        
        let xss_payloads = vec![
            "<script>alert('xss')</script>",
            "javascript:alert(1)",
            "<img src=x onerror=alert(1)>",
            "onmouseover=\"alert('xss')\"",
            "<iframe src=\"javascript:alert('xss')\"></iframe>",
            "<svg onload=alert('xss')>",
        ];
        
        for payload in xss_payloads {
            let scan_result = vulnerability_scanner.scan_for_xss(payload).await.unwrap();
            
            // Should detect XSS attempts
            assert!(scan_result.is_vulnerable);
            assert_eq!(scan_result.vulnerability_type, VulnerabilityType::XssAttack);
            
            // Verify payload is sanitized
            let sanitizer = InputSanitizer::new();
            let sanitized = sanitizer.sanitize_html(payload).unwrap();
            assert!(!sanitized.contains("<script"));
            assert!(!sanitized.contains("javascript:"));
            assert!(!sanitized.contains("onerror"));
        }
    }

    #[tokio::test]
    async fn test_path_traversal_protection() {
        let vulnerability_scanner = VulnerabilityScanner::new();
        
        let path_traversal_payloads = vec![
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd",
        ];
        
        for payload in path_traversal_payloads {
            let scan_result = vulnerability_scanner.scan_for_path_traversal(payload).await.unwrap();
            
            // Should detect path traversal attempts
            assert!(scan_result.is_vulnerable);
            assert_eq!(scan_result.vulnerability_type, VulnerabilityType::PathTraversal);
            
            // Verify path is sanitized
            let path_sanitizer = PathSanitizer::new();
            let sanitized_path = path_sanitizer.sanitize_path(payload).unwrap();
            assert!(!sanitized_path.contains(".."));
            assert!(!sanitized_path.contains("%2e"));
        }
    }

    #[tokio::test]
    async fn test_command_injection_protection() {
        let vulnerability_scanner = VulnerabilityScanner::new();
        
        let command_injection_payloads = vec![
            "; cat /etc/passwd",
            "| whoami",
            "&& rm -rf /",
            "`id`",
            "$(cat /etc/passwd)",
            "; ping -c 1 attacker.com",
        ];
        
        for payload in command_injection_payloads {
            let scan_result = vulnerability_scanner.scan_for_command_injection(payload).await.unwrap();
            
            // Should detect command injection attempts
            assert!(scan_result.is_vulnerable);
            assert_eq!(scan_result.vulnerability_type, VulnerabilityType::CommandInjection);
            
            // Verify command execution is blocked
            let command_validator = CommandValidator::new();
            let is_safe = command_validator.is_safe_command(payload).unwrap();
            assert!(!is_safe);
        }
    }

    #[tokio::test]
    async fn test_ldap_injection_protection() {
        let vulnerability_scanner = VulnerabilityScanner::new();
        
        let ldap_injection_payloads = vec![
            "*)(uid=*",
            "*)(|(password=*))",
            "admin)(&(password=*))",
            "*))%00",
            "*))(|(cn=*",
        ];
        
        for payload in ldap_injection_payloads {
            let scan_result = vulnerability_scanner.scan_for_ldap_injection(payload).await.unwrap();
            
            // Should detect LDAP injection attempts
            assert!(scan_result.is_vulnerable);
            assert_eq!(scan_result.vulnerability_type, VulnerabilityType::LdapInjection);
        }
    }
}

/// HIPAA compliance validation tests
#[cfg(test)]
mod hipaa_compliance_tests {
    use super::*;

    #[tokio::test]
    async fn test_phi_exposure_detection() {
        let hipaa_validator = HipaaComplianceValidator::new();
        
        // Test data with various PHI types
        let phi_test_cases = vec![
            ("SSN", "Patient SSN: ***********"),
            ("Phone", "Contact: (*************"),
            ("Email", "Email: <EMAIL>"),
            ("Address", "123 Main St, Anytown, ST 12345"),
            ("Medical Record Number", "MRN: 123456789"),
            ("Insurance ID", "Insurance ID: ABC123456789"),
        ];
        
        for (phi_type, test_data) in phi_test_cases {
            let validation_result = hipaa_validator.validate_phi_exposure(test_data).await.unwrap();
            
            // Should detect PHI exposure
            assert!(!validation_result.is_compliant);
            assert!(validation_result.violations.iter().any(|v| v.contains(phi_type)));
            
            // Verify PHI is properly sanitized
            let sanitizer = PhiSanitizer::new();
            let sanitized = sanitizer.sanitize(test_data, SanitizationMode::Redact).unwrap();
            
            let sanitized_validation = hipaa_validator.validate_phi_exposure(&sanitized).await.unwrap();
            assert!(sanitized_validation.is_compliant);
        }
    }

    #[tokio::test]
    async fn test_minimum_necessary_compliance() {
        let hipaa_validator = HipaaComplianceValidator::new();
        
        let patient_data = TestDataFactory::create_test_patient();
        let user_roles = vec![
            ("doctor", vec!["demographics", "medical_history", "insurance_info"]),
            ("nurse", vec!["demographics", "medical_history"]),
            ("billing", vec!["demographics", "insurance_info"]),
            ("receptionist", vec!["demographics"]),
        ];
        
        for (role, allowed_fields) in user_roles {
            let access_context = AccessContext {
                user_role: role.to_string(),
                purpose: "patient_care".to_string(),
                ip_address: "*************".to_string(),
                timestamp: Utc::now(),
            };
            
            let validation_result = hipaa_validator.validate_minimum_necessary(
                &patient_data, 
                &access_context
            ).await.unwrap();
            
            // Should enforce minimum necessary access
            assert!(validation_result.is_compliant);
            
            for field in &allowed_fields {
                assert!(validation_result.accessible_fields.contains(field));
            }
            
            // Verify restricted fields are not accessible
            if role != "doctor" {
                assert!(!validation_result.accessible_fields.contains(&"psychiatric_notes".to_string()));
            }
        }
    }

    #[tokio::test]
    async fn test_audit_trail_completeness() {
        let hipaa_validator = HipaaComplianceValidator::new();
        let audit_logger = AuditLogger::new("hipaa_test".to_string());
        
        // Simulate various healthcare operations
        let operations = vec![
            ("PATIENT_CREATE", "Patient", "CREATE"),
            ("PATIENT_READ", "Patient", "READ"),
            ("PATIENT_UPDATE", "Patient", "UPDATE"),
            ("PHI_ACCESS", "MedicalRecord", "READ"),
            ("DATA_EXPORT", "Patient", "EXPORT"),
        ];
        
        let mut audit_events = Vec::new();
        for (operation, resource_type, action) in operations {
            let event = AuditEvent::new(
                AuditEventType::PatientDataAccess,
                Uuid::new_v4(),
                resource_type.to_string(),
                Uuid::new_v4(),
                action.to_string(),
                AuditOutcome::Success,
            );
            
            let logged_event = audit_logger.log_event(event).unwrap();
            audit_events.push(logged_event);
        }
        
        // Validate audit trail completeness
        let validation_result = hipaa_validator.validate_audit_trail(&audit_events).await.unwrap();
        
        assert!(validation_result.is_compliant);
        assert_eq!(validation_result.total_events, audit_events.len());
        assert!(validation_result.missing_events.is_empty());
        
        // Verify audit trail integrity
        let integrity_result = audit_logger.verify_trail_integrity(&audit_events).unwrap();
        assert!(integrity_result);
    }

    #[tokio::test]
    async fn test_data_retention_compliance() {
        let hipaa_validator = HipaaComplianceValidator::new();
        let retention_manager = DataRetentionManager::new();
        
        // Create test data with various ages
        let test_data = vec![
            (DataType::AuditLog, Duration::days(6 * 365)), // 6 years old
            (DataType::PatientRecord, Duration::days(8 * 365)), // 8 years old
            (DataType::SessionData, Duration::days(2)), // 2 days old
            (DataType::TempFile, Duration::hours(2)), // 2 hours old
        ];
        
        for (data_type, age) in test_data {
            let created_at = Utc::now() - age;
            let data_record = DataRecord {
                id: Uuid::new_v4(),
                data_type: data_type.clone(),
                created_at,
                data: "test data".to_string(),
            };
            
            retention_manager.store_data(data_record.clone()).unwrap();
            
            let validation_result = hipaa_validator.validate_data_retention(
                &data_record, 
                &data_type
            ).await.unwrap();
            
            // Check if data should be retained based on HIPAA requirements
            match data_type {
                DataType::AuditLog | DataType::PatientRecord => {
                    if age.num_days() > 7 * 365 { // 7 years
                        assert!(!validation_result.should_retain);
                    } else {
                        assert!(validation_result.should_retain);
                    }
                },
                DataType::SessionData => {
                    if age.num_hours() > 24 {
                        assert!(!validation_result.should_retain);
                    }
                },
                DataType::TempFile => {
                    if age.num_hours() > 1 {
                        assert!(!validation_result.should_retain);
                    }
                },
            }
        }
    }
}

/// Authentication and authorization security tests
#[cfg(test)]
mod auth_security_tests {
    use super::*;

    #[tokio::test]
    async fn test_password_security() {
        let auth_service = AuthenticationService::new();
        
        // Test weak passwords
        let weak_passwords = vec![
            "password",
            "123456",
            "qwerty",
            "admin",
            "password123",
            "12345678",
        ];
        
        for weak_password in weak_passwords {
            let validation_result = auth_service.validate_password_strength(weak_password).unwrap();
            assert!(!validation_result.is_strong);
            assert!(!validation_result.meets_requirements);
        }
        
        // Test strong passwords
        let strong_passwords = vec![
            "MyStr0ng!P@ssw0rd",
            "C0mpl3x#P@ssw0rd!",
            "S3cur3$H3@lthc@r3",
        ];
        
        for strong_password in strong_passwords {
            let validation_result = auth_service.validate_password_strength(strong_password).unwrap();
            assert!(validation_result.is_strong);
            assert!(validation_result.meets_requirements);
        }
    }

    #[tokio::test]
    async fn test_session_security() {
        let session_manager = SessionManager::new();
        
        // Test session creation
        let user_id = Uuid::new_v4();
        let session = session_manager.create_session(user_id, "doctor".to_string()).unwrap();
        
        // Verify session properties
        assert!(session.is_secure);
        assert!(session.expires_at > Utc::now());
        assert!(session.expires_at <= Utc::now() + Duration::hours(8)); // Max session time
        
        // Test session hijacking protection
        let hijack_attempt = session_manager.validate_session_with_context(
            &session.session_id,
            &SessionContext {
                ip_address: "*************".to_string(), // Different IP
                user_agent: "Different Browser".to_string(),
            }
        ).unwrap();
        
        // Should detect potential session hijacking
        assert!(!hijack_attempt.is_valid);
        assert_eq!(hijack_attempt.security_issue, Some(SecurityIssue::SuspiciousActivity));
    }

    #[tokio::test]
    async fn test_jwt_token_security() {
        let jwt_service = JwtService::new("secure-secret-key-for-testing".to_string());
        
        // Test token creation and validation
        let claims = JwtClaims {
            user_id: Uuid::new_v4(),
            role: "doctor".to_string(),
            permissions: vec!["read_patients".to_string(), "write_patients".to_string()],
            exp: (Utc::now() + Duration::hours(1)).timestamp() as usize,
        };
        
        let token = jwt_service.create_token(&claims).unwrap();
        let validated_claims = jwt_service.validate_token(&token).unwrap();
        
        assert_eq!(validated_claims.user_id, claims.user_id);
        assert_eq!(validated_claims.role, claims.role);
        
        // Test token tampering detection
        let tampered_token = format!("{}x", token); // Tamper with token
        let tampered_result = jwt_service.validate_token(&tampered_token);
        assert!(tampered_result.is_err());
        
        // Test expired token
        let expired_claims = JwtClaims {
            user_id: Uuid::new_v4(),
            role: "doctor".to_string(),
            permissions: vec!["read_patients".to_string()],
            exp: (Utc::now() - Duration::hours(1)).timestamp() as usize, // Expired
        };
        
        let expired_token = jwt_service.create_token(&expired_claims).unwrap();
        let expired_result = jwt_service.validate_token(&expired_token);
        assert!(expired_result.is_err());
    }

    #[tokio::test]
    async fn test_brute_force_protection() {
        let auth_service = AuthenticationService::new();
        let rate_limiter = RateLimiter::new();
        
        let user_email = "<EMAIL>";
        let wrong_password = "wrongpassword";
        
        // Simulate multiple failed login attempts
        for attempt in 1..=10 {
            let login_result = auth_service.authenticate(user_email, wrong_password).await;
            assert!(login_result.is_err());
            
            // Check if account gets locked after threshold
            if attempt >= 5 {
                let rate_limit_result = rate_limiter.check_rate_limit(user_email).unwrap();
                assert!(rate_limit_result.is_rate_limited);
                assert!(rate_limit_result.lockout_duration > Duration::minutes(0));
            }
        }
        
        // Verify legitimate login is blocked during lockout
        let legitimate_login = auth_service.authenticate(user_email, "correctpassword").await;
        assert!(legitimate_login.is_err());
        
        // Verify lockout reason
        let lockout_status = auth_service.get_lockout_status(user_email).unwrap();
        assert!(lockout_status.is_locked);
        assert_eq!(lockout_status.reason, LockoutReason::BruteForceProtection);
    }
}

/// Data encryption validation tests
#[cfg(test)]
mod encryption_security_tests {
    use super::*;

    #[tokio::test]
    async fn test_encryption_strength() {
        let encryption_validator = EncryptionValidator::new();
        
        // Test AES-256 encryption
        let aes_encryptor = DataEncryptor::new_with_algorithm(EncryptionAlgorithm::Aes256Gcm);
        let test_data = b"Sensitive patient data that needs strong encryption";
        
        let encrypted = aes_encryptor.encrypt(test_data).unwrap();
        let validation_result = encryption_validator.validate_encryption_strength(&encrypted).unwrap();
        
        assert!(validation_result.is_strong);
        assert_eq!(validation_result.algorithm, EncryptionAlgorithm::Aes256Gcm);
        assert!(validation_result.key_length >= 256);
        
        // Test encryption randomness
        let encrypted2 = aes_encryptor.encrypt(test_data).unwrap();
        assert_ne!(encrypted.ciphertext, encrypted2.ciphertext); // Should be different due to random nonce
        
        // Test decryption integrity
        let decrypted = aes_encryptor.decrypt(&encrypted).unwrap();
        assert_eq!(test_data, decrypted.as_slice());
    }

    #[tokio::test]
    async fn test_key_management_security() {
        let key_manager = EncryptionKeyManager::new();
        
        // Test key generation
        let key = key_manager.generate_key(EncryptionAlgorithm::Aes256Gcm).unwrap();
        assert_eq!(key.key_data.len(), 32); // 256 bits
        assert!(key.created_at <= Utc::now());
        assert_eq!(key.status, KeyStatus::Active);
        
        // Test key rotation
        let rotated_key = key_manager.rotate_key(key.key_id).unwrap();
        assert_ne!(key.key_id, rotated_key.key_id);
        assert_ne!(key.key_data, rotated_key.key_data);
        
        // Verify old key is marked as rotated
        let old_key_status = key_manager.get_key_status(key.key_id).unwrap();
        assert_eq!(old_key_status, KeyStatus::Rotated);
        
        // Test key derivation security
        let derived_key = key_manager.derive_key(&key, "patient_data").unwrap();
        assert_ne!(key.key_data, derived_key.key_data);
        assert_eq!(derived_key.purpose, "patient_data");
    }

    #[tokio::test]
    async fn test_encryption_at_rest() {
        let storage_encryptor = StorageEncryptor::new();
        
        // Test database field encryption
        let sensitive_fields = vec![
            ("ssn", "***********"),
            ("medical_notes", "Patient has history of hypertension"),
            ("insurance_id", "INS123456789"),
        ];
        
        for (field_name, field_value) in sensitive_fields {
            let encrypted_field = storage_encryptor.encrypt_field(field_name, field_value).unwrap();
            
            // Verify field is encrypted
            assert_ne!(field_value, encrypted_field.encrypted_value);
            assert!(encrypted_field.is_encrypted);
            assert_eq!(encrypted_field.field_name, field_name);
            
            // Verify decryption
            let decrypted_value = storage_encryptor.decrypt_field(&encrypted_field).unwrap();
            assert_eq!(field_value, decrypted_value);
        }
    }

    #[tokio::test]
    async fn test_encryption_in_transit() {
        let transport_security = TransportSecurityValidator::new();
        
        // Test TLS configuration
        let tls_config = TlsConfiguration {
            min_version: TlsVersion::V1_3,
            cipher_suites: vec![
                CipherSuite::TLS_AES_256_GCM_SHA384,
                CipherSuite::TLS_CHACHA20_POLY1305_SHA256,
            ],
            certificate_validation: true,
            perfect_forward_secrecy: true,
        };
        
        let validation_result = transport_security.validate_tls_config(&tls_config).unwrap();
        assert!(validation_result.is_secure);
        assert!(validation_result.meets_healthcare_standards);
        
        // Test certificate validation
        let certificate = TestCertificate::new_self_signed();
        let cert_validation = transport_security.validate_certificate(&certificate).unwrap();
        
        // Self-signed certificates should not be accepted in production
        assert!(!cert_validation.is_trusted);
        assert!(cert_validation.issues.contains(&CertificateIssue::SelfSigned));
    }
}

// Helper types and functions for security tests

#[derive(Debug, Clone, PartialEq)]
enum VulnerabilityType {
    SqlInjection,
    XssAttack,
    PathTraversal,
    CommandInjection,
    LdapInjection,
}

#[derive(Debug, Clone, PartialEq)]
enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

#[derive(Debug)]
struct VulnerabilityScanResult {
    is_vulnerable: bool,
    vulnerability_type: VulnerabilityType,
    risk_level: RiskLevel,
    description: String,
}

#[derive(Debug)]
struct HipaaValidationResult {
    is_compliant: bool,
    violations: Vec<String>,
    accessible_fields: Vec<String>,
}

#[derive(Debug)]
struct AccessContext {
    user_role: String,
    purpose: String,
    ip_address: String,
    timestamp: DateTime<Utc>,
}

#[derive(Debug, Clone, PartialEq)]
enum DataType {
    AuditLog,
    PatientRecord,
    SessionData,
    TempFile,
}

#[derive(Debug)]
struct DataRecord {
    id: Uuid,
    data_type: DataType,
    created_at: DateTime<Utc>,
    data: String,
}

#[derive(Debug)]
struct SessionContext {
    ip_address: String,
    user_agent: String,
}

#[derive(Debug, PartialEq)]
enum SecurityIssue {
    SuspiciousActivity,
    InvalidCredentials,
    SessionExpired,
}

#[derive(Debug)]
struct SessionValidationResult {
    is_valid: bool,
    security_issue: Option<SecurityIssue>,
}

#[derive(Debug)]
struct JwtClaims {
    user_id: Uuid,
    role: String,
    permissions: Vec<String>,
    exp: usize,
}

#[derive(Debug, PartialEq)]
enum LockoutReason {
    BruteForceProtection,
    SuspiciousActivity,
    AdminAction,
}

#[derive(Debug)]
struct LockoutStatus {
    is_locked: bool,
    reason: LockoutReason,
    lockout_duration: Duration,
}

#[derive(Debug, PartialEq)]
enum EncryptionAlgorithm {
    Aes256Gcm,
    ChaCha20Poly1305,
}

#[derive(Debug, PartialEq)]
enum KeyStatus {
    Active,
    Rotated,
    Revoked,
}

#[derive(Debug)]
struct EncryptionValidationResult {
    is_strong: bool,
    algorithm: EncryptionAlgorithm,
    key_length: usize,
}

#[derive(Debug)]
struct EncryptedField {
    field_name: String,
    encrypted_value: String,
    is_encrypted: bool,
}

#[derive(Debug)]
struct TlsConfiguration {
    min_version: TlsVersion,
    cipher_suites: Vec<CipherSuite>,
    certificate_validation: bool,
    perfect_forward_secrecy: bool,
}

#[derive(Debug, PartialEq)]
enum TlsVersion {
    V1_2,
    V1_3,
}

#[derive(Debug, PartialEq)]
enum CipherSuite {
    TLS_AES_256_GCM_SHA384,
    TLS_CHACHA20_POLY1305_SHA256,
}

#[derive(Debug)]
struct TlsValidationResult {
    is_secure: bool,
    meets_healthcare_standards: bool,
}

#[derive(Debug)]
struct TestCertificate {
    is_self_signed: bool,
}

impl TestCertificate {
    fn new_self_signed() -> Self {
        Self { is_self_signed: true }
    }
}

#[derive(Debug, PartialEq)]
enum CertificateIssue {
    SelfSigned,
    Expired,
    InvalidChain,
}

#[derive(Debug)]
struct CertificateValidationResult {
    is_trusted: bool,
    issues: Vec<CertificateIssue>,
}
