//! Integration tests for OCTAVE Healthcare System
//! 
//! Comprehensive integration tests covering:
//! - API endpoint integration
//! - Database integration and transactions
//! - End-to-end workflow testing
//! - External service integration
//! - Cross-module functionality

use std::sync::Once;
use tokio::test;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde_json::Value;
use octave_core::{OctaveResult, OctaveError};
use octave_healthcare::*;
use octave_semantic::*;
use octave_compliance::*;
use octave_auth::*;
use octave_database::*;

mod common;
use common::{TestDataFactory, MockInsuranceApi, MockEhrSystem, MockNotificationService};

static INIT: Once = Once::new();

/// Initialize test environment for integration tests
fn init_integration_test_env() {
    INIT.call_once(|| {
        tracing_subscriber::fmt()
            .with_env_filter("debug")
            .with_test_writer()
            .init();
    });
}

/// Test database integration and transactions
#[cfg(test)]
mod database_integration_tests {
    use super::*;

    #[tokio::test]
    async fn test_patient_crud_operations() {
        init_integration_test_env();
        
        let db = setup_test_database().await.unwrap();
        let patient_repo = PatientRepository::new(db.clone());
        
        // Create patient
        let patient_data = TestDataFactory::create_test_patient();
        let created_patient = patient_repo.create(patient_data.clone()).await.unwrap();
        
        assert_eq!(created_patient.first_name, patient_data.first_name);
        assert_eq!(created_patient.last_name, patient_data.last_name);
        assert!(created_patient.id != Uuid::nil());
        
        // Read patient
        let retrieved_patient = patient_repo.get_by_id(created_patient.id).await.unwrap();
        assert!(retrieved_patient.is_some());
        assert_eq!(retrieved_patient.unwrap().id, created_patient.id);
        
        // Update patient
        let mut updated_data = patient_data.clone();
        updated_data.phone = "************".to_string();
        let updated_patient = patient_repo.update(created_patient.id, updated_data).await.unwrap();
        assert_eq!(updated_patient.phone, "************");
        
        // Delete patient
        let deleted = patient_repo.delete(created_patient.id).await.unwrap();
        assert!(deleted);
        
        // Verify deletion
        let deleted_patient = patient_repo.get_by_id(created_patient.id).await.unwrap();
        assert!(deleted_patient.is_none());
        
        cleanup_test_database(db).await.unwrap();
    }

    #[tokio::test]
    async fn test_prior_auth_workflow_integration() {
        init_integration_test_env();
        
        let db = setup_test_database().await.unwrap();
        let patient_repo = PatientRepository::new(db.clone());
        let prior_auth_repo = PriorAuthRepository::new(db.clone());
        let audit_logger = AuditLogger::new("integration_test".to_string());
        
        // Create patient first
        let patient = patient_repo.create(TestDataFactory::create_test_patient()).await.unwrap();
        
        // Create prior authorization
        let prior_auth_data = TestDataFactory::create_test_prior_auth(patient.id);
        let created_auth = prior_auth_repo.create(prior_auth_data.clone()).await.unwrap();
        
        // Verify audit trail was created
        let audit_events = audit_logger.get_events_by_resource_id(created_auth.id).await.unwrap();
        assert!(!audit_events.is_empty());
        assert!(audit_events.iter().any(|e| e.action == "CREATE_PRIOR_AUTH"));
        
        // Test status transitions
        let updated_auth = prior_auth_repo.update_status(
            created_auth.id, 
            PriorAuthStatus::UnderReview
        ).await.unwrap();
        assert_eq!(updated_auth.status, PriorAuthStatus::UnderReview);
        
        // Verify status change audit
        let status_audit = audit_logger.get_events_by_resource_id(created_auth.id).await.unwrap();
        assert!(status_audit.iter().any(|e| e.action == "UPDATE_STATUS"));
        
        cleanup_test_database(db).await.unwrap();
    }

    #[tokio::test]
    async fn test_database_transaction_rollback() {
        init_integration_test_env();
        
        let db = setup_test_database().await.unwrap();
        let patient_repo = PatientRepository::new(db.clone());
        
        // Start transaction
        let mut tx = db.begin_transaction().await.unwrap();
        
        // Create patient within transaction
        let patient_data = TestDataFactory::create_test_patient();
        let created_patient = patient_repo.create_with_tx(&mut tx, patient_data).await.unwrap();
        
        // Verify patient exists within transaction
        let patient_in_tx = patient_repo.get_by_id_with_tx(&tx, created_patient.id).await.unwrap();
        assert!(patient_in_tx.is_some());
        
        // Rollback transaction
        tx.rollback().await.unwrap();
        
        // Verify patient doesn't exist after rollback
        let patient_after_rollback = patient_repo.get_by_id(created_patient.id).await.unwrap();
        assert!(patient_after_rollback.is_none());
        
        cleanup_test_database(db).await.unwrap();
    }

    #[tokio::test]
    async fn test_concurrent_database_operations() {
        init_integration_test_env();
        
        let db = setup_test_database().await.unwrap();
        let patient_repo = PatientRepository::new(db.clone());
        
        // Create multiple patients concurrently
        let mut handles = Vec::new();
        
        for i in 0..10 {
            let repo = patient_repo.clone();
            let handle = tokio::spawn(async move {
                let mut patient_data = TestDataFactory::create_test_patient();
                patient_data.first_name = format!("Patient{}", i);
                repo.create(patient_data).await
            });
            handles.push(handle);
        }
        
        // Wait for all operations to complete
        let results: Vec<_> = futures::future::join_all(handles).await;
        
        // Verify all operations succeeded
        for result in results {
            assert!(result.is_ok());
            assert!(result.unwrap().is_ok());
        }
        
        // Verify all patients were created
        let all_patients = patient_repo.get_all().await.unwrap();
        assert_eq!(all_patients.len(), 10);
        
        cleanup_test_database(db).await.unwrap();
    }
}

/// Test API endpoint integration
#[cfg(test)]
mod api_integration_tests {
    use super::*;
    use axum::http::StatusCode;
    use tower::ServiceExt;

    #[tokio::test]
    async fn test_patient_api_endpoints() {
        init_integration_test_env();
        
        let app = create_test_app().await;
        
        // Test POST /patients
        let patient_data = TestDataFactory::create_test_patient();
        let create_request = axum::http::Request::builder()
            .method("POST")
            .uri("/api/v1/patients")
            .header("content-type", "application/json")
            .header("authorization", "Bearer test-token")
            .body(serde_json::to_string(&patient_data).unwrap().into())
            .unwrap();
        
        let response = app.clone().oneshot(create_request).await.unwrap();
        assert_eq!(response.status(), StatusCode::CREATED);
        
        let body = hyper::body::to_bytes(response.into_body()).await.unwrap();
        let created_patient: Patient = serde_json::from_slice(&body).unwrap();
        
        // Test GET /patients/{id}
        let get_request = axum::http::Request::builder()
            .method("GET")
            .uri(&format!("/api/v1/patients/{}", created_patient.id))
            .header("authorization", "Bearer test-token")
            .body(axum::body::Body::empty())
            .unwrap();
        
        let response = app.clone().oneshot(get_request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
        
        let body = hyper::body::to_bytes(response.into_body()).await.unwrap();
        let retrieved_patient: Patient = serde_json::from_slice(&body).unwrap();
        assert_eq!(retrieved_patient.id, created_patient.id);
        
        // Test PUT /patients/{id}
        let mut update_data = patient_data.clone();
        update_data.phone = "************".to_string();
        
        let update_request = axum::http::Request::builder()
            .method("PUT")
            .uri(&format!("/api/v1/patients/{}", created_patient.id))
            .header("content-type", "application/json")
            .header("authorization", "Bearer test-token")
            .body(serde_json::to_string(&update_data).unwrap().into())
            .unwrap();
        
        let response = app.clone().oneshot(update_request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
        
        // Test DELETE /patients/{id}
        let delete_request = axum::http::Request::builder()
            .method("DELETE")
            .uri(&format!("/api/v1/patients/{}", created_patient.id))
            .header("authorization", "Bearer test-token")
            .body(axum::body::Body::empty())
            .unwrap();
        
        let response = app.oneshot(delete_request).await.unwrap();
        assert_eq!(response.status(), StatusCode::NO_CONTENT);
    }

    #[tokio::test]
    async fn test_prior_auth_api_workflow() {
        init_integration_test_env();
        
        let app = create_test_app().await;
        
        // First create a patient
        let patient_data = TestDataFactory::create_test_patient();
        let patient = create_patient_via_api(&app, patient_data).await;
        
        // Create prior authorization
        let prior_auth_data = TestDataFactory::create_test_prior_auth(patient.id);
        let create_request = axum::http::Request::builder()
            .method("POST")
            .uri("/api/v1/prior-authorizations")
            .header("content-type", "application/json")
            .header("authorization", "Bearer test-token")
            .body(serde_json::to_string(&prior_auth_data).unwrap().into())
            .unwrap();
        
        let response = app.clone().oneshot(create_request).await.unwrap();
        assert_eq!(response.status(), StatusCode::CREATED);
        
        let body = hyper::body::to_bytes(response.into_body()).await.unwrap();
        let created_auth: PriorAuth = serde_json::from_slice(&body).unwrap();
        
        // Test status update
        let status_update = serde_json::json!({
            "status": "under_review",
            "notes": "Insurance review in progress"
        });
        
        let update_request = axum::http::Request::builder()
            .method("PATCH")
            .uri(&format!("/api/v1/prior-authorizations/{}/status", created_auth.id))
            .header("content-type", "application/json")
            .header("authorization", "Bearer test-token")
            .body(status_update.to_string().into())
            .unwrap();
        
        let response = app.clone().oneshot(update_request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
        
        // Verify status was updated
        let get_request = axum::http::Request::builder()
            .method("GET")
            .uri(&format!("/api/v1/prior-authorizations/{}", created_auth.id))
            .header("authorization", "Bearer test-token")
            .body(axum::body::Body::empty())
            .unwrap();
        
        let response = app.oneshot(get_request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
        
        let body = hyper::body::to_bytes(response.into_body()).await.unwrap();
        let updated_auth: PriorAuth = serde_json::from_slice(&body).unwrap();
        assert_eq!(updated_auth.status, PriorAuthStatus::UnderReview);
    }

    #[tokio::test]
    async fn test_api_authentication_and_authorization() {
        init_integration_test_env();
        
        let app = create_test_app().await;
        
        // Test unauthenticated request
        let request = axum::http::Request::builder()
            .method("GET")
            .uri("/api/v1/patients")
            .body(axum::body::Body::empty())
            .unwrap();
        
        let response = app.clone().oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
        
        // Test invalid token
        let request = axum::http::Request::builder()
            .method("GET")
            .uri("/api/v1/patients")
            .header("authorization", "Bearer invalid-token")
            .body(axum::body::Body::empty())
            .unwrap();
        
        let response = app.clone().oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
        
        // Test insufficient permissions
        let limited_token = create_limited_access_token("billing_staff").await;
        let request = axum::http::Request::builder()
            .method("POST")
            .uri("/api/v1/patients")
            .header("authorization", &format!("Bearer {}", limited_token))
            .header("content-type", "application/json")
            .body(serde_json::to_string(&TestDataFactory::create_test_patient()).unwrap().into())
            .unwrap();
        
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::FORBIDDEN);
    }
}

/// Test end-to-end workflow integration
#[cfg(test)]
mod end_to_end_workflow_tests {
    use super::*;

    #[tokio::test]
    async fn test_complete_prior_authorization_workflow() {
        init_integration_test_env();
        
        let app = create_test_app().await;
        let mock_insurance = MockInsuranceApi::new();
        let mock_notifications = MockNotificationService::new();
        
        // Step 1: Create patient
        let patient_data = TestDataFactory::create_test_patient();
        let patient = create_patient_via_api(&app, patient_data).await;
        
        // Step 2: Submit prior authorization request
        let prior_auth_data = TestDataFactory::create_test_prior_auth(patient.id);
        let prior_auth = create_prior_auth_via_api(&app, prior_auth_data).await;
        
        // Step 3: Simulate insurance API response
        mock_insurance.setup_response(&prior_auth.tracking_id, InsuranceResponse::PriorAuth(
            PriorAuthResponse {
                tracking_id: prior_auth.tracking_id.clone(),
                status: "approved".to_string(),
                reference_number: "REF-12345".to_string(),
                estimated_decision_date: Utc::now() + chrono::Duration::days(1),
            }
        ));
        
        // Step 4: Process insurance response (this would be triggered by webhook or polling)
        let insurance_response = mock_insurance.submit_prior_auth(PriorAuthRequest {
            tracking_id: prior_auth.tracking_id.clone(),
            member_id: patient.insurance_info.member_id.clone(),
            procedure_code: prior_auth.procedure_code.clone(),
            diagnosis_code: prior_auth.diagnosis_code.clone(),
            provider_id: "PROV123".to_string(),
        }).await.unwrap();
        
        // Step 5: Update prior auth status based on insurance response
        update_prior_auth_status_via_api(&app, prior_auth.id, "approved").await;
        
        // Step 6: Verify notification was sent
        let notifications = mock_notifications.get_sent_notifications();
        assert!(!notifications.is_empty());
        
        // Step 7: Verify audit trail
        let audit_events = get_audit_trail_via_api(&app, prior_auth.id).await;
        assert!(audit_events.len() >= 3); // Create, status updates, approval
        
        // Step 8: Verify final state
        let final_auth = get_prior_auth_via_api(&app, prior_auth.id).await;
        assert_eq!(final_auth.status, PriorAuthStatus::Approved);
        assert!(final_auth.approval_date.is_some());
    }

    #[tokio::test]
    async fn test_semantic_protection_integration() {
        init_integration_test_env();
        
        let app = create_test_app().await;
        
        // Test that semantic protection blocks malicious input
        let malicious_patient_data = serde_json::json!({
            "first_name": "'; DROP TABLE patients; --",
            "last_name": "Test",
            "date_of_birth": "1990-01-01",
            "ssn": "***********",
            "phone": "************",
            "email": "<EMAIL>"
        });
        
        let request = axum::http::Request::builder()
            .method("POST")
            .uri("/api/v1/patients")
            .header("content-type", "application/json")
            .header("authorization", "Bearer test-token")
            .body(malicious_patient_data.to_string().into())
            .unwrap();
        
        let response = app.clone().oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::BAD_REQUEST);
        
        // Verify threat was logged
        let security_events = get_security_events_via_api(&app).await;
        assert!(security_events.iter().any(|e| e.threat_type == "sql_injection"));
    }

    #[tokio::test]
    async fn test_phi_protection_integration() {
        init_integration_test_env();
        
        let app = create_test_app().await;
        
        // Create patient with PHI
        let patient_data = TestDataFactory::create_test_patient();
        let patient = create_patient_via_api(&app, patient_data).await;
        
        // Test that PHI is properly sanitized in responses
        let request = axum::http::Request::builder()
            .method("GET")
            .uri(&format!("/api/v1/patients/{}", patient.id))
            .header("authorization", "Bearer limited-access-token")
            .body(axum::body::Body::empty())
            .unwrap();
        
        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
        
        let body = hyper::body::to_bytes(response.into_body()).await.unwrap();
        let response_text = String::from_utf8(body.to_vec()).unwrap();
        
        // Verify SSN is redacted
        assert!(!response_text.contains("***********"));
        assert!(response_text.contains("[REDACTED_SSN]") || !response_text.contains("ssn"));
    }
}

// Helper functions for integration tests

async fn setup_test_database() -> OctaveResult<TestDatabase> {
    // Implementation would set up isolated test database
    Ok(TestDatabase {
        connection_string: "postgresql://test:test@localhost:5432/octave_test".to_string(),
    })
}

async fn cleanup_test_database(_db: TestDatabase) -> OctaveResult<()> {
    // Implementation would clean up test database
    Ok(())
}

async fn create_test_app() -> axum::Router {
    // Implementation would create test app with all routes and middleware
    axum::Router::new()
}

async fn create_patient_via_api(_app: &axum::Router, _patient_data: TestPatient) -> Patient {
    // Implementation would create patient via API
    Patient {
        id: Uuid::new_v4(),
        first_name: "Test".to_string(),
        last_name: "Patient".to_string(),
        date_of_birth: Utc::now(),
        insurance_info: InsuranceInfo {
            member_id: "MEM123".to_string(),
            company: "Test Insurance".to_string(),
        },
    }
}

async fn create_prior_auth_via_api(_app: &axum::Router, _prior_auth_data: TestPriorAuth) -> PriorAuth {
    // Implementation would create prior auth via API
    PriorAuth {
        id: Uuid::new_v4(),
        patient_id: Uuid::new_v4(),
        tracking_id: "PA-123456".to_string(),
        procedure_code: "99213".to_string(),
        diagnosis_code: "Z00.00".to_string(),
        status: PriorAuthStatus::Submitted,
        approval_date: None,
    }
}

async fn update_prior_auth_status_via_api(_app: &axum::Router, _id: Uuid, _status: &str) {
    // Implementation would update status via API
}

async fn get_prior_auth_via_api(_app: &axum::Router, _id: Uuid) -> PriorAuth {
    // Implementation would get prior auth via API
    PriorAuth {
        id: Uuid::new_v4(),
        patient_id: Uuid::new_v4(),
        tracking_id: "PA-123456".to_string(),
        procedure_code: "99213".to_string(),
        diagnosis_code: "Z00.00".to_string(),
        status: PriorAuthStatus::Approved,
        approval_date: Some(Utc::now()),
    }
}

async fn get_audit_trail_via_api(_app: &axum::Router, _resource_id: Uuid) -> Vec<AuditEvent> {
    // Implementation would get audit trail via API
    vec![]
}

async fn get_security_events_via_api(_app: &axum::Router) -> Vec<SecurityEvent> {
    // Implementation would get security events via API
    vec![]
}

async fn create_limited_access_token(_role: &str) -> String {
    // Implementation would create JWT token with limited permissions
    "limited-access-token".to_string()
}

// Mock types for integration tests

#[derive(Debug)]
struct TestDatabase {
    connection_string: String,
}

#[derive(Debug, Clone)]
struct Patient {
    id: Uuid,
    first_name: String,
    last_name: String,
    date_of_birth: DateTime<Utc>,
    insurance_info: InsuranceInfo,
}

#[derive(Debug, Clone)]
struct InsuranceInfo {
    member_id: String,
    company: String,
}

#[derive(Debug, Clone)]
struct PriorAuth {
    id: Uuid,
    patient_id: Uuid,
    tracking_id: String,
    procedure_code: String,
    diagnosis_code: String,
    status: PriorAuthStatus,
    approval_date: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone, PartialEq)]
enum PriorAuthStatus {
    Submitted,
    UnderReview,
    Approved,
    Denied,
}

#[derive(Debug)]
struct AuditEvent {
    id: Uuid,
    action: String,
    resource_id: Uuid,
    timestamp: DateTime<Utc>,
}

#[derive(Debug)]
struct SecurityEvent {
    id: Uuid,
    threat_type: String,
    severity: String,
    timestamp: DateTime<Utc>,
}
