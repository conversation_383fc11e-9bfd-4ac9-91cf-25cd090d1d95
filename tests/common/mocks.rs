//! Mock services for testing OCTAVE Healthcare System
//! 
//! This module provides mock implementations of external services and dependencies
//! for comprehensive testing without relying on real external systems.

use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use async_trait::async_trait;
use mockall::predicate::*;
use mockall::mock;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde_json::Value;
use octave_core::{OctaveResult, OctaveError};

/// Mock insurance API service
#[derive(Debug, Clone)]
pub struct MockInsuranceApi {
    responses: Arc<Mutex<HashMap<String, InsuranceResponse>>>,
}

impl MockInsuranceApi {
    pub fn new() -> Self {
        Self {
            responses: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// Set up a mock response for a specific request
    pub fn setup_response(&self, request_id: &str, response: InsuranceResponse) {
        let mut responses = self.responses.lock().unwrap();
        responses.insert(request_id.to_string(), response);
    }

    /// Simulate insurance eligibility check
    pub async fn check_eligibility(&self, member_id: &str) -> OctaveResult<EligibilityResponse> {
        // Simulate network delay
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        let responses = self.responses.lock().unwrap();
        if let Some(response) = responses.get(member_id) {
            match response {
                InsuranceResponse::Eligibility(eligibility) => Ok(eligibility.clone()),
                _ => Err(OctaveError::ExternalServiceError("Invalid response type".to_string())),
            }
        } else {
            // Default response for unknown member IDs
            Ok(EligibilityResponse {
                member_id: member_id.to_string(),
                is_eligible: true,
                coverage_type: "Standard".to_string(),
                effective_date: Utc::now() - chrono::Duration::days(30),
                expiration_date: Utc::now() + chrono::Duration::days(365),
            })
        }
    }

    /// Simulate prior authorization submission
    pub async fn submit_prior_auth(&self, request: PriorAuthRequest) -> OctaveResult<PriorAuthResponse> {
        // Simulate processing time
        tokio::time::sleep(tokio::time::Duration::from_millis(200)).await;

        let responses = self.responses.lock().unwrap();
        if let Some(response) = responses.get(&request.tracking_id) {
            match response {
                InsuranceResponse::PriorAuth(auth_response) => Ok(auth_response.clone()),
                _ => Err(OctaveError::ExternalServiceError("Invalid response type".to_string())),
            }
        } else {
            // Default response
            Ok(PriorAuthResponse {
                tracking_id: request.tracking_id,
                status: "Submitted".to_string(),
                reference_number: format!("REF-{}", Uuid::new_v4()),
                estimated_decision_date: Utc::now() + chrono::Duration::days(14),
            })
        }
    }
}

/// Mock EHR system
#[derive(Debug, Clone)]
pub struct MockEhrSystem {
    patient_data: Arc<Mutex<HashMap<Uuid, PatientRecord>>>,
}

impl MockEhrSystem {
    pub fn new() -> Self {
        Self {
            patient_data: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// Add patient data to mock EHR
    pub fn add_patient(&self, patient_id: Uuid, record: PatientRecord) {
        let mut data = self.patient_data.lock().unwrap();
        data.insert(patient_id, record);
    }

    /// Retrieve patient data
    pub async fn get_patient(&self, patient_id: Uuid) -> OctaveResult<Option<PatientRecord>> {
        // Simulate network delay
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

        let data = self.patient_data.lock().unwrap();
        Ok(data.get(&patient_id).cloned())
    }

    /// Update patient data
    pub async fn update_patient(&self, patient_id: Uuid, record: PatientRecord) -> OctaveResult<()> {
        // Simulate processing time
        tokio::time::sleep(tokio::time::Duration::from_millis(75)).await;

        let mut data = self.patient_data.lock().unwrap();
        data.insert(patient_id, record);
        Ok(())
    }
}

/// Mock notification service
#[derive(Debug, Clone)]
pub struct MockNotificationService {
    sent_notifications: Arc<Mutex<Vec<Notification>>>,
    should_fail: Arc<Mutex<bool>>,
}

impl MockNotificationService {
    pub fn new() -> Self {
        Self {
            sent_notifications: Arc::new(Mutex::new(Vec::new())),
            should_fail: Arc::new(Mutex::new(false)),
        }
    }

    /// Set whether the service should fail
    pub fn set_should_fail(&self, should_fail: bool) {
        let mut fail_flag = self.should_fail.lock().unwrap();
        *fail_flag = should_fail;
    }

    /// Get all sent notifications
    pub fn get_sent_notifications(&self) -> Vec<Notification> {
        let notifications = self.sent_notifications.lock().unwrap();
        notifications.clone()
    }

    /// Send email notification
    pub async fn send_email(&self, notification: EmailNotification) -> OctaveResult<()> {
        let should_fail = *self.should_fail.lock().unwrap();
        if should_fail {
            return Err(OctaveError::ExternalServiceError("Email service unavailable".to_string()));
        }

        // Simulate sending delay
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        let mut notifications = self.sent_notifications.lock().unwrap();
        notifications.push(Notification::Email(notification));
        Ok(())
    }

    /// Send SMS notification
    pub async fn send_sms(&self, notification: SmsNotification) -> OctaveResult<()> {
        let should_fail = *self.should_fail.lock().unwrap();
        if should_fail {
            return Err(OctaveError::ExternalServiceError("SMS service unavailable".to_string()));
        }

        // Simulate sending delay
        tokio::time::sleep(tokio::time::Duration::from_millis(150)).await;

        let mut notifications = self.sent_notifications.lock().unwrap();
        notifications.push(Notification::Sms(notification));
        Ok(())
    }
}

/// Mock file storage service
#[derive(Debug, Clone)]
pub struct MockFileStorage {
    files: Arc<Mutex<HashMap<String, StoredFile>>>,
}

impl MockFileStorage {
    pub fn new() -> Self {
        Self {
            files: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    /// Upload file
    pub async fn upload(&self, key: &str, data: Vec<u8>, metadata: FileMetadata) -> OctaveResult<()> {
        // Simulate upload time
        tokio::time::sleep(tokio::time::Duration::from_millis(data.len() as u64 / 1000)).await;

        let mut files = self.files.lock().unwrap();
        files.insert(key.to_string(), StoredFile {
            data,
            metadata,
            uploaded_at: Utc::now(),
        });
        Ok(())
    }

    /// Download file
    pub async fn download(&self, key: &str) -> OctaveResult<Option<StoredFile>> {
        // Simulate download time
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

        let files = self.files.lock().unwrap();
        Ok(files.get(key).cloned())
    }

    /// Delete file
    pub async fn delete(&self, key: &str) -> OctaveResult<()> {
        let mut files = self.files.lock().unwrap();
        files.remove(key);
        Ok(())
    }

    /// List files
    pub async fn list(&self, prefix: &str) -> OctaveResult<Vec<String>> {
        let files = self.files.lock().unwrap();
        let keys: Vec<String> = files.keys()
            .filter(|key| key.starts_with(prefix))
            .cloned()
            .collect();
        Ok(keys)
    }
}

// Data structures for mock services

#[derive(Debug, Clone)]
pub enum InsuranceResponse {
    Eligibility(EligibilityResponse),
    PriorAuth(PriorAuthResponse),
}

#[derive(Debug, Clone)]
pub struct EligibilityResponse {
    pub member_id: String,
    pub is_eligible: bool,
    pub coverage_type: String,
    pub effective_date: DateTime<Utc>,
    pub expiration_date: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct PriorAuthRequest {
    pub tracking_id: String,
    pub member_id: String,
    pub procedure_code: String,
    pub diagnosis_code: String,
    pub provider_id: String,
}

#[derive(Debug, Clone)]
pub struct PriorAuthResponse {
    pub tracking_id: String,
    pub status: String,
    pub reference_number: String,
    pub estimated_decision_date: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct PatientRecord {
    pub patient_id: Uuid,
    pub demographics: PatientDemographics,
    pub medical_history: Vec<MedicalEvent>,
    pub allergies: Vec<String>,
    pub medications: Vec<Medication>,
}

#[derive(Debug, Clone)]
pub struct PatientDemographics {
    pub first_name: String,
    pub last_name: String,
    pub date_of_birth: DateTime<Utc>,
    pub gender: String,
    pub address: String,
    pub phone: String,
    pub email: String,
}

#[derive(Debug, Clone)]
pub struct MedicalEvent {
    pub date: DateTime<Utc>,
    pub event_type: String,
    pub description: String,
    pub provider: String,
}

#[derive(Debug, Clone)]
pub struct Medication {
    pub name: String,
    pub dosage: String,
    pub frequency: String,
    pub start_date: DateTime<Utc>,
    pub end_date: Option<DateTime<Utc>>,
}

#[derive(Debug, Clone)]
pub enum Notification {
    Email(EmailNotification),
    Sms(SmsNotification),
}

#[derive(Debug, Clone)]
pub struct EmailNotification {
    pub to: String,
    pub subject: String,
    pub body: String,
    pub sent_at: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct SmsNotification {
    pub to: String,
    pub message: String,
    pub sent_at: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct StoredFile {
    pub data: Vec<u8>,
    pub metadata: FileMetadata,
    pub uploaded_at: DateTime<Utc>,
}

#[derive(Debug, Clone)]
pub struct FileMetadata {
    pub content_type: String,
    pub size: usize,
    pub checksum: String,
}
