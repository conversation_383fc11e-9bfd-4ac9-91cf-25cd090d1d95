//! API Gateway for OCTAVE Healthcare System
//!
//! Provides centralized API management with OCTAVE semantic protection,
//! rate limiting, authentication, and comprehensive monitoring.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};

use axum::{
    extract::{Request, State},
    http::{HeaderMap, Method, StatusCode, Uri},
    middleware::Next,
    response::Response,
};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use octave_core::error::{OctaveError, OctaveResult};
use octave_database::models::{
    ApiEndpoint, ApiRequestLog, HttpMethod, Practice, User, UserRole,
};
use octave_healthcare::phi_protection::PhiProtectionService;

/// API Gateway configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiGatewayConfig {
    /// Default rate limit per minute
    pub default_rate_limit: u32,
    /// Request timeout in seconds
    pub request_timeout_seconds: u64,
    /// Maximum request body size in bytes
    pub max_request_size_bytes: u64,
    /// Enable OCTAVE protection
    pub enable_octave_protection: bool,
    /// Enable request logging
    pub enable_request_logging: bool,
    /// Enable PHI detection
    pub enable_phi_detection: bool,
    /// API version
    pub api_version: String,
}

impl Default for ApiGatewayConfig {
    fn default() -> Self {
        Self {
            default_rate_limit: 1000, // 1000 requests per minute
            request_timeout_seconds: 30,
            max_request_size_bytes: 10 * 1024 * 1024, // 10MB
            enable_octave_protection: true,
            enable_request_logging: true,
            enable_phi_detection: true,
            api_version: "v1".to_string(),
        }
    }
}

/// API Gateway state
#[derive(Clone)]
pub struct ApiGatewayState {
    /// Gateway configuration
    pub config: ApiGatewayConfig,
    /// PHI protection service
    pub phi_service: Arc<PhiProtectionService>,
    /// Rate limiter
    pub rate_limiter: Arc<dyn RateLimiter>,
    /// Request authenticator
    pub authenticator: Arc<dyn RequestAuthenticator>,
    /// Request validator
    pub validator: Arc<dyn RequestValidator>,
    /// Request logger
    pub logger: Arc<dyn RequestLogger>,
    /// Metrics collector
    pub metrics: Arc<dyn MetricsCollector>,
}

impl ApiGatewayState {
    /// Create a new API gateway state
    pub fn new(
        config: ApiGatewayConfig,
        phi_service: Arc<PhiProtectionService>,
        rate_limiter: Arc<dyn RateLimiter>,
        authenticator: Arc<dyn RequestAuthenticator>,
        validator: Arc<dyn RequestValidator>,
        logger: Arc<dyn RequestLogger>,
        metrics: Arc<dyn MetricsCollector>,
    ) -> Self {
        Self {
            config,
            phi_service,
            rate_limiter,
            authenticator,
            validator,
            logger,
            metrics,
        }
    }
}

/// API Gateway middleware for comprehensive request processing
pub async fn api_gateway_middleware(
    State(state): State<ApiGatewayState>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let start_time = Instant::now();
    let method = request.method().clone();
    let uri = request.uri().clone();
    let headers = request.headers().clone();

    // Extract client information
    let client_ip = extract_client_ip(&headers);
    let user_agent = extract_user_agent(&headers);

    // Create request context
    let mut context = RequestContext::new(
        method.clone(),
        uri.clone(),
        client_ip.clone(),
        user_agent.clone(),
    );

    // 1. Rate limiting check
    if let Err(e) = state.rate_limiter.check_rate_limit(&context).await {
        state.metrics.record_rate_limit_exceeded(&context).await;
        return Err(StatusCode::TOO_MANY_REQUESTS);
    }

    // 2. Authentication
    match state.authenticator.authenticate(&mut request, &mut context).await {
        Ok(auth_result) => {
            context.user = auth_result.user;
            context.practice = auth_result.practice;
        }
        Err(e) => {
            state.metrics.record_authentication_failure(&context).await;
            return Err(StatusCode::UNAUTHORIZED);
        }
    }

    // 3. Request validation
    if let Err(e) = state.validator.validate_request(&request, &context).await {
        state.metrics.record_validation_failure(&context).await;
        return Err(StatusCode::BAD_REQUEST);
    }

    // 4. OCTAVE protection (if enabled)
    if state.config.enable_octave_protection {
        if let Err(e) = check_octave_protection(&request, &context, &state).await {
            state.metrics.record_security_violation(&context).await;
            return Err(StatusCode::FORBIDDEN);
        }
    }

    // 5. PHI detection (if enabled)
    if state.config.enable_phi_detection {
        context.contains_phi = detect_phi_in_request(&request, &state.phi_service).await;
    }

    // Process the request
    let response = next.run(request).await;

    // Calculate processing time
    let processing_time = start_time.elapsed();
    context.processing_time_ms = processing_time.as_millis() as u32;
    context.response_status = response.status().as_u16();

    // 6. Response processing
    let processed_response = process_response(response, &context, &state).await;

    // 7. Request logging (if enabled)
    if state.config.enable_request_logging {
        if let Err(e) = state.logger.log_request(&context).await {
            tracing::error!("Failed to log request: {}", e);
        }
    }

    // 8. Metrics collection
    state.metrics.record_request(&context).await;

    Ok(processed_response)
}

/// Request context for API gateway processing
#[derive(Debug, Clone)]
pub struct RequestContext {
    /// HTTP method
    pub method: Method,
    /// Request URI
    pub uri: Uri,
    /// Client IP address
    pub client_ip: String,
    /// User agent
    pub user_agent: Option<String>,
    /// Authenticated user
    pub user: Option<User>,
    /// User's practice
    pub practice: Option<Practice>,
    /// Request contains PHI
    pub contains_phi: bool,
    /// OCTAVE threat score
    pub threat_score: Option<f32>,
    /// Response status code
    pub response_status: u16,
    /// Processing time in milliseconds
    pub processing_time_ms: u32,
    /// Request size in bytes
    pub request_size: u64,
    /// Response size in bytes
    pub response_size: u64,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
}

impl RequestContext {
    /// Create a new request context
    pub fn new(
        method: Method,
        uri: Uri,
        client_ip: String,
        user_agent: Option<String>,
    ) -> Self {
        Self {
            method,
            uri,
            client_ip,
            user_agent,
            user: None,
            practice: None,
            contains_phi: false,
            threat_score: None,
            response_status: 0,
            processing_time_ms: 0,
            request_size: 0,
            response_size: 0,
            metadata: HashMap::new(),
        }
    }

    /// Get endpoint path
    pub fn endpoint_path(&self) -> String {
        self.uri.path().to_string()
    }

    /// Get HTTP method as HttpMethod enum
    pub fn http_method(&self) -> HttpMethod {
        match self.method {
            Method::GET => HttpMethod::GET,
            Method::POST => HttpMethod::POST,
            Method::PUT => HttpMethod::PUT,
            Method::PATCH => HttpMethod::PATCH,
            Method::DELETE => HttpMethod::DELETE,
            Method::HEAD => HttpMethod::HEAD,
            Method::OPTIONS => HttpMethod::OPTIONS,
            _ => HttpMethod::GET, // Default fallback
        }
    }

    /// Check if request was successful
    pub fn is_successful(&self) -> bool {
        self.response_status >= 200 && self.response_status < 300
    }

    /// Check if request was an error
    pub fn is_error(&self) -> bool {
        self.response_status >= 400
    }

    /// Add metadata
    pub fn add_metadata(&mut self, key: String, value: String) {
        self.metadata.insert(key, value);
    }
}

/// Authentication result
#[derive(Debug, Clone)]
pub struct AuthenticationResult {
    /// Authenticated user
    pub user: Option<User>,
    /// User's practice
    pub practice: Option<Practice>,
    /// Authentication token
    pub token: Option<String>,
}

/// Rate limiter trait
#[async_trait::async_trait]
pub trait RateLimiter: Send + Sync {
    /// Check if request is within rate limits
    async fn check_rate_limit(&self, context: &RequestContext) -> OctaveResult<()>;

    /// Get current rate limit status
    async fn get_rate_limit_status(&self, context: &RequestContext) -> OctaveResult<RateLimitStatus>;
}

/// Rate limit status
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitStatus {
    /// Requests remaining in current window
    pub remaining: u32,
    /// Rate limit window reset time
    pub reset_at: DateTime<Utc>,
    /// Current rate limit
    pub limit: u32,
}

/// Request authenticator trait
#[async_trait::async_trait]
pub trait RequestAuthenticator: Send + Sync {
    /// Authenticate a request
    async fn authenticate(
        &self,
        request: &mut Request,
        context: &mut RequestContext,
    ) -> OctaveResult<AuthenticationResult>;
}

/// Request validator trait
#[async_trait::async_trait]
pub trait RequestValidator: Send + Sync {
    /// Validate a request
    async fn validate_request(
        &self,
        request: &Request,
        context: &RequestContext,
    ) -> OctaveResult<()>;
}

/// Request logger trait
#[async_trait::async_trait]
pub trait RequestLogger: Send + Sync {
    /// Log a request
    async fn log_request(&self, context: &RequestContext) -> OctaveResult<()>;
}

/// Metrics collector trait
#[async_trait::async_trait]
pub trait MetricsCollector: Send + Sync {
    /// Record a request
    async fn record_request(&self, context: &RequestContext);

    /// Record rate limit exceeded
    async fn record_rate_limit_exceeded(&self, context: &RequestContext);

    /// Record authentication failure
    async fn record_authentication_failure(&self, context: &RequestContext);

    /// Record validation failure
    async fn record_validation_failure(&self, context: &RequestContext);

    /// Record security violation
    async fn record_security_violation(&self, context: &RequestContext);
}

// Helper functions

/// Extract client IP from headers
fn extract_client_ip(headers: &HeaderMap) -> String {
    // Check various headers for client IP
    if let Some(forwarded_for) = headers.get("x-forwarded-for") {
        if let Ok(value) = forwarded_for.to_str() {
            if let Some(ip) = value.split(',').next() {
                return ip.trim().to_string();
            }
        }
    }

    if let Some(real_ip) = headers.get("x-real-ip") {
        if let Ok(value) = real_ip.to_str() {
            return value.to_string();
        }
    }

    "unknown".to_string()
}

/// Extract user agent from headers
fn extract_user_agent(headers: &HeaderMap) -> Option<String> {
    headers
        .get("user-agent")
        .and_then(|value| value.to_str().ok())
        .map(|s| s.to_string())
}

/// Check OCTAVE protection
async fn check_octave_protection(
    request: &Request,
    context: &RequestContext,
    state: &ApiGatewayState,
) -> OctaveResult<()> {
    // TODO: Implement OCTAVE semantic protection
    // This would integrate with the OCTAVE semantic immune system
    // to analyze the request for threats and anomalies
    Ok(())
}

/// Detect PHI in request
async fn detect_phi_in_request(
    request: &Request,
    phi_service: &PhiProtectionService,
) -> bool {
    // TODO: Implement PHI detection in request body and parameters
    // This would use the PHI protection service to scan for sensitive data
    false
}

/// Process response with PHI protection and sanitization
async fn process_response(
    response: Response,
    context: &RequestContext,
    state: &ApiGatewayState,
) -> Response {
    // TODO: Implement response processing
    // - PHI sanitization based on user permissions
    // - Response compression
    // - Security headers
    // - CORS headers
    response
}
