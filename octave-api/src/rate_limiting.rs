//! Rate Limiting for OCTAVE API
//!
//! Provides intelligent rate limiting with healthcare-specific considerations,
//! priority-based limiting, and OCTAVE integration.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::{Duration, Instant};

use chrono::{DateTime, Utc};
use governor::{
    clock::DefaultClock,
    middleware::NoOpMiddleware,
    state::{InMemoryState, NotKeyed},
    Quota, RateLimiter as GovernorRateLimiter,
};
use serde::{Deserialize, Serialize};
use tokio::sync::RwLock;
use uuid::Uuid;

use octave_core::error::{OctaveError, OctaveResult};
use octave_database::models::{Practice, User, UserRole};

use crate::gateway::{RateLimiter, RateLimitStatus, RequestContext};

/// Rate limiting configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitConfig {
    /// Default requests per minute
    pub default_requests_per_minute: u32,
    /// Burst allowance
    pub burst_allowance: u32,
    /// Rate limits by user role
    pub role_limits: HashMap<String, u32>,
    /// Rate limits by endpoint pattern
    pub endpoint_limits: HashMap<String, u32>,
    /// Emergency bypass for critical operations
    pub emergency_bypass_enabled: bool,
    /// Rate limit window in seconds
    pub window_seconds: u32,
}

impl Default for RateLimitConfig {
    fn default() -> Self {
        let mut role_limits = HashMap::new();
        role_limits.insert("system_admin".to_string(), 10000);
        role_limits.insert("practice_admin".to_string(), 5000);
        role_limits.insert("practice_manager".to_string(), 3000);
        role_limits.insert("provider".to_string(), 2000);
        role_limits.insert("staff".to_string(), 1000);
        role_limits.insert("billing".to_string(), 1500);
        role_limits.insert("read_only".to_string(), 500);

        let mut endpoint_limits = HashMap::new();
        endpoint_limits.insert("/api/v1/auth/login".to_string(), 10); // Login attempts
        endpoint_limits.insert("/api/v1/search".to_string(), 100); // Search queries
        endpoint_limits.insert("/api/v1/reports".to_string(), 50); // Report generation

        Self {
            default_requests_per_minute: 1000,
            burst_allowance: 100,
            role_limits,
            endpoint_limits,
            emergency_bypass_enabled: true,
            window_seconds: 60,
        }
    }
}

/// Rate limit key for tracking different types of limits
#[derive(Debug, Clone, Hash, PartialEq, Eq)]
pub enum RateLimitKey {
    /// Rate limit by IP address
    IpAddress(String),
    /// Rate limit by user ID
    User(Uuid),
    /// Rate limit by practice ID
    Practice(Uuid),
    /// Rate limit by endpoint
    Endpoint(String),
    /// Rate limit by user role
    Role(String),
    /// Global rate limit
    Global,
}

impl RateLimitKey {
    /// Convert to string representation
    pub fn to_string(&self) -> String {
        match self {
            RateLimitKey::IpAddress(ip) => format!("ip:{}", ip),
            RateLimitKey::User(id) => format!("user:{}", id),
            RateLimitKey::Practice(id) => format!("practice:{}", id),
            RateLimitKey::Endpoint(endpoint) => format!("endpoint:{}", endpoint),
            RateLimitKey::Role(role) => format!("role:{}", role),
            RateLimitKey::Global => "global".to_string(),
        }
    }
}

/// Rate limit entry
#[derive(Debug, Clone)]
pub struct RateLimitEntry {
    /// Rate limiter instance
    pub limiter: Arc<GovernorRateLimiter<NotKeyed, InMemoryState, DefaultClock, NoOpMiddleware>>,
    /// Current count in window
    pub current_count: u32,
    /// Window start time
    pub window_start: Instant,
    /// Rate limit configuration
    pub limit: u32,
    /// Last reset time
    pub last_reset: DateTime<Utc>,
}

impl RateLimitEntry {
    /// Create a new rate limit entry
    pub fn new(requests_per_minute: u32, burst_allowance: u32) -> Self {
        let quota = Quota::per_minute(std::num::NonZeroU32::new(requests_per_minute).unwrap())
            .allow_burst(std::num::NonZeroU32::new(burst_allowance).unwrap());
        
        let limiter = Arc::new(GovernorRateLimiter::direct(quota));

        Self {
            limiter,
            current_count: 0,
            window_start: Instant::now(),
            limit: requests_per_minute,
            last_reset: Utc::now(),
        }
    }

    /// Check if request is allowed
    pub fn check(&mut self) -> bool {
        self.limiter.check().is_ok()
    }

    /// Get remaining requests in current window
    pub fn remaining(&self) -> u32 {
        if self.current_count >= self.limit {
            0
        } else {
            self.limit - self.current_count
        }
    }

    /// Get reset time for current window
    pub fn reset_time(&self) -> DateTime<Utc> {
        self.last_reset + chrono::Duration::minutes(1)
    }
}

/// Healthcare-aware rate limiter implementation
pub struct HealthcareRateLimiter {
    /// Rate limiting configuration
    config: RateLimitConfig,
    /// Rate limit entries by key
    entries: Arc<RwLock<HashMap<RateLimitKey, RateLimitEntry>>>,
    /// Emergency bypass patterns
    emergency_patterns: Vec<String>,
}

impl HealthcareRateLimiter {
    /// Create a new healthcare rate limiter
    pub fn new(config: RateLimitConfig) -> Self {
        let emergency_patterns = vec![
            "/api/v1/prior-auth/emergency".to_string(),
            "/api/v1/patients/emergency".to_string(),
            "/api/v1/communications/urgent".to_string(),
        ];

        Self {
            config,
            entries: Arc::new(RwLock::new(HashMap::new())),
            emergency_patterns,
        }
    }

    /// Check if endpoint qualifies for emergency bypass
    fn is_emergency_endpoint(&self, endpoint: &str) -> bool {
        if !self.config.emergency_bypass_enabled {
            return false;
        }

        self.emergency_patterns.iter().any(|pattern| endpoint.contains(pattern))
    }

    /// Get rate limit for user role
    fn get_role_limit(&self, role: &UserRole) -> u32 {
        self.config.role_limits
            .get(role.as_str())
            .copied()
            .unwrap_or(self.config.default_requests_per_minute)
    }

    /// Get rate limit for endpoint
    fn get_endpoint_limit(&self, endpoint: &str) -> Option<u32> {
        self.config.endpoint_limits
            .iter()
            .find(|(pattern, _)| endpoint.contains(*pattern))
            .map(|(_, limit)| *limit)
    }

    /// Get or create rate limit entry
    async fn get_or_create_entry(&self, key: RateLimitKey, limit: u32) -> RateLimitEntry {
        let mut entries = self.entries.write().await;
        
        entries.entry(key).or_insert_with(|| {
            RateLimitEntry::new(limit, self.config.burst_allowance)
        }).clone()
    }

    /// Check multiple rate limits
    async fn check_multiple_limits(&self, context: &RequestContext) -> OctaveResult<()> {
        let endpoint = context.endpoint_path();

        // Check for emergency bypass
        if self.is_emergency_endpoint(&endpoint) {
            return Ok(());
        }

        // 1. Check IP-based rate limit
        let ip_key = RateLimitKey::IpAddress(context.client_ip.clone());
        let mut ip_entry = self.get_or_create_entry(
            ip_key,
            self.config.default_requests_per_minute,
        ).await;

        if !ip_entry.check() {
            return Err(OctaveError::rate_limit("IP address rate limit exceeded"));
        }

        // 2. Check user-based rate limit (if authenticated)
        if let Some(ref user) = context.user {
            let user_limit = self.get_role_limit(&user.role);
            let user_key = RateLimitKey::User(user.id);
            let mut user_entry = self.get_or_create_entry(user_key, user_limit).await;

            if !user_entry.check() {
                return Err(OctaveError::rate_limit("User rate limit exceeded"));
            }
        }

        // 3. Check practice-based rate limit (if authenticated)
        if let Some(ref practice) = context.practice {
            let practice_key = RateLimitKey::Practice(practice.id);
            let mut practice_entry = self.get_or_create_entry(
                practice_key,
                self.config.default_requests_per_minute * 10, // Higher limit for practice
            ).await;

            if !practice_entry.check() {
                return Err(OctaveError::rate_limit("Practice rate limit exceeded"));
            }
        }

        // 4. Check endpoint-specific rate limit
        if let Some(endpoint_limit) = self.get_endpoint_limit(&endpoint) {
            let endpoint_key = RateLimitKey::Endpoint(endpoint.clone());
            let mut endpoint_entry = self.get_or_create_entry(endpoint_key, endpoint_limit).await;

            if !endpoint_entry.check() {
                return Err(OctaveError::rate_limit("Endpoint rate limit exceeded"));
            }
        }

        // 5. Check global rate limit
        let global_key = RateLimitKey::Global;
        let mut global_entry = self.get_or_create_entry(
            global_key,
            self.config.default_requests_per_minute * 100, // High global limit
        ).await;

        if !global_entry.check() {
            return Err(OctaveError::rate_limit("Global rate limit exceeded"));
        }

        Ok(())
    }

    /// Get rate limit status for a specific key
    async fn get_status_for_key(&self, key: RateLimitKey) -> Option<RateLimitStatus> {
        let entries = self.entries.read().await;
        entries.get(&key).map(|entry| {
            RateLimitStatus {
                remaining: entry.remaining(),
                reset_at: entry.reset_time(),
                limit: entry.limit,
            }
        })
    }

    /// Clean up expired entries
    pub async fn cleanup_expired_entries(&self) {
        let mut entries = self.entries.write().await;
        let now = Instant::now();
        
        entries.retain(|_, entry| {
            now.duration_since(entry.window_start) < Duration::from_secs(300) // Keep for 5 minutes
        });
    }

    /// Get rate limiting statistics
    pub async fn get_statistics(&self) -> RateLimitStatistics {
        let entries = self.entries.read().await;
        
        let total_entries = entries.len();
        let active_entries = entries.values()
            .filter(|entry| entry.current_count > 0)
            .count();

        let total_requests: u32 = entries.values()
            .map(|entry| entry.current_count)
            .sum();

        RateLimitStatistics {
            total_entries,
            active_entries,
            total_requests,
            entries_by_type: self.count_entries_by_type(&entries).await,
        }
    }

    /// Count entries by type
    async fn count_entries_by_type(&self, entries: &HashMap<RateLimitKey, RateLimitEntry>) -> HashMap<String, usize> {
        let mut counts = HashMap::new();
        
        for key in entries.keys() {
            let key_type = match key {
                RateLimitKey::IpAddress(_) => "ip",
                RateLimitKey::User(_) => "user",
                RateLimitKey::Practice(_) => "practice",
                RateLimitKey::Endpoint(_) => "endpoint",
                RateLimitKey::Role(_) => "role",
                RateLimitKey::Global => "global",
            };
            
            *counts.entry(key_type.to_string()).or_insert(0) += 1;
        }
        
        counts
    }
}

#[async_trait::async_trait]
impl RateLimiter for HealthcareRateLimiter {
    async fn check_rate_limit(&self, context: &RequestContext) -> OctaveResult<()> {
        self.check_multiple_limits(context).await
    }

    async fn get_rate_limit_status(&self, context: &RequestContext) -> OctaveResult<RateLimitStatus> {
        // Return status for the most restrictive limit (user-based if authenticated, IP-based otherwise)
        let key = if let Some(ref user) = context.user {
            RateLimitKey::User(user.id)
        } else {
            RateLimitKey::IpAddress(context.client_ip.clone())
        };

        self.get_status_for_key(key).await
            .ok_or_else(|| OctaveError::not_found("Rate limit status not found"))
    }
}

/// Rate limiting statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimitStatistics {
    /// Total number of rate limit entries
    pub total_entries: usize,
    /// Number of active entries (with current requests)
    pub active_entries: usize,
    /// Total requests across all entries
    pub total_requests: u32,
    /// Entries by type
    pub entries_by_type: HashMap<String, usize>,
}

/// Rate limit middleware for specific endpoints
pub struct EndpointRateLimiter {
    /// Rate limiter instance
    limiter: Arc<HealthcareRateLimiter>,
    /// Endpoint-specific configuration
    endpoint_config: HashMap<String, u32>,
}

impl EndpointRateLimiter {
    /// Create a new endpoint rate limiter
    pub fn new(limiter: Arc<HealthcareRateLimiter>) -> Self {
        let mut endpoint_config = HashMap::new();
        endpoint_config.insert("/api/v1/auth/login".to_string(), 5); // 5 login attempts per minute
        endpoint_config.insert("/api/v1/auth/register".to_string(), 3); // 3 registration attempts per minute
        endpoint_config.insert("/api/v1/password/reset".to_string(), 3); // 3 password reset attempts per minute

        Self {
            limiter,
            endpoint_config,
        }
    }

    /// Check rate limit for specific endpoint
    pub async fn check_endpoint_limit(&self, endpoint: &str, context: &RequestContext) -> OctaveResult<()> {
        if let Some(&limit) = self.endpoint_config.get(endpoint) {
            let key = RateLimitKey::Endpoint(endpoint.to_string());
            let mut entry = self.limiter.get_or_create_entry(key, limit).await;
            
            if !entry.check() {
                return Err(OctaveError::rate_limit(&format!("Rate limit exceeded for endpoint: {}", endpoint)));
            }
        }

        Ok(())
    }
}
