//! # OCTAVE API
//!
//! REST API server for OCTAVE Healthcare Prior Authorization System.
//! Provides web endpoints, middleware, and request handling.

#![deny(missing_docs)]
#![warn(clippy::all)]
#![warn(clippy::pedantic)]
#![warn(clippy::nursery)]

pub mod handlers;
pub mod middleware;
pub mod responses;
pub mod routes;
pub mod server;

// API Gateway and Integration modules
pub mod gateway;
pub mod auth;
pub mod rate_limiting;
pub mod validation;
pub mod integration;
pub mod storage;
pub mod search;
pub mod webhooks;
pub mod monitoring;

// Re-export commonly used types
pub use handlers::*;
pub use middleware::*;
pub use responses::*;
pub use routes::*;
pub use server::*;

/// API library version
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_version_info() {
        assert!(!VERSION.is_empty());
    }
}
