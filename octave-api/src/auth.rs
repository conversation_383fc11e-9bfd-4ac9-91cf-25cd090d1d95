//! Authentication and Authorization for OCTAVE API
//!
//! Provides JWT-based authentication, role-based access control,
//! and healthcare-specific security features.

use std::collections::HashMap;
use std::sync::Arc;

use axum::{
    extract::Request,
    http::{HeaderMap, StatusCode},
};
use chrono::{DateTime, Duration, Utc};
use jsonwebtoken::{decode, encode, Algorithm, DecodingKey, EncodingKey, <PERSON><PERSON>, Valida<PERSON>};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use octave_core::error::{OctaveError, OctaveResult};
use octave_database::models::{Practice, User, UserRole};
use octave_database::repositories::{PracticeRepository, UserRepository};

use crate::gateway::{AuthenticationResult, RequestAuthenticator, RequestContext};

/// JWT claims for OCTAVE authentication
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Claims {
    /// Subject (user ID)
    pub sub: String,
    /// Practice ID
    pub practice_id: String,
    /// User role
    pub role: String,
    /// Issued at timestamp
    pub iat: i64,
    /// Expiration timestamp
    pub exp: i64,
    /// Issuer
    pub iss: String,
    /// Audience
    pub aud: String,
    /// JWT ID
    pub jti: String,
    /// Custom claims
    pub custom: HashMap<String, String>,
}

impl Claims {
    /// Create new claims for user
    pub fn new(
        user: &User,
        practice: &Practice,
        expiration_hours: i64,
    ) -> Self {
        let now = Utc::now();
        let exp = now + Duration::hours(expiration_hours);

        Self {
            sub: user.id.to_string(),
            practice_id: practice.id.to_string(),
            role: user.role.as_str().to_string(),
            iat: now.timestamp(),
            exp: exp.timestamp(),
            iss: "octave-api".to_string(),
            aud: "octave-healthcare".to_string(),
            jti: Uuid::new_v4().to_string(),
            custom: HashMap::new(),
        }
    }

    /// Check if token is expired
    pub fn is_expired(&self) -> bool {
        Utc::now().timestamp() > self.exp
    }

    /// Get user ID as UUID
    pub fn user_id(&self) -> OctaveResult<Uuid> {
        Uuid::parse_str(&self.sub)
            .map_err(|_| OctaveError::validation("sub", "Invalid user ID format"))
    }

    /// Get practice ID as UUID
    pub fn practice_id(&self) -> OctaveResult<Uuid> {
        Uuid::parse_str(&self.practice_id)
            .map_err(|_| OctaveError::validation("practice_id", "Invalid practice ID format"))
    }

    /// Get user role
    pub fn user_role(&self) -> OctaveResult<UserRole> {
        UserRole::from_str(&self.role)
            .ok_or_else(|| OctaveError::validation("role", "Invalid user role"))
    }

    /// Add custom claim
    pub fn add_custom_claim(&mut self, key: String, value: String) {
        self.custom.insert(key, value);
    }

    /// Get custom claim
    pub fn get_custom_claim(&self, key: &str) -> Option<&String> {
        self.custom.get(key)
    }
}

/// JWT token service for authentication
pub struct JwtTokenService {
    /// Encoding key for signing tokens
    encoding_key: EncodingKey,
    /// Decoding key for verifying tokens
    decoding_key: DecodingKey,
    /// Token validation configuration
    validation: Validation,
    /// Default token expiration in hours
    default_expiration_hours: i64,
}

impl JwtTokenService {
    /// Create a new JWT token service
    pub fn new(secret: &[u8], default_expiration_hours: i64) -> Self {
        let encoding_key = EncodingKey::from_secret(secret);
        let decoding_key = DecodingKey::from_secret(secret);

        let mut validation = Validation::new(Algorithm::HS256);
        validation.set_issuer(&["octave-api"]);
        validation.set_audience(&["octave-healthcare"]);

        Self {
            encoding_key,
            decoding_key,
            validation,
            default_expiration_hours,
        }
    }

    /// Generate a JWT token for user
    pub fn generate_token(&self, user: &User, practice: &Practice) -> OctaveResult<String> {
        let claims = Claims::new(user, practice, self.default_expiration_hours);
        
        encode(&Header::default(), &claims, &self.encoding_key)
            .map_err(|e| OctaveError::internal(&format!("Failed to generate token: {}", e)))
    }

    /// Generate a refresh token
    pub fn generate_refresh_token(&self, user: &User, practice: &Practice) -> OctaveResult<String> {
        let claims = Claims::new(user, practice, self.default_expiration_hours * 24); // 24x longer
        
        encode(&Header::default(), &claims, &self.encoding_key)
            .map_err(|e| OctaveError::internal(&format!("Failed to generate refresh token: {}", e)))
    }

    /// Verify and decode a JWT token
    pub fn verify_token(&self, token: &str) -> OctaveResult<Claims> {
        decode::<Claims>(token, &self.decoding_key, &self.validation)
            .map(|token_data| token_data.claims)
            .map_err(|e| OctaveError::authentication(&format!("Invalid token: {}", e)))
    }

    /// Refresh a token
    pub fn refresh_token(&self, refresh_token: &str) -> OctaveResult<String> {
        let claims = self.verify_token(refresh_token)?;
        
        // Create new token with updated expiration
        let mut new_claims = claims;
        let now = Utc::now();
        new_claims.iat = now.timestamp();
        new_claims.exp = (now + Duration::hours(self.default_expiration_hours)).timestamp();
        new_claims.jti = Uuid::new_v4().to_string();

        encode(&Header::default(), &new_claims, &self.encoding_key)
            .map_err(|e| OctaveError::internal(&format!("Failed to refresh token: {}", e)))
    }
}

/// OCTAVE request authenticator implementation
pub struct OctaveAuthenticator {
    /// JWT token service
    token_service: Arc<JwtTokenService>,
    /// User repository
    user_repository: Arc<dyn UserRepository>,
    /// Practice repository
    practice_repository: Arc<dyn PracticeRepository>,
}

impl OctaveAuthenticator {
    /// Create a new OCTAVE authenticator
    pub fn new(
        token_service: Arc<JwtTokenService>,
        user_repository: Arc<dyn UserRepository>,
        practice_repository: Arc<dyn PracticeRepository>,
    ) -> Self {
        Self {
            token_service,
            user_repository,
            practice_repository,
        }
    }

    /// Extract bearer token from authorization header
    fn extract_bearer_token(&self, headers: &HeaderMap) -> Option<String> {
        headers
            .get("authorization")
            .and_then(|value| value.to_str().ok())
            .and_then(|auth_header| {
                if auth_header.starts_with("Bearer ") {
                    Some(auth_header[7..].to_string())
                } else {
                    None
                }
            })
    }

    /// Authenticate using API key (for service-to-service communication)
    async fn authenticate_api_key(&self, api_key: &str) -> OctaveResult<AuthenticationResult> {
        // TODO: Implement API key authentication
        // This would validate API keys for service-to-service communication
        Err(OctaveError::authentication("API key authentication not implemented"))
    }

    /// Authenticate using JWT token
    async fn authenticate_jwt(&self, token: &str) -> OctaveResult<AuthenticationResult> {
        // Verify and decode the token
        let claims = self.token_service.verify_token(token)?;

        // Check if token is expired
        if claims.is_expired() {
            return Err(OctaveError::authentication("Token has expired"));
        }

        // Get user and practice from database
        let user_id = claims.user_id()?;
        let practice_id = claims.practice_id()?;

        let user = self.user_repository.get_by_id(user_id).await?
            .ok_or_else(|| OctaveError::authentication("User not found"))?;

        let practice = self.practice_repository.get_by_id(practice_id).await?
            .ok_or_else(|| OctaveError::authentication("Practice not found"))?;

        // Verify user is active and can login
        if !user.can_login() {
            return Err(OctaveError::authentication("User account is not active"));
        }

        // Verify practice is active
        if !practice.is_active() {
            return Err(OctaveError::authentication("Practice is not active"));
        }

        // Verify user belongs to practice
        if user.practice_id != practice.id {
            return Err(OctaveError::authentication("User does not belong to practice"));
        }

        Ok(AuthenticationResult {
            user: Some(user),
            practice: Some(practice),
            token: Some(token.to_string()),
        })
    }
}

#[async_trait::async_trait]
impl RequestAuthenticator for OctaveAuthenticator {
    async fn authenticate(
        &self,
        request: &mut Request,
        context: &mut RequestContext,
    ) -> OctaveResult<AuthenticationResult> {
        let headers = request.headers();

        // Try JWT authentication first
        if let Some(token) = self.extract_bearer_token(headers) {
            return self.authenticate_jwt(&token).await;
        }

        // Try API key authentication
        if let Some(api_key) = headers.get("x-api-key").and_then(|v| v.to_str().ok()) {
            return self.authenticate_api_key(api_key).await;
        }

        // No authentication provided
        Err(OctaveError::authentication("No authentication provided"))
    }
}

/// Permission checker for role-based access control
pub struct PermissionChecker;

impl PermissionChecker {
    /// Check if user has permission for specific action
    pub fn check_permission(
        user: &User,
        resource: &str,
        action: &str,
    ) -> bool {
        match user.role {
            UserRole::SystemAdmin => true, // System admin has all permissions
            UserRole::PracticeAdmin => {
                // Practice admin has most permissions within their practice
                !matches!(action, "system_config" | "global_admin")
            }
            UserRole::PracticeManager => {
                // Practice manager has management permissions
                matches!(action, "read" | "create" | "update" | "manage_users" | "view_reports")
            }
            UserRole::Provider => {
                // Providers can access patient data and create prior auths
                matches!(resource, "patient" | "prior_auth" | "document" | "communication") &&
                matches!(action, "read" | "create" | "update")
            }
            UserRole::Staff => {
                // Staff can read most data and create basic records
                matches!(action, "read" | "create") &&
                !matches!(resource, "user" | "practice" | "system")
            }
            UserRole::Billing => {
                // Billing staff can access billing-related data
                matches!(resource, "prior_auth" | "patient" | "insurance") &&
                matches!(action, "read" | "update")
            }
            UserRole::ReadOnly => {
                // Read-only users can only read non-sensitive data
                action == "read" && !matches!(resource, "user" | "practice" | "system")
            }
        }
    }

    /// Check if user can access PHI
    pub fn can_access_phi(user: &User) -> bool {
        user.role.can_access_phi()
    }

    /// Check if user can manage other users
    pub fn can_manage_users(user: &User) -> bool {
        user.role.can_manage_users()
    }

    /// Check if user has administrative privileges
    pub fn is_admin(user: &User) -> bool {
        user.role.is_admin()
    }
}

/// Login request
#[derive(Debug, Deserialize)]
pub struct LoginRequest {
    /// Email address
    pub email: String,
    /// Password
    pub password: String,
    /// Practice ID (optional, for multi-practice users)
    pub practice_id: Option<Uuid>,
}

/// Login response
#[derive(Debug, Serialize)]
pub struct LoginResponse {
    /// Access token
    pub access_token: String,
    /// Refresh token
    pub refresh_token: String,
    /// Token type
    pub token_type: String,
    /// Token expiration in seconds
    pub expires_in: i64,
    /// User information
    pub user: UserInfo,
    /// Practice information
    pub practice: PracticeInfo,
}

/// User information for API responses
#[derive(Debug, Serialize)]
pub struct UserInfo {
    /// User ID
    pub id: Uuid,
    /// Email address
    pub email: String,
    /// Full name
    pub name: String,
    /// User role
    pub role: String,
    /// User permissions
    pub permissions: Vec<String>,
}

/// Practice information for API responses
#[derive(Debug, Serialize)]
pub struct PracticeInfo {
    /// Practice ID
    pub id: Uuid,
    /// Practice name
    pub name: String,
    /// Practice type
    pub practice_type: String,
    /// Practice status
    pub status: String,
}
