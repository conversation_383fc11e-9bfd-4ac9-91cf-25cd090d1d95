//! Search and Indexing for OCTAVE API
//!
//! Provides full-text search, faceted search, and intelligent indexing
//! with PHI protection and healthcare-specific features.

use std::collections::HashMap;
use std::sync::Arc;

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use tantivy::{
    collector::TopDocs,
    directory::MmapDirectory,
    doc,
    query::{BooleanQuery, FuzzyTermQuery, PhraseQuery, QueryParser, TermQuery},
    schema::{Field, Schema, STORED, TEXT},
    Index, IndexReader, IndexWriter, ReloadPolicy, Term,
};
use uuid::Uuid;

use octave_core::error::{OctaveError, OctaveResult};
use octave_database::models::{
    EntityType, IndexType, Patient, PriorAuth, Practice, SearchIndex, SearchQuery, User,
};
use octave_database::repositories::{SearchIndexRepository, SearchQueryRepository};
use octave_healthcare::phi_protection::PhiProtectionService;

/// Search service for OCTAVE healthcare data
pub struct SearchService {
    /// Search indices by practice and type
    indices: Arc<tokio::sync::RwLock<HashMap<(Uuid, IndexType), Arc<SearchIndex>>>>,
    /// Tantivy indices
    tantivy_indices: Arc<tokio::sync::RwLock<HashMap<String, Index>>>,
    /// Index repository
    index_repository: Arc<dyn SearchIndexRepository>,
    /// Query repository
    query_repository: Arc<dyn SearchQueryRepository>,
    /// PHI protection service
    phi_service: Arc<PhiProtectionService>,
}

impl SearchService {
    /// Create a new search service
    pub fn new(
        index_repository: Arc<dyn SearchIndexRepository>,
        query_repository: Arc<dyn SearchQueryRepository>,
        phi_service: Arc<PhiProtectionService>,
    ) -> Self {
        Self {
            indices: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
            tantivy_indices: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
            index_repository,
            query_repository,
            phi_service,
        }
    }

    /// Initialize search indices for a practice
    pub async fn initialize_practice_indices(&self, practice_id: Uuid) -> OctaveResult<()> {
        // Create full-text search index
        self.create_index(practice_id, IndexType::FullText, vec![
            EntityType::Patient,
            EntityType::PriorAuth,
            EntityType::Document,
            EntityType::Communication,
        ]).await?;

        // Create faceted search index
        self.create_index(practice_id, IndexType::Faceted, vec![
            EntityType::Patient,
            EntityType::PriorAuth,
        ]).await?;

        // Create autocomplete index
        self.create_index(practice_id, IndexType::Autocomplete, vec![
            EntityType::Patient,
            EntityType::Provider,
        ]).await?;

        Ok(())
    }

    /// Create a search index
    pub async fn create_index(
        &self,
        practice_id: Uuid,
        index_type: IndexType,
        entity_types: Vec<EntityType>,
    ) -> OctaveResult<SearchIndex> {
        // Create Tantivy schema based on index type
        let schema = self.create_schema_for_type(&index_type)?;
        
        // Create Tantivy index
        let index_name = format!("{}_{}", practice_id, index_type.as_str());
        let index_dir = format!("./search_indices/{}", index_name);
        
        std::fs::create_dir_all(&index_dir)
            .map_err(|e| OctaveError::internal(&format!("Failed to create index directory: {}", e)))?;

        let directory = MmapDirectory::open(&index_dir)
            .map_err(|e| OctaveError::internal(&format!("Failed to open index directory: {}", e)))?;

        let index = Index::create_in_dir(&index_dir, schema)
            .map_err(|e| OctaveError::internal(&format!("Failed to create Tantivy index: {}", e)))?;

        // Store Tantivy index
        {
            let mut tantivy_indices = self.tantivy_indices.write().await;
            tantivy_indices.insert(index_name.clone(), index);
        }

        // Create search index record
        let search_index = SearchIndex {
            id: Uuid::new_v4(),
            practice_id,
            name: index_name,
            index_type,
            entity_types,
            configuration: "{}".to_string(),
            is_active: true,
            last_indexed_at: None,
            document_count: 0,
            size_bytes: 0,
            metadata: "{}".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let created_index = self.index_repository.create(search_index).await?;

        // Store in memory
        {
            let mut indices = self.indices.write().await;
            indices.insert((practice_id, index_type), Arc::new(created_index.clone()));
        }

        Ok(created_index)
    }

    /// Search across all relevant indices
    pub async fn search(
        &self,
        practice_id: Uuid,
        user: &User,
        search_request: SearchRequest,
    ) -> OctaveResult<SearchResponse> {
        let start_time = std::time::Instant::now();

        // Log the search query
        let search_query = SearchQuery {
            id: Uuid::new_v4(),
            practice_id,
            user_id: user.id,
            query_text: search_request.query.clone(),
            filters: serde_json::to_string(&search_request.filters).unwrap_or_default(),
            result_count: 0,
            processing_time_ms: 0,
            contains_phi: self.contains_phi(&search_request.query).await,
            index_name: "multiple".to_string(),
            metadata: "{}".to_string(),
            timestamp: Utc::now(),
        };

        // Perform search based on type
        let results = match search_request.search_type {
            SearchType::FullText => self.full_text_search(practice_id, user, &search_request).await?,
            SearchType::Faceted => self.faceted_search(practice_id, user, &search_request).await?,
            SearchType::Autocomplete => self.autocomplete_search(practice_id, user, &search_request).await?,
        };

        let processing_time = start_time.elapsed();

        // Update search query log
        let mut logged_query = search_query;
        logged_query.result_count = results.len() as u32;
        logged_query.processing_time_ms = processing_time.as_millis() as u32;

        if let Err(e) = self.query_repository.log_query(logged_query).await {
            tracing::warn!("Failed to log search query: {}", e);
        }

        Ok(SearchResponse {
            results,
            total_count: results.len() as u32,
            processing_time_ms: processing_time.as_millis() as u32,
            facets: HashMap::new(), // TODO: Implement facet extraction
            suggestions: vec![], // TODO: Implement search suggestions
        })
    }

    /// Perform full-text search
    async fn full_text_search(
        &self,
        practice_id: Uuid,
        user: &User,
        request: &SearchRequest,
    ) -> OctaveResult<Vec<SearchResult>> {
        let index_key = (practice_id, IndexType::FullText);
        let tantivy_indices = self.tantivy_indices.read().await;
        
        let index_name = format!("{}_{}", practice_id, IndexType::FullText.as_str());
        let index = tantivy_indices.get(&index_name)
            .ok_or_else(|| OctaveError::not_found("Search index not found"))?;

        let reader = index.reader_builder()
            .reload_policy(ReloadPolicy::OnCommit)
            .try_into()
            .map_err(|e| OctaveError::internal(&format!("Failed to create index reader: {}", e)))?;

        let searcher = reader.searcher();
        let schema = index.schema();

        // Get fields
        let content_field = schema.get_field("content")
            .ok_or_else(|| OctaveError::internal("Content field not found"))?;
        let entity_type_field = schema.get_field("entity_type")
            .ok_or_else(|| OctaveError::internal("Entity type field not found"))?;

        // Create query parser
        let query_parser = QueryParser::for_index(index, vec![content_field]);
        
        // Parse query
        let query = query_parser.parse_query(&request.query)
            .map_err(|e| OctaveError::validation("query", &format!("Invalid search query: {}", e)))?;

        // Execute search
        let top_docs = searcher.search(&query, &TopDocs::with_limit(request.limit.unwrap_or(50)))
            .map_err(|e| OctaveError::internal(&format!("Search execution failed: {}", e)))?;

        let mut results = Vec::new();
        for (score, doc_address) in top_docs {
            let retrieved_doc = searcher.doc(doc_address)
                .map_err(|e| OctaveError::internal(&format!("Failed to retrieve document: {}", e)))?;

            // Extract document fields
            let content = retrieved_doc.get_first(content_field)
                .and_then(|v| v.as_text())
                .unwrap_or("")
                .to_string();

            let entity_type = retrieved_doc.get_first(entity_type_field)
                .and_then(|v| v.as_text())
                .unwrap_or("")
                .to_string();

            // Apply PHI protection based on user permissions
            let protected_content = if user.can_access_phi() {
                content
            } else {
                self.phi_service.sanitize_text(&content).await.unwrap_or(content)
            };

            results.push(SearchResult {
                id: Uuid::new_v4(), // TODO: Extract actual document ID
                entity_type: EntityType::from_str(&entity_type).unwrap_or(EntityType::Document),
                title: "Search Result".to_string(), // TODO: Extract title
                content: protected_content,
                score: score as f64,
                metadata: HashMap::new(),
                created_at: Utc::now(), // TODO: Extract actual creation date
            });
        }

        Ok(results)
    }

    /// Perform faceted search
    async fn faceted_search(
        &self,
        practice_id: Uuid,
        user: &User,
        request: &SearchRequest,
    ) -> OctaveResult<Vec<SearchResult>> {
        // TODO: Implement faceted search with Tantivy facets
        Ok(vec![])
    }

    /// Perform autocomplete search
    async fn autocomplete_search(
        &self,
        practice_id: Uuid,
        user: &User,
        request: &SearchRequest,
    ) -> OctaveResult<Vec<SearchResult>> {
        // TODO: Implement autocomplete search with prefix matching
        Ok(vec![])
    }

    /// Index a document
    pub async fn index_document(&self, practice_id: Uuid, document: IndexDocument) -> OctaveResult<()> {
        let index_name = format!("{}_{}", practice_id, IndexType::FullText.as_str());
        let tantivy_indices = self.tantivy_indices.read().await;
        
        let index = tantivy_indices.get(&index_name)
            .ok_or_else(|| OctaveError::not_found("Search index not found"))?;

        let schema = index.schema();
        let mut index_writer = index.writer(50_000_000)
            .map_err(|e| OctaveError::internal(&format!("Failed to create index writer: {}", e)))?;

        // Get fields
        let id_field = schema.get_field("id").unwrap();
        let entity_type_field = schema.get_field("entity_type").unwrap();
        let title_field = schema.get_field("title").unwrap();
        let content_field = schema.get_field("content").unwrap();
        let created_at_field = schema.get_field("created_at").unwrap();

        // Create Tantivy document
        let tantivy_doc = doc!(
            id_field => document.id.to_string(),
            entity_type_field => document.entity_type.as_str(),
            title_field => document.title,
            content_field => document.content,
            created_at_field => document.created_at.timestamp()
        );

        // Add document to index
        index_writer.add_document(tantivy_doc)
            .map_err(|e| OctaveError::internal(&format!("Failed to add document to index: {}", e)))?;

        // Commit changes
        index_writer.commit()
            .map_err(|e| OctaveError::internal(&format!("Failed to commit index changes: {}", e)))?;

        Ok(())
    }

    /// Create Tantivy schema for index type
    fn create_schema_for_type(&self, index_type: &IndexType) -> OctaveResult<Schema> {
        let mut schema_builder = Schema::builder();

        // Common fields for all index types
        schema_builder.add_text_field("id", STORED);
        schema_builder.add_text_field("entity_type", STORED);
        schema_builder.add_text_field("title", TEXT | STORED);
        schema_builder.add_text_field("content", TEXT);
        schema_builder.add_i64_field("created_at", STORED);

        match index_type {
            IndexType::FullText => {
                // Additional fields for full-text search
                schema_builder.add_text_field("tags", TEXT);
                schema_builder.add_text_field("metadata", STORED);
            }
            IndexType::Faceted => {
                // Additional fields for faceted search
                schema_builder.add_text_field("category", STORED);
                schema_builder.add_text_field("status", STORED);
                schema_builder.add_text_field("priority", STORED);
            }
            IndexType::Autocomplete => {
                // Additional fields for autocomplete
                schema_builder.add_text_field("suggestions", TEXT);
            }
            _ => {}
        }

        Ok(schema_builder.build())
    }

    /// Check if query contains PHI
    async fn contains_phi(&self, query: &str) -> bool {
        // TODO: Use PHI protection service to detect PHI in search query
        false
    }

    /// Get search suggestions
    pub async fn get_suggestions(
        &self,
        practice_id: Uuid,
        prefix: &str,
        limit: u32,
    ) -> OctaveResult<Vec<String>> {
        // TODO: Implement search suggestions based on query history and indexed content
        Ok(vec![])
    }

    /// Get search analytics
    pub async fn get_analytics(&self, practice_id: Uuid) -> OctaveResult<SearchAnalytics> {
        // TODO: Implement search analytics aggregation
        Ok(SearchAnalytics {
            total_queries: 0,
            avg_query_time_ms: 0.0,
            queries_per_hour: 0.0,
            popular_queries: vec![],
            avg_results_per_query: 0.0,
            phi_queries: 0,
            zero_result_queries: 0,
        })
    }
}

/// Search request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchRequest {
    /// Search query text
    pub query: String,
    /// Search type
    pub search_type: SearchType,
    /// Search filters
    pub filters: HashMap<String, String>,
    /// Maximum number of results
    pub limit: Option<usize>,
    /// Result offset for pagination
    pub offset: Option<usize>,
    /// Sort order
    pub sort: Option<SortOrder>,
}

/// Search type enumeration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SearchType {
    /// Full-text search
    FullText,
    /// Faceted search
    Faceted,
    /// Autocomplete search
    Autocomplete,
}

/// Sort order
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SortOrder {
    /// Field to sort by
    pub field: String,
    /// Sort direction
    pub direction: SortDirection,
}

/// Sort direction
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum SortDirection {
    /// Ascending order
    Asc,
    /// Descending order
    Desc,
}

/// Search response
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResponse {
    /// Search results
    pub results: Vec<SearchResult>,
    /// Total number of results
    pub total_count: u32,
    /// Processing time in milliseconds
    pub processing_time_ms: u32,
    /// Search facets
    pub facets: HashMap<String, Vec<FacetValue>>,
    /// Search suggestions
    pub suggestions: Vec<String>,
}

/// Search result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    /// Document ID
    pub id: Uuid,
    /// Entity type
    pub entity_type: EntityType,
    /// Result title
    pub title: String,
    /// Result content (may be truncated)
    pub content: String,
    /// Relevance score
    pub score: f64,
    /// Additional metadata
    pub metadata: HashMap<String, String>,
    /// Creation timestamp
    pub created_at: DateTime<Utc>,
}

/// Facet value
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FacetValue {
    /// Facet value
    pub value: String,
    /// Number of documents with this value
    pub count: u32,
}

/// Document to be indexed
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IndexDocument {
    /// Document ID
    pub id: Uuid,
    /// Entity type
    pub entity_type: EntityType,
    /// Document title
    pub title: String,
    /// Document content
    pub content: String,
    /// Document tags
    pub tags: Vec<String>,
    /// Document metadata
    pub metadata: HashMap<String, String>,
    /// Creation timestamp
    pub created_at: DateTime<Utc>,
}

/// Search analytics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchAnalytics {
    /// Total queries
    pub total_queries: u64,
    /// Average query time
    pub avg_query_time_ms: f64,
    /// Queries per hour
    pub queries_per_hour: f64,
    /// Most popular queries
    pub popular_queries: Vec<(String, u32)>,
    /// Average results per query
    pub avg_results_per_query: f64,
    /// PHI queries count
    pub phi_queries: u64,
    /// Zero result queries
    pub zero_result_queries: u64,
}
