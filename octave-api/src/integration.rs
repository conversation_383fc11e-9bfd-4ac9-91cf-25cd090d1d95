//! External System Integration for OCTAVE API
//!
//! Provides integration with insurance portals, EHR systems, email services,
//! SMS services, and other external healthcare systems.

use std::collections::HashMap;
use std::sync::Arc;
use std::time::Duration;

use chrono::{DateTime, Utc};
use reqwest::{Client, Response};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use uuid::Uuid;

use octave_core::error::{OctaveError, OctaveResult};
use octave_database::models::{
    ConnectionStatus, ExternalIntegration, IntegrationType, Practice, PriorAuth, Patient,
};
use octave_database::repositories::ExternalIntegrationRepository;

/// Integration manager for external systems
pub struct IntegrationManager {
    /// HTTP client for external requests
    client: Client,
    /// Integration repository
    integration_repository: Arc<dyn ExternalIntegrationRepository>,
    /// Integration configurations
    integrations: Arc<tokio::sync::RwLock<HashMap<Uuid, Arc<dyn Integration>>>>,
}

impl IntegrationManager {
    /// Create a new integration manager
    pub fn new(integration_repository: Arc<dyn ExternalIntegrationRepository>) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            client,
            integration_repository,
            integrations: Arc::new(tokio::sync::RwLock::new(HashMap::new())),
        }
    }

    /// Register an integration
    pub async fn register_integration(&self, integration_id: Uuid, integration: Arc<dyn Integration>) {
        let mut integrations = self.integrations.write().await;
        integrations.insert(integration_id, integration);
    }

    /// Get integration by ID
    pub async fn get_integration(&self, integration_id: Uuid) -> Option<Arc<dyn Integration>> {
        let integrations = self.integrations.read().await;
        integrations.get(&integration_id).cloned()
    }

    /// Test integration connection
    pub async fn test_connection(&self, integration_id: Uuid) -> OctaveResult<bool> {
        let integration = self.get_integration(integration_id).await
            .ok_or_else(|| OctaveError::not_found("Integration not found"))?;

        match integration.test_connection().await {
            Ok(_) => {
                self.integration_repository
                    .update_connection_status(integration_id, ConnectionStatus::Connected, None)
                    .await?;
                Ok(true)
            }
            Err(e) => {
                self.integration_repository
                    .update_connection_status(integration_id, ConnectionStatus::Failed, Some(e.to_string()))
                    .await?;
                Ok(false)
            }
        }
    }

    /// Send prior authorization to insurance portal
    pub async fn submit_prior_auth(&self, practice_id: Uuid, prior_auth: &PriorAuth) -> OctaveResult<String> {
        let integrations = self.integration_repository
            .get_by_type(practice_id, IntegrationType::InsurancePortal)
            .await?;

        for integration_config in integrations {
            if let Some(integration) = self.get_integration(integration_config.id).await {
                if let Ok(result) = integration.submit_prior_auth(prior_auth).await {
                    return Ok(result);
                }
            }
        }

        Err(OctaveError::integration("No available insurance portal integration"))
    }

    /// Send email notification
    pub async fn send_email(&self, practice_id: Uuid, email_request: EmailRequest) -> OctaveResult<String> {
        let integrations = self.integration_repository
            .get_by_type(practice_id, IntegrationType::EmailService)
            .await?;

        for integration_config in integrations {
            if let Some(integration) = self.get_integration(integration_config.id).await {
                if let Ok(result) = integration.send_email(&email_request).await {
                    return Ok(result);
                }
            }
        }

        Err(OctaveError::integration("No available email service integration"))
    }

    /// Send SMS notification
    pub async fn send_sms(&self, practice_id: Uuid, sms_request: SmsRequest) -> OctaveResult<String> {
        let integrations = self.integration_repository
            .get_by_type(practice_id, IntegrationType::SMSService)
            .await?;

        for integration_config in integrations {
            if let Some(integration) = self.get_integration(integration_config.id).await {
                if let Ok(result) = integration.send_sms(&sms_request).await {
                    return Ok(result);
                }
            }
        }

        Err(OctaveError::integration("No available SMS service integration"))
    }

    /// Sync patient data with EHR
    pub async fn sync_patient_data(&self, practice_id: Uuid, patient: &Patient) -> OctaveResult<()> {
        let integrations = self.integration_repository
            .get_by_type(practice_id, IntegrationType::EHR)
            .await?;

        for integration_config in integrations {
            if let Some(integration) = self.get_integration(integration_config.id).await {
                if let Err(e) = integration.sync_patient_data(patient).await {
                    tracing::warn!("Failed to sync patient data with EHR {}: {}", integration_config.name, e);
                }
            }
        }

        Ok(())
    }
}

/// Generic integration trait
#[async_trait::async_trait]
pub trait Integration: Send + Sync {
    /// Test connection to external system
    async fn test_connection(&self) -> OctaveResult<()>;

    /// Submit prior authorization (for insurance portals)
    async fn submit_prior_auth(&self, prior_auth: &PriorAuth) -> OctaveResult<String> {
        Err(OctaveError::not_implemented("Prior auth submission not supported"))
    }

    /// Send email (for email services)
    async fn send_email(&self, email_request: &EmailRequest) -> OctaveResult<String> {
        Err(OctaveError::not_implemented("Email sending not supported"))
    }

    /// Send SMS (for SMS services)
    async fn send_sms(&self, sms_request: &SmsRequest) -> OctaveResult<String> {
        Err(OctaveError::not_implemented("SMS sending not supported"))
    }

    /// Sync patient data (for EHR systems)
    async fn sync_patient_data(&self, patient: &Patient) -> OctaveResult<()> {
        Err(OctaveError::not_implemented("Patient data sync not supported"))
    }

    /// Get integration type
    fn integration_type(&self) -> IntegrationType;

    /// Get integration name
    fn name(&self) -> &str;
}

/// Email request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailRequest {
    /// Recipient email address
    pub to: String,
    /// Sender email address
    pub from: String,
    /// Email subject
    pub subject: String,
    /// Email body (HTML)
    pub body_html: String,
    /// Email body (plain text)
    pub body_text: String,
    /// Attachments
    pub attachments: Vec<EmailAttachment>,
    /// Email metadata
    pub metadata: HashMap<String, String>,
}

/// Email attachment
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailAttachment {
    /// Attachment filename
    pub filename: String,
    /// Content type
    pub content_type: String,
    /// Attachment data (base64 encoded)
    pub data: String,
}

/// SMS request
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SmsRequest {
    /// Recipient phone number
    pub to: String,
    /// Sender phone number
    pub from: String,
    /// SMS message body
    pub body: String,
    /// SMS metadata
    pub metadata: HashMap<String, String>,
}

/// Availity insurance portal integration
pub struct AvailityIntegration {
    /// API base URL
    base_url: String,
    /// API credentials
    credentials: AvailityCredentials,
    /// HTTP client
    client: Client,
}

/// Availity credentials
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AvailityCredentials {
    /// Client ID
    pub client_id: String,
    /// Client secret
    pub client_secret: String,
    /// API key
    pub api_key: String,
}

impl AvailityIntegration {
    /// Create a new Availity integration
    pub fn new(base_url: String, credentials: AvailityCredentials) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            base_url,
            credentials,
            client,
        }
    }

    /// Authenticate with Availity API
    async fn authenticate(&self) -> OctaveResult<String> {
        let auth_url = format!("{}/oauth/token", self.base_url);
        
        let params = [
            ("grant_type", "client_credentials"),
            ("client_id", &self.credentials.client_id),
            ("client_secret", &self.credentials.client_secret),
        ];

        let response = self.client
            .post(&auth_url)
            .form(&params)
            .send()
            .await
            .map_err(|e| OctaveError::integration(&format!("Availity auth request failed: {}", e)))?;

        if !response.status().is_success() {
            return Err(OctaveError::integration("Availity authentication failed"));
        }

        let auth_response: Value = response.json().await
            .map_err(|e| OctaveError::integration(&format!("Failed to parse auth response: {}", e)))?;

        auth_response["access_token"]
            .as_str()
            .map(|token| token.to_string())
            .ok_or_else(|| OctaveError::integration("No access token in response"))
    }
}

#[async_trait::async_trait]
impl Integration for AvailityIntegration {
    async fn test_connection(&self) -> OctaveResult<()> {
        self.authenticate().await.map(|_| ())
    }

    async fn submit_prior_auth(&self, prior_auth: &PriorAuth) -> OctaveResult<String> {
        let token = self.authenticate().await?;
        
        let submit_url = format!("{}/api/v1/prior-auth", self.base_url);
        
        // Convert prior auth to Availity format
        let availity_request = self.convert_to_availity_format(prior_auth)?;
        
        let response = self.client
            .post(&submit_url)
            .bearer_auth(&token)
            .json(&availity_request)
            .send()
            .await
            .map_err(|e| OctaveError::integration(&format!("Prior auth submission failed: {}", e)))?;

        if !response.status().is_success() {
            return Err(OctaveError::integration("Prior auth submission rejected"));
        }

        let response_data: Value = response.json().await
            .map_err(|e| OctaveError::integration(&format!("Failed to parse response: {}", e)))?;

        response_data["reference_number"]
            .as_str()
            .map(|ref_num| ref_num.to_string())
            .ok_or_else(|| OctaveError::integration("No reference number in response"))
    }

    fn integration_type(&self) -> IntegrationType {
        IntegrationType::InsurancePortal
    }

    fn name(&self) -> &str {
        "Availity"
    }
}

impl AvailityIntegration {
    /// Convert OCTAVE prior auth to Availity format
    fn convert_to_availity_format(&self, prior_auth: &PriorAuth) -> OctaveResult<Value> {
        // TODO: Implement actual conversion to Availity API format
        // This would map OCTAVE fields to Availity's expected format
        Ok(serde_json::json!({
            "tracking_id": prior_auth.tracking_id,
            "procedure_code": prior_auth.procedure_code,
            "diagnosis_codes": prior_auth.diagnosis_codes,
            "clinical_justification": prior_auth.clinical_justification,
            "urgency": prior_auth.urgency_level
        }))
    }
}

/// SendGrid email integration
pub struct SendGridIntegration {
    /// API key
    api_key: String,
    /// HTTP client
    client: Client,
}

impl SendGridIntegration {
    /// Create a new SendGrid integration
    pub fn new(api_key: String) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            api_key,
            client,
        }
    }
}

#[async_trait::async_trait]
impl Integration for SendGridIntegration {
    async fn test_connection(&self) -> OctaveResult<()> {
        let response = self.client
            .get("https://api.sendgrid.com/v3/user/profile")
            .bearer_auth(&self.api_key)
            .send()
            .await
            .map_err(|e| OctaveError::integration(&format!("SendGrid test failed: {}", e)))?;

        if response.status().is_success() {
            Ok(())
        } else {
            Err(OctaveError::integration("SendGrid authentication failed"))
        }
    }

    async fn send_email(&self, email_request: &EmailRequest) -> OctaveResult<String> {
        let email_data = serde_json::json!({
            "personalizations": [{
                "to": [{"email": email_request.to}]
            }],
            "from": {"email": email_request.from},
            "subject": email_request.subject,
            "content": [
                {
                    "type": "text/html",
                    "value": email_request.body_html
                },
                {
                    "type": "text/plain",
                    "value": email_request.body_text
                }
            ]
        });

        let response = self.client
            .post("https://api.sendgrid.com/v3/mail/send")
            .bearer_auth(&self.api_key)
            .json(&email_data)
            .send()
            .await
            .map_err(|e| OctaveError::integration(&format!("Email send failed: {}", e)))?;

        if response.status().is_success() {
            Ok(Uuid::new_v4().to_string()) // Return a message ID
        } else {
            Err(OctaveError::integration("Email sending failed"))
        }
    }

    fn integration_type(&self) -> IntegrationType {
        IntegrationType::EmailService
    }

    fn name(&self) -> &str {
        "SendGrid"
    }
}

/// Twilio SMS integration
pub struct TwilioIntegration {
    /// Account SID
    account_sid: String,
    /// Auth token
    auth_token: String,
    /// HTTP client
    client: Client,
}

impl TwilioIntegration {
    /// Create a new Twilio integration
    pub fn new(account_sid: String, auth_token: String) -> Self {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .expect("Failed to create HTTP client");

        Self {
            account_sid,
            auth_token,
            client,
        }
    }
}

#[async_trait::async_trait]
impl Integration for TwilioIntegration {
    async fn test_connection(&self) -> OctaveResult<()> {
        let url = format!("https://api.twilio.com/2010-04-01/Accounts/{}.json", self.account_sid);
        
        let response = self.client
            .get(&url)
            .basic_auth(&self.account_sid, Some(&self.auth_token))
            .send()
            .await
            .map_err(|e| OctaveError::integration(&format!("Twilio test failed: {}", e)))?;

        if response.status().is_success() {
            Ok(())
        } else {
            Err(OctaveError::integration("Twilio authentication failed"))
        }
    }

    async fn send_sms(&self, sms_request: &SmsRequest) -> OctaveResult<String> {
        let url = format!("https://api.twilio.com/2010-04-01/Accounts/{}/Messages.json", self.account_sid);
        
        let params = [
            ("To", &sms_request.to),
            ("From", &sms_request.from),
            ("Body", &sms_request.body),
        ];

        let response = self.client
            .post(&url)
            .basic_auth(&self.account_sid, Some(&self.auth_token))
            .form(&params)
            .send()
            .await
            .map_err(|e| OctaveError::integration(&format!("SMS send failed: {}", e)))?;

        if response.status().is_success() {
            let response_data: Value = response.json().await
                .map_err(|e| OctaveError::integration(&format!("Failed to parse response: {}", e)))?;

            response_data["sid"]
                .as_str()
                .map(|sid| sid.to_string())
                .ok_or_else(|| OctaveError::integration("No message SID in response"))
        } else {
            Err(OctaveError::integration("SMS sending failed"))
        }
    }

    fn integration_type(&self) -> IntegrationType {
        IntegrationType::SMSService
    }

    fn name(&self) -> &str {
        "Twilio"
    }
}
