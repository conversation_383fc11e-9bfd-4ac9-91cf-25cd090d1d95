// OCTAVE Healthcare Platform - Main Application Component
// HIPAA Compliant Healthcare Prior Authorization Tracking System

import React, { useEffect } from 'react';
import { BrowserRouter as Router } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ThemeProvider, CssBaseline } from '@mui/material';
import { useSelector, useDispatch } from 'react-redux';

import { store } from '@/store';
import { createHealthcareTheme } from '@/styles/theme';
import { selectTheme } from '@/store/slices/uiSlice';
import { restoreSession } from '@/store/slices/authSlice';
import AppRoutes from '@/components/routing/AppRoutes';
import ErrorBoundary from '@/components/common/ErrorBoundary';
import LoadingProvider from '@/components/common/LoadingProvider';
import NotificationProvider from '@/components/common/NotificationProvider';

// Main App Content Component
const AppContent: React.FC = () => {
  const dispatch = useDispatch();
  const themeMode = useSelector(selectTheme);
  const theme = createHealthcareTheme(themeMode);

  useEffect(() => {
    // Restore user session on app load
    dispatch(restoreSession());
  }, [dispatch]);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <ErrorBoundary>
        <LoadingProvider>
          <NotificationProvider>
            <Router>
              <AppRoutes />
            </Router>
          </NotificationProvider>
        </LoadingProvider>
      </ErrorBoundary>
    </ThemeProvider>
  );
};

// Root App Component with Redux Provider
const App: React.FC = () => {
  return (
    <Provider store={store}>
      <AppContent />
    </Provider>
  );
};

export default App;
