// OCTAVE Healthcare Platform - Authentication Service
// HIPAA Compliant Authentication with Semantic Protection Integration

import axios, { AxiosResponse } from 'axios';
import {
  LoginRequest,
  LoginResponse,
  MfaVerificationRequest,
  PasswordResetRequest,
  PasswordResetResponse,
  PasswordResetConfirmRequest,
  SecuritySettings,
  SessionInfo,
  ApiResponse,
  User,
} from '@/types';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

// Create axios instance with default configuration
const authApi = axios.create({
  baseURL: `${API_BASE_URL}/auth`,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
authApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor for error handling
authApi.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      localStorage.removeItem('expiresAt');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export class AuthService {
  /**
   * Authenticate user with email and password
   */
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response: AxiosResponse<ApiResponse<LoginResponse>> = await authApi.post(
        '/login',
        credentials
      );

      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Login failed');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Login failed');
    }
  }

  /**
   * Verify MFA code
   */
  static async verifyMfa(request: MfaVerificationRequest): Promise<LoginResponse> {
    try {
      const response: AxiosResponse<ApiResponse<LoginResponse>> = await authApi.post(
        '/verify-mfa',
        request
      );

      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'MFA verification failed');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'MFA verification failed');
    }
  }

  /**
   * Logout user and invalidate session
   */
  static async logout(): Promise<void> {
    try {
      await authApi.post('/logout');
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      // Clear local storage
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      localStorage.removeItem('expiresAt');
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(): Promise<{ token: string; expiresAt: Date }> {
    try {
      const refreshToken = localStorage.getItem('refreshToken');
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response: AxiosResponse<ApiResponse<{ token: string; expiresAt: string }>> = 
        await authApi.post('/refresh', { refreshToken });

      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Token refresh failed');
      }

      return {
        token: response.data.data.token,
        expiresAt: new Date(response.data.data.expiresAt),
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Token refresh failed');
    }
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(request: PasswordResetRequest): Promise<PasswordResetResponse> {
    try {
      const response: AxiosResponse<ApiResponse<PasswordResetResponse>> = await authApi.post(
        '/password-reset',
        request
      );

      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Password reset request failed');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Password reset request failed');
    }
  }

  /**
   * Confirm password reset with new password
   */
  static async confirmPasswordReset(request: PasswordResetConfirmRequest): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse<void>> = await authApi.post(
        '/password-reset/confirm',
        request
      );

      if (!response.data.success) {
        throw new Error(response.data.error || 'Password reset confirmation failed');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Password reset confirmation failed');
    }
  }

  /**
   * Get current user profile
   */
  static async getCurrentUser(): Promise<User> {
    try {
      const response: AxiosResponse<ApiResponse<User>> = await authApi.get('/profile');

      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to get user profile');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Failed to get user profile');
    }
  }

  /**
   * Get user security settings
   */
  static async getSecuritySettings(): Promise<SecuritySettings> {
    try {
      const response: AxiosResponse<ApiResponse<SecuritySettings>> = await authApi.get('/security-settings');

      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to get security settings');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Failed to get security settings');
    }
  }

  /**
   * Update security settings
   */
  static async updateSecuritySettings(settings: Partial<SecuritySettings>): Promise<SecuritySettings> {
    try {
      const response: AxiosResponse<ApiResponse<SecuritySettings>> = await authApi.put(
        '/security-settings',
        settings
      );

      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to update security settings');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Failed to update security settings');
    }
  }

  /**
   * Get active sessions
   */
  static async getActiveSessions(): Promise<SessionInfo[]> {
    try {
      const response: AxiosResponse<ApiResponse<SessionInfo[]>> = await authApi.get('/sessions');

      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to get active sessions');
      }

      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Failed to get active sessions');
    }
  }

  /**
   * Terminate a specific session
   */
  static async terminateSession(sessionId: string): Promise<void> {
    try {
      const response: AxiosResponse<ApiResponse<void>> = await authApi.delete(`/sessions/${sessionId}`);

      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to terminate session');
      }
    } catch (error: any) {
      throw new Error(error.response?.data?.error || error.message || 'Failed to terminate session');
    }
  }

  /**
   * Check if token is expired
   */
  static isTokenExpired(): boolean {
    const expiresAtStr = localStorage.getItem('expiresAt');
    if (!expiresAtStr) return true;

    const expiresAt = new Date(expiresAtStr);
    const now = new Date();
    
    // Add 5 minute buffer
    return expiresAt.getTime() - now.getTime() < 5 * 60 * 1000;
  }

  /**
   * Validate password strength
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean;
    score: number;
    feedback: string[];
  } {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) score += 1;
    else feedback.push('Password must be at least 8 characters long');

    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('Password must contain lowercase letters');

    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('Password must contain uppercase letters');

    if (/\d/.test(password)) score += 1;
    else feedback.push('Password must contain numbers');

    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 1;
    else feedback.push('Password must contain special characters');

    if (password.length >= 12) score += 1;

    return {
      isValid: score >= 4,
      score,
      feedback,
    };
  }
}

export default AuthService;
