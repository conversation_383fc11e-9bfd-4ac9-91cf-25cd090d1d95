// OCTAVE Healthcare Platform - WebSocket Service
// Real-time Updates for Healthcare Workflows

import { store } from '@/store';
import { addNotification } from '@/store/slices/uiSlice';
import { logout } from '@/store/slices/authSlice';

interface WebSocketMessage {
  type: string;
  payload: any;
  timestamp: string;
  userId?: string;
  practiceId?: string;
}

interface WebSocketConfig {
  url: string;
  reconnectInterval: number;
  maxReconnectAttempts: number;
  heartbeatInterval: number;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private config: WebSocketConfig;
  private reconnectAttempts = 0;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private isAuthenticated = false;
  private messageQueue: WebSocketMessage[] = [];
  private eventListeners: Map<string, Array<(data: any) => void>> = new Map();

  constructor(config: WebSocketConfig) {
    this.config = config;
  }

  // Connect to WebSocket server
  connect(token: string): void {
    if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
      return;
    }

    this.isConnecting = true;
    
    try {
      const wsUrl = `${this.config.url}?token=${encodeURIComponent(token)}`;
      this.ws = new WebSocket(wsUrl);
      
      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);
      
    } catch (error) {
      console.error('WebSocket connection failed:', error);
      this.isConnecting = false;
      this.scheduleReconnect();
    }
  }

  // Disconnect from WebSocket server
  disconnect(): void {
    this.isAuthenticated = false;
    this.clearTimers();
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect');
      this.ws = null;
    }
    
    this.reconnectAttempts = 0;
  }

  // Send message to server
  send(message: Omit<WebSocketMessage, 'timestamp'>): void {
    const fullMessage: WebSocketMessage = {
      ...message,
      timestamp: new Date().toISOString(),
    };

    if (this.ws && this.ws.readyState === WebSocket.OPEN && this.isAuthenticated) {
      this.ws.send(JSON.stringify(fullMessage));
    } else {
      // Queue message for later sending
      this.messageQueue.push(fullMessage);
    }
  }

  // Subscribe to specific event types
  subscribe(eventType: string, callback: (data: any) => void): () => void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    
    this.eventListeners.get(eventType)!.push(callback);
    
    // Return unsubscribe function
    return () => {
      const listeners = this.eventListeners.get(eventType);
      if (listeners) {
        const index = listeners.indexOf(callback);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }

  // Get connection status
  getConnectionStatus(): 'connecting' | 'connected' | 'disconnected' | 'error' {
    if (!this.ws) return 'disconnected';
    
    switch (this.ws.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return this.isAuthenticated ? 'connected' : 'connecting';
      case WebSocket.CLOSING:
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'error';
    }
  }

  // Handle WebSocket open event
  private handleOpen(): void {
    console.log('WebSocket connected');
    this.isConnecting = false;
    this.reconnectAttempts = 0;
    
    // Start heartbeat
    this.startHeartbeat();
    
    // Authentication will be handled by the server based on the token in the URL
    // Wait for authentication confirmation
  }

  // Handle WebSocket message event
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      switch (message.type) {
        case 'auth_success':
          this.handleAuthSuccess();
          break;
          
        case 'auth_failed':
          this.handleAuthFailed();
          break;
          
        case 'heartbeat_response':
          // Heartbeat acknowledged
          break;
          
        case 'prior_auth_status_update':
          this.handlePriorAuthUpdate(message.payload);
          break;
          
        case 'patient_update':
          this.handlePatientUpdate(message.payload);
          break;
          
        case 'document_uploaded':
          this.handleDocumentUpdate(message.payload);
          break;
          
        case 'communication_received':
          this.handleCommunicationUpdate(message.payload);
          break;
          
        case 'system_notification':
          this.handleSystemNotification(message.payload);
          break;
          
        case 'user_session_expired':
          this.handleSessionExpired();
          break;
          
        default:
          // Emit to custom event listeners
          this.emitEvent(message.type, message.payload);
          break;
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  // Handle WebSocket close event
  private handleClose(event: CloseEvent): void {
    console.log('WebSocket disconnected:', event.code, event.reason);
    this.isAuthenticated = false;
    this.clearTimers();
    
    // Don't reconnect if it was a normal closure or authentication failure
    if (event.code !== 1000 && event.code !== 1008) {
      this.scheduleReconnect();
    }
  }

  // Handle WebSocket error event
  private handleError(event: Event): void {
    console.error('WebSocket error:', event);
    this.isConnecting = false;
  }

  // Handle successful authentication
  private handleAuthSuccess(): void {
    console.log('WebSocket authenticated');
    this.isAuthenticated = true;
    
    // Send queued messages
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message && this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify(message));
      }
    }
    
    // Notify listeners
    this.emitEvent('connected', null);
  }

  // Handle authentication failure
  private handleAuthFailed(): void {
    console.error('WebSocket authentication failed');
    store.dispatch(logout());
    this.disconnect();
  }

  // Handle session expiration
  private handleSessionExpired(): void {
    console.warn('User session expired');
    store.dispatch(addNotification({
      type: 'warning',
      title: 'Session Expired',
      message: 'Your session has expired. Please log in again.',
    }));
    store.dispatch(logout());
    this.disconnect();
  }

  // Handle prior authorization updates
  private handlePriorAuthUpdate(payload: any): void {
    store.dispatch(addNotification({
      type: 'info',
      title: 'Prior Authorization Update',
      message: `Prior authorization ${payload.trackingId} status changed to ${payload.status}`,
    }));
    
    // Invalidate relevant cache entries
    store.dispatch({
      type: 'api/invalidateTags',
      payload: [
        { type: 'PriorAuth', id: payload.id },
        { type: 'PriorAuth', id: 'LIST' },
      ],
    });
    
    this.emitEvent('prior_auth_update', payload);
  }

  // Handle patient updates
  private handlePatientUpdate(payload: any): void {
    // Invalidate patient cache
    store.dispatch({
      type: 'api/invalidateTags',
      payload: [
        { type: 'Patient', id: payload.id },
        { type: 'Patient', id: 'LIST' },
      ],
    });
    
    this.emitEvent('patient_update', payload);
  }

  // Handle document updates
  private handleDocumentUpdate(payload: any): void {
    store.dispatch(addNotification({
      type: 'success',
      title: 'Document Uploaded',
      message: `New document uploaded: ${payload.fileName}`,
    }));
    
    this.emitEvent('document_update', payload);
  }

  // Handle communication updates
  private handleCommunicationUpdate(payload: any): void {
    store.dispatch(addNotification({
      type: 'info',
      title: 'New Communication',
      message: `New ${payload.type.toLowerCase()} received`,
    }));
    
    this.emitEvent('communication_update', payload);
  }

  // Handle system notifications
  private handleSystemNotification(payload: any): void {
    store.dispatch(addNotification({
      type: payload.severity || 'info',
      title: payload.title || 'System Notification',
      message: payload.message,
    }));
    
    this.emitEvent('system_notification', payload);
  }

  // Emit event to custom listeners
  private emitEvent(eventType: string, data: any): void {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`Error in WebSocket event listener for ${eventType}:`, error);
        }
      });
    }
  }

  // Schedule reconnection attempt
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }
    
    const delay = Math.min(
      this.config.reconnectInterval * Math.pow(2, this.reconnectAttempts),
      30000 // Max 30 seconds
    );
    
    this.reconnectTimer = setTimeout(() => {
      this.reconnectAttempts++;
      const state = store.getState();
      const token = state.auth.token;
      
      if (token) {
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`);
        this.connect(token);
      }
    }, delay);
  }

  // Start heartbeat to keep connection alive
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({ type: 'heartbeat', payload: null });
      }
    }, this.config.heartbeatInterval);
  }

  // Clear all timers
  private clearTimers(): void {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }
}

// Create singleton instance
const websocketService = new WebSocketService({
  url: import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8080/ws',
  reconnectInterval: 5000, // 5 seconds
  maxReconnectAttempts: 10,
  heartbeatInterval: 30000, // 30 seconds
});

export default websocketService;
