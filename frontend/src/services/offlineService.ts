// OCTAVE Healthcare Platform - Offline Support Service
// HIPAA Compliant Offline Data Management

import { store } from '@/store';
import { addNotification } from '@/store/slices/uiSlice';

interface OfflineAction {
  id: string;
  type: string;
  payload: any;
  timestamp: Date;
  retryCount: number;
  maxRetries: number;
}

interface OfflineData {
  key: string;
  data: any;
  timestamp: Date;
  expiresAt?: Date;
}

class OfflineService {
  private isOnline = navigator.onLine;
  private actionQueue: OfflineAction[] = [];
  private offlineData: Map<string, OfflineData> = new Map();
  private syncInProgress = false;
  private readonly STORAGE_KEY = 'octave_offline_data';
  private readonly ACTIONS_KEY = 'octave_offline_actions';
  private readonly MAX_STORAGE_SIZE = 50 * 1024 * 1024; // 50MB limit for HIPAA compliance

  constructor() {
    this.initializeEventListeners();
    this.loadOfflineData();
    this.loadActionQueue();
    this.startPeriodicCleanup();
  }

  // Initialize event listeners for online/offline status
  private initializeEventListeners(): void {
    window.addEventListener('online', this.handleOnline.bind(this));
    window.addEventListener('offline', this.handleOffline.bind(this));
    
    // Listen for visibility change to sync when app becomes visible
    document.addEventListener('visibilitychange', () => {
      if (!document.hidden && this.isOnline) {
        this.syncPendingActions();
      }
    });
  }

  // Handle online event
  private handleOnline(): void {
    console.log('Application is online');
    this.isOnline = true;
    
    store.dispatch(addNotification({
      type: 'success',
      title: 'Connection Restored',
      message: 'You are back online. Syncing pending changes...',
    }));
    
    this.syncPendingActions();
  }

  // Handle offline event
  private handleOffline(): void {
    console.log('Application is offline');
    this.isOnline = false;
    
    store.dispatch(addNotification({
      type: 'warning',
      title: 'Connection Lost',
      message: 'You are offline. Changes will be saved locally and synced when connection is restored.',
      autoHide: false,
    }));
  }

  // Check if application is online
  isApplicationOnline(): boolean {
    return this.isOnline;
  }

  // Queue action for offline execution
  queueAction(
    type: string,
    payload: any,
    maxRetries: number = 3
  ): string {
    const action: OfflineAction = {
      id: this.generateId(),
      type,
      payload,
      timestamp: new Date(),
      retryCount: 0,
      maxRetries,
    };
    
    this.actionQueue.push(action);
    this.saveActionQueue();
    
    // Try to execute immediately if online
    if (this.isOnline) {
      this.syncPendingActions();
    }
    
    return action.id;
  }

  // Store data for offline access
  storeOfflineData(
    key: string,
    data: any,
    expiresIn?: number // milliseconds
  ): void {
    const offlineData: OfflineData = {
      key,
      data: this.sanitizeData(data), // Remove PHI if necessary
      timestamp: new Date(),
      ...(expiresIn && { expiresAt: new Date(Date.now() + expiresIn) }),
    };
    
    this.offlineData.set(key, offlineData);
    this.saveOfflineData();
    this.enforceStorageLimit();
  }

  // Retrieve offline data
  getOfflineData(key: string): any | null {
    const data = this.offlineData.get(key);
    
    if (!data) {
      return null;
    }
    
    // Check if data has expired
    if (data.expiresAt && data.expiresAt < new Date()) {
      this.offlineData.delete(key);
      this.saveOfflineData();
      return null;
    }
    
    return data.data;
  }

  // Remove offline data
  removeOfflineData(key: string): void {
    this.offlineData.delete(key);
    this.saveOfflineData();
  }

  // Clear all offline data
  clearOfflineData(): void {
    this.offlineData.clear();
    this.actionQueue = [];
    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.ACTIONS_KEY);
  }

  // Get pending actions count
  getPendingActionsCount(): number {
    return this.actionQueue.length;
  }

  // Sync pending actions with server
  private async syncPendingActions(): Promise<void> {
    if (this.syncInProgress || !this.isOnline || this.actionQueue.length === 0) {
      return;
    }
    
    this.syncInProgress = true;
    
    try {
      const actionsToSync = [...this.actionQueue];
      
      for (const action of actionsToSync) {
        try {
          await this.executeAction(action);
          
          // Remove successful action from queue
          const index = this.actionQueue.findIndex(a => a.id === action.id);
          if (index > -1) {
            this.actionQueue.splice(index, 1);
          }
          
        } catch (error) {
          console.error(`Failed to sync action ${action.id}:`, error);
          
          // Increment retry count
          action.retryCount++;
          
          // Remove action if max retries exceeded
          if (action.retryCount >= action.maxRetries) {
            const index = this.actionQueue.findIndex(a => a.id === action.id);
            if (index > -1) {
              this.actionQueue.splice(index, 1);
            }
            
            store.dispatch(addNotification({
              type: 'error',
              title: 'Sync Failed',
              message: `Failed to sync action after ${action.maxRetries} attempts`,
            }));
          }
        }
      }
      
      this.saveActionQueue();
      
      if (this.actionQueue.length === 0) {
        store.dispatch(addNotification({
          type: 'success',
          title: 'Sync Complete',
          message: 'All pending changes have been synchronized',
        }));
      }
      
    } finally {
      this.syncInProgress = false;
    }
  }

  // Execute a specific action
  private async executeAction(action: OfflineAction): Promise<void> {
    const { type, payload: _payload } = action;
    
    // Map action types to API calls
    switch (type) {
      case 'CREATE_PATIENT':
        // await store.dispatch(patientsApi.endpoints.createPatient.initiate(payload));
        break;
        
      case 'UPDATE_PATIENT':
        // await store.dispatch(patientsApi.endpoints.updatePatient.initiate(payload));
        break;
        
      case 'CREATE_PRIOR_AUTH':
        // await store.dispatch(priorAuthApi.endpoints.createPriorAuthorization.initiate(payload));
        break;
        
      case 'UPDATE_PRIOR_AUTH':
        // await store.dispatch(priorAuthApi.endpoints.updatePriorAuthorization.initiate(payload));
        break;
        
      case 'ADD_COMMUNICATION':
        // await store.dispatch(priorAuthApi.endpoints.addPriorAuthCommunication.initiate(payload));
        break;
        
      default:
        console.warn(`Unknown action type: ${type}`);
        break;
    }
  }

  // Sanitize data to remove PHI for offline storage
  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }
    
    // Create a deep copy
    const sanitized = JSON.parse(JSON.stringify(data));
    
    // Remove sensitive fields that shouldn't be stored offline
    const sensitiveFields = [
      'ssn',
      'socialSecurityNumber',
      'creditCardNumber',
      'bankAccount',
      'password',
      'token',
      'refreshToken',
    ];
    
    const removeSensitiveFields = (obj: any): void => {
      if (Array.isArray(obj)) {
        obj.forEach(removeSensitiveFields);
      } else if (obj && typeof obj === 'object') {
        sensitiveFields.forEach(field => {
          if (field in obj) {
            delete obj[field];
          }
        });
        
        Object.values(obj).forEach(removeSensitiveFields);
      }
    };
    
    removeSensitiveFields(sanitized);
    return sanitized;
  }

  // Load offline data from localStorage
  private loadOfflineData(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        this.offlineData = new Map(
          data.map((item: any) => [
            item.key,
            {
              ...item,
              timestamp: new Date(item.timestamp),
              expiresAt: item.expiresAt ? new Date(item.expiresAt) : undefined,
            },
          ])
        );
      }
    } catch (error) {
      console.error('Failed to load offline data:', error);
      this.offlineData.clear();
    }
  }

  // Save offline data to localStorage
  private saveOfflineData(): void {
    try {
      const data = Array.from(this.offlineData.values());
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      console.error('Failed to save offline data:', error);
      // If storage is full, clear old data and try again
      this.cleanupExpiredData();
      try {
        const data = Array.from(this.offlineData.values());
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
      } catch (retryError) {
        console.error('Failed to save offline data after cleanup:', retryError);
      }
    }
  }

  // Load action queue from localStorage
  private loadActionQueue(): void {
    try {
      const stored = localStorage.getItem(this.ACTIONS_KEY);
      if (stored) {
        this.actionQueue = JSON.parse(stored).map((action: any) => ({
          ...action,
          timestamp: new Date(action.timestamp),
        }));
      }
    } catch (error) {
      console.error('Failed to load action queue:', error);
      this.actionQueue = [];
    }
  }

  // Save action queue to localStorage
  private saveActionQueue(): void {
    try {
      localStorage.setItem(this.ACTIONS_KEY, JSON.stringify(this.actionQueue));
    } catch (error) {
      console.error('Failed to save action queue:', error);
    }
  }

  // Enforce storage size limit
  private enforceStorageLimit(): void {
    const currentSize = this.getStorageSize();
    
    if (currentSize > this.MAX_STORAGE_SIZE) {
      // Remove oldest data until under limit
      const sortedData = Array.from(this.offlineData.entries())
        .sort(([, a], [, b]) => a.timestamp.getTime() - b.timestamp.getTime());
      
      while (this.getStorageSize() > this.MAX_STORAGE_SIZE && sortedData.length > 0) {
        const [key] = sortedData.shift()!;
        this.offlineData.delete(key);
      }
      
      this.saveOfflineData();
    }
  }

  // Get current storage size
  private getStorageSize(): number {
    const data = localStorage.getItem(this.STORAGE_KEY);
    const actions = localStorage.getItem(this.ACTIONS_KEY);
    
    return (data?.length || 0) + (actions?.length || 0);
  }

  // Clean up expired data
  private cleanupExpiredData(): void {
    const now = new Date();
    const keysToRemove: string[] = [];
    
    this.offlineData.forEach((data, key) => {
      if (data.expiresAt && data.expiresAt < now) {
        keysToRemove.push(key);
      }
    });
    
    keysToRemove.forEach(key => this.offlineData.delete(key));
    
    if (keysToRemove.length > 0) {
      this.saveOfflineData();
    }
  }

  // Start periodic cleanup
  private startPeriodicCleanup(): void {
    setInterval(() => {
      this.cleanupExpiredData();
    }, 60000); // Clean up every minute
  }

  // Generate unique ID
  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Create singleton instance
const offlineService = new OfflineService();

export default offlineService;
