// OCTAVE Healthcare Platform - Application Entry Point
// HIPAA Compliant Healthcare Prior Authorization Tracking System

import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App';

// Initialize the React application
const container = document.getElementById('root');
if (!container) {
  throw new Error('Root container not found');
}

const root = createRoot(container);

root.render(
  <StrictMode>
    <App />
  </StrictMode>
);
