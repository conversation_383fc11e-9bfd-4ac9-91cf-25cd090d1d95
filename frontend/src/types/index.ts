// OCTAVE Healthcare Platform - Core Type Definitions
// HIPAA Compliant Type System

export interface User {
  readonly id: string;
  readonly email: string;
  readonly firstName: string;
  readonly lastName: string;
  readonly role: UserRole;
  readonly practiceId: string;
  readonly isActive: boolean;
  readonly lastLogin?: Date;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

export enum UserRole {
  SYSTEM_ADMIN = 'SystemAdmin',
  PRACTICE_ADMIN = 'PracticeAdmin',
  PROVIDER = 'Provider',
  STAFF = 'Staff',
  BILLING = 'Billing',
  PATIENT = 'Patient',
  READ_ONLY = 'ReadOnly',
}

export interface Patient {
  readonly id: string;
  readonly mrn: string; // Medical Record Number
  readonly firstName: string;
  readonly lastName: string;
  readonly dateOfBirth: Date;
  readonly gender: Gender;
  readonly phone?: string;
  readonly email?: string;
  readonly address?: Address;
  readonly insurance: InsuranceInfo[];
  readonly practiceId: string;
  readonly isActive: boolean;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

export enum Gender {
  MALE = 'Male',
  FEMALE = 'Female',
  OTHER = 'Other',
  PREFER_NOT_TO_SAY = 'PreferNotToSay',
}

export interface Address {
  readonly street: string;
  readonly city: string;
  readonly state: string;
  readonly zipCode: string;
  readonly country: string;
}

export interface InsuranceInfo {
  readonly id: string;
  readonly isPrimary: boolean;
  readonly insuranceCompany: string;
  readonly policyNumber: string;
  readonly groupNumber?: string;
  readonly subscriberId: string;
  readonly subscriberName: string;
  readonly effectiveDate: Date;
  readonly expirationDate?: Date;
}

export interface PriorAuthorization {
  readonly id: string;
  readonly trackingId: string;
  readonly patientId: string;
  readonly providerId: string;
  readonly practiceId: string;
  readonly status: PriorAuthStatus;
  readonly priority: Priority;
  readonly procedureCode: string;
  readonly procedureDescription: string;
  readonly diagnosisCodes: string[];
  readonly clinicalJustification: string;
  readonly insuranceId: string;
  readonly requestedDate: Date;
  readonly submittedDate?: Date;
  readonly approvedDate?: Date;
  readonly deniedDate?: Date;
  readonly expirationDate?: Date;
  readonly authorizationNumber?: string;
  readonly denialReason?: string;
  readonly documents: Document[];
  readonly communications: Communication[];
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

export enum PriorAuthStatus {
  DRAFT = 'Draft',
  SUBMITTED = 'Submitted',
  UNDER_REVIEW = 'UnderReview',
  MORE_INFO_REQUIRED = 'MoreInfoRequired',
  APPROVED = 'Approved',
  DENIED = 'Denied',
  EXPIRED = 'Expired',
  CANCELLED = 'Cancelled',
  COMPLETED = 'Completed',
  APPEAL = 'Appeal',
}

export enum Priority {
  ROUTINE = 'Routine',
  URGENT = 'Urgent',
  STAT = 'STAT',
}

export interface Document {
  readonly id: string;
  readonly fileName: string;
  readonly fileType: string;
  readonly fileSize: number;
  readonly uploadedBy: string;
  readonly uploadedAt: Date;
  readonly isEncrypted: boolean;
  readonly documentType: DocumentType;
}

export enum DocumentType {
  MEDICAL_RECORD = 'MedicalRecord',
  PHYSICIAN_NOTES = 'PhysicianNotes',
  LAB_RESULTS = 'LabResults',
  IMAGING = 'Imaging',
  INSURANCE_CARD = 'InsuranceCard',
  AUTHORIZATION_LETTER = 'AuthorizationLetter',
  APPEAL_LETTER = 'AppealLetter',
  OTHER = 'Other',
}

export interface Communication {
  readonly id: string;
  readonly priorAuthId: string;
  readonly type: CommunicationType;
  readonly direction: CommunicationDirection;
  readonly subject: string;
  readonly content: string;
  readonly contactName?: string;
  readonly contactPhone?: string;
  readonly contactEmail?: string;
  readonly createdBy: string;
  readonly createdAt: Date;
}

export enum CommunicationType {
  PHONE_CALL = 'PhoneCall',
  EMAIL = 'Email',
  FAX = 'Fax',
  PORTAL_MESSAGE = 'PortalMessage',
  INTERNAL_NOTE = 'InternalNote',
}

export enum CommunicationDirection {
  INBOUND = 'Inbound',
  OUTBOUND = 'Outbound',
}

export interface Practice {
  readonly id: string;
  readonly name: string;
  readonly npi: string; // National Provider Identifier
  readonly taxId: string;
  readonly address: Address;
  readonly phone: string;
  readonly email: string;
  readonly website?: string;
  readonly specialties: string[];
  readonly isActive: boolean;
  readonly createdAt: Date;
  readonly updatedAt: Date;
}

// API Response Types
export interface ApiResponse<T> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: string;
  readonly message?: string;
}

export interface PaginatedResponse<T> {
  readonly items: T[];
  readonly totalCount: number;
  readonly pageSize: number;
  readonly currentPage: number;
  readonly totalPages: number;
}

// Authentication Types
export interface AuthState {
  readonly isAuthenticated: boolean;
  readonly user: User | null;
  readonly token: string | null;
  readonly refreshToken: string | null;
  readonly expiresAt: Date | null;
}

export interface LoginRequest {
  readonly email: string;
  readonly password: string;
  readonly rememberMe?: boolean;
}

export interface LoginResponse {
  readonly user: User;
  readonly token: string;
  readonly refreshToken: string;
  readonly expiresAt: Date;
}

// Form Types
export interface FormState<T> {
  readonly values: T;
  readonly errors: Partial<Record<keyof T, string>>;
  readonly touched: Partial<Record<keyof T, boolean>>;
  readonly isSubmitting: boolean;
  readonly isValid: boolean;
}

// Filter and Search Types
export interface SearchFilters {
  readonly query?: string;
  readonly status?: PriorAuthStatus[];
  readonly priority?: Priority[];
  readonly dateRange?: {
    readonly start: Date;
    readonly end: Date;
  };
  readonly providerId?: string;
  readonly insuranceCompany?: string;
}

export interface SortOptions {
  readonly field: string;
  readonly direction: 'asc' | 'desc';
}

// Theme Types
export interface ThemeMode {
  readonly mode: 'light' | 'dark';
}

// Error Types
export interface AppError {
  readonly code: string;
  readonly message: string;
  readonly details?: unknown;
  readonly timestamp: Date;
}

// Audit Types (HIPAA Compliance)
export interface AuditLog {
  readonly id: string;
  readonly userId: string;
  readonly action: string;
  readonly resource: string;
  readonly resourceId: string;
  readonly ipAddress: string;
  readonly userAgent: string;
  readonly timestamp: Date;
  readonly success: boolean;
  readonly details?: Record<string, unknown>;
}
