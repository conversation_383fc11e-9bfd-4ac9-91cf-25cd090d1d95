// OCTAVE Healthcare Platform - Patients API Endpoints
// HIPAA Compliant Patient Management with Audit Logging

import { apiSlice } from './apiSlice';
import { Patient, PaginatedResponse, SearchFilters, SortOptions } from '@/types';

export const patientsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get paginated list of patients
    getPatients: builder.query<
      PaginatedResponse<Patient>,
      {
        page?: number;
        pageSize?: number;
        search?: string;
        filters?: SearchFilters;
        sort?: SortOptions;
      }
    >({
      query: ({ page = 1, pageSize = 25, search, filters, sort }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          ...(search && { search }),
          ...(filters && { filters: JSON.stringify(filters) }),
          ...(sort && { sort: JSON.stringify(sort) }),
        });
        
        return `/patients?${params.toString()}`;
      },
      providesTags: (result) =>
        result
          ? [
              ...result.items.map(({ id }) => ({ type: 'Patient' as const, id })),
              { type: 'Patient', id: 'LIST' },
            ]
          : [{ type: 'Patient', id: 'LIST' }],
    }),

    // Get single patient by ID
    getPatient: builder.query<Patient, string>({
      query: (id) => `/patients/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'Patient', id }],
    }),

    // Create new patient
    createPatient: builder.mutation<Patient, Omit<Patient, 'id' | 'createdAt' | 'updatedAt'>>({
      query: (newPatient) => ({
        url: '/patients',
        method: 'POST',
        body: newPatient,
      }),
      invalidatesTags: [{ type: 'Patient', id: 'LIST' }],
    }),

    // Update existing patient
    updatePatient: builder.mutation<Patient, { id: string; updates: Partial<Patient> }>({
      query: ({ id, updates }) => ({
        url: `/patients/${id}`,
        method: 'PATCH',
        body: updates,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: 'Patient', id },
        { type: 'Patient', id: 'LIST' },
      ],
    }),

    // Deactivate patient (soft delete for HIPAA compliance)
    deactivatePatient: builder.mutation<void, string>({
      query: (id) => ({
        url: `/patients/${id}/deactivate`,
        method: 'PATCH',
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: 'Patient', id },
        { type: 'Patient', id: 'LIST' },
      ],
    }),

    // Reactivate patient
    reactivatePatient: builder.mutation<void, string>({
      query: (id) => ({
        url: `/patients/${id}/reactivate`,
        method: 'PATCH',
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: 'Patient', id },
        { type: 'Patient', id: 'LIST' },
      ],
    }),

    // Search patients by various criteria
    searchPatients: builder.query<Patient[], {
      query: string;
      searchType: 'name' | 'mrn' | 'phone' | 'email';
      limit?: number;
    }>({
      query: ({ query, searchType, limit = 10 }) => ({
        url: '/patients/search',
        params: { query, searchType, limit },
      }),
      providesTags: [{ type: 'Patient', id: 'SEARCH' }],
    }),

    // Get patient's prior authorizations
    getPatientPriorAuths: builder.query<PaginatedResponse<any>, {
      patientId: string;
      page?: number;
      pageSize?: number;
      status?: string[];
    }>({
      query: ({ patientId, page = 1, pageSize = 25, status }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          ...(status && { status: status.join(',') }),
        });
        
        return `/patients/${patientId}/prior-authorizations?${params.toString()}`;
      },
      providesTags: (_result, _error, { patientId }) => [
        { type: 'PriorAuth', id: `PATIENT_${patientId}` },
      ],
    }),

    // Get patient's documents
    getPatientDocuments: builder.query<PaginatedResponse<any>, {
      patientId: string;
      page?: number;
      pageSize?: number;
      documentType?: string;
    }>({
      query: ({ patientId, page = 1, pageSize = 25, documentType }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          ...(documentType && { documentType }),
        });
        
        return `/patients/${patientId}/documents?${params.toString()}`;
      },
      providesTags: (_result, _error, { patientId }) => [
        { type: 'Document', id: `PATIENT_${patientId}` },
      ],
    }),

    // Get patient's communications
    getPatientCommunications: builder.query<PaginatedResponse<any>, {
      patientId: string;
      page?: number;
      pageSize?: number;
      type?: string;
    }>({
      query: ({ patientId, page = 1, pageSize = 25, type }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          ...(type && { type }),
        });
        
        return `/patients/${patientId}/communications?${params.toString()}`;
      },
      providesTags: (_result, _error, { patientId }) => [
        { type: 'Communication', id: `PATIENT_${patientId}` },
      ],
    }),

    // Verify patient insurance
    verifyPatientInsurance: builder.mutation<{
      isValid: boolean;
      details: any;
      verifiedAt: Date;
    }, {
      patientId: string;
      insuranceId: string;
    }>({
      query: ({ patientId, insuranceId }) => ({
        url: `/patients/${patientId}/insurance/${insuranceId}/verify`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, { patientId }) => [
        { type: 'Patient', id: patientId },
      ],
    }),

    // Get patient eligibility
    getPatientEligibility: builder.query<{
      isEligible: boolean;
      benefits: any[];
      effectiveDate: Date;
      expirationDate: Date;
    }, {
      patientId: string;
      insuranceId: string;
      serviceDate?: Date;
    }>({
      query: ({ patientId, insuranceId, serviceDate }) => {
        const params = new URLSearchParams({
          ...(serviceDate && { serviceDate: serviceDate.toISOString() }),
        });
        
        return `/patients/${patientId}/insurance/${insuranceId}/eligibility?${params.toString()}`;
      },
      providesTags: (_result, _error, { patientId, insuranceId }) => [
        { type: 'Patient', id: `${patientId}_ELIGIBILITY_${insuranceId}` },
      ],
    }),

    // Export patient data (for patient requests)
    exportPatientData: builder.mutation<{
      downloadUrl: string;
      expiresAt: Date;
    }, string>({
      query: (patientId) => ({
        url: `/patients/${patientId}/export`,
        method: 'POST',
      }),
    }),

    // Get patient audit log
    getPatientAuditLog: builder.query<PaginatedResponse<any>, {
      patientId: string;
      page?: number;
      pageSize?: number;
      startDate?: Date;
      endDate?: Date;
    }>({
      query: ({ patientId, page = 1, pageSize = 25, startDate, endDate }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          ...(startDate && { startDate: startDate.toISOString() }),
          ...(endDate && { endDate: endDate.toISOString() }),
        });
        
        return `/patients/${patientId}/audit-log?${params.toString()}`;
      },
      providesTags: (_result, _error, { patientId }) => [
        { type: 'Patient', id: `${patientId}_AUDIT` },
      ],
    }),
  }),
});

export const {
  useGetPatientsQuery,
  useGetPatientQuery,
  useCreatePatientMutation,
  useUpdatePatientMutation,
  useDeactivatePatientMutation,
  useReactivatePatientMutation,
  useSearchPatientsQuery,
  useGetPatientPriorAuthsQuery,
  useGetPatientDocumentsQuery,
  useGetPatientCommunicationsQuery,
  useVerifyPatientInsuranceMutation,
  useGetPatientEligibilityQuery,
  useExportPatientDataMutation,
  useGetPatientAuditLogQuery,
} = patientsApi;
