// OCTAVE Healthcare Platform - Prior Authorization API Endpoints
// HIPAA Compliant Prior Authorization Management

import { apiSlice } from './apiSlice';
import { PriorAuthorization, PaginatedResponse, SearchFilters, SortOptions, Communication, Document } from '@/types';

export const priorAuthApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get paginated list of prior authorizations
    getPriorAuthorizations: builder.query<
      PaginatedResponse<PriorAuthorization>,
      {
        page?: number;
        pageSize?: number;
        search?: string;
        filters?: SearchFilters;
        sort?: SortOptions;
      }
    >({
      query: ({ page = 1, pageSize = 25, search, filters, sort }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          ...(search && { search }),
          ...(filters && { filters: JSON.stringify(filters) }),
          ...(sort && { sort: JSON.stringify(sort) }),
        });
        
        return `/prior-authorizations?${params.toString()}`;
      },
      providesTags: (result) =>
        result
          ? [
              ...result.items.map(({ id }) => ({ type: 'PriorAuth' as const, id })),
              { type: 'PriorAuth', id: 'LIST' },
            ]
          : [{ type: 'PriorAuth', id: 'LIST' }],
    }),

    // Get single prior authorization by ID
    getPriorAuthorization: builder.query<PriorAuthorization, string>({
      query: (id) => `/prior-authorizations/${id}`,
      providesTags: (_result, _error, id) => [{ type: 'PriorAuth', id }],
    }),

    // Create new prior authorization
    createPriorAuthorization: builder.mutation<
      PriorAuthorization,
      Omit<PriorAuthorization, 'id' | 'trackingId' | 'createdAt' | 'updatedAt'>
    >({
      query: (newPriorAuth) => ({
        url: '/prior-authorizations',
        method: 'POST',
        body: newPriorAuth,
      }),
      invalidatesTags: [
        { type: 'PriorAuth', id: 'LIST' },
        { type: 'Patient', id: 'LIST' },
      ],
    }),

    // Update prior authorization
    updatePriorAuthorization: builder.mutation<
      PriorAuthorization,
      { id: string; updates: Partial<PriorAuthorization> }
    >({
      query: ({ id, updates }) => ({
        url: `/prior-authorizations/${id}`,
        method: 'PATCH',
        body: updates,
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: 'PriorAuth', id },
        { type: 'PriorAuth', id: 'LIST' },
      ],
    }),

    // Submit prior authorization for review
    submitPriorAuthorization: builder.mutation<PriorAuthorization, string>({
      query: (id) => ({
        url: `/prior-authorizations/${id}/submit`,
        method: 'POST',
      }),
      invalidatesTags: (_result, _error, id) => [
        { type: 'PriorAuth', id },
        { type: 'PriorAuth', id: 'LIST' },
      ],
    }),

    // Cancel prior authorization
    cancelPriorAuthorization: builder.mutation<PriorAuthorization, {
      id: string;
      reason: string;
    }>({
      query: ({ id, reason }) => ({
        url: `/prior-authorizations/${id}/cancel`,
        method: 'POST',
        body: { reason },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: 'PriorAuth', id },
        { type: 'PriorAuth', id: 'LIST' },
      ],
    }),

    // Appeal denied prior authorization
    appealPriorAuthorization: builder.mutation<PriorAuthorization, {
      id: string;
      appealReason: string;
      additionalDocuments?: string[];
    }>({
      query: ({ id, appealReason, additionalDocuments }) => ({
        url: `/prior-authorizations/${id}/appeal`,
        method: 'POST',
        body: { appealReason, additionalDocuments },
      }),
      invalidatesTags: (_result, _error, { id }) => [
        { type: 'PriorAuth', id },
        { type: 'PriorAuth', id: 'LIST' },
      ],
    }),

    // Get prior authorization status history
    getPriorAuthStatusHistory: builder.query<Array<{
      status: string;
      changedAt: Date;
      changedBy: string;
      reason?: string;
      notes?: string;
    }>, string>({
      query: (id) => `/prior-authorizations/${id}/status-history`,
      providesTags: (_result, _error, id) => [
        { type: 'PriorAuth', id: `${id}_HISTORY` },
      ],
    }),

    // Add communication to prior authorization
    addPriorAuthCommunication: builder.mutation<Communication, {
      priorAuthId: string;
      communication: Omit<Communication, 'id' | 'priorAuthId' | 'createdAt'>;
    }>({
      query: ({ priorAuthId, communication }) => ({
        url: `/prior-authorizations/${priorAuthId}/communications`,
        method: 'POST',
        body: communication,
      }),
      invalidatesTags: (_result, _error, { priorAuthId }) => [
        { type: 'PriorAuth', id: priorAuthId },
        { type: 'Communication', id: `PRIOR_AUTH_${priorAuthId}` },
      ],
    }),

    // Get prior authorization communications
    getPriorAuthCommunications: builder.query<PaginatedResponse<Communication>, {
      priorAuthId: string;
      page?: number;
      pageSize?: number;
      type?: string;
    }>({
      query: ({ priorAuthId, page = 1, pageSize = 25, type }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          ...(type && { type }),
        });
        
        return `/prior-authorizations/${priorAuthId}/communications?${params.toString()}`;
      },
      providesTags: (_result, _error, { priorAuthId }) => [
        { type: 'Communication', id: `PRIOR_AUTH_${priorAuthId}` },
      ],
    }),

    // Upload document to prior authorization
    uploadPriorAuthDocument: builder.mutation<Document, {
      priorAuthId: string;
      file: File;
      documentType: string;
      description?: string;
    }>({
      query: ({ priorAuthId, file, documentType, description }) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('documentType', documentType);
        if (description) {
          formData.append('description', description);
        }
        
        return {
          url: `/prior-authorizations/${priorAuthId}/documents`,
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: (_result, _error, { priorAuthId }) => [
        { type: 'PriorAuth', id: priorAuthId },
        { type: 'Document', id: `PRIOR_AUTH_${priorAuthId}` },
      ],
    }),

    // Get prior authorization documents
    getPriorAuthDocuments: builder.query<PaginatedResponse<Document>, {
      priorAuthId: string;
      page?: number;
      pageSize?: number;
      documentType?: string;
    }>({
      query: ({ priorAuthId, page = 1, pageSize = 25, documentType }) => {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          ...(documentType && { documentType }),
        });
        
        return `/prior-authorizations/${priorAuthId}/documents?${params.toString()}`;
      },
      providesTags: (_result, _error, { priorAuthId }) => [
        { type: 'Document', id: `PRIOR_AUTH_${priorAuthId}` },
      ],
    }),

    // Check prior authorization requirements
    checkPriorAuthRequirements: builder.query<{
      isRequired: boolean;
      requirements: string[];
      estimatedProcessingTime: number;
      additionalInfo?: string;
    }, {
      procedureCode: string;
      diagnosisCodes: string[];
      insuranceId: string;
      patientId: string;
    }>({
      query: ({ procedureCode, diagnosisCodes, insuranceId, patientId }) => {
        const params = new URLSearchParams({
          procedureCode,
          diagnosisCodes: diagnosisCodes.join(','),
          insuranceId,
          patientId,
        });
        
        return `/prior-authorizations/check-requirements?${params.toString()}`;
      },
    }),

    // Get prior authorization analytics
    getPriorAuthAnalytics: builder.query<{
      totalCount: number;
      statusBreakdown: Record<string, number>;
      averageProcessingTime: number;
      approvalRate: number;
      denialReasons: Array<{ reason: string; count: number }>;
      monthlyTrends: Array<{ month: string; count: number; approvalRate: number }>;
    }, {
      startDate?: Date;
      endDate?: Date;
      providerId?: string;
      insuranceCompany?: string;
    }>({
      query: ({ startDate, endDate, providerId, insuranceCompany }) => {
        const params = new URLSearchParams({
          ...(startDate && { startDate: startDate.toISOString() }),
          ...(endDate && { endDate: endDate.toISOString() }),
          ...(providerId && { providerId }),
          ...(insuranceCompany && { insuranceCompany }),
        });
        
        return `/prior-authorizations/analytics?${params.toString()}`;
      },
      providesTags: [{ type: 'PriorAuth', id: 'ANALYTICS' }],
    }),

    // Bulk update prior authorizations
    bulkUpdatePriorAuthorizations: builder.mutation<{
      updated: number;
      failed: number;
      errors: Array<{ id: string; error: string }>;
    }, {
      ids: string[];
      updates: Partial<PriorAuthorization>;
    }>({
      query: ({ ids, updates }) => ({
        url: '/prior-authorizations/bulk-update',
        method: 'PATCH',
        body: { ids, updates },
      }),
      invalidatesTags: [{ type: 'PriorAuth', id: 'LIST' }],
    }),

    // Export prior authorizations
    exportPriorAuthorizations: builder.mutation<{
      downloadUrl: string;
      expiresAt: Date;
    }, {
      filters?: SearchFilters;
      format: 'csv' | 'excel' | 'pdf';
      includeDocuments?: boolean;
    }>({
      query: ({ filters, format, includeDocuments }) => ({
        url: '/prior-authorizations/export',
        method: 'POST',
        body: { filters, format, includeDocuments },
      }),
    }),
  }),
});

export const {
  useGetPriorAuthorizationsQuery,
  useGetPriorAuthorizationQuery,
  useCreatePriorAuthorizationMutation,
  useUpdatePriorAuthorizationMutation,
  useSubmitPriorAuthorizationMutation,
  useCancelPriorAuthorizationMutation,
  useAppealPriorAuthorizationMutation,
  useGetPriorAuthStatusHistoryQuery,
  useAddPriorAuthCommunicationMutation,
  useGetPriorAuthCommunicationsQuery,
  useUploadPriorAuthDocumentMutation,
  useGetPriorAuthDocumentsQuery,
  useCheckPriorAuthRequirementsQuery,
  useGetPriorAuthAnalyticsQuery,
  useBulkUpdatePriorAuthorizationsMutation,
  useExportPriorAuthorizationsMutation,
} = priorAuthApi;
