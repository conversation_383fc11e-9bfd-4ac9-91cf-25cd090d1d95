// OCTAVE Healthcare Platform - Authentication State Management
// HIPAA Compliant Authentication with Audit Logging

import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AuthState, User, LoginResponse } from '@/types';

const initialState: AuthState = {
  isAuthenticated: false,
  user: null,
  token: null,
  refreshToken: null,
  expiresAt: null,
};

export const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    loginSuccess: (state, action: PayloadAction<LoginResponse>) => {
      const { user, token, refreshToken, expiresAt } = action.payload;
      state.isAuthenticated = true;
      state.user = user;
      state.token = token;
      state.refreshToken = refreshToken;
      state.expiresAt = expiresAt;
      
      // Store in localStorage for persistence
      localStorage.setItem('authToken', token);
      localStorage.setItem('refreshToken', refreshToken);
      localStorage.setItem('user', JSON.stringify(user));
      localStorage.setItem('expiresAt', expiresAt.toString());
    },
    
    logout: (state) => {
      state.isAuthenticated = false;
      state.user = null;
      state.token = null;
      state.refreshToken = null;
      state.expiresAt = null;
      
      // Clear localStorage
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      localStorage.removeItem('expiresAt');
    },
    
    refreshTokenSuccess: (state, action: PayloadAction<{ token: string; expiresAt: Date }>) => {
      const { token, expiresAt } = action.payload;
      state.token = token;
      state.expiresAt = expiresAt;
      
      // Update localStorage
      localStorage.setItem('authToken', token);
      localStorage.setItem('expiresAt', expiresAt.toString());
    },
    
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
        localStorage.setItem('user', JSON.stringify(state.user));
      }
    },
    
    restoreSession: (state) => {
      const token = localStorage.getItem('authToken');
      const refreshToken = localStorage.getItem('refreshToken');
      const userStr = localStorage.getItem('user');
      const expiresAtStr = localStorage.getItem('expiresAt');
      
      if (token && refreshToken && userStr && expiresAtStr) {
        const expiresAt = new Date(expiresAtStr);
        const now = new Date();
        
        // Check if token is still valid
        if (expiresAt > now) {
          state.isAuthenticated = true;
          state.token = token;
          state.refreshToken = refreshToken;
          state.user = JSON.parse(userStr);
          state.expiresAt = expiresAt;
        } else {
          // Token expired, clear storage
          localStorage.removeItem('authToken');
          localStorage.removeItem('refreshToken');
          localStorage.removeItem('user');
          localStorage.removeItem('expiresAt');
        }
      }
    },
    
    sessionTimeout: (state) => {
      // Handle session timeout - keep user info but require re-authentication
      state.isAuthenticated = false;
      state.token = null;
      state.refreshToken = null;
      state.expiresAt = null;
      
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('expiresAt');
      // Keep user info for re-authentication
    },
  },
});

export const {
  loginSuccess,
  logout,
  refreshTokenSuccess,
  updateUser,
  restoreSession,
  sessionTimeout,
} = authSlice.actions;

// Selectors
export const selectAuth = (state: { auth: AuthState }): AuthState => state.auth;
export const selectUser = (state: { auth: AuthState }): User | null => state.auth.user;
export const selectIsAuthenticated = (state: { auth: AuthState }): boolean => state.auth.isAuthenticated;
export const selectUserRole = (state: { auth: AuthState }): string | null => state.auth.user?.role || null;

export default authSlice.reducer;
