// OCTAVE Healthcare Platform - UI State Management
// Theme, Navigation, and User Interface State

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface UIState {
  readonly theme: 'light' | 'dark';
  readonly sidebarOpen: boolean;
  readonly loading: boolean;
  readonly notifications: Notification[];
  readonly currentPage: string;
  readonly breadcrumbs: Breadcrumb[];
  readonly searchQuery: string;
  readonly filters: Record<string, unknown>;
}

interface Notification {
  readonly id: string;
  readonly type: 'success' | 'error' | 'warning' | 'info';
  readonly title: string;
  readonly message: string;
  readonly timestamp: Date;
  readonly read: boolean;
  readonly autoHide?: boolean;
}

interface Breadcrumb {
  readonly label: string;
  readonly path: string;
  readonly isActive: boolean;
}

const initialState: UIState = {
  theme: (localStorage.getItem('theme') as 'light' | 'dark') || 'light',
  sidebarOpen: true,
  loading: false,
  notifications: [],
  currentPage: '',
  breadcrumbs: [],
  searchQuery: '',
  filters: {},
};

export const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {
      state.theme = action.payload;
      localStorage.setItem('theme', action.payload);
    },
    
    toggleTheme: (state) => {
      const newTheme = state.theme === 'light' ? 'dark' : 'light';
      state.theme = newTheme;
      localStorage.setItem('theme', newTheme);
    },
    
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    
    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp' | 'read'>>) => {
      const notification: Notification = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: new Date(),
        read: false,
      };
      state.notifications.unshift(notification);
      
      // Limit to 50 notifications
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50);
      }
    },
    
    markNotificationRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        (notification as { read: boolean }).read = true;
      }
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    setCurrentPage: (state, action: PayloadAction<string>) => {
      state.currentPage = action.payload;
    },
    
    setBreadcrumbs: (state, action: PayloadAction<Breadcrumb[]>) => {
      state.breadcrumbs = action.payload;
    },
    
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    
    setFilters: (state, action: PayloadAction<Record<string, unknown>>) => {
      state.filters = action.payload;
    },
    
    updateFilter: (state, action: PayloadAction<{ key: string; value: unknown }>) => {
      const { key, value } = action.payload;
      state.filters = { ...state.filters, [key]: value };
    },
    
    clearFilters: (state) => {
      state.filters = {};
      state.searchQuery = '';
    },
  },
});

export const {
  setTheme,
  toggleTheme,
  setSidebarOpen,
  toggleSidebar,
  setLoading,
  addNotification,
  markNotificationRead,
  removeNotification,
  clearNotifications,
  setCurrentPage,
  setBreadcrumbs,
  setSearchQuery,
  setFilters,
  updateFilter,
  clearFilters,
} = uiSlice.actions;

// Selectors
export const selectTheme = (state: { ui: UIState }): 'light' | 'dark' => state.ui.theme;
export const selectSidebarOpen = (state: { ui: UIState }): boolean => state.ui.sidebarOpen;
export const selectLoading = (state: { ui: UIState }): boolean => state.ui.loading;
export const selectNotifications = (state: { ui: UIState }): Notification[] => state.ui.notifications;
export const selectUnreadNotifications = (state: { ui: UIState }): Notification[] => 
  state.ui.notifications.filter(n => !n.read);
export const selectCurrentPage = (state: { ui: UIState }): string => state.ui.currentPage;
export const selectBreadcrumbs = (state: { ui: UIState }): Breadcrumb[] => state.ui.breadcrumbs;
export const selectSearchQuery = (state: { ui: UIState }): string => state.ui.searchQuery;
export const selectFilters = (state: { ui: UIState }): Record<string, unknown> => state.ui.filters;

export default uiSlice.reducer;
