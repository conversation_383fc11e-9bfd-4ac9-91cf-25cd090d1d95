// OCTAVE Healthcare Platform - Login Page
// HIPAA Compliant Authentication Entry Point

import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  Box,
  Container,
  Alert,
  useTheme,
} from '@mui/material';

import { selectIsAuthenticated } from '@/store/slices/authSlice';
import LoginForm from '@/components/auth/LoginForm';
import PasswordResetForm from '@/components/auth/PasswordResetForm';
import RegistrationForm from '@/components/auth/RegistrationForm';
import SessionTimeoutWarning from '@/components/auth/SessionTimeoutWarning';

type AuthView = 'login' | 'register' | 'forgot-password';

export const LoginPage: React.FC = () => {
  const theme = useTheme();
  const location = useLocation();
  const navigate = useNavigate();
  const isAuthenticated = useSelector(selectIsAuthenticated);

  const [currentView, setCurrentView] = useState<AuthView>('login');
  const [message, setMessage] = useState<string | null>(null);

  useEffect(() => {
    // Redirect if already authenticated
    if (isAuthenticated) {
      const from = (location.state as any)?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }

    // Check for messages from navigation state
    const state = location.state as any;
    if (state?.message) {
      setMessage(state.message);
    }
  }, [isAuthenticated, location.state, navigate]);

  const handleViewChange = (view: AuthView) => {
    setCurrentView(view);
    setMessage(null);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'register':
        return (
          <RegistrationForm
            onBackToLogin={() => handleViewChange('login')}
          />
        );
      case 'forgot-password':
        return (
          <PasswordResetForm
            onBackToLogin={() => handleViewChange('login')}
          />
        );
      case 'login':
      default:
        return (
          <LoginForm
            onForgotPassword={() => handleViewChange('forgot-password')}
            onRegister={() => handleViewChange('register')}
          />
        );
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${theme.palette.primary.main}10 0%, ${theme.palette.secondary.main}10 100%)`,
        display: 'flex',
        alignItems: 'center',
        py: 4,
      }}
    >
      <Container maxWidth="sm">
        {message && (
          <Alert
            severity="info"
            sx={{ mb: 2 }}
            onClose={() => setMessage(null)}
          >
            {message}
          </Alert>
        )}

        {renderCurrentView()}
      </Container>

      {/* Session timeout warning for authenticated users */}
      <SessionTimeoutWarning />
    </Box>
  );
};

export default LoginPage;
