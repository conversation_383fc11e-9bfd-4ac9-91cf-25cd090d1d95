// OCTAVE Healthcare Platform - Password Reset Confirmation Page
// HIPAA Compliant Password Reset Completion

import React from 'react';
import {
  Box,
  Container,
  useTheme,
} from '@mui/material';

import PasswordResetConfirmForm from '@/components/auth/PasswordResetConfirmForm';

export const PasswordResetConfirmPage: React.FC = () => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${theme.palette.primary.main}10 0%, ${theme.palette.secondary.main}10 100%)`,
        display: 'flex',
        alignItems: 'center',
        py: 4,
      }}
    >
      <Container maxWidth="sm">
        <PasswordResetConfirmForm />
      </Container>
    </Box>
  );
};

export default PasswordResetConfirmPage;
