// OCTAVE Healthcare Platform - Forgot Password Page
// Secure Password Reset Request

import React, { useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  TextField,
  Button,
  Link,
  Alert,
  InputAdornment,
  Typography,
} from '@mui/material';
import {
  EmailRounded,
  SendRounded,
  ArrowBackRounded,
} from '@mui/icons-material';

const ForgotPasswordPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (event: React.FormEvent): Promise<void> => {
    event.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      // TODO: Implement password reset request
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      setIsSubmitted(true);
    } catch (err) {
      setError('Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <Box sx={{ textAlign: 'center' }}>
        <Alert severity="success" sx={{ mb: 3 }}>
          <Typography variant="h6" component="div" gutterBottom>
            Reset Email Sent
          </Typography>
          <Typography variant="body2">
            If an account with that email exists, we've sent password reset instructions to {email}.
          </Typography>
        </Alert>
        
        <Link
          component={RouterLink}
          to="/login"
          sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}
        >
          <ArrowBackRounded fontSize="small" />
          Back to Sign In
        </Link>
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={handleSubmit} noValidate>
      <Typography variant="body1" sx={{ mb: 3, textAlign: 'center' }}>
        Enter your email address and we'll send you instructions to reset your password.
      </Typography>

      <TextField
        fullWidth
        id="email"
        name="email"
        label="Email Address"
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        error={!!error}
        helperText={error}
        margin="normal"
        required
        autoComplete="email"
        autoFocus
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <EmailRounded color="action" />
            </InputAdornment>
          ),
        }}
      />

      <Button
        type="submit"
        fullWidth
        variant="contained"
        size="large"
        disabled={isLoading || !email}
        startIcon={<SendRounded />}
        sx={{ mt: 3, mb: 2 }}
      >
        {isLoading ? 'Sending...' : 'Send Reset Instructions'}
      </Button>

      <Box sx={{ textAlign: 'center' }}>
        <Link
          component={RouterLink}
          to="/login"
          variant="body2"
          sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}
        >
          <ArrowBackRounded fontSize="small" />
          Back to Sign In
        </Link>
      </Box>
    </Box>
  );
};

export default ForgotPasswordPage;
