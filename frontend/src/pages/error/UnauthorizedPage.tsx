import React from 'react';
import { Typography, <PERSON>, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';

const UnauthorizedPage: React.FC = () => {
  const navigate = useNavigate();

  return (
    <Box sx={{ textAlign: 'center', p: 4 }}>
      <Typography variant="h3" gutterBottom>403</Typography>
      <Typography variant="h5" gutterBottom>Access Denied</Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        You don't have permission to access this resource.
      </Typography>
      <Button variant="contained" onClick={() => navigate('/dashboard')}>
        Go to Dashboard
      </Button>
    </Box>
  );
};

export default UnauthorizedPage;
