// OCTAVE Healthcare Platform - Main Dashboard Page
// Role-based dashboard routing based on user type

import React from 'react';
import { useSelector } from 'react-redux';
import { Typography, Box, Alert } from '@mui/material';

import { selectUser, selectUserRole } from '@/store/slices/authSlice';
import { UserRole } from '@/types';
import ProviderDashboard from '@/components/dashboard/ProviderDashboard';
import StaffDashboard from '@/components/dashboard/StaffDashboard';
import PatientDashboard from '@/components/dashboard/PatientDashboard';

const DashboardPage: React.FC = () => {
  const user = useSelector(selectUser);
  const userRole = useSelector(selectUserRole);

  // Render role-specific dashboard
  const renderDashboard = () => {
    switch (userRole) {
      case UserRole.PROVIDER:
        return <ProviderDashboard />;

      case UserRole.STAFF:
      case UserRole.BILLING:
        return <StaffDashboard />;

      case UserRole.PATIENT:
        return <PatientDashboard />;

      case UserRole.PRACTICE_ADMIN:
      case UserRole.SYSTEM_ADMIN:
        return <ProviderDashboard />; // Admin can see provider view

      default:
        return (
          <Box sx={{ p: 3 }}>
            <Alert severity="warning">
              <Typography variant="h6" gutterBottom>
                Dashboard Not Available
              </Typography>
              <Typography variant="body2">
                Your user role ({userRole}) does not have a specific dashboard configured.
                Please contact your administrator for assistance.
              </Typography>
            </Alert>
          </Box>
        );
    }
  };

  if (!user) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          <Typography variant="h6" gutterBottom>
            Authentication Required
          </Typography>
          <Typography variant="body2">
            Please log in to access your dashboard.
          </Typography>
        </Alert>
      </Box>
    );
  }

  return renderDashboard();
};

export default DashboardPage;
