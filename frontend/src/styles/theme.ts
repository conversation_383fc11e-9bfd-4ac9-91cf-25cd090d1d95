// OCTAVE Healthcare Platform - Material-UI Theme Configuration
// Healthcare-Specific Design System with WCAG 2.1 AA Compliance

import { createTheme, ThemeOptions } from '@mui/material/styles';
import { PaletteMode } from '@mui/material';

// Healthcare-specific color palette based on mockup design system
// Color Palette: https://colorhunt.co/palette/3aa6b9ffd0d0ff9eaac1ece4
const healthcareColors = {
  primary: {
    main: '#3AA6B9', // Teal Blue (from mockup)
    light: '#5CB8C8',
    dark: '#2A7A87',
    contrastText: '#ffffff',
  },
  secondary: {
    main: '#FFD0D0', // Light Pink (from mockup)
    light: '#FFE0E0',
    dark: '#E6BABA',
    contrastText: '#2C3E50',
  },
  error: {
    main: '#E74C3C', // Danger red (from mockup)
    light: '#EC7063',
    dark: '#C0392B',
    contrastText: '#ffffff',
  },
  warning: {
    main: '#F39C12', // Warning orange (from mockup)
    light: '#F5B041',
    dark: '#D68910',
    contrastText: '#ffffff',
  },
  info: {
    main: '#0066CC', // PhilHealth blue (from mockup)
    light: '#3385D6',
    dark: '#004499',
    contrastText: '#ffffff',
  },
  success: {
    main: '#009639', // PhilHealth green (from mockup)
    light: '#33A852',
    dark: '#006B29',
    contrastText: '#ffffff',
  },
};

// Priority colors for healthcare workflows (matching mockup)
const priorityColors = {
  routine: '#009639', // PhilHealth green
  urgent: '#F39C12',  // Warning orange
  stat: '#E74C3C',    // Danger red
};

// Status colors for prior authorization (matching mockup)
const statusColors = {
  draft: '#95A5A6',        // Light gray
  submitted: '#3AA6B9',    // Primary teal
  underReview: '#F39C12',  // Warning orange
  approved: '#009639',     // PhilHealth green
  denied: '#E74C3C',       // Danger red
  expired: '#7F8C8D',      // Dark gray
  cancelled: '#BDC3C7',    // Light gray
};

const getThemeOptions = (mode: PaletteMode): ThemeOptions => ({
  palette: {
    mode,
    ...healthcareColors,
    background: {
      default: mode === 'light' ? '#f5f5f5' : '#121212',
      paper: mode === 'light' ? '#ffffff' : '#1e1e1e',
    },
    text: {
      primary: mode === 'light' ? '#212121' : '#ffffff',
      secondary: mode === 'light' ? '#757575' : '#b3b3b3',
    },
  },
  typography: {
    fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 500,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 500,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.5,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
      lineHeight: 1.6,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.5,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.43,
    },
    button: {
      textTransform: 'none',
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 8,
  },
  spacing: 8,
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
          minHeight: 44, // WCAG 2.1 AA touch target size
          padding: '8px 16px',
        },
        contained: {
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
          '&:hover': {
            boxShadow: '0 4px 8px rgba(0,0,0,0.15)',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          border: mode === 'light' ? '1px solid #e0e0e0' : '1px solid #333333',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: 8,
            minHeight: 44, // WCAG 2.1 AA touch target size
          },
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: 16,
          fontWeight: 500,
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          borderRight: mode === 'light' ? '1px solid #e0e0e0' : '1px solid #333333',
        },
      },
    },
    MuiListItemButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          margin: '2px 8px',
          minHeight: 44, // WCAG 2.1 AA touch target size
          '&.Mui-selected': {
            backgroundColor: mode === 'light' ? '#e3f2fd' : '#1e3a8a',
            '&:hover': {
              backgroundColor: mode === 'light' ? '#bbdefb' : '#1e40af',
            },
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: mode === 'light' ? '1px solid #e0e0e0' : '1px solid #333333',
        },
        head: {
          fontWeight: 600,
          backgroundColor: mode === 'light' ? '#f5f5f5' : '#2a2a2a',
        },
      },
    },
    MuiAlert: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
      },
    },
  },
});

export const createHealthcareTheme = (mode: PaletteMode) => {
  const theme = createTheme(getThemeOptions(mode));
  
  // Add custom properties
  return createTheme(theme, {
    custom: {
      priority: priorityColors,
      status: statusColors,
      spacing: {
        xs: theme.spacing(1),
        sm: theme.spacing(2),
        md: theme.spacing(3),
        lg: theme.spacing(4),
        xl: theme.spacing(6),
      },
      shadows: {
        card: '0 2px 8px rgba(0,0,0,0.1)',
        elevated: '0 4px 16px rgba(0,0,0,0.15)',
        focus: `0 0 0 2px ${theme.palette.primary.main}40`,
      },
      transitions: {
        fast: '0.15s ease-in-out',
        normal: '0.3s ease-in-out',
        slow: '0.5s ease-in-out',
      },
    },
  });
};

// Extend the Theme interface to include custom properties
declare module '@mui/material/styles' {
  interface Theme {
    custom: {
      priority: typeof priorityColors;
      status: typeof statusColors;
      spacing: {
        xs: string;
        sm: string;
        md: string;
        lg: string;
        xl: string;
      };
      shadows: {
        card: string;
        elevated: string;
        focus: string;
      };
      transitions: {
        fast: string;
        normal: string;
        slow: string;
      };
    };
  }
  
  interface ThemeOptions {
    custom?: {
      priority?: typeof priorityColors;
      status?: typeof statusColors;
      spacing?: {
        xs?: string;
        sm?: string;
        md?: string;
        lg?: string;
        xl?: string;
      };
      shadows?: {
        card?: string;
        elevated?: string;
        focus?: string;
      };
      transitions?: {
        fast?: string;
        normal?: string;
        slow?: string;
      };
    };
  }
}
