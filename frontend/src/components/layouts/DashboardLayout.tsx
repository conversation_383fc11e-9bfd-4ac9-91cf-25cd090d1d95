// OCTAVE Healthcare Platform - Dashboard Layout
// Comprehensive application layout with navigation, breadcrumbs, and responsive design

import React from 'react';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  useTheme,
  useMediaQuery,
  Container,
} from '@mui/material';
import {
  MenuRounded,
  NotificationsRounded,
  AccountCircleRounded,
  LogoutRounded,
  SettingsRounded,
} from '@mui/icons-material';
import { useSelector, useDispatch } from 'react-redux';

import { selectUser, logout } from '@/store/slices/authSlice';
import { selectSidebarOpen, setSidebarOpen, selectNotifications } from '@/store/slices/uiSlice';
import NavigationProvider from '@/components/navigation/NavigationProvider';
import Sidebar from '@/components/navigation/Sidebar';
import Breadcrumbs from '@/components/navigation/Breadcrumbs';
import { ThemeToggleButton } from '@/components/common/theme/ThemeToggle';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const dispatch = useDispatch();

  const user = useSelector(selectUser);
  const sidebarOpen = useSelector(selectSidebarOpen);
  const notifications = useSelector(selectNotifications);

  const [userMenuAnchor, setUserMenuAnchor] = React.useState<null | HTMLElement>(null);
  const [notificationMenuAnchor, setNotificationMenuAnchor] = React.useState<null | HTMLElement>(null);

  const handleDrawerToggle = (): void => {
    dispatch(setSidebarOpen(!sidebarOpen));
  };

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>): void => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = (): void => {
    setUserMenuAnchor(null);
  };

  const handleNotificationMenuOpen = (event: React.MouseEvent<HTMLElement>): void => {
    setNotificationMenuAnchor(event.currentTarget);
  };

  const handleNotificationMenuClose = (): void => {
    setNotificationMenuAnchor(null);
  };

  const handleLogout = (): void => {
    dispatch(logout());
    handleUserMenuClose();
  };

  const unreadNotifications = notifications.filter(n => !n.read);

  return (
    <NavigationProvider>
      <Box sx={{ display: 'flex', minHeight: '100vh' }}>
        {/* App Bar */}
        <AppBar
          position="fixed"
          sx={{
            width: { md: `calc(100% - ${sidebarOpen ? 280 : 72}px)` },
            ml: { md: sidebarOpen ? '280px' : '72px' },
            transition: theme.transitions.create(['width', 'margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
            backgroundColor: 'background.paper',
            color: 'text.primary',
            boxShadow: 'none',
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="open drawer"
              edge="start"
              onClick={handleDrawerToggle}
              sx={{ mr: 2, display: { md: 'none' } }}
            >
              <MenuRounded />
            </IconButton>

            <Box sx={{ flexGrow: 1 }}>
              <Breadcrumbs variant={isMobile ? 'mobile' : 'default'} />
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {/* Theme Toggle */}
              <ThemeToggleButton size="small" />

              {/* Notifications */}
              <IconButton
                color="inherit"
                onClick={handleNotificationMenuOpen}
                aria-label={`${unreadNotifications.length} unread notifications`}
              >
                <NotificationsRounded />
                {unreadNotifications.length > 0 && (
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 8,
                      right: 8,
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      backgroundColor: 'error.main',
                    }}
                  />
                )}
              </IconButton>

              {/* User Menu */}
              <IconButton
                color="inherit"
                onClick={handleUserMenuOpen}
                aria-label="user account menu"
              >
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: 'primary.main',
                    fontSize: '0.875rem',
                  }}
                >
                  {user?.firstName?.[0]}{user?.lastName?.[0]}
                </Avatar>
              </IconButton>
            </Box>
          </Toolbar>
        </AppBar>

        {/* Sidebar */}
        <Sidebar variant={isMobile ? 'temporary' : 'permanent'} />

        {/* Main Content */}
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            width: { md: `calc(100% - ${sidebarOpen ? 280 : 72}px)` },
            transition: theme.transitions.create(['width', 'margin'], {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
          }}
        >
          <Toolbar /> {/* Spacer for fixed AppBar */}

          <Container
            maxWidth={false}
            sx={{
              py: 3,
              px: { xs: 2, sm: 3 },
              minHeight: 'calc(100vh - 64px)',
            }}
          >
            {children}
          </Container>
        </Box>

        {/* User Menu */}
        <Menu
          anchorEl={userMenuAnchor}
          open={Boolean(userMenuAnchor)}
          onClose={handleUserMenuClose}
          onClick={handleUserMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
        >
          <Box sx={{ px: 2, py: 1 }}>
            <Typography variant="subtitle2" noWrap>
              {user?.firstName} {user?.lastName}
            </Typography>
            <Typography variant="caption" color="text.secondary" noWrap>
              {user?.email}
            </Typography>
          </Box>
          <Divider />
          <MenuItem onClick={handleUserMenuClose}>
            <AccountCircleRounded sx={{ mr: 2 }} />
            Profile
          </MenuItem>
          <MenuItem onClick={handleUserMenuClose}>
            <SettingsRounded sx={{ mr: 2 }} />
            Settings
          </MenuItem>
          <Divider />
          <MenuItem onClick={handleLogout}>
            <LogoutRounded sx={{ mr: 2 }} />
            Logout
          </MenuItem>
        </Menu>

        {/* Notification Menu */}
        <Menu
          anchorEl={notificationMenuAnchor}
          open={Boolean(notificationMenuAnchor)}
          onClose={handleNotificationMenuClose}
          transformOrigin={{ horizontal: 'right', vertical: 'top' }}
          anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
          PaperProps={{
            sx: { width: 320, maxHeight: 400 },
          }}
        >
          <Box sx={{ px: 2, py: 1 }}>
            <Typography variant="subtitle2">
              Notifications ({unreadNotifications.length} unread)
            </Typography>
          </Box>
          <Divider />
          {notifications.length === 0 ? (
            <MenuItem disabled>
              <Typography variant="body2" color="text.secondary">
                No notifications
              </Typography>
            </MenuItem>
          ) : (
            notifications.slice(0, 5).map((notification) => (
              <MenuItem key={notification.id} onClick={handleNotificationMenuClose}>
                <Box>
                  <Typography variant="body2" noWrap>
                    {notification.title}
                  </Typography>
                  <Typography variant="caption" color="text.secondary" noWrap>
                    {notification.message}
                  </Typography>
                </Box>
              </MenuItem>
            ))
          )}
        </Menu>
      </Box>
    </NavigationProvider>
  );
};

export default DashboardLayout;
