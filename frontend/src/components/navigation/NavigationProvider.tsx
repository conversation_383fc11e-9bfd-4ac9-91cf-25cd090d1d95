// OCTAVE Healthcare Platform - Navigation Provider
// Comprehensive Navigation System with Breadcrumbs and Analytics

import React, { createContext, useContext, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { selectUserRole } from '@/store/slices/authSlice';
import { setBreadcrumbs, setCurrentPage } from '@/store/slices/uiSlice';
import { UserRole } from '@/types';

interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon?: React.ReactNode;
  roles?: UserRole[];
  children?: NavigationItem[];
  badge?: string | number;
  isExternal?: boolean;
  description?: string;
}

interface Breadcrumb {
  label: string;
  path: string;
  isActive: boolean;
}

interface NavigationContextType {
  navigationItems: NavigationItem[];
  currentPath: string;
  breadcrumbs: Breadcrumb[];
  navigateTo: (path: string, options?: { replace?: boolean; state?: any }) => void;
  goBack: () => void;
  canNavigate: (path: string) => boolean;
  getPageTitle: (path: string) => string;
  trackNavigation: (path: string, metadata?: Record<string, any>) => void;
}

const NavigationContext = createContext<NavigationContextType | undefined>(undefined);

// Navigation configuration
const createNavigationItems = (userRole: UserRole | null): NavigationItem[] => {
  const baseItems: NavigationItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      path: '/dashboard',
      description: 'Overview of your healthcare workflow',
    },
    {
      id: 'prior-auth',
      label: 'Prior Authorizations',
      path: '/prior-auth',
      description: 'Manage prior authorization requests',
      children: [
        {
          id: 'prior-auth-list',
          label: 'All Requests',
          path: '/prior-auth',
        },
        {
          id: 'prior-auth-new',
          label: 'New Request',
          path: '/prior-auth/new',
          roles: [UserRole.PROVIDER, UserRole.STAFF, UserRole.PRACTICE_ADMIN],
        },
        {
          id: 'prior-auth-pending',
          label: 'Pending Review',
          path: '/prior-auth?status=pending',
        },
        {
          id: 'prior-auth-approved',
          label: 'Approved',
          path: '/prior-auth?status=approved',
        },
        {
          id: 'prior-auth-denied',
          label: 'Denied',
          path: '/prior-auth?status=denied',
        },
      ],
    },
    {
      id: 'patients',
      label: 'Patients',
      path: '/patients',
      roles: [UserRole.PROVIDER, UserRole.STAFF, UserRole.PRACTICE_ADMIN],
      description: 'Patient management and records',
      children: [
        {
          id: 'patients-list',
          label: 'All Patients',
          path: '/patients',
        },
        {
          id: 'patients-search',
          label: 'Search Patients',
          path: '/patients/search',
        },
      ],
    },
    {
      id: 'communications',
      label: 'Communications',
      path: '/communications',
      description: 'Messages and correspondence',
    },
    {
      id: 'documents',
      label: 'Documents',
      path: '/documents',
      description: 'Document management and storage',
    },
    {
      id: 'reports',
      label: 'Reports & Analytics',
      path: '/reports',
      roles: [UserRole.PROVIDER, UserRole.PRACTICE_ADMIN, UserRole.BILLING],
      description: 'Analytics and reporting dashboard',
      children: [
        {
          id: 'reports-overview',
          label: 'Overview',
          path: '/reports',
        },
        {
          id: 'reports-prior-auth',
          label: 'Prior Auth Analytics',
          path: '/reports/prior-auth',
        },
        {
          id: 'reports-performance',
          label: 'Performance Metrics',
          path: '/reports/performance',
        },
      ],
    },
  ];

  // Add admin-only items
  if (userRole === UserRole.SYSTEM_ADMIN || userRole === UserRole.PRACTICE_ADMIN) {
    baseItems.push({
      id: 'admin',
      label: 'Administration',
      path: '/admin',
      roles: [UserRole.SYSTEM_ADMIN, UserRole.PRACTICE_ADMIN],
      description: 'System administration and settings',
      children: [
        {
          id: 'admin-dashboard',
          label: 'Admin Dashboard',
          path: '/admin',
        },
        {
          id: 'admin-users',
          label: 'User Management',
          path: '/admin/users',
        },
        {
          id: 'admin-practice',
          label: 'Practice Settings',
          path: '/admin/practice',
        },
      ],
    });
  }

  // Add user settings
  baseItems.push({
    id: 'settings',
    label: 'Settings',
    path: '/settings',
    description: 'Application and user preferences',
    children: [
      {
        id: 'settings-profile',
        label: 'Profile',
        path: '/profile',
      },
      {
        id: 'settings-preferences',
        label: 'Preferences',
        path: '/settings/preferences',
      },
      {
        id: 'settings-security',
        label: 'Security',
        path: '/settings/security',
      },
    ],
  });

  return baseItems;
};

// Page title mapping
const pageTitles: Record<string, string> = {
  '/dashboard': 'Dashboard',
  '/prior-auth': 'Prior Authorizations',
  '/prior-auth/new': 'New Prior Authorization',
  '/patients': 'Patients',
  '/patients/search': 'Search Patients',
  '/communications': 'Communications',
  '/documents': 'Documents',
  '/reports': 'Reports & Analytics',
  '/reports/prior-auth': 'Prior Authorization Analytics',
  '/reports/performance': 'Performance Metrics',
  '/admin': 'Administration',
  '/admin/users': 'User Management',
  '/admin/practice': 'Practice Settings',
  '/settings': 'Settings',
  '/profile': 'Profile',
  '/settings/preferences': 'Preferences',
  '/settings/security': 'Security Settings',
};

// Generate breadcrumbs from path
const generateBreadcrumbs = (path: string, navigationItems: NavigationItem[]): Breadcrumb[] => {
  const breadcrumbs: Breadcrumb[] = [];
  const pathSegments = path.split('/').filter(Boolean);
  
  // Always start with Dashboard
  if (path !== '/dashboard') {
    breadcrumbs.push({
      label: 'Dashboard',
      path: '/dashboard',
      isActive: false,
    });
  }
  
  let currentPath = '';
  
  for (let i = 0; i < pathSegments.length; i++) {
    currentPath += `/${pathSegments[i]}`;
    const isLast = i === pathSegments.length - 1;
    
    // Find the navigation item for this path
    const findItem = (items: NavigationItem[]): NavigationItem | undefined => {
      for (const item of items) {
        if (item.path === currentPath) {
          return item;
        }
        if (item.children) {
          const found = findItem(item.children);
          if (found) return found;
        }
      }
      return undefined;
    };
    
    const item = findItem(navigationItems);
    
    if (item) {
      breadcrumbs.push({
        label: item.label,
        path: currentPath,
        isActive: isLast,
      });
    } else {
      // Fallback for dynamic routes
      const title = pageTitles[currentPath] || pathSegments[i] || 'Unknown';
      breadcrumbs.push({
        label: title,
        path: currentPath,
        isActive: isLast,
      });
    }
  }
  
  return breadcrumbs;
};

interface NavigationProviderProps {
  children: React.ReactNode;
}

export const NavigationProvider: React.FC<NavigationProviderProps> = ({ children }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const userRole = useSelector(selectUserRole) as UserRole | null;
  
  const [navigationItems] = useState(() => createNavigationItems(userRole));
  const [currentPath, setCurrentPath] = useState(location.pathname);

  // Update current path and breadcrumbs when location changes
  useEffect(() => {
    setCurrentPath(location.pathname);
    
    const breadcrumbs = generateBreadcrumbs(location.pathname, navigationItems);
    dispatch(setBreadcrumbs(breadcrumbs));
    
    const pageTitle = getPageTitle(location.pathname);
    dispatch(setCurrentPage(pageTitle));
    
    // Update document title
    document.title = `${pageTitle} - OCTAVE Healthcare`;
    
    // Track navigation
    trackNavigation(location.pathname, {
      referrer: document.referrer,
      timestamp: new Date().toISOString(),
    });
  }, [location.pathname, navigationItems, dispatch]);

  const navigateTo = (path: string, options?: { replace?: boolean; state?: any }): void => {
    if (canNavigate(path)) {
      navigate(path, options);
    } else {
      console.warn(`Navigation to ${path} is not allowed for current user role`);
    }
  };

  const goBack = (): void => {
    navigate(-1);
  };

  const canNavigate = (path: string): boolean => {
    if (!userRole) return false;
    
    const findItem = (items: NavigationItem[]): NavigationItem | undefined => {
      for (const item of items) {
        if (item.path === path) {
          return item;
        }
        if (item.children) {
          const found = findItem(item.children);
          if (found) return found;
        }
      }
      return undefined;
    };
    
    const item = findItem(navigationItems);
    
    if (!item) {
      // Allow navigation to paths not in navigation (like dynamic routes)
      return true;
    }
    
    if (!item.roles || item.roles.length === 0) {
      // No role restrictions
      return true;
    }
    
    return item.roles.includes(userRole);
  };

  const getPageTitle = (path: string): string => {
    return pageTitles[path] || 'OCTAVE Healthcare';
  };

  const trackNavigation = (path: string, metadata?: Record<string, any>): void => {
    // Analytics tracking
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', 'GA_MEASUREMENT_ID', {
        page_path: path,
      });
    }
    
    // Custom analytics
    console.log('Navigation tracked:', { path, metadata });
  };

  const breadcrumbs = generateBreadcrumbs(currentPath, navigationItems);

  const contextValue: NavigationContextType = {
    navigationItems,
    currentPath,
    breadcrumbs,
    navigateTo,
    goBack,
    canNavigate,
    getPageTitle,
    trackNavigation,
  };

  return (
    <NavigationContext.Provider value={contextValue}>
      {children}
    </NavigationContext.Provider>
  );
};

export const useNavigation = (): NavigationContextType => {
  const context = useContext(NavigationContext);
  if (!context) {
    throw new Error('useNavigation must be used within a NavigationProvider');
  }
  return context;
};

export default NavigationProvider;
