// OCTAVE Healthcare Platform - Breadcrumb Navigation Component
// Accessible Breadcrumb Navigation with Healthcare Workflow Support

import React from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  Breadcrumbs as MuiBreadcrumbs,
  Link,
  Typography,
  Box,
  Chip,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  NavigateNextRounded,
  HomeRounded,
  ChevronLeftRounded,
} from '@mui/icons-material';

import { selectBreadcrumbs } from '@/store/slices/uiSlice';
import { useNavigation } from './NavigationProvider';

interface BreadcrumbsProps {
  showHomeIcon?: boolean;
  showBackButton?: boolean;
  maxItems?: number;
  separator?: React.ReactNode;
  variant?: 'default' | 'compact' | 'mobile';
}

const Breadcrumbs: React.FC<BreadcrumbsProps> = ({
  showHomeIcon = true,
  showBackButton = false,
  maxItems = 8,
  separator,
  variant = 'default',
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const { goBack, canNavigate } = useNavigation();
  const breadcrumbs = useSelector(selectBreadcrumbs);

  // Auto-adjust variant for mobile
  const effectiveVariant = isMobile ? 'mobile' : variant;

  const handleLinkClick = (event: React.MouseEvent, path: string): void => {
    event.preventDefault();
    
    if (canNavigate(path)) {
      navigate(path);
    }
  };

  const handleBackClick = (): void => {
    goBack();
  };

  // Render different variants
  const renderDefault = (): React.ReactNode => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        py: 1,
      }}
    >
      {showBackButton && breadcrumbs.length > 1 && (
        <Chip
          icon={<ChevronLeftRounded />}
          label="Back"
          variant="outlined"
          size="small"
          onClick={handleBackClick}
          sx={{
            mr: 1,
            '&:hover': {
              backgroundColor: 'action.hover',
            },
          }}
        />
      )}
      
      <MuiBreadcrumbs
        maxItems={maxItems}
        separator={separator || <NavigateNextRounded fontSize="small" />}
        aria-label="breadcrumb navigation"
        sx={{
          '& .MuiBreadcrumbs-separator': {
            color: 'text.secondary',
          },
        }}
      >
        {breadcrumbs.map((breadcrumb, index) => {
          const isLast = index === breadcrumbs.length - 1;
          const isFirst = index === 0;
          
          if (isLast) {
            return (
              <Typography
                key={breadcrumb.path}
                color="text.primary"
                variant="body2"
                sx={{
                  fontWeight: 500,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 0.5,
                }}
              >
                {isFirst && showHomeIcon && (
                  <HomeRounded fontSize="small" />
                )}
                {breadcrumb.label}
              </Typography>
            );
          }
          
          return (
            <Link
              key={breadcrumb.path}
              component="button"
              variant="body2"
              onClick={(e) => handleLinkClick(e, breadcrumb.path)}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
                color: 'text.secondary',
                textDecoration: 'none',
                '&:hover': {
                  color: 'primary.main',
                  textDecoration: 'underline',
                },
                '&:focus-visible': {
                  outline: `2px solid ${theme.palette.primary.main}`,
                  outlineOffset: 2,
                  borderRadius: 1,
                },
              }}
            >
              {isFirst && showHomeIcon && (
                <HomeRounded fontSize="small" />
              )}
              {breadcrumb.label}
            </Link>
          );
        })}
      </MuiBreadcrumbs>
    </Box>
  );

  const renderCompact = (): React.ReactNode => (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: 0.5,
        py: 0.5,
      }}
    >
      {breadcrumbs.length > 1 && (
        <Chip
          icon={<ChevronLeftRounded />}
          label="Back"
          variant="outlined"
          size="small"
          onClick={handleBackClick}
          sx={{
            height: 24,
            fontSize: '0.75rem',
            '& .MuiChip-icon': {
              fontSize: 16,
            },
          }}
        />
      )}
      
      <Typography variant="caption" color="text.secondary">
        {breadcrumbs.slice(0, -1).map(b => b.label).join(' > ')}
        {breadcrumbs.length > 1 && ' > '}
      </Typography>
      
      <Typography variant="body2" color="text.primary" sx={{ fontWeight: 500 }}>
        {breadcrumbs[breadcrumbs.length - 1]?.label}
      </Typography>
    </Box>
  );

  const renderMobile = (): React.ReactNode => {
    const currentPage = breadcrumbs[breadcrumbs.length - 1];
    const parentPage = breadcrumbs[breadcrumbs.length - 2];
    
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          py: 1,
        }}
      >
        {parentPage && (
          <Chip
            icon={<ChevronLeftRounded />}
            label={parentPage.label}
            variant="outlined"
            size="small"
            onClick={(e) => handleLinkClick(e, parentPage.path)}
            sx={{
              maxWidth: 120,
              '& .MuiChip-label': {
                overflow: 'hidden',
                textOverflow: 'ellipsis',
              },
            }}
          />
        )}
        
        <Typography
          variant="h6"
          color="text.primary"
          sx={{
            fontWeight: 600,
            flex: 1,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {currentPage?.label}
        </Typography>
      </Box>
    );
  };

  // Don't render if no breadcrumbs
  if (!breadcrumbs || breadcrumbs.length === 0) {
    return null;
  }

  // Render based on variant
  switch (effectiveVariant) {
    case 'compact':
      return renderCompact();
    case 'mobile':
      return renderMobile();
    default:
      return renderDefault();
  }
};

// Structured data breadcrumbs for SEO
export const StructuredBreadcrumbs: React.FC = () => {
  const breadcrumbs = useSelector(selectBreadcrumbs);
  
  if (!breadcrumbs || breadcrumbs.length === 0) {
    return null;
  }
  
  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((breadcrumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: breadcrumb.label,
      item: `${window.location.origin}${breadcrumb.path}`,
    })),
  };
  
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
};

// Breadcrumb hook for custom implementations
export const useBreadcrumbs = () => {
  const breadcrumbs = useSelector(selectBreadcrumbs);
  const { navigateTo, canNavigate } = useNavigation();
  
  const navigateToBreadcrumb = (index: number): void => {
    if (breadcrumbs && breadcrumbs[index]) {
      const breadcrumb = breadcrumbs[index];
      if (canNavigate(breadcrumb.path)) {
        navigateTo(breadcrumb.path);
      }
    }
  };
  
  const getCurrentPage = (): string => {
    return breadcrumbs?.[breadcrumbs.length - 1]?.label || '';
  };
  
  const getParentPage = (): { label: string; path: string } | null => {
    if (breadcrumbs && breadcrumbs.length > 1) {
      const parent = breadcrumbs[breadcrumbs.length - 2];
      if (parent) {
        return { label: parent.label, path: parent.path };
      }
    }
    return null;
  };
  
  return {
    breadcrumbs,
    navigateToBreadcrumb,
    getCurrentPage,
    getParentPage,
  };
};

export default Breadcrumbs;
