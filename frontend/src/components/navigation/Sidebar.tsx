// OCTAVE Healthcare Platform - Sidebar Navigation Component
// Role-Based Navigation with Healthcare Workflow Support

import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Collapse,
  Box,
  Typography,
  Divider,
  Badge,
  Tooltip,
  IconButton,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  DashboardRounded,
  AssignmentRounded,
  PeopleRounded,
  MessageRounded,
  FolderRounded,
  AnalyticsRounded,
  SettingsRounded,
  AdminPanelSettingsRounded,
  ExpandLessRounded,
  ExpandMoreRounded,
  MenuRounded,
  CloseRounded,
} from '@mui/icons-material';

import { selectSidebarOpen, setSidebarOpen } from '@/store/slices/uiSlice';
import { selectUserRole } from '@/store/slices/authSlice';
import { useNavigation } from './NavigationProvider';
import { UserRole } from '@/types';

const DRAWER_WIDTH = 280;
const DRAWER_WIDTH_COLLAPSED = 72;

// Icon mapping for navigation items
const iconMap: Record<string, React.ReactElement> = {
  dashboard: <DashboardRounded />,
  'prior-auth': <AssignmentRounded />,
  patients: <PeopleRounded />,
  communications: <MessageRounded />,
  documents: <FolderRounded />,
  reports: <AnalyticsRounded />,
  admin: <AdminPanelSettingsRounded />,
  settings: <SettingsRounded />,
};

interface SidebarProps {
  variant?: 'permanent' | 'temporary' | 'persistent';
  collapsible?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({
  variant = 'permanent',
  collapsible = true,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const location = useLocation();
  const dispatch = useDispatch();
  
  const isOpen = useSelector(selectSidebarOpen);
  const userRole = useSelector(selectUserRole);
  const { navigationItems, navigateTo, canNavigate } = useNavigation();
  
  const [collapsed, setCollapsed] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());

  // Auto-adjust for mobile
  const effectiveVariant = isMobile ? 'temporary' : variant;
  const drawerWidth = collapsed ? DRAWER_WIDTH_COLLAPSED : DRAWER_WIDTH;

  const handleDrawerToggle = (): void => {
    dispatch(setSidebarOpen(!isOpen));
  };

  const handleCollapseToggle = (): void => {
    if (collapsible) {
      setCollapsed(!collapsed);
      // Close all expanded items when collapsing
      if (!collapsed) {
        setExpandedItems(new Set());
      }
    }
  };

  const handleItemClick = (path: string): void => {
    if (canNavigate(path)) {
      navigateTo(path);
      
      // Close drawer on mobile after navigation
      if (isMobile) {
        dispatch(setSidebarOpen(false));
      }
    }
  };

  const handleExpandClick = (itemId: string): void => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };

  const isItemActive = (path: string): boolean => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  const isItemVisible = (roles?: UserRole[]): boolean => {
    if (!roles || roles.length === 0) return true;
    if (!userRole) return false;
    return roles.includes(userRole as UserRole);
  };

  const renderNavigationItem = (item: any, level: number = 0): React.ReactNode => {
    if (!isItemVisible(item.roles)) {
      return null;
    }

    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);
    const isActive = isItemActive(item.path);
    const icon = iconMap[item.id] || <DashboardRounded />;

    return (
      <React.Fragment key={item.id}>
        <ListItem disablePadding sx={{ display: 'block' }}>
          <Tooltip
            title={collapsed ? item.label : ''}
            placement="right"
            disableHoverListener={!collapsed}
          >
            <ListItemButton
              onClick={() => {
                if (hasChildren && !collapsed) {
                  handleExpandClick(item.id);
                } else {
                  handleItemClick(item.path);
                }
              }}
              selected={isActive}
              sx={{
                minHeight: 48,
                justifyContent: collapsed ? 'center' : 'initial',
                px: 2.5,
                ml: level * 2,
                borderRadius: 1,
                mx: 1,
                mb: 0.5,
                '&.Mui-selected': {
                  backgroundColor: 'primary.main',
                  color: 'primary.contrastText',
                  '&:hover': {
                    backgroundColor: 'primary.dark',
                  },
                  '& .MuiListItemIcon-root': {
                    color: 'inherit',
                  },
                },
                '&:hover': {
                  backgroundColor: 'action.hover',
                },
              }}
            >
              <ListItemIcon
                sx={{
                  minWidth: 0,
                  mr: collapsed ? 0 : 3,
                  justifyContent: 'center',
                }}
              >
                <Badge
                  badgeContent={item.badge}
                  color="error"
                  variant={typeof item.badge === 'number' ? 'standard' : 'dot'}
                >
                  {icon}
                </Badge>
              </ListItemIcon>
              
              {!collapsed && (
                <>
                  <ListItemText
                    primary={item.label}
                    secondary={level === 0 ? item.description : undefined}
                    primaryTypographyProps={{
                      fontSize: level === 0 ? '0.875rem' : '0.8125rem',
                      fontWeight: level === 0 ? 500 : 400,
                    }}
                    secondaryTypographyProps={{
                      fontSize: '0.75rem',
                      noWrap: true,
                    }}
                  />
                  
                  {hasChildren && (
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleExpandClick(item.id);
                      }}
                      sx={{ color: 'inherit' }}
                    >
                      {isExpanded ? <ExpandLessRounded /> : <ExpandMoreRounded />}
                    </IconButton>
                  )}
                </>
              )}
            </ListItemButton>
          </Tooltip>
        </ListItem>
        
        {hasChildren && !collapsed && (
          <Collapse in={isExpanded} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {item.children.map((child: any) => 
                renderNavigationItem(child, level + 1)
              )}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  const drawerContent = (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box
        sx={{
          p: 2,
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'space-between',
          minHeight: 64,
        }}
      >
        {!collapsed && (
          <Box>
            <Typography variant="h6" noWrap sx={{ fontWeight: 700 }}>
              OCTAVE
            </Typography>
            <Typography variant="caption" color="text.secondary" noWrap>
              Healthcare Platform
            </Typography>
          </Box>
        )}
        
        {collapsible && !isMobile && (
          <IconButton
            onClick={handleCollapseToggle}
            size="small"
            sx={{
              color: 'text.secondary',
              '&:hover': {
                backgroundColor: 'action.hover',
              },
            }}
          >
            {collapsed ? <MenuRounded /> : <CloseRounded />}
          </IconButton>
        )}
      </Box>
      
      <Divider />
      
      {/* Navigation Items */}
      <Box sx={{ flex: 1, overflow: 'auto', py: 1 }}>
        <List>
          {navigationItems.map((item) => renderNavigationItem(item))}
        </List>
      </Box>
      
      {/* Footer */}
      {!collapsed && (
        <>
          <Divider />
          <Box sx={{ p: 2 }}>
            <Typography variant="caption" color="text.secondary" align="center" display="block">
              HIPAA Compliant • Secure
            </Typography>
            <Typography variant="caption" color="text.secondary" align="center" display="block">
              © 2025 OCTAVE Healthcare
            </Typography>
          </Box>
        </>
      )}
    </Box>
  );

  return (
    <Box
      component="nav"
      sx={{
        width: { md: drawerWidth },
        flexShrink: { md: 0 },
      }}
    >
      <Drawer
        variant={effectiveVariant}
        open={isOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile
        }}
        sx={{
          '& .MuiDrawer-paper': {
            boxSizing: 'border-box',
            width: drawerWidth,
            borderRight: `1px solid ${theme.palette.divider}`,
            backgroundColor: 'background.paper',
            transition: theme.transitions.create('width', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.enteringScreen,
            }),
          },
        }}
      >
        {drawerContent}
      </Drawer>
    </Box>
  );
};

export default Sidebar;
