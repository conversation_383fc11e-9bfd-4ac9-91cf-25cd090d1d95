// OCTAVE Healthcare Platform - Error Boundary Component
// HIPAA Compliant Error Handling with Audit Logging

import { Component, ErrorInfo, ReactNode } from 'react';
import {
  Box,
  Button,
  Container,
  Paper,
  Typography,
  Alert,
  AlertTitle,
} from '@mui/material';
import { RefreshRounded, BugReportRounded } from '@mui/icons-material';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  override componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log error for monitoring and debugging
    this.setState({
      error,
      errorInfo,
    });

    // Log to console in development
    if (import.meta.env.DEV) {
      console.error('ErrorBoundary caught an error:', error, errorInfo);
    }

    // In production, send to error reporting service
    this.logErrorToService(error, errorInfo);
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo): void => {
    // HIPAA-compliant error logging
    const errorData = {
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      // Note: Do not log any PHI or sensitive user data
    };

    // Send to monitoring service (implement based on your monitoring solution)
    if (import.meta.env.PROD) {
      // Example: Send to error monitoring service
      // errorReportingService.logError(errorData);
      console.error('Production error logged:', errorData);
    }
  };

  private handleRefresh = (): void => {
    // Reset error state and reload the page
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    });
    window.location.reload();
  };

  private handleReportError = (): void => {
    // Open support/feedback mechanism
    const errorId = this.state.errorId;
    const subject = encodeURIComponent('Application Error Report');
    const body = encodeURIComponent(
      `Error ID: ${errorId}\n\nPlease describe what you were doing when this error occurred:\n\n`
    );
    
    // Open email client or support form
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  override render(): ReactNode {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Container maxWidth="md" sx={{ py: 8 }}>
          <Paper
            elevation={3}
            sx={{
              p: 4,
              textAlign: 'center',
              borderRadius: 2,
            }}
          >
            <Box sx={{ mb: 3 }}>
              <BugReportRounded
                sx={{
                  fontSize: 64,
                  color: 'error.main',
                  mb: 2,
                }}
              />
              <Typography variant="h4" component="h1" gutterBottom>
                Oops! Something went wrong
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                We apologize for the inconvenience. An unexpected error has occurred
                in the application.
              </Typography>
            </Box>

            <Alert severity="error" sx={{ mb: 3, textAlign: 'left' }}>
              <AlertTitle>Error Details</AlertTitle>
              <Typography variant="body2" component="div">
                <strong>Error ID:</strong> {this.state.errorId}
              </Typography>
              {import.meta.env.DEV && this.state.error && (
                <Box sx={{ mt: 1 }}>
                  <Typography variant="body2" component="div">
                    <strong>Message:</strong> {this.state.error.message}
                  </Typography>
                  {this.state.error.stack && (
                    <Box
                      component="pre"
                      sx={{
                        mt: 1,
                        p: 1,
                        bgcolor: 'grey.100',
                        borderRadius: 1,
                        fontSize: '0.75rem',
                        overflow: 'auto',
                        maxHeight: 200,
                      }}
                    >
                      {this.state.error.stack}
                    </Box>
                  )}
                </Box>
              )}
            </Alert>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button
                variant="contained"
                startIcon={<RefreshRounded />}
                onClick={this.handleRefresh}
                size="large"
              >
                Refresh Page
              </Button>
              <Button
                variant="outlined"
                onClick={this.handleReportError}
                size="large"
              >
                Report Error
              </Button>
            </Box>

            <Typography
              variant="caption"
              color="text.secondary"
              sx={{ display: 'block', mt: 3 }}
            >
              If this problem persists, please contact our support team with the Error ID above.
            </Typography>
          </Paper>
        </Container>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
