// OCTAVE Healthcare Platform - Loading Provider Component
// Global Loading State Management

import React from 'react';
import { useSelector } from 'react-redux';
import { Backdrop, CircularProgress, Typography, Box } from '@mui/material';
import { selectLoading } from '@/store/slices/uiSlice';

interface LoadingProviderProps {
  children: React.ReactNode;
}

const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
  const isLoading = useSelector(selectLoading);

  return (
    <>
      {children}
      <Backdrop
        sx={{
          color: '#fff',
          zIndex: (theme) => theme.zIndex.drawer + 1,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
        }}
        open={isLoading}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 2,
          }}
        >
          <CircularProgress color="inherit" size={60} />
          <Typography variant="h6" component="div">
            Loading...
          </Typography>
          <Typography variant="body2" component="div" sx={{ opacity: 0.8 }}>
            Please wait while we process your request
          </Typography>
        </Box>
      </Backdrop>
    </>
  );
};

export default LoadingProvider;
