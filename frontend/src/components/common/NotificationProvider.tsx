// OCTAVE Healthcare Platform - Notification Provider Component
// Global Notification System

import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Snackbar, Alert, AlertTitle } from '@mui/material';
import { selectNotifications, removeNotification } from '@/store/slices/uiSlice';

interface NotificationProviderProps {
  children: React.ReactNode;
}

const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const notifications = useSelector(selectNotifications);
  
  // Get the most recent notification
  const currentNotification = notifications[0];

  const handleClose = (_event?: React.SyntheticEvent | Event, reason?: string): void => {
    if (reason === 'clickaway') {
      return;
    }
    
    if (currentNotification) {
      dispatch(removeNotification(currentNotification.id));
    }
  };

  // Auto-hide notifications after 6 seconds
  useEffect(() => {
    if (currentNotification?.autoHide !== false) {
      const timer = setTimeout(() => {
        if (currentNotification) {
          dispatch(removeNotification(currentNotification.id));
        }
      }, 6000);

      return () => clearTimeout(timer);
    }
    return undefined;
  }, [currentNotification, dispatch]);

  return (
    <>
      {children}
      {currentNotification && (
        <Snackbar
          open={true}
          autoHideDuration={currentNotification.autoHide !== false ? 6000 : null}
          onClose={handleClose}
          anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
          sx={{ mt: 8 }}
        >
          <Alert
            onClose={handleClose}
            severity={currentNotification.type}
            variant="filled"
            sx={{ width: '100%', minWidth: 300 }}
          >
            <AlertTitle>{currentNotification.title}</AlertTitle>
            {currentNotification.message}
          </Alert>
        </Snackbar>
      )}
    </>
  );
};

export default NotificationProvider;
