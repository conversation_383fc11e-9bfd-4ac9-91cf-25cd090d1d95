// OCTAVE Healthcare Platform - Theme Toggle Component
// Dark/Light Mode Toggle with Accessibility Support

import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  IconButton,
  Tooltip,
  useTheme,
  Box,
  Switch,
  FormControlLabel,
  Typography,
} from '@mui/material';
import {
  LightModeRounded,
  DarkModeRounded,
  SettingsBrightnessRounded,
} from '@mui/icons-material';

import { selectTheme, toggleTheme, setTheme } from '@/store/slices/uiSlice';

// Simple icon toggle button
interface ThemeToggleButtonProps {
  size?: 'small' | 'medium' | 'large';
  showTooltip?: boolean;
}

export const ThemeToggleButton: React.FC<ThemeToggleButtonProps> = ({
  size = 'medium',
  showTooltip = true,
}) => {
  const dispatch = useDispatch();
  const currentTheme = useSelector(selectTheme);
  const theme = useTheme();

  const handleToggle = (): void => {
    dispatch(toggleTheme());
  };

  const icon = currentTheme === 'light' ? <DarkModeRounded /> : <LightModeRounded />;
  const label = `Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`;

  const button = (
    <IconButton
      onClick={handleToggle}
      size={size}
      aria-label={label}
      sx={{
        color: 'text.primary',
        '&:hover': {
          backgroundColor: 'action.hover',
        },
        '&:focus-visible': {
          outline: `2px solid ${theme.palette.primary.main}`,
          outlineOffset: 2,
        },
      }}
    >
      {icon}
    </IconButton>
  );

  if (showTooltip) {
    return <Tooltip title={label}>{button}</Tooltip>;
  }

  return button;
};

// Switch-style toggle
interface ThemeSwitchProps {
  showLabel?: boolean;
  labelPosition?: 'start' | 'end' | 'top' | 'bottom';
  size?: 'small' | 'medium';
}

export const ThemeSwitch: React.FC<ThemeSwitchProps> = ({
  showLabel = true,
  labelPosition = 'end',
  size = 'medium',
}) => {
  const dispatch = useDispatch();
  const currentTheme = useSelector(selectTheme);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    const newTheme = event.target.checked ? 'dark' : 'light';
    dispatch(setTheme(newTheme));
  };

  const switchComponent = (
    <Switch
      checked={currentTheme === 'dark'}
      onChange={handleChange}
      size={size}
      inputProps={{
        'aria-label': 'Toggle dark mode',
      }}
      sx={{
        '& .MuiSwitch-thumb': {
          backgroundColor: currentTheme === 'dark' ? 'primary.main' : 'grey.300',
        },
        '& .MuiSwitch-track': {
          backgroundColor: currentTheme === 'dark' ? 'primary.dark' : 'grey.400',
        },
      }}
    />
  );

  if (!showLabel) {
    return switchComponent;
  }

  const label = (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <LightModeRounded sx={{ fontSize: 16, color: 'text.secondary' }} />
      <Typography variant="body2" color="text.secondary">
        Dark Mode
      </Typography>
      <DarkModeRounded sx={{ fontSize: 16, color: 'text.secondary' }} />
    </Box>
  );

  return (
    <FormControlLabel
      control={switchComponent}
      label={label}
      labelPlacement={labelPosition}
      sx={{
        margin: 0,
        '& .MuiFormControlLabel-label': {
          fontSize: '0.875rem',
        },
      }}
    />
  );
};

// Advanced theme selector with system preference
interface ThemeSelectorProps {
  showSystemOption?: boolean;
  orientation?: 'horizontal' | 'vertical';
}

export const ThemeSelector: React.FC<ThemeSelectorProps> = ({
  showSystemOption = true,
  orientation = 'horizontal',
}) => {
  const dispatch = useDispatch();
  const currentTheme = useSelector(selectTheme);
  const [systemPreference, setSystemPreference] = React.useState<'light' | 'dark'>('light');

  // Detect system theme preference
  React.useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setSystemPreference(mediaQuery.matches ? 'dark' : 'light');

    const handleChange = (e: MediaQueryListEvent): void => {
      setSystemPreference(e.matches ? 'dark' : 'light');
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  const options = [
    {
      value: 'light',
      label: 'Light',
      icon: <LightModeRounded />,
      description: 'Light theme for better visibility in bright environments',
    },
    {
      value: 'dark',
      label: 'Dark',
      icon: <DarkModeRounded />,
      description: 'Dark theme to reduce eye strain in low-light conditions',
    },
    ...(showSystemOption
      ? [
          {
            value: 'system',
            label: 'System',
            icon: <SettingsBrightnessRounded />,
            description: `Follow system preference (currently ${systemPreference})`,
          },
        ]
      : []),
  ];

  const handleSelect = (value: string): void => {
    if (value === 'system') {
      dispatch(setTheme(systemPreference));
    } else {
      dispatch(setTheme(value as 'light' | 'dark'));
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: orientation === 'vertical' ? 'column' : 'row',
        gap: 1,
        p: 1,
        borderRadius: 1,
        backgroundColor: 'background.paper',
        border: 1,
        borderColor: 'divider',
      }}
    >
      {options.map((option) => {
        const isSelected = 
          option.value === 'system' 
            ? false // System option logic can be enhanced
            : currentTheme === option.value;

        return (
          <Tooltip key={option.value} title={option.description}>
            <IconButton
              onClick={() => handleSelect(option.value)}
              size="small"
              aria-label={`Select ${option.label} theme`}
              sx={{
                flexDirection: 'column',
                gap: 0.5,
                p: 1.5,
                borderRadius: 1,
                backgroundColor: isSelected ? 'primary.main' : 'transparent',
                color: isSelected ? 'primary.contrastText' : 'text.primary',
                '&:hover': {
                  backgroundColor: isSelected ? 'primary.dark' : 'action.hover',
                },
                '&:focus-visible': {
                  outline: '2px solid',
                  outlineColor: 'primary.main',
                  outlineOffset: 2,
                },
              }}
            >
              {option.icon}
              <Typography variant="caption" sx={{ fontSize: '0.6875rem' }}>
                {option.label}
              </Typography>
            </IconButton>
          </Tooltip>
        );
      })}
    </Box>
  );
};

// Compact theme toggle for headers/toolbars
export const CompactThemeToggle: React.FC = () => {
  const currentTheme = useSelector(selectTheme);
  
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <LightModeRounded 
        sx={{ 
          fontSize: 16, 
          color: currentTheme === 'light' ? 'primary.main' : 'text.disabled' 
        }} 
      />
      <ThemeSwitch showLabel={false} size="small" />
      <DarkModeRounded 
        sx={{ 
          fontSize: 16, 
          color: currentTheme === 'dark' ? 'primary.main' : 'text.disabled' 
        }} 
      />
    </Box>
  );
};

// Theme preference hook for components
export const useThemePreference = () => {
  const currentTheme = useSelector(selectTheme);
  const dispatch = useDispatch();

  const setThemePreference = React.useCallback((theme: 'light' | 'dark') => {
    dispatch(setTheme(theme));
  }, [dispatch]);

  const toggleThemePreference = React.useCallback(() => {
    dispatch(toggleTheme());
  }, [dispatch]);

  return {
    currentTheme,
    setTheme: setThemePreference,
    toggleTheme: toggleThemePreference,
    isDark: currentTheme === 'dark',
    isLight: currentTheme === 'light',
  };
};

export default {
  ThemeToggleButton,
  ThemeSwitch,
  ThemeSelector,
  CompactThemeToggle,
  useThemePreference,
};
