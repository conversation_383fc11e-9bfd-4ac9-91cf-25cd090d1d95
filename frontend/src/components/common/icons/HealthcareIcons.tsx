// OCTAVE Healthcare Platform - Healthcare-Specific Icon Library
// Comprehensive icon set for medical workflows

import React from 'react';
import {
  // Medical & Healthcare Icons
  LocalHospitalRounded,
  MedicalServicesRounded,
  VaccinesRounded,
  BloodtypeRounded,
  PsychologyRounded,
  FavoriteRounded,
  MonitorHeartRounded,
  
  // Prior Authorization Workflow Icons
  AssignmentRounded,
  AssignmentTurnedInRounded,
  AssignmentLateRounded,
  AssignmentReturnRounded,
  PendingActionsRounded,
  CheckCircleRounded,
  CancelRounded,
  ErrorRounded,
  HourglassEmptyRounded,
  
  // User Role Icons
  PersonRounded,
  AdminPanelSettingsRounded,
  BadgeRounded,
  GroupRounded,
  SupervisorAccountRounded,
  
  // Communication Icons
  PhoneRounded,
  EmailRounded,
  MessageRounded,
  ChatRounded,
  NotificationsRounded,
  
  // Document Icons
  DescriptionRounded,
  AttachFileRounded,
  PictureAsPdfRounded,
  ImageRounded,
  UploadFileRounded,
  DownloadRounded,
  
  // Navigation Icons
  DashboardRounded,
  PeopleRounded,
  FolderRounded,
  AnalyticsRounded,
  SettingsRounded,
  AccountCircleRounded,
  
  // Action Icons
  AddRounded,
  EditRounded,
  DeleteRounded,
  SearchRounded,
  FilterListRounded,
  RefreshRounded,
  SaveRounded,
  
  // Status Icons
  InfoRounded,
  WarningRounded,
  CheckRounded,
  CloseRounded,
  
  // UI Icons
  MenuRounded,
  MoreVertRounded,
  ExpandMoreRounded,
  ChevronRightRounded,
  ArrowBackRounded,
  ArrowForwardRounded,
  
} from '@mui/icons-material';

// Healthcare-specific icon mapping
export const HealthcareIcons = {
  // Medical & Healthcare
  hospital: LocalHospitalRounded,
  medical: MedicalServicesRounded,
  vaccine: VaccinesRounded,
  blood: BloodtypeRounded,
  psychology: PsychologyRounded,
  heart: FavoriteRounded,
  monitor: MonitorHeartRounded,
  
  // Prior Authorization Status
  priorAuth: AssignmentRounded,
  approved: AssignmentTurnedInRounded,
  denied: AssignmentLateRounded,
  returned: AssignmentReturnRounded,
  pending: PendingActionsRounded,
  success: CheckCircleRounded,
  cancelled: CancelRounded,
  error: ErrorRounded,
  waiting: HourglassEmptyRounded,
  
  // User Roles
  user: PersonRounded,
  admin: AdminPanelSettingsRounded,
  provider: BadgeRounded,
  staff: GroupRounded,
  supervisor: SupervisorAccountRounded,
  
  // Communication
  phone: PhoneRounded,
  email: EmailRounded,
  message: MessageRounded,
  chat: ChatRounded,
  notification: NotificationsRounded,
  
  // Documents
  document: DescriptionRounded,
  attachment: AttachFileRounded,
  pdf: PictureAsPdfRounded,
  image: ImageRounded,
  upload: UploadFileRounded,
  download: DownloadRounded,
  
  // Navigation
  dashboard: DashboardRounded,
  patients: PeopleRounded,
  documents: FolderRounded,
  reports: AnalyticsRounded,
  settings: SettingsRounded,
  profile: AccountCircleRounded,
  
  // Actions
  add: AddRounded,
  edit: EditRounded,
  delete: DeleteRounded,
  search: SearchRounded,
  filter: FilterListRounded,
  refresh: RefreshRounded,
  save: SaveRounded,
  
  // Status
  info: InfoRounded,
  warning: WarningRounded,
  check: CheckRounded,
  close: CloseRounded,
  
  // UI
  menu: MenuRounded,
  more: MoreVertRounded,
  expand: ExpandMoreRounded,
  chevronRight: ChevronRightRounded,
  back: ArrowBackRounded,
  forward: ArrowForwardRounded,
} as const;

// Priority level icons with colors
export const PriorityIcons = {
  routine: { icon: InfoRounded, color: '#4caf50' },
  urgent: { icon: WarningRounded, color: '#ff9800' },
  stat: { icon: ErrorRounded, color: '#f44336' },
} as const;

// Status icons with colors
export const StatusIcons = {
  draft: { icon: EditRounded, color: '#9e9e9e' },
  submitted: { icon: AssignmentRounded, color: '#2196f3' },
  underReview: { icon: PendingActionsRounded, color: '#ff9800' },
  moreInfoRequired: { icon: AssignmentReturnRounded, color: '#ff5722' },
  approved: { icon: CheckCircleRounded, color: '#4caf50' },
  denied: { icon: CancelRounded, color: '#f44336' },
  expired: { icon: HourglassEmptyRounded, color: '#795548' },
  cancelled: { icon: CloseRounded, color: '#607d8b' },
  completed: { icon: AssignmentTurnedInRounded, color: '#4caf50' },
  appeal: { icon: AssignmentReturnRounded, color: '#9c27b0' },
} as const;

// Icon component with healthcare-specific styling
interface HealthcareIconProps {
  name: keyof typeof HealthcareIcons;
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
}

export const HealthcareIcon: React.FC<HealthcareIconProps> = ({
  name,
  size = 'medium',
  color,
  className,
}) => {
  const IconComponent = HealthcareIcons[name];
  
  const sizeMap = {
    small: 20,
    medium: 24,
    large: 32,
  };

  return (
    <IconComponent
      style={{
        fontSize: sizeMap[size],
        ...(color && { color }),
      }}
      {...(className && { className })}
    />
  );
};

// Priority icon component
interface PriorityIconProps {
  priority: keyof typeof PriorityIcons;
  size?: 'small' | 'medium' | 'large';
}

export const PriorityIcon: React.FC<PriorityIconProps> = ({
  priority,
  size = 'medium',
}) => {
  const { icon: IconComponent, color } = PriorityIcons[priority];
  
  const sizeMap = {
    small: 16,
    medium: 20,
    large: 24,
  };

  return (
    <IconComponent
      sx={{
        fontSize: sizeMap[size],
        color: color,
      }}
    />
  );
};

// Status icon component
interface StatusIconProps {
  status: keyof typeof StatusIcons;
  size?: 'small' | 'medium' | 'large';
}

export const StatusIcon: React.FC<StatusIconProps> = ({
  status,
  size = 'medium',
}) => {
  const { icon: IconComponent, color } = StatusIcons[status];
  
  const sizeMap = {
    small: 16,
    medium: 20,
    large: 24,
  };

  return (
    <IconComponent
      sx={{
        fontSize: sizeMap[size],
        color: color,
      }}
    />
  );
};

export default HealthcareIcons;
