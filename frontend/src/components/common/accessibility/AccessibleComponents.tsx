// OCTAVE Healthcare Platform - Accessibility Components
// WCAG 2.1 AA Compliant Components for Healthcare Applications

import React, { useId } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Alert,
  Chip,
  IconButton,
  Tooltip,
} from '@mui/material';
import { styled } from '@mui/material/styles';

// Skip link for keyboard navigation
interface SkipLinkProps {
  href: string;
  children: React.ReactNode;
}

const StyledSkipLink = styled('a')(({ theme }) => ({
  position: 'absolute',
  top: -40,
  left: 6,
  zIndex: 9999,
  padding: theme.spacing(1, 2),
  backgroundColor: theme.palette.primary.main,
  color: theme.palette.primary.contrastText,
  textDecoration: 'none',
  borderRadius: theme.shape.borderRadius,
  fontSize: '0.875rem',
  fontWeight: 500,
  '&:focus': {
    top: 6,
  },
}));

export const SkipLink: React.FC<SkipLinkProps> = ({ href, children }) => {
  return <StyledSkipLink href={href}>{children}</StyledSkipLink>;
};

// Accessible form field with proper labeling
interface AccessibleFieldProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  type?: 'text' | 'email' | 'password' | 'tel' | 'url';
  required?: boolean;
  error?: string;
  helperText?: string;
  placeholder?: string;
  disabled?: boolean;
  multiline?: boolean;
  rows?: number;
  autoComplete?: string;
}

export const AccessibleField: React.FC<AccessibleFieldProps> = ({
  label,
  value,
  onChange,
  type = 'text',
  required = false,
  error,
  helperText,
  placeholder,
  disabled = false,
  multiline = false,
  rows,
  autoComplete,

}) => {
  const fieldId = useId();
  const errorId = useId();
  const helperTextId = useId();

  // Remove unused describedBy for now

  return (
    <Box sx={{ mb: 2 }}>
      <TextField
        id={fieldId}
        label={label}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        type={type}
        required={required}
        error={!!error}
        {...(placeholder && { placeholder })}
        disabled={disabled}
        multiline={multiline}
        {...(rows && { rows })}
        {...(autoComplete && { autoComplete })}
        fullWidth
        variant="outlined"
      />
      {error && (
        <Typography
          id={errorId}
          variant="caption"
          color="error"
          sx={{ display: 'block', mt: 0.5 }}
          role="alert"
          aria-live="polite"
        >
          {error}
        </Typography>
      )}
      {helperText && !error && (
        <Typography
          id={helperTextId}
          variant="caption"
          color="text.secondary"
          sx={{ display: 'block', mt: 0.5 }}
        >
          {helperText}
        </Typography>
      )}
    </Box>
  );
};

// Accessible button with proper focus management
interface AccessibleButtonProps {
  children: React.ReactNode;
  onClick: () => void;
  variant?: 'contained' | 'outlined' | 'text';
  color?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  'aria-label'?: string;
  'aria-describedby'?: string;
  type?: 'button' | 'submit' | 'reset';
}

export const AccessibleButton: React.FC<AccessibleButtonProps> = ({
  children,
  onClick,
  variant = 'contained',
  color = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  startIcon,
  endIcon,
  'aria-label': ariaLabel,
  'aria-describedby': ariaDescribedBy,
  type = 'button',
}) => {
  return (
    <Button
      variant={variant}
      color={color}
      size={size}
      disabled={disabled || loading}
      onClick={onClick}
      startIcon={startIcon}
      endIcon={endIcon}
      type={type}
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      aria-busy={loading}
    >
      {loading ? 'Loading...' : children}
    </Button>
  );
};

// Accessible status chip with proper semantics
interface AccessibleStatusChipProps {
  status: string;
  color?: 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  icon?: React.ReactElement;
  'aria-label'?: string;
}

export const AccessibleStatusChip: React.FC<AccessibleStatusChipProps> = ({
  status,
  color = 'default',
  icon,
  'aria-label': ariaLabel,
}) => {
  return (
    <Chip
      label={status}
      color={color}
      {...(icon && { icon })}
      size="small"
      aria-label={ariaLabel || `Status: ${status}`}
    />
  );
};

// Accessible icon button with tooltip
interface AccessibleIconButtonProps {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  disabled?: boolean;
  color?: 'inherit' | 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning';
  size?: 'small' | 'medium' | 'large';
  tooltipPlacement?: 'top' | 'bottom' | 'left' | 'right';
}

export const AccessibleIconButton: React.FC<AccessibleIconButtonProps> = ({
  icon,
  label,
  onClick,
  disabled = false,
  color = 'default',
  size = 'medium',
  tooltipPlacement = 'top',
}) => {
  return (
    <Tooltip title={label} placement={tooltipPlacement}>
      <span>
        <IconButton
          onClick={onClick}
          disabled={disabled}
          color={color}
          size={size}
          aria-label={label}
        >
          {icon}
        </IconButton>
      </span>
    </Tooltip>
  );
};

// Accessible alert with proper ARIA attributes
interface AccessibleAlertProps {
  severity: 'error' | 'warning' | 'info' | 'success';
  title?: string;
  children: React.ReactNode;
  onClose?: (event: React.SyntheticEvent) => void;
  persistent?: boolean;
}

export const AccessibleAlert: React.FC<AccessibleAlertProps> = ({
  severity,
  title,
  children,
  onClose,
  persistent = false,
}) => {
  const alertId = useId();

  return (
    <Alert
      id={alertId}
      severity={severity}
      {...(onClose && { onClose })}
      role={persistent ? 'alert' : 'status'}
      aria-live={persistent ? 'assertive' : 'polite'}
      aria-atomic="true"
    >
      {title && (
        <Typography variant="subtitle2" component="div" sx={{ fontWeight: 600, mb: 0.5 }}>
          {title}
        </Typography>
      )}
      {children}
    </Alert>
  );
};

// Accessible heading with proper hierarchy
interface AccessibleHeadingProps {
  level: 1 | 2 | 3 | 4 | 5 | 6;
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  color?: string;
  gutterBottom?: boolean;
  id?: string;
}

export const AccessibleHeading: React.FC<AccessibleHeadingProps> = ({
  level,
  children,
  variant,
  color,
  gutterBottom = true,
  id,
}) => {
  const component = `h${level}` as const;
  const typographyVariant = variant || component;

  return (
    <Typography
      component={component}
      variant={typographyVariant}
      {...(color && { color })}
      gutterBottom={gutterBottom}
      {...(id && { id })}
    >
      {children}
    </Typography>
  );
};

// Focus trap component for modals and dialogs
interface FocusTrapProps {
  children: React.ReactNode;
  active?: boolean;
}

export const FocusTrap: React.FC<FocusTrapProps> = ({ children, active = true }) => {
  const containerRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!active || !containerRef.current) return;

    const container = containerRef.current;
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          lastElement?.focus();
          e.preventDefault();
        }
      } else {
        if (document.activeElement === lastElement) {
          firstElement?.focus();
          e.preventDefault();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, [active]);

  return (
    <div ref={containerRef} tabIndex={-1}>
      {children}
    </div>
  );
};

export default {
  SkipLink,
  AccessibleField,
  AccessibleButton,
  AccessibleStatusChip,
  AccessibleIconButton,
  AccessibleAlert,
  AccessibleHeading,
  FocusTrap,
};
