// OCTAVE Healthcare Platform - PHI Masking and Redaction Component
// HIPAA Compliant Data Protection with Configurable Sensitivity Levels

import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  Box,
  Typography,
  IconButton,
  Tooltip,
  Chip,
  Alert,
  Switch,
  FormControlLabel,
  useTheme,
} from '@mui/material';
import {
  VisibilityRounded,
  VisibilityOffRounded,
  SecurityRounded,
  WarningRounded,
  ShieldRounded,
} from '@mui/icons-material';

import { selectUserRole } from '@/store/slices/authSlice';
import { UserRole } from '@/types';

export enum PHIType {
  SSN = 'ssn',
  MRN = 'mrn',
  DOB = 'dob',
  PHONE = 'phone',
  EMAIL = 'email',
  ADDRESS = 'address',
  NAME = 'name',
  INSURANCE_ID = 'insurance_id',
  CREDIT_CARD = 'credit_card',
  ACCOUNT_NUMBER = 'account_number',
}

export enum SensitivityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface PHIMaskingConfig {
  phiType: PHIType;
  sensitivityLevel: SensitivityLevel;
  allowedRoles: UserRole[];
  maskingPattern?: string;
  showPartial?: boolean;
  auditAccess?: boolean;
}

interface PHIMaskingComponentProps {
  data: string;
  config: PHIMaskingConfig;
  label?: string;
  showControls?: boolean;
  onAccessAttempt?: (data: string, config: PHIMaskingConfig) => void;
  className?: string;
}

// Default masking patterns for different PHI types
const DEFAULT_MASKING_PATTERNS: Record<PHIType, string> = {
  [PHIType.SSN]: 'XXX-XX-####',
  [PHIType.MRN]: 'MRN-####',
  [PHIType.DOB]: 'XX/XX/XXXX',
  [PHIType.PHONE]: '(XXX) XXX-####',
  [PHIType.EMAIL]: '<EMAIL>',
  [PHIType.ADDRESS]: 'XXXX XXXX St, City, ST',
  [PHIType.NAME]: 'XXXX XXXX',
  [PHIType.INSURANCE_ID]: 'INS-XXXXXXXX',
  [PHIType.CREDIT_CARD]: 'XXXX-XXXX-XXXX-####',
  [PHIType.ACCOUNT_NUMBER]: 'ACC-XXXXXXXX',
};

// Role-based access permissions
const ROLE_PERMISSIONS: Record<UserRole, PHIType[]> = {
  [UserRole.SYSTEM_ADMIN]: Object.values(PHIType),
  [UserRole.PRACTICE_ADMIN]: Object.values(PHIType),
  [UserRole.PROVIDER]: [
    PHIType.SSN, PHIType.MRN, PHIType.DOB, PHIType.PHONE, 
    PHIType.EMAIL, PHIType.ADDRESS, PHIType.NAME, PHIType.INSURANCE_ID
  ],
  [UserRole.STAFF]: [
    PHIType.MRN, PHIType.DOB, PHIType.PHONE, PHIType.EMAIL, 
    PHIType.NAME, PHIType.INSURANCE_ID
  ],
  [UserRole.BILLING]: [
    PHIType.MRN, PHIType.DOB, PHIType.NAME, PHIType.INSURANCE_ID, 
    PHIType.ACCOUNT_NUMBER
  ],
  [UserRole.PATIENT]: [PHIType.NAME],
  [UserRole.READ_ONLY]: [],
};

export const PHIMaskingComponent: React.FC<PHIMaskingComponentProps> = ({
  data,
  config,
  label,
  showControls = true,
  onAccessAttempt,
  className,
}) => {
  const theme = useTheme();
  const userRole = useSelector(selectUserRole) as UserRole;

  const [isRevealed, setIsRevealed] = useState(false);
  const [hasAccess, setHasAccess] = useState(false);
  const [accessDeniedReason, setAccessDeniedReason] = useState<string | null>(null);

  useEffect(() => {
    checkAccess();
  }, [userRole, config]);

  const checkAccess = () => {
    // Check if user role is in allowed roles
    if (!config.allowedRoles.includes(userRole)) {
      setHasAccess(false);
      setAccessDeniedReason('Insufficient role permissions');
      return;
    }

    // Check role-based PHI type permissions
    const allowedPHITypes = ROLE_PERMISSIONS[userRole] || [];
    if (!allowedPHITypes.includes(config.phiType)) {
      setHasAccess(false);
      setAccessDeniedReason('PHI type not accessible for your role');
      return;
    }

    setHasAccess(true);
    setAccessDeniedReason(null);
  };

  const maskData = (data: string): string => {
    if (!data) return '';

    const pattern = config.maskingPattern || DEFAULT_MASKING_PATTERNS[config.phiType];
    
    if (config.showPartial) {
      return applyPartialMasking(data, config.phiType);
    }

    return pattern;
  };

  const applyPartialMasking = (data: string, phiType: PHIType): string => {
    switch (phiType) {
      case PHIType.SSN:
        return data.length >= 4 ? `XXX-XX-${data.slice(-4)}` : 'XXX-XX-XXXX';
      case PHIType.PHONE:
        return data.length >= 4 ? `(XXX) XXX-${data.slice(-4)}` : '(XXX) XXX-XXXX';
      case PHIType.EMAIL:
        const emailParts = data.split('@');
        if (emailParts.length === 2) {
          const username = emailParts[0];
          const domain = emailParts[1];
          const maskedUsername = username.length > 2 
            ? `${username[0]}${'X'.repeat(username.length - 2)}${username.slice(-1)}`
            : 'XXX';
          return `${maskedUsername}@${domain}`;
        }
        return '<EMAIL>';
      case PHIType.CREDIT_CARD:
        return data.length >= 4 ? `XXXX-XXXX-XXXX-${data.slice(-4)}` : 'XXXX-XXXX-XXXX-XXXX';
      case PHIType.NAME:
        const nameParts = data.split(' ');
        if (nameParts.length >= 2) {
          return `${nameParts[0][0]}*** ${nameParts[nameParts.length - 1][0]}***`;
        }
        return 'XXXX XXXX';
      default:
        return maskData(data);
    }
  };

  const handleRevealToggle = () => {
    if (!hasAccess) {
      return;
    }

    const newRevealState = !isRevealed;
    setIsRevealed(newRevealState);

    // Log access attempt
    if (config.auditAccess && onAccessAttempt) {
      onAccessAttempt(data, config);
    }
  };

  const getSensitivityColor = (level: SensitivityLevel) => {
    switch (level) {
      case SensitivityLevel.LOW:
        return theme.palette.info.main;
      case SensitivityLevel.MEDIUM:
        return theme.palette.warning.main;
      case SensitivityLevel.HIGH:
        return theme.palette.error.main;
      case SensitivityLevel.CRITICAL:
        return theme.palette.error.dark;
      default:
        return theme.palette.grey[500];
    }
  };

  const getSensitivityIcon = (level: SensitivityLevel) => {
    switch (level) {
      case SensitivityLevel.LOW:
        return <SecurityRounded />;
      case SensitivityLevel.MEDIUM:
        return <WarningRounded />;
      case SensitivityLevel.HIGH:
      case SensitivityLevel.CRITICAL:
        return <ShieldRounded />;
      default:
        return <SecurityRounded />;
    }
  };

  const displayData = hasAccess && isRevealed ? data : maskData(data);

  return (
    <Box className={className} sx={{ display: 'inline-flex', alignItems: 'center', gap: 1 }}>
      {label && (
        <Typography variant="body2" color="text.secondary">
          {label}:
        </Typography>
      )}

      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Typography
          variant="body2"
          sx={{
            fontFamily: hasAccess && isRevealed ? 'inherit' : 'monospace',
            color: hasAccess && isRevealed ? 'text.primary' : 'text.secondary',
            backgroundColor: hasAccess && isRevealed ? 'transparent' : 'grey.100',
            padding: hasAccess && isRevealed ? 0 : '2px 6px',
            borderRadius: hasAccess && isRevealed ? 0 : 1,
          }}
        >
          {displayData}
        </Typography>

        {showControls && (
          <>
            <Chip
              size="small"
              icon={getSensitivityIcon(config.sensitivityLevel)}
              label={config.sensitivityLevel.toUpperCase()}
              sx={{
                backgroundColor: getSensitivityColor(config.sensitivityLevel),
                color: 'white',
                fontSize: '0.7rem',
                height: 20,
              }}
            />

            {hasAccess ? (
              <Tooltip title={isRevealed ? 'Hide PHI' : 'Reveal PHI'}>
                <IconButton
                  size="small"
                  onClick={handleRevealToggle}
                  sx={{ color: getSensitivityColor(config.sensitivityLevel) }}
                >
                  {isRevealed ? <VisibilityOffRounded /> : <VisibilityRounded />}
                </IconButton>
              </Tooltip>
            ) : (
              <Tooltip title={accessDeniedReason || 'Access denied'}>
                <IconButton size="small" disabled>
                  <VisibilityOffRounded />
                </IconButton>
              </Tooltip>
            )}
          </>
        )}
      </Box>

      {!hasAccess && accessDeniedReason && (
        <Alert severity="warning" sx={{ mt: 1, fontSize: '0.75rem' }}>
          {accessDeniedReason}
        </Alert>
      )}
    </Box>
  );
};

// Hook for managing PHI masking settings
export const usePHIMasking = () => {
  const [globalMaskingEnabled, setGlobalMaskingEnabled] = useState(true);
  const [sensitivityThreshold, setSensitivityThreshold] = useState(SensitivityLevel.MEDIUM);

  const createPHIConfig = (
    phiType: PHIType,
    sensitivityLevel: SensitivityLevel = SensitivityLevel.MEDIUM,
    allowedRoles: UserRole[] = [UserRole.PROVIDER, UserRole.PRACTICE_ADMIN, UserRole.SYSTEM_ADMIN]
  ): PHIMaskingConfig => ({
    phiType,
    sensitivityLevel,
    allowedRoles,
    showPartial: sensitivityLevel === SensitivityLevel.LOW,
    auditAccess: sensitivityLevel >= SensitivityLevel.MEDIUM,
  });

  return {
    globalMaskingEnabled,
    setGlobalMaskingEnabled,
    sensitivityThreshold,
    setSensitivityThreshold,
    createPHIConfig,
  };
};

// Utility function to detect PHI type from data
export const detectPHIType = (data: string): PHIType | null => {
  if (!data) return null;

  // SSN pattern
  if (/^\d{3}-?\d{2}-?\d{4}$/.test(data.replace(/\s/g, ''))) {
    return PHIType.SSN;
  }

  // Phone pattern
  if (/^(\+1[-.\s]?)?\(?[0-9]{3}\)?[-.\s]?[0-9]{3}[-.\s]?[0-9]{4}$/.test(data)) {
    return PHIType.PHONE;
  }

  // Email pattern
  if (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data)) {
    return PHIType.EMAIL;
  }

  // Date pattern (potential DOB)
  if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(data) || /^\d{4}-\d{2}-\d{2}$/.test(data)) {
    return PHIType.DOB;
  }

  // Credit card pattern
  if (/^\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}$/.test(data.replace(/\s/g, ''))) {
    return PHIType.CREDIT_CARD;
  }

  return null;
};

export default PHIMaskingComponent;
