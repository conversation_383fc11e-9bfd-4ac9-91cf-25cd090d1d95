// OCTAVE Healthcare Platform - Breach Notification and Incident UI
// HIPAA Compliant Breach Detection, Reporting, and Notification Management

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  Stepper,
  Step,
  StepLabel,
  StepContent,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Timeline,
  TimelineItem,
  TimelineSeparator,
  TimelineConnector,
  TimelineContent,
  TimelineDot,
  useTheme,
} from '@mui/material';
import {
  ErrorRounded,
  WarningRounded,
  SecurityRounded,
  ReportRounded,
  NotificationsRounded,
  AccessTimeRounded,
  PersonRounded,
  EmailRounded,
  PhoneRounded,
  CheckCircleRounded,
  EditRounded,
  SendRounded,
} from '@mui/icons-material';

export interface SecurityIncident {
  id: string;
  type: 'data_breach' | 'unauthorized_access' | 'system_compromise' | 'phi_exposure' | 'malware' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'detected' | 'investigating' | 'contained' | 'resolved' | 'reported';
  title: string;
  description: string;
  detectedAt: Date;
  reportedAt?: Date;
  resolvedAt?: Date;
  affectedRecords?: number;
  affectedPatients?: string[];
  reportingRequired: boolean;
  reportingDeadline?: Date;
  assignedTo?: string;
  notifications: BreachNotification[];
  timeline: IncidentTimelineEvent[];
}

export interface BreachNotification {
  id: string;
  type: 'patient' | 'hhs' | 'media' | 'internal';
  status: 'pending' | 'sent' | 'failed' | 'acknowledged';
  recipient: string;
  sentAt?: Date;
  acknowledgedAt?: Date;
  method: 'email' | 'mail' | 'phone' | 'website';
  content?: string;
}

export interface IncidentTimelineEvent {
  id: string;
  timestamp: Date;
  type: 'detection' | 'investigation' | 'containment' | 'notification' | 'resolution';
  description: string;
  userId: string;
  automated: boolean;
}

interface BreachNotificationUIProps {
  incidents: SecurityIncident[];
  onCreateIncident?: (incident: Partial<SecurityIncident>) => void;
  onUpdateIncident?: (id: string, updates: Partial<SecurityIncident>) => void;
  onSendNotification?: (incidentId: string, notification: Partial<BreachNotification>) => void;
}

const incidentSteps = [
  'Detection & Assessment',
  'Investigation & Containment',
  'Risk Assessment',
  'Notification Planning',
  'Notification Execution',
  'Resolution & Documentation'
];

export const BreachNotificationUI: React.FC<BreachNotificationUIProps> = ({
  incidents,
  onCreateIncident,
  onUpdateIncident,
  onSendNotification,
}) => {
  const theme = useTheme();
  const [showIncidentDialog, setShowIncidentDialog] = useState(false);
  const [showNotificationDialog, setShowNotificationDialog] = useState(false);
  const [selectedIncident, setSelectedIncident] = useState<SecurityIncident | null>(null);
  const [newIncident, setNewIncident] = useState<Partial<SecurityIncident>>({
    type: 'data_breach',
    severity: 'medium',
    title: '',
    description: '',
    reportingRequired: false,
  });

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return theme.palette.error.dark;
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
      default:
        return theme.palette.info.main;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'resolved':
        return 'success';
      case 'contained':
        return 'info';
      case 'investigating':
        return 'warning';
      case 'detected':
      default:
        return 'error';
    }
  };

  const getIncidentIcon = (type: string) => {
    switch (type) {
      case 'data_breach':
        return <ErrorRounded />;
      case 'unauthorized_access':
        return <SecurityRounded />;
      case 'system_compromise':
        return <WarningRounded />;
      case 'phi_exposure':
        return <PersonRounded />;
      default:
        return <ReportRounded />;
    }
  };

  const getStepForStatus = (status: string): number => {
    switch (status) {
      case 'detected':
        return 0;
      case 'investigating':
        return 1;
      case 'contained':
        return 3;
      case 'reported':
        return 4;
      case 'resolved':
        return 5;
      default:
        return 0;
    }
  };

  const isReportingRequired = (incident: Partial<SecurityIncident>): boolean => {
    return incident.severity === 'high' || incident.severity === 'critical' ||
           incident.type === 'data_breach' || incident.type === 'phi_exposure';
  };

  const calculateReportingDeadline = (detectedAt: Date): Date => {
    // HIPAA requires notification within 60 days for patients, 60 days for HHS
    const deadline = new Date(detectedAt);
    deadline.setDate(deadline.getDate() + 60);
    return deadline;
  };

  const handleCreateIncident = () => {
    const incident: Partial<SecurityIncident> = {
      ...newIncident,
      detectedAt: new Date(),
      status: 'detected',
      reportingRequired: isReportingRequired(newIncident),
      reportingDeadline: isReportingRequired(newIncident) ? 
        calculateReportingDeadline(new Date()) : undefined,
      notifications: [],
      timeline: [{
        id: `timeline_${Date.now()}`,
        timestamp: new Date(),
        type: 'detection',
        description: 'Incident detected and reported',
        userId: 'current_user',
        automated: false,
      }],
    };

    onCreateIncident?.(incident);
    setNewIncident({
      type: 'data_breach',
      severity: 'medium',
      title: '',
      description: '',
      reportingRequired: false,
    });
    setShowIncidentDialog(false);
  };

  const criticalIncidents = incidents.filter(i => i.severity === 'critical' && i.status !== 'resolved');
  const pendingNotifications = incidents.reduce((acc, incident) => 
    acc + incident.notifications.filter(n => n.status === 'pending').length, 0
  );

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">Breach Notification & Incident Management</Typography>
        <Button
          variant="contained"
          startIcon={<ReportRounded />}
          onClick={() => setShowIncidentDialog(true)}
          color="error"
        >
          Report Incident
        </Button>
      </Box>

      {/* Critical Alerts */}
      {criticalIncidents.length > 0 && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="body1" fontWeight="bold">
            {criticalIncidents.length} Critical Security Incident{criticalIncidents.length > 1 ? 's' : ''} Require Immediate Attention
          </Typography>
          <Typography variant="body2">
            These incidents may require regulatory reporting under HIPAA breach notification rules.
          </Typography>
        </Alert>
      )}

      {/* Summary Cards */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3, flexWrap: 'wrap' }}>
        <Card sx={{ minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h3" color="error.main">
              {criticalIncidents.length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Critical Incidents
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{ minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h3" color="warning.main">
              {pendingNotifications}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Pending Notifications
            </Typography>
          </CardContent>
        </Card>

        <Card sx={{ minWidth: 200 }}>
          <CardContent sx={{ textAlign: 'center' }}>
            <Typography variant="h3" color="info.main">
              {incidents.filter(i => i.reportingRequired && !i.reportedAt).length}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Reporting Required
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* Incidents List */}
      <Card>
        <CardHeader title="Security Incidents" />
        <CardContent>
          <List>
            {incidents.slice(0, 10).map((incident) => (
              <ListItem
                key={incident.id}
                button
                onClick={() => setSelectedIncident(incident)}
                sx={{
                  borderLeft: `4px solid ${getSeverityColor(incident.severity)}`,
                  mb: 1,
                  borderRadius: 1,
                }}
              >
                <ListItemIcon>
                  {getIncidentIcon(incident.type)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body1" fontWeight="medium">
                        {incident.title}
                      </Typography>
                      <Chip
                        label={incident.severity.toUpperCase()}
                        size="small"
                        sx={{
                          backgroundColor: getSeverityColor(incident.severity),
                          color: 'white',
                        }}
                      />
                      <Chip
                        label={incident.status.toUpperCase()}
                        size="small"
                        color={getStatusColor(incident.status) as any}
                        variant="outlined"
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {incident.description}
                      </Typography>
                      <Typography variant="caption" display="block">
                        Detected: {incident.detectedAt.toLocaleString()}
                        {incident.reportingRequired && (
                          <Chip
                            label="Reporting Required"
                            size="small"
                            color="warning"
                            sx={{ ml: 1 }}
                          />
                        )}
                      </Typography>
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <Tooltip title="View Details">
                    <IconButton onClick={() => setSelectedIncident(incident)}>
                      <EditRounded />
                    </IconButton>
                  </Tooltip>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>

      {/* Incident Detail Dialog */}
      <Dialog
        open={!!selectedIncident}
        onClose={() => setSelectedIncident(null)}
        maxWidth="lg"
        fullWidth
      >
        {selectedIncident && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getIncidentIcon(selectedIncident.type)}
                <Typography variant="h6">{selectedIncident.title}</Typography>
                <Chip
                  label={selectedIncident.severity.toUpperCase()}
                  size="small"
                  sx={{
                    backgroundColor: getSeverityColor(selectedIncident.severity),
                    color: 'white',
                  }}
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Box sx={{ display: 'flex', gap: 3 }}>
                {/* Incident Progress */}
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h6" gutterBottom>
                    Incident Progress
                  </Typography>
                  <Stepper activeStep={getStepForStatus(selectedIncident.status)} orientation="vertical">
                    {incidentSteps.map((label, index) => (
                      <Step key={label}>
                        <StepLabel>{label}</StepLabel>
                        <StepContent>
                          <Typography variant="body2" color="text.secondary">
                            {index === 0 && 'Incident detected and initial assessment completed'}
                            {index === 1 && 'Investigation in progress, containment measures applied'}
                            {index === 2 && 'Risk assessment and impact analysis'}
                            {index === 3 && 'Planning notification strategy and timeline'}
                            {index === 4 && 'Sending required notifications to affected parties'}
                            {index === 5 && 'Incident resolved and documentation complete'}
                          </Typography>
                        </StepContent>
                      </Step>
                    ))}
                  </Stepper>
                </Box>

                {/* Notifications */}
                <Box sx={{ flex: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6">
                      Notifications ({selectedIncident.notifications.length})
                    </Typography>
                    <Button
                      size="small"
                      startIcon={<SendRounded />}
                      onClick={() => setShowNotificationDialog(true)}
                    >
                      Send Notification
                    </Button>
                  </Box>

                  <List dense>
                    {selectedIncident.notifications.map((notification) => (
                      <ListItem key={notification.id}>
                        <ListItemIcon>
                          {notification.method === 'email' ? <EmailRounded /> : <PhoneRounded />}
                        </ListItemIcon>
                        <ListItemText
                          primary={`${notification.type.toUpperCase()}: ${notification.recipient}`}
                          secondary={
                            <Box>
                              <Chip
                                label={notification.status}
                                size="small"
                                color={notification.status === 'sent' ? 'success' : 'warning'}
                              />
                              {notification.sentAt && (
                                <Typography variant="caption" display="block">
                                  Sent: {notification.sentAt.toLocaleString()}
                                </Typography>
                              )}
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                  </List>

                  {selectedIncident.reportingRequired && !selectedIncident.reportedAt && (
                    <Alert severity="warning" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        <strong>HIPAA Reporting Required</strong>
                        <br />
                        Deadline: {selectedIncident.reportingDeadline?.toLocaleDateString()}
                      </Typography>
                    </Alert>
                  )}
                </Box>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setSelectedIncident(null)}>Close</Button>
              <Button variant="contained" color="primary">
                Update Status
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Create Incident Dialog */}
      <Dialog
        open={showIncidentDialog}
        onClose={() => setShowIncidentDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Report Security Incident</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              fullWidth
              label="Incident Title"
              value={newIncident.title}
              onChange={(e) => setNewIncident(prev => ({ ...prev, title: e.target.value }))}
            />

            <TextField
              fullWidth
              multiline
              rows={4}
              label="Description"
              value={newIncident.description}
              onChange={(e) => setNewIncident(prev => ({ ...prev, description: e.target.value }))}
            />

            <FormControl fullWidth>
              <InputLabel>Incident Type</InputLabel>
              <Select
                value={newIncident.type}
                onChange={(e) => setNewIncident(prev => ({ ...prev, type: e.target.value as any }))}
                label="Incident Type"
              >
                <MenuItem value="data_breach">Data Breach</MenuItem>
                <MenuItem value="unauthorized_access">Unauthorized Access</MenuItem>
                <MenuItem value="system_compromise">System Compromise</MenuItem>
                <MenuItem value="phi_exposure">PHI Exposure</MenuItem>
                <MenuItem value="malware">Malware</MenuItem>
                <MenuItem value="other">Other</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Severity</InputLabel>
              <Select
                value={newIncident.severity}
                onChange={(e) => setNewIncident(prev => ({ ...prev, severity: e.target.value as any }))}
                label="Severity"
              >
                <MenuItem value="low">Low</MenuItem>
                <MenuItem value="medium">Medium</MenuItem>
                <MenuItem value="high">High</MenuItem>
                <MenuItem value="critical">Critical</MenuItem>
              </Select>
            </FormControl>

            {isReportingRequired(newIncident) && (
              <Alert severity="warning">
                <Typography variant="body2">
                  <strong>HIPAA Reporting Required:</strong> This incident type and severity 
                  requires notification under HIPAA breach notification rules.
                </Typography>
              </Alert>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowIncidentDialog(false)}>Cancel</Button>
          <Button
            onClick={handleCreateIncident}
            variant="contained"
            color="error"
            disabled={!newIncident.title || !newIncident.description}
          >
            Report Incident
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BreachNotificationUI;
