// OCTAVE Healthcare Platform - Minimum Necessary Access UI Controls
// HIPAA Compliant Data Visibility Based on User Role and Context

import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Chip,
  Alert,
  Switch,
  FormControlLabel,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Tooltip,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  useTheme,
} from '@mui/material';
import {
  VisibilityRounded,
  VisibilityOffRounded,
  SecurityRounded,
  PersonRounded,
  ExpandMoreRounded,
  InfoRounded,
  WarningRounded,
  CheckCircleRounded,
  LockRounded,
} from '@mui/icons-material';

import { selectUserRole } from '@/store/slices/authSlice';
import { UserRole } from '@/types';

export interface AccessContext {
  purpose: 'treatment' | 'payment' | 'operations' | 'research' | 'audit';
  patientId?: string;
  procedureId?: string;
  timeframe?: {
    start: Date;
    end: Date;
  };
  justification?: string;
}

export interface DataField {
  id: string;
  name: string;
  category: 'demographic' | 'clinical' | 'financial' | 'administrative';
  sensitivityLevel: 'low' | 'medium' | 'high' | 'critical';
  requiredRoles: UserRole[];
  requiredPurposes: AccessContext['purpose'][];
  isVisible: boolean;
  accessReason?: string;
}

interface MinimumNecessaryAccessProps {
  context: AccessContext;
  dataFields: DataField[];
  onAccessRequest?: (fieldId: string, justification: string) => void;
  onContextChange?: (context: AccessContext) => void;
  showAccessLog?: boolean;
}

export const MinimumNecessaryAccess: React.FC<MinimumNecessaryAccessProps> = ({
  context,
  dataFields,
  onAccessRequest,
  onContextChange,
  showAccessLog = true,
}) => {
  const theme = useTheme();
  const userRole = useSelector(selectUserRole) as UserRole;

  const [showJustificationDialog, setShowJustificationDialog] = useState(false);
  const [selectedField, setSelectedField] = useState<DataField | null>(null);
  const [justification, setJustification] = useState('');
  const [accessLog, setAccessLog] = useState<Array<{
    fieldId: string;
    timestamp: Date;
    justification: string;
  }>>([]);

  // Calculate which fields should be visible based on minimum necessary principle
  const calculateFieldVisibility = (field: DataField): boolean => {
    // Check role permissions
    if (!field.requiredRoles.includes(userRole)) {
      return false;
    }

    // Check purpose alignment
    if (!field.requiredPurposes.includes(context.purpose)) {
      return false;
    }

    // Additional context-based rules
    switch (context.purpose) {
      case 'treatment':
        // Treatment requires clinical and demographic data
        return field.category === 'clinical' || field.category === 'demographic';
      
      case 'payment':
        // Payment requires financial and basic demographic data
        return field.category === 'financial' || 
               (field.category === 'demographic' && field.sensitivityLevel !== 'critical');
      
      case 'operations':
        // Operations may need administrative and some clinical data
        return field.category === 'administrative' || 
               (field.category === 'clinical' && field.sensitivityLevel !== 'critical');
      
      case 'research':
        // Research typically needs de-identified or aggregated data
        return field.sensitivityLevel === 'low' || field.sensitivityLevel === 'medium';
      
      case 'audit':
        // Audit may need access to all data types but with justification
        return true;
      
      default:
        return false;
    }
  };

  const visibleFields = dataFields.filter(calculateFieldVisibility);
  const hiddenFields = dataFields.filter(field => !calculateFieldVisibility(field));

  const handleAccessRequest = (field: DataField) => {
    setSelectedField(field);
    setShowJustificationDialog(true);
  };

  const handleJustificationSubmit = () => {
    if (selectedField && justification.trim()) {
      onAccessRequest?.(selectedField.id, justification);
      setAccessLog(prev => [...prev, {
        fieldId: selectedField.id,
        timestamp: new Date(),
        justification: justification.trim(),
      }]);
      setJustification('');
      setSelectedField(null);
      setShowJustificationDialog(false);
    }
  };

  const getSensitivityColor = (level: string) => {
    switch (level) {
      case 'critical':
        return theme.palette.error.dark;
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
      default:
        return theme.palette.success.main;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'clinical':
        return <SecurityRounded />;
      case 'demographic':
        return <PersonRounded />;
      case 'financial':
        return <LockRounded />;
      case 'administrative':
        return <InfoRounded />;
      default:
        return <InfoRounded />;
    }
  };

  const getPurposeDescription = (purpose: AccessContext['purpose']) => {
    switch (purpose) {
      case 'treatment':
        return 'Providing healthcare services to the patient';
      case 'payment':
        return 'Processing payment and billing activities';
      case 'operations':
        return 'Healthcare operations and quality improvement';
      case 'research':
        return 'Research activities with appropriate approvals';
      case 'audit':
        return 'Compliance audit and regulatory review';
      default:
        return 'General healthcare activities';
    }
  };

  return (
    <Box>
      {/* Access Context Information */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          title="Minimum Necessary Access Control"
          subheader="Data visibility based on HIPAA minimum necessary standard"
        />
        <CardContent>
          <Box sx={{ display: 'flex', gap: 2, mb: 2, flexWrap: 'wrap' }}>
            <Chip
              icon={<PersonRounded />}
              label={`Role: ${userRole}`}
              color="primary"
            />
            <Chip
              icon={<SecurityRounded />}
              label={`Purpose: ${context.purpose}`}
              color="secondary"
            />
            {context.patientId && (
              <Chip
                icon={<InfoRounded />}
                label={`Patient: ${context.patientId}`}
                variant="outlined"
              />
            )}
          </Box>

          <Typography variant="body2" color="text.secondary">
            {getPurposeDescription(context.purpose)}
          </Typography>

          <Alert severity="info" sx={{ mt: 2 }}>
            <Typography variant="body2">
              <strong>HIPAA Minimum Necessary:</strong> You can only access the minimum amount of 
              protected health information necessary to accomplish your intended purpose.
            </Typography>
          </Alert>
        </CardContent>
      </Card>

      {/* Visible Data Fields */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          title={`Accessible Data Fields (${visibleFields.length})`}
          subheader="Data you can access based on your role and current context"
        />
        <CardContent>
          {visibleFields.length === 0 ? (
            <Alert severity="warning">
              No data fields are accessible for your current role and purpose.
            </Alert>
          ) : (
            <List>
              {visibleFields.map((field) => (
                <ListItem key={field.id}>
                  <ListItemIcon>
                    {getCategoryIcon(field.category)}
                  </ListItemIcon>
                  <ListItemText
                    primary={field.name}
                    secondary={
                      <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                        <Chip
                          label={field.category}
                          size="small"
                          variant="outlined"
                        />
                        <Chip
                          label={field.sensitivityLevel}
                          size="small"
                          sx={{
                            backgroundColor: getSensitivityColor(field.sensitivityLevel),
                            color: 'white',
                          }}
                        />
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Tooltip title="Data is accessible">
                      <CheckCircleRounded sx={{ color: 'success.main' }} />
                    </Tooltip>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* Hidden Data Fields */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreRounded />}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <VisibilityOffRounded />
            <Typography>Restricted Data Fields ({hiddenFields.length})</Typography>
            <Chip
              label="Requires Justification"
              size="small"
              color="warning"
            />
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          {hiddenFields.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              All available data fields are accessible for your current context.
            </Typography>
          ) : (
            <List>
              {hiddenFields.map((field) => (
                <ListItem key={field.id}>
                  <ListItemIcon>
                    {getCategoryIcon(field.category)}
                  </ListItemIcon>
                  <ListItemText
                    primary={field.name}
                    secondary={
                      <Box>
                        <Box sx={{ display: 'flex', gap: 1, mt: 0.5, mb: 1 }}>
                          <Chip
                            label={field.category}
                            size="small"
                            variant="outlined"
                          />
                          <Chip
                            label={field.sensitivityLevel}
                            size="small"
                            sx={{
                              backgroundColor: getSensitivityColor(field.sensitivityLevel),
                              color: 'white',
                            }}
                          />
                        </Box>
                        <Typography variant="caption" color="text.secondary">
                          Required roles: {field.requiredRoles.join(', ')}
                          <br />
                          Required purposes: {field.requiredPurposes.join(', ')}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => handleAccessRequest(field)}
                      startIcon={<LockRounded />}
                    >
                      Request Access
                    </Button>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </AccordionDetails>
      </Accordion>

      {/* Access Log */}
      {showAccessLog && accessLog.length > 0 && (
        <Card sx={{ mt: 3 }}>
          <CardHeader title="Access Requests Log" />
          <CardContent>
            <List>
              {accessLog.slice(-5).map((entry, index) => (
                <ListItem key={index}>
                  <ListItemIcon>
                    <InfoRounded />
                  </ListItemIcon>
                  <ListItemText
                    primary={`Access requested for field: ${entry.fieldId}`}
                    secondary={
                      <Box>
                        <Typography variant="caption" display="block">
                          {entry.timestamp.toLocaleString()}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Justification: {entry.justification}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </CardContent>
        </Card>
      )}

      {/* Access Justification Dialog */}
      <Dialog
        open={showJustificationDialog}
        onClose={() => setShowJustificationDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <LockRounded />
            <Typography variant="h6">Request Data Access</Typography>
          </Box>
        </DialogTitle>
        <DialogContent>
          {selectedField && (
            <Box>
              <Typography variant="body1" gutterBottom>
                You are requesting access to: <strong>{selectedField.name}</strong>
              </Typography>
              
              <Alert severity="warning" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>HIPAA Requirement:</strong> You must provide a valid justification 
                  for accessing this data that demonstrates it is necessary for your intended purpose.
                </Typography>
              </Alert>

              <TextField
                fullWidth
                multiline
                rows={4}
                label="Justification for Access"
                value={justification}
                onChange={(e) => setJustification(e.target.value)}
                placeholder="Explain why access to this data is necessary for your current task..."
                helperText="This justification will be logged for audit purposes"
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowJustificationDialog(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleJustificationSubmit}
            variant="contained"
            disabled={!justification.trim()}
          >
            Submit Request
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

// Hook for managing minimum necessary access
export const useMinimumNecessaryAccess = (userRole: UserRole) => {
  const [context, setContext] = useState<AccessContext>({
    purpose: 'treatment',
  });

  const updateContext = (newContext: Partial<AccessContext>) => {
    setContext(prev => ({ ...prev, ...newContext }));
  };

  const checkFieldAccess = (field: DataField): boolean => {
    return field.requiredRoles.includes(userRole) && 
           field.requiredPurposes.includes(context.purpose);
  };

  return {
    context,
    updateContext,
    checkFieldAccess,
  };
};

export default MinimumNecessaryAccess;
