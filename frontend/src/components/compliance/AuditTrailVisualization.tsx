// OCTAVE Healthcare Platform - Audit Trail Visualization Component
// HIPAA Compliant Audit Log Display with Advanced Filtering

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Typography,
  Alert,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  useTheme,
} from '@mui/material';
import {
  SearchRounded,
  FilterListRounded,
  ExpandMoreRounded,
  SecurityRounded,
  PersonRounded,
  AccessTimeRounded,
  ComputerRounded,
  WarningRounded,
  CheckCircleRounded,
  ErrorRounded,
  InfoRounded,
  VisibilityRounded,
  EditRounded,
  DeleteRounded,
  DownloadRounded,
} from '@mui/icons-material';

import { AuditLog } from '@/types';

export interface AuditTrailFilter {
  userId?: string;
  action?: string;
  resource?: string;
  dateRange?: {
    start: Date;
    end: Date;
  };
  success?: boolean;
  ipAddress?: string;
  severity?: 'low' | 'medium' | 'high' | 'critical';
}

interface AuditTrailVisualizationProps {
  auditLogs: AuditLog[];
  loading?: boolean;
  onFilterChange?: (filters: AuditTrailFilter) => void;
  onExport?: (filters: AuditTrailFilter) => void;
  showAdvancedFilters?: boolean;
}

export const AuditTrailVisualization: React.FC<AuditTrailVisualizationProps> = ({
  auditLogs,
  loading = false,
  onFilterChange,
  onExport,
  showAdvancedFilters = true,
}) => {
  const theme = useTheme();

  // State
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<AuditTrailFilter>({});
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(false);

  // Filter options
  const actionTypes = [...new Set(auditLogs.map(log => log.action))];
  const resourceTypes = [...new Set(auditLogs.map(log => log.resource))];
  const userIds = [...new Set(auditLogs.map(log => log.userId))];

  useEffect(() => {
    if (onFilterChange) {
      onFilterChange({ ...filters, query: searchQuery });
    }
  }, [filters, searchQuery, onFilterChange]);

  const handleFilterChange = (field: keyof AuditTrailFilter, value: any) => {
    setFilters(prev => ({ ...prev, [field]: value }));
  };

  const handlePageChange = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleRowsPerPageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const getActionIcon = (action: string) => {
    const actionLower = action.toLowerCase();
    if (actionLower.includes('view') || actionLower.includes('read')) {
      return <VisibilityRounded />;
    }
    if (actionLower.includes('edit') || actionLower.includes('update')) {
      return <EditRounded />;
    }
    if (actionLower.includes('delete')) {
      return <DeleteRounded />;
    }
    if (actionLower.includes('login') || actionLower.includes('auth')) {
      return <SecurityRounded />;
    }
    return <InfoRounded />;
  };

  const getSuccessIcon = (success: boolean) => {
    return success ? (
      <CheckCircleRounded sx={{ color: 'success.main' }} />
    ) : (
      <ErrorRounded sx={{ color: 'error.main' }} />
    );
  };

  const getSeverityColor = (action: string, success: boolean) => {
    if (!success) return 'error';
    
    const actionLower = action.toLowerCase();
    if (actionLower.includes('delete') || actionLower.includes('admin')) {
      return 'error';
    }
    if (actionLower.includes('edit') || actionLower.includes('update')) {
      return 'warning';
    }
    if (actionLower.includes('phi') || actionLower.includes('patient')) {
      return 'info';
    }
    return 'default';
  };

  const formatTimestamp = (timestamp: Date): string => {
    return new Date(timestamp).toLocaleString();
  };

  const filteredLogs = auditLogs.filter(log => {
    if (searchQuery && !Object.values(log).some(value => 
      String(value).toLowerCase().includes(searchQuery.toLowerCase())
    )) {
      return false;
    }

    if (filters.userId && log.userId !== filters.userId) {
      return false;
    }

    if (filters.action && log.action !== filters.action) {
      return false;
    }

    if (filters.resource && log.resource !== filters.resource) {
      return false;
    }

    if (filters.success !== undefined && log.success !== filters.success) {
      return false;
    }

    if (filters.ipAddress && log.ipAddress !== filters.ipAddress) {
      return false;
    }

    if (filters.dateRange) {
      const logDate = new Date(log.timestamp);
      if (logDate < filters.dateRange.start || logDate > filters.dateRange.end) {
        return false;
      }
    }

    return true;
  });

  const paginatedLogs = filteredLogs.slice(
    page * rowsPerPage,
    page * rowsPerPage + rowsPerPage
  );

  return (
    <Card>
      <CardHeader
        title="Audit Trail"
        subheader={`${filteredLogs.length} entries found`}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Tooltip title="Export Audit Log">
              <IconButton onClick={() => onExport?.(filters)}>
                <DownloadRounded />
              </IconButton>
            </Tooltip>
          </Box>
        }
      />

      <CardContent>
        {/* Search and Basic Filters */}
        <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <TextField
            placeholder="Search audit logs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: <SearchRounded sx={{ mr: 1, color: 'text.secondary' }} />,
            }}
            sx={{ minWidth: 300 }}
          />

          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Action</InputLabel>
            <Select
              value={filters.action || ''}
              onChange={(e) => handleFilterChange('action', e.target.value || undefined)}
              label="Action"
            >
              <MenuItem value="">All Actions</MenuItem>
              {actionTypes.map(action => (
                <MenuItem key={action} value={action}>{action}</MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 150 }}>
            <InputLabel>Resource</InputLabel>
            <Select
              value={filters.resource || ''}
              onChange={(e) => handleFilterChange('resource', e.target.value || undefined)}
              label="Resource"
            >
              <MenuItem value="">All Resources</MenuItem>
              {resourceTypes.map(resource => (
                <MenuItem key={resource} value={resource}>{resource}</MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.success === undefined ? '' : filters.success ? 'success' : 'failure'}
              onChange={(e) => {
                const value = e.target.value;
                handleFilterChange('success', 
                  value === '' ? undefined : value === 'success'
                );
              }}
              label="Status"
            >
              <MenuItem value="">All</MenuItem>
              <MenuItem value="success">Success</MenuItem>
              <MenuItem value="failure">Failure</MenuItem>
            </Select>
          </FormControl>
        </Box>

        {/* Advanced Filters */}
        {showAdvancedFilters && (
          <Accordion 
            expanded={expandedAccordion === 'filters'} 
            onChange={(e, isExpanded) => setExpandedAccordion(isExpanded ? 'filters' : false)}
          >
            <AccordionSummary expandIcon={<ExpandMoreRounded />}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <FilterListRounded />
                <Typography>Advanced Filters</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                <FormControl sx={{ minWidth: 150 }}>
                  <InputLabel>User</InputLabel>
                  <Select
                    value={filters.userId || ''}
                    onChange={(e) => handleFilterChange('userId', e.target.value || undefined)}
                    label="User"
                  >
                    <MenuItem value="">All Users</MenuItem>
                    {userIds.map(userId => (
                      <MenuItem key={userId} value={userId}>User {userId}</MenuItem>
                    ))}
                  </Select>
                </FormControl>

                <TextField
                  label="IP Address"
                  value={filters.ipAddress || ''}
                  onChange={(e) => handleFilterChange('ipAddress', e.target.value || undefined)}
                  sx={{ minWidth: 150 }}
                />

                <TextField
                  label="Start Date"
                  type="datetime-local"
                  value={filters.dateRange?.start ? 
                    filters.dateRange.start.toISOString().slice(0, 16) : ''
                  }
                  onChange={(e) => {
                    const start = e.target.value ? new Date(e.target.value) : undefined;
                    handleFilterChange('dateRange', start ? 
                      { ...filters.dateRange, start } : undefined
                    );
                  }}
                  InputLabelProps={{ shrink: true }}
                  sx={{ minWidth: 200 }}
                />

                <TextField
                  label="End Date"
                  type="datetime-local"
                  value={filters.dateRange?.end ? 
                    filters.dateRange.end.toISOString().slice(0, 16) : ''
                  }
                  onChange={(e) => {
                    const end = e.target.value ? new Date(e.target.value) : undefined;
                    handleFilterChange('dateRange', end ? 
                      { ...filters.dateRange, end } : undefined
                    );
                  }}
                  InputLabelProps={{ shrink: true }}
                  sx={{ minWidth: 200 }}
                />
              </Box>
            </AccordionDetails>
          </Accordion>
        )}

        {/* Audit Log Table */}
        <TableContainer component={Paper} sx={{ mt: 2 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Timestamp</TableCell>
                <TableCell>User</TableCell>
                <TableCell>Action</TableCell>
                <TableCell>Resource</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>IP Address</TableCell>
                <TableCell>Details</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {paginatedLogs.map((log) => (
                <TableRow 
                  key={log.id}
                  hover
                  onClick={() => setSelectedLog(log)}
                  sx={{ cursor: 'pointer' }}
                >
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AccessTimeRounded sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {formatTimestamp(log.timestamp)}
                      </Typography>
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PersonRounded sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        User {log.userId}
                      </Typography>
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {getActionIcon(log.action)}
                      <Typography variant="body2">
                        {log.action}
                      </Typography>
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    <Typography variant="body2">
                      {log.resource}
                    </Typography>
                  </TableCell>
                  
                  <TableCell>
                    <Chip
                      icon={getSuccessIcon(log.success)}
                      label={log.success ? 'Success' : 'Failure'}
                      color={getSeverityColor(log.action, log.success) as any}
                      size="small"
                    />
                  </TableCell>
                  
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ComputerRounded sx={{ fontSize: 16, color: 'text.secondary' }} />
                      <Typography variant="body2">
                        {log.ipAddress}
                      </Typography>
                    </Box>
                  </TableCell>
                  
                  <TableCell>
                    {log.details && (
                      <Tooltip title="View Details">
                        <IconButton size="small">
                          <InfoRounded />
                        </IconButton>
                      </Tooltip>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={filteredLogs.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
        />

        {/* No Results */}
        {filteredLogs.length === 0 && !loading && (
          <Alert severity="info" sx={{ mt: 2 }}>
            No audit logs found matching the current filters.
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};

// Audit Log Detail Dialog Component
export const AuditLogDetailDialog: React.FC<{
  log: AuditLog | null;
  open: boolean;
  onClose: () => void;
}> = ({ log, open, onClose }) => {
  if (!log) return null;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {getActionIcon(log.action)}
          <Typography variant="h6">Audit Log Details</Typography>
        </Box>
      </DialogTitle>
      <DialogContent>
        <List>
          <ListItem>
            <ListItemIcon><AccessTimeRounded /></ListItemIcon>
            <ListItemText
              primary="Timestamp"
              secondary={formatTimestamp(log.timestamp)}
            />
          </ListItem>
          <ListItem>
            <ListItemIcon><PersonRounded /></ListItemIcon>
            <ListItemText
              primary="User ID"
              secondary={log.userId}
            />
          </ListItem>
          <ListItem>
            <ListItemIcon>{getActionIcon(log.action)}</ListItemIcon>
            <ListItemText
              primary="Action"
              secondary={log.action}
            />
          </ListItem>
          <ListItem>
            <ListItemIcon><ComputerRounded /></ListItemIcon>
            <ListItemText
              primary="IP Address"
              secondary={log.ipAddress}
            />
          </ListItem>
          <ListItem>
            <ListItemIcon><InfoRounded /></ListItemIcon>
            <ListItemText
              primary="User Agent"
              secondary={log.userAgent}
            />
          </ListItem>
          {log.details && (
            <ListItem>
              <ListItemIcon><InfoRounded /></ListItemIcon>
              <ListItemText
                primary="Additional Details"
                secondary={
                  <pre style={{ fontSize: '0.8rem', whiteSpace: 'pre-wrap' }}>
                    {JSON.stringify(log.details, null, 2)}
                  </pre>
                }
              />
            </ListItem>
          )}
        </List>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default AuditTrailVisualization;
