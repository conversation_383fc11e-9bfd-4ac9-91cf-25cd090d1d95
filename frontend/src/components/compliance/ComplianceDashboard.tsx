// OCTAVE Healthcare Platform - Compliance Dashboard Component
// HIPAA Compliant Dashboard with Violation Tracking and Risk Assessment

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  useTheme,
} from '@mui/material';
import {
  SecurityRounded,
  WarningRounded,
  ErrorRounded,
  CheckCircleRounded,
  AssessmentRounded,
  ReportRounded,
  DownloadRounded,
  RefreshRounded,
  TrendingUpRounded,
  TrendingDownRounded,
  ShieldRounded,
} from '@mui/icons-material';

export interface ComplianceMetrics {
  overallScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  violations: ComplianceViolation[];
  auditScore: number;
  dataProtectionScore: number;
  accessControlScore: number;
  incidentCount: number;
  lastAssessment: Date;
}

export interface ComplianceViolation {
  id: string;
  type: 'data_breach' | 'unauthorized_access' | 'phi_exposure' | 'audit_failure' | 'policy_violation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: Date;
  userId?: string;
  resourceId?: string;
  status: 'open' | 'investigating' | 'resolved' | 'false_positive';
  remediation?: string;
  reportRequired: boolean;
}

export interface ComplianceReport {
  id: string;
  type: 'monthly' | 'quarterly' | 'annual' | 'incident';
  period: {
    start: Date;
    end: Date;
  };
  status: 'generating' | 'ready' | 'sent';
  violations: number;
  riskScore: number;
  generatedAt: Date;
}

interface ComplianceDashboardProps {
  metrics: ComplianceMetrics;
  reports: ComplianceReport[];
  onGenerateReport?: (type: string) => void;
  onExportData?: () => void;
  onRefresh?: () => void;
}

export const ComplianceDashboard: React.FC<ComplianceDashboardProps> = ({
  metrics,
  reports,
  onGenerateReport,
  onExportData,
  onRefresh,
}) => {
  const theme = useTheme();
  const [selectedViolation, setSelectedViolation] = useState<ComplianceViolation | null>(null);
  const [showReportDialog, setShowReportDialog] = useState(false);

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return theme.palette.error.dark;
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
      default:
        return theme.palette.success.main;
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return theme.palette.success.main;
    if (score >= 70) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleDateString();
  };

  const getViolationIcon = (type: string) => {
    switch (type) {
      case 'data_breach':
        return <ErrorRounded />;
      case 'unauthorized_access':
        return <WarningRounded />;
      case 'phi_exposure':
        return <SecurityRounded />;
      case 'audit_failure':
        return <AssessmentRounded />;
      case 'policy_violation':
        return <ReportRounded />;
      default:
        return <WarningRounded />;
    }
  };

  const criticalViolations = metrics.violations.filter(v => v.severity === 'critical');
  const openViolations = metrics.violations.filter(v => v.status === 'open');
  const recentViolations = metrics.violations
    .filter(v => new Date(v.timestamp) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
    .slice(0, 5);

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">HIPAA Compliance Dashboard</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Refresh Data">
            <IconButton onClick={onRefresh}>
              <RefreshRounded />
            </IconButton>
          </Tooltip>
          <Button
            variant="outlined"
            startIcon={<ReportRounded />}
            onClick={() => setShowReportDialog(true)}
          >
            Generate Report
          </Button>
          <Button
            variant="contained"
            startIcon={<DownloadRounded />}
            onClick={onExportData}
          >
            Export Data
          </Button>
        </Box>
      </Box>

      {/* Overall Compliance Score */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Overall Compliance Score
              </Typography>
              <Typography
                variant="h2"
                sx={{ color: getScoreColor(metrics.overallScore), mb: 1 }}
              >
                {metrics.overallScore}%
              </Typography>
              <Chip
                label={metrics.riskLevel.toUpperCase()}
                color={
                  metrics.riskLevel === 'low' ? 'success' :
                  metrics.riskLevel === 'medium' ? 'warning' : 'error'
                }
                sx={{ fontWeight: 'bold' }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Critical Violations
              </Typography>
              <Typography
                variant="h2"
                sx={{ color: criticalViolations.length > 0 ? 'error.main' : 'success.main', mb: 1 }}
              >
                {criticalViolations.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Require immediate attention
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Open Violations
              </Typography>
              <Typography
                variant="h2"
                sx={{ color: openViolations.length > 0 ? 'warning.main' : 'success.main', mb: 1 }}
              >
                {openViolations.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Under investigation
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Security Incidents
              </Typography>
              <Typography
                variant="h2"
                sx={{ color: metrics.incidentCount > 0 ? 'error.main' : 'success.main', mb: 1 }}
              >
                {metrics.incidentCount}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Compliance Scores Breakdown */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Compliance Scores Breakdown" />
            <CardContent>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Audit Compliance</Typography>
                  <Typography variant="body2">{metrics.auditScore}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={metrics.auditScore}
                  sx={{
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getScoreColor(metrics.auditScore),
                    },
                  }}
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Data Protection</Typography>
                  <Typography variant="body2">{metrics.dataProtectionScore}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={metrics.dataProtectionScore}
                  sx={{
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getScoreColor(metrics.dataProtectionScore),
                    },
                  }}
                />
              </Box>

              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Access Control</Typography>
                  <Typography variant="body2">{metrics.accessControlScore}%</Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={metrics.accessControlScore}
                  sx={{
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getScoreColor(metrics.accessControlScore),
                    },
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Recent Violations" />
            <CardContent>
              {recentViolations.length === 0 ? (
                <Alert severity="success" icon={<CheckCircleRounded />}>
                  No violations in the past 7 days
                </Alert>
              ) : (
                <List>
                  {recentViolations.map((violation) => (
                    <ListItem
                      key={violation.id}
                      button
                      onClick={() => setSelectedViolation(violation)}
                      sx={{
                        borderLeft: `3px solid ${getRiskColor(violation.severity)}`,
                        mb: 1,
                        borderRadius: 1,
                      }}
                    >
                      <ListItemIcon>
                        {getViolationIcon(violation.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={violation.description}
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              {formatDate(violation.timestamp)} • {violation.severity.toUpperCase()}
                            </Typography>
                            <Chip
                              label={violation.status}
                              size="small"
                              color={violation.status === 'resolved' ? 'success' : 'warning'}
                              sx={{ mt: 0.5 }}
                            />
                          </Box>
                        }
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Recent Reports */}
      <Card>
        <CardHeader title="Compliance Reports" />
        <CardContent>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Report Type</TableCell>
                  <TableCell>Period</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Violations</TableCell>
                  <TableCell>Risk Score</TableCell>
                  <TableCell>Generated</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {reports.slice(0, 5).map((report) => (
                  <TableRow key={report.id}>
                    <TableCell>{report.type}</TableCell>
                    <TableCell>
                      {formatDate(report.period.start)} - {formatDate(report.period.end)}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={report.status}
                        color={
                          report.status === 'ready' ? 'success' :
                          report.status === 'generating' ? 'warning' : 'default'
                        }
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{report.violations}</TableCell>
                    <TableCell>
                      <Typography sx={{ color: getScoreColor(report.riskScore) }}>
                        {report.riskScore}%
                      </Typography>
                    </TableCell>
                    <TableCell>{formatDate(report.generatedAt)}</TableCell>
                    <TableCell>
                      <IconButton size="small" disabled={report.status !== 'ready'}>
                        <DownloadRounded />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Violation Detail Dialog */}
      <Dialog
        open={!!selectedViolation}
        onClose={() => setSelectedViolation(null)}
        maxWidth="md"
        fullWidth
      >
        {selectedViolation && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getViolationIcon(selectedViolation.type)}
                <Typography variant="h6">Violation Details</Typography>
                <Chip
                  label={selectedViolation.severity.toUpperCase()}
                  color={
                    selectedViolation.severity === 'critical' ? 'error' :
                    selectedViolation.severity === 'high' ? 'error' :
                    selectedViolation.severity === 'medium' ? 'warning' : 'default'
                  }
                  size="small"
                />
              </Box>
            </DialogTitle>
            <DialogContent>
              <Typography variant="body1" paragraph>
                {selectedViolation.description}
              </Typography>
              
              <Typography variant="subtitle2" gutterBottom>
                Details:
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Type: {selectedViolation.type.replace('_', ' ').toUpperCase()}
                <br />
                Timestamp: {new Date(selectedViolation.timestamp).toLocaleString()}
                <br />
                Status: {selectedViolation.status.toUpperCase()}
                <br />
                Report Required: {selectedViolation.reportRequired ? 'Yes' : 'No'}
              </Typography>

              {selectedViolation.remediation && (
                <>
                  <Typography variant="subtitle2" gutterBottom>
                    Remediation:
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {selectedViolation.remediation}
                  </Typography>
                </>
              )}

              {selectedViolation.reportRequired && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  This violation requires regulatory reporting under HIPAA breach notification rules.
                </Alert>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setSelectedViolation(null)}>Close</Button>
              <Button variant="contained" color="primary">
                Update Status
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Report Generation Dialog */}
      <Dialog
        open={showReportDialog}
        onClose={() => setShowReportDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Generate Compliance Report</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Select the type of compliance report to generate:
          </Typography>
          
          <List>
            {['monthly', 'quarterly', 'annual', 'incident'].map((type) => (
              <ListItem
                key={type}
                button
                onClick={() => {
                  onGenerateReport?.(type);
                  setShowReportDialog(false);
                }}
              >
                <ListItemIcon>
                  <ReportRounded />
                </ListItemIcon>
                <ListItemText
                  primary={type.charAt(0).toUpperCase() + type.slice(1) + ' Report'}
                  secondary={
                    type === 'incident' ? 'Generate incident-specific report' :
                    `Generate ${type} compliance summary`
                  }
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowReportDialog(false)}>Cancel</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ComplianceDashboard;
