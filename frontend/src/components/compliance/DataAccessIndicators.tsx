// OCTAVE Healthcare Platform - Data Access Logging Indicators
// HIPAA Compliant Visual Indicators for Data Access Events

import React, { useState, useEffect } from 'react';
import {
  Box,
  Badge,
  Chip,
  Tooltip,
  Typography,
  Popover,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Avatar,
  IconButton,
  Alert,
  Divider,
  useTheme,
} from '@mui/material';
import {
  VisibilityRounded,
  PersonRounded,
  AccessTimeRounded,
  SecurityRounded,
  WarningRounded,
  ComputerRounded,
  LocationOnRounded,
  NotificationsRounded,
  HistoryRounded,
} from '@mui/icons-material';

export interface DataAccessEvent {
  id: string;
  userId: string;
  userName: string;
  userRole: string;
  action: string;
  resourceType: string;
  resourceId: string;
  timestamp: Date;
  ipAddress: string;
  location?: string;
  deviceInfo?: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  isAuthorized: boolean;
  duration?: number; // in seconds
}

interface DataAccessIndicatorProps {
  resourceId: string;
  resourceType: string;
  accessEvents: DataAccessEvent[];
  showRecentCount?: number;
  showRiskIndicator?: boolean;
  onViewDetails?: (event: DataAccessEvent) => void;
}

export const DataAccessIndicator: React.FC<DataAccessIndicatorProps> = ({
  resourceId,
  resourceType,
  accessEvents,
  showRecentCount = 5,
  showRiskIndicator = true,
  onViewDetails,
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  // Filter events for this specific resource
  const resourceEvents = accessEvents.filter(
    event => event.resourceId === resourceId && event.resourceType === resourceType
  );

  // Sort by timestamp (most recent first)
  const sortedEvents = resourceEvents.sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  const recentEvents = sortedEvents.slice(0, showRecentCount);
  const totalAccesses = resourceEvents.length;
  const unauthorizedAccesses = resourceEvents.filter(event => !event.isAuthorized).length;
  const highRiskAccesses = resourceEvents.filter(event => 
    event.riskLevel === 'high' || event.riskLevel === 'critical'
  ).length;

  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return theme.palette.error.dark;
      case 'high':
        return theme.palette.error.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'low':
      default:
        return theme.palette.success.main;
    }
  };

  const getOverallRiskLevel = (): 'low' | 'medium' | 'high' | 'critical' => {
    if (unauthorizedAccesses > 0) return 'critical';
    if (highRiskAccesses > 0) return 'high';
    if (totalAccesses > 10) return 'medium';
    return 'low';
  };

  const formatTimestamp = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);

  if (totalAccesses === 0) {
    return (
      <Tooltip title="No access events recorded">
        <Chip
          icon={<SecurityRounded />}
          label="No Access"
          size="small"
          variant="outlined"
          sx={{ opacity: 0.6 }}
        />
      </Tooltip>
    );
  }

  return (
    <>
      <Tooltip title={`${totalAccesses} access events - Click for details`}>
        <Badge
          badgeContent={unauthorizedAccesses > 0 ? unauthorizedAccesses : null}
          color="error"
          onClick={handleClick}
          sx={{ cursor: 'pointer' }}
        >
          <Chip
            icon={<VisibilityRounded />}
            label={`${totalAccesses} accesses`}
            size="small"
            color={showRiskIndicator ? 
              (getOverallRiskLevel() === 'low' ? 'success' : 
               getOverallRiskLevel() === 'medium' ? 'warning' : 'error') : 
              'default'
            }
            sx={{
              '&:hover': {
                backgroundColor: theme.palette.action.hover,
              },
            }}
          />
        </Badge>
      </Tooltip>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        PaperProps={{
          sx: { maxWidth: 400, maxHeight: 500 },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            Data Access History
          </Typography>
          
          <Typography variant="body2" color="text.secondary" gutterBottom>
            {resourceType}: {resourceId}
          </Typography>

          {/* Summary Stats */}
          <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
            <Chip
              icon={<VisibilityRounded />}
              label={`${totalAccesses} total`}
              size="small"
              variant="outlined"
            />
            {unauthorizedAccesses > 0 && (
              <Chip
                icon={<WarningRounded />}
                label={`${unauthorizedAccesses} unauthorized`}
                size="small"
                color="error"
              />
            )}
            {highRiskAccesses > 0 && (
              <Chip
                icon={<SecurityRounded />}
                label={`${highRiskAccesses} high risk`}
                size="small"
                color="warning"
              />
            )}
          </Box>

          <Divider sx={{ mb: 2 }} />

          {/* Recent Access Events */}
          <Typography variant="subtitle2" gutterBottom>
            Recent Access Events
          </Typography>

          <List dense>
            {recentEvents.map((event) => (
              <ListItem
                key={event.id}
                button
                onClick={() => onViewDetails?.(event)}
                sx={{
                  borderLeft: `3px solid ${getRiskColor(event.riskLevel)}`,
                  mb: 1,
                  borderRadius: 1,
                  backgroundColor: event.isAuthorized ? 'transparent' : 'error.light',
                }}
              >
                <ListItemIcon>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: getRiskColor(event.riskLevel),
                      fontSize: '0.8rem',
                    }}
                  >
                    {event.userName.charAt(0).toUpperCase()}
                  </Avatar>
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" fontWeight="medium">
                        {event.userName}
                      </Typography>
                      <Chip
                        label={event.userRole}
                        size="small"
                        variant="outlined"
                        sx={{ fontSize: '0.7rem', height: 20 }}
                      />
                      {!event.isAuthorized && (
                        <WarningRounded sx={{ color: 'error.main', fontSize: 16 }} />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="caption" display="block">
                        {event.action} • {formatTimestamp(event.timestamp)}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                        <ComputerRounded sx={{ fontSize: 12 }} />
                        <Typography variant="caption">
                          {event.ipAddress}
                        </Typography>
                        {event.location && (
                          <>
                            <LocationOnRounded sx={{ fontSize: 12 }} />
                            <Typography variant="caption">
                              {event.location}
                            </Typography>
                          </>
                        )}
                      </Box>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>

          {sortedEvents.length > showRecentCount && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              Showing {showRecentCount} of {totalAccesses} access events
            </Typography>
          )}

          {/* Compliance Alert */}
          {unauthorizedAccesses > 0 && (
            <Alert severity="error" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>HIPAA Compliance Alert:</strong> Unauthorized access detected. 
                This incident requires immediate review and may need to be reported.
              </Typography>
            </Alert>
          )}
        </Box>
      </Popover>
    </>
  );
};

// Real-time access notification component
export const RealTimeAccessNotification: React.FC<{
  event: DataAccessEvent;
  onDismiss: () => void;
  autoHideDuration?: number;
}> = ({ event, onDismiss, autoHideDuration = 5000 }) => {
  const theme = useTheme();

  useEffect(() => {
    if (autoHideDuration > 0) {
      const timer = setTimeout(onDismiss, autoHideDuration);
      return () => clearTimeout(timer);
    }
  }, [autoHideDuration, onDismiss]);

  return (
    <Alert
      severity={event.isAuthorized ? 'info' : 'error'}
      onClose={onDismiss}
      sx={{
        position: 'fixed',
        top: 16,
        right: 16,
        zIndex: theme.zIndex.snackbar,
        minWidth: 300,
        maxWidth: 400,
      }}
      icon={<NotificationsRounded />}
    >
      <Typography variant="body2" fontWeight="medium">
        {event.isAuthorized ? 'Data Access Event' : 'Unauthorized Access Detected'}
      </Typography>
      <Typography variant="caption" display="block">
        {event.userName} ({event.userRole}) accessed {event.resourceType}
      </Typography>
      <Typography variant="caption" display="block">
        {formatTimestamp(event.timestamp)} • {event.ipAddress}
      </Typography>
    </Alert>
  );
};

// Hook for managing data access events
export const useDataAccessTracking = () => {
  const [accessEvents, setAccessEvents] = useState<DataAccessEvent[]>([]);
  const [realtimeNotifications, setRealtimeNotifications] = useState<DataAccessEvent[]>([]);

  const addAccessEvent = (event: DataAccessEvent) => {
    setAccessEvents(prev => [event, ...prev]);
    
    // Show real-time notification for high-risk or unauthorized access
    if (!event.isAuthorized || event.riskLevel === 'high' || event.riskLevel === 'critical') {
      setRealtimeNotifications(prev => [event, ...prev]);
    }
  };

  const dismissNotification = (eventId: string) => {
    setRealtimeNotifications(prev => prev.filter(event => event.id !== eventId));
  };

  const getAccessEventsForResource = (resourceId: string, resourceType: string) => {
    return accessEvents.filter(
      event => event.resourceId === resourceId && event.resourceType === resourceType
    );
  };

  return {
    accessEvents,
    realtimeNotifications,
    addAccessEvent,
    dismissNotification,
    getAccessEventsForResource,
  };
};

export default DataAccessIndicator;
