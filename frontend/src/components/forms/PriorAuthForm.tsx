// OCTAVE Healthcare Platform - Prior Authorization Form Component
// Comprehensive form based on mockup design with PhilHealth integration

import React, { useState } from 'react';
import {
  Box,
  Grid,
  CardContent,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Divider,
  Alert,
  Stepper,
  Step,
  StepLabel,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
} from '@mui/material';
import {
  SaveRounded,
  SendRounded,
  AttachFileRounded,
  DeleteRounded,
  PersonRounded,
  LocalHospitalRounded,
  AssignmentRounded,
} from '@mui/icons-material';

import { ResponsiveCard } from '@/components/common/layout/ResponsiveContainer';
import { AccessibleField, AccessibleButton } from '@/components/common/accessibility/AccessibleComponents';

interface PriorAuthFormData {
  // Patient Information
  patientId: string;
  patientName: string;
  patientDOB: string;
  philhealthNumber: string;
  
  // Provider Information
  providerId: string;
  providerName: string;
  facilityName: string;
  
  // Authorization Details
  procedureCode: string;
  procedureName: string;
  diagnosisCode: string;
  diagnosisDescription: string;
  urgencyLevel: 'routine' | 'urgent' | 'stat';
  
  // Clinical Information
  clinicalHistory: string;
  justification: string;
  expectedDuration: string;
  
  // Insurance Information
  insuranceProvider: string;
  policyNumber: string;
  groupNumber: string;
}

const steps = [
  'Patient Information',
  'Clinical Details',
  'Authorization Request',
  'Review & Submit'
];

const PriorAuthForm: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<PriorAuthFormData>({
    patientId: '',
    patientName: '',
    patientDOB: '',
    philhealthNumber: '',
    providerId: '',
    providerName: '',
    facilityName: '',
    procedureCode: '',
    procedureName: '',
    diagnosisCode: '',
    diagnosisDescription: '',
    urgencyLevel: 'routine',
    clinicalHistory: '',
    justification: '',
    expectedDuration: '',
    insuranceProvider: '',
    policyNumber: '',
    groupNumber: '',
  });
  
  const [attachedDocuments, setAttachedDocuments] = useState<File[]>([]);

  const handleInputChange = (field: keyof PriorAuthFormData) => (value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleNext = () => {
    setActiveStep(prev => Math.min(prev + 1, steps.length - 1));
  };

  const handleBack = () => {
    setActiveStep(prev => Math.max(prev - 1, 0));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachedDocuments(prev => [...prev, ...files]);
  };

  const handleRemoveDocument = (index: number) => {
    setAttachedDocuments(prev => prev.filter((_, i) => i !== index));
  };

  const renderPatientInformation = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <PersonRounded color="primary" />
          Patient Information
        </Typography>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="Patient ID"
          value={formData.patientId}
          onChange={handleInputChange('patientId')}
          required
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="Patient Name"
          value={formData.patientName}
          onChange={handleInputChange('patientName')}
          required
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="Date of Birth"
          value={formData.patientDOB}
          onChange={handleInputChange('patientDOB')}
          type="date"
          required
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="PhilHealth Number"
          value={formData.philhealthNumber}
          onChange={handleInputChange('philhealthNumber')}
          helperText="12-digit PhilHealth member ID"
        />
      </Grid>

      <Grid item xs={12}>
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>PhilHealth Integration:</strong> Patient eligibility will be automatically verified 
            when you enter a valid PhilHealth number.
          </Typography>
        </Alert>
      </Grid>
    </Grid>
  );

  const renderClinicalDetails = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <LocalHospitalRounded color="primary" />
          Clinical Information
        </Typography>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="Procedure Code (CPT/ICD-10)"
          value={formData.procedureCode}
          onChange={handleInputChange('procedureCode')}
          required
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="Procedure Name"
          value={formData.procedureName}
          onChange={handleInputChange('procedureName')}
          required
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="Primary Diagnosis Code"
          value={formData.diagnosisCode}
          onChange={handleInputChange('diagnosisCode')}
          required
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="Diagnosis Description"
          value={formData.diagnosisDescription}
          onChange={handleInputChange('diagnosisDescription')}
          required
        />
      </Grid>
      
      <Grid item xs={12}>
        <AccessibleField
          label="Clinical History"
          value={formData.clinicalHistory}
          onChange={handleInputChange('clinicalHistory')}
          multiline
          rows={4}
          helperText="Provide relevant medical history and current symptoms"
        />
      </Grid>
      
      <Grid item xs={12}>
        <AccessibleField
          label="Medical Justification"
          value={formData.justification}
          onChange={handleInputChange('justification')}
          multiline
          rows={4}
          required
          helperText="Explain why this procedure is medically necessary"
        />
      </Grid>
    </Grid>
  );

  const renderAuthorizationRequest = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <AssignmentRounded color="primary" />
          Authorization Details
        </Typography>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <FormControl fullWidth>
          <InputLabel>Urgency Level</InputLabel>
          <Select
            value={formData.urgencyLevel}
            onChange={(e) => handleInputChange('urgencyLevel')(e.target.value)}
            label="Urgency Level"
          >
            <MenuItem value="routine">Routine</MenuItem>
            <MenuItem value="urgent">Urgent</MenuItem>
            <MenuItem value="stat">STAT (Emergency)</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="Expected Duration"
          value={formData.expectedDuration}
          onChange={handleInputChange('expectedDuration')}
          helperText="e.g., 1 session, 6 weeks, ongoing"
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="Insurance Provider"
          value={formData.insuranceProvider}
          onChange={handleInputChange('insuranceProvider')}
          required
        />
      </Grid>
      
      <Grid item xs={12} md={6}>
        <AccessibleField
          label="Policy Number"
          value={formData.policyNumber}
          onChange={handleInputChange('policyNumber')}
          required
        />
      </Grid>

      {/* Document Upload Section */}
      <Grid item xs={12}>
        <Divider sx={{ my: 2 }} />
        <Typography variant="h6" gutterBottom>
          Supporting Documents
        </Typography>
        
        <input
          accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
          style={{ display: 'none' }}
          id="file-upload"
          multiple
          type="file"
          onChange={handleFileUpload}
        />
        <label htmlFor="file-upload">
          <Button
            variant="outlined"
            component="span"
            startIcon={<AttachFileRounded />}
            sx={{ mb: 2 }}
          >
            Upload Documents
          </Button>
        </label>
        
        {attachedDocuments.length > 0 && (
          <Paper variant="outlined" sx={{ mt: 2 }}>
            <List>
              {attachedDocuments.map((file, index) => (
                <ListItem key={index}>
                  <ListItemText
                    primary={file.name}
                    secondary={`${(file.size / 1024 / 1024).toFixed(2)} MB`}
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={() => handleRemoveDocument(index)}
                      color="error"
                    >
                      <DeleteRounded />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          </Paper>
        )}
      </Grid>
    </Grid>
  );

  const renderReviewSubmit = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Typography variant="h6" gutterBottom>
          Review Your Request
        </Typography>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2">
            Please review all information carefully before submitting. 
            Once submitted, changes may require a new authorization request.
          </Typography>
        </Alert>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <ResponsiveCard>
          <CardContent>
            <Typography variant="subtitle1" fontWeight={600} gutterBottom>
              Patient Information
            </Typography>
            <Typography variant="body2">Name: {formData.patientName}</Typography>
            <Typography variant="body2">DOB: {formData.patientDOB}</Typography>
            <Typography variant="body2">PhilHealth: {formData.philhealthNumber}</Typography>
          </CardContent>
        </ResponsiveCard>
      </Grid>
      
      <Grid item xs={12} md={6}>
        <ResponsiveCard>
          <CardContent>
            <Typography variant="subtitle1" fontWeight={600} gutterBottom>
              Procedure Details
            </Typography>
            <Typography variant="body2">Code: {formData.procedureCode}</Typography>
            <Typography variant="body2">Name: {formData.procedureName}</Typography>
            <Typography variant="body2">
              Urgency: 
              <Chip 
                label={formData.urgencyLevel} 
                size="small" 
                color={formData.urgencyLevel === 'stat' ? 'error' : 
                       formData.urgencyLevel === 'urgent' ? 'warning' : 'success'}
                sx={{ ml: 1 }}
              />
            </Typography>
          </CardContent>
        </ResponsiveCard>
      </Grid>
    </Grid>
  );

  const renderStepContent = () => {
    switch (activeStep) {
      case 0: return renderPatientInformation();
      case 1: return renderClinicalDetails();
      case 2: return renderAuthorizationRequest();
      case 3: return renderReviewSubmit();
      default: return null;
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" fontWeight={600} gutterBottom>
        New Prior Authorization Request
      </Typography>
      
      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      <ResponsiveCard>
        <CardContent sx={{ p: 4 }}>
          {renderStepContent()}
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
            <Button
              onClick={handleBack}
              disabled={activeStep === 0}
              variant="outlined"
            >
              Back
            </Button>
            
            <Box sx={{ display: 'flex', gap: 2 }}>
              <AccessibleButton
                onClick={() => console.log('Save draft')}
                variant="outlined"
                startIcon={<SaveRounded />}
              >
                Save Draft
              </AccessibleButton>
              
              {activeStep === steps.length - 1 ? (
                <AccessibleButton
                  onClick={() => console.log('Submit')}
                  variant="contained"
                  startIcon={<SendRounded />}
                  color="primary"
                >
                  Submit Request
                </AccessibleButton>
              ) : (
                <AccessibleButton
                  onClick={handleNext}
                  variant="contained"
                  color="primary"
                >
                  Next
                </AccessibleButton>
              )}
            </Box>
          </Box>
        </CardContent>
      </ResponsiveCard>
    </Box>
  );
};

export default PriorAuthForm;
