// OCTAVE Healthcare Platform - Application Routing
// Role-Based Access Control with Protected Routes

import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useSelector } from 'react-redux';

import { selectIsAuthenticated, selectUserRole } from '@/store/slices/authSlice';
import { UserRole } from '@/types';

// Layout Components
import AuthLayout from '@/components/layouts/AuthLayout';
import DashboardLayout from '@/components/layouts/DashboardLayout';

// Auth Pages
import LoginPage from '@/pages/auth/LoginPage';
import ForgotPasswordPage from '@/pages/auth/ForgotPasswordPage';
import ResetPasswordPage from '@/pages/auth/ResetPasswordPage';

// Dashboard Pages
import DashboardPage from '@/pages/dashboard/DashboardPage';
import PatientsPage from '@/pages/patients/PatientsPage';
import PatientDetailPage from '@/pages/patients/PatientDetailPage';
import PriorAuthPage from '@/pages/prior-auth/PriorAuthPage';
import PriorAuthDetailPage from '@/pages/prior-auth/PriorAuthDetailPage';
import CreatePriorAuthPage from '@/pages/prior-auth/CreatePriorAuthPage';
import DocumentsPage from '@/pages/documents/DocumentsPage';
import CommunicationsPage from '@/pages/communications/CommunicationsPage';
import ReportsPage from '@/pages/reports/ReportsPage';
import SettingsPage from '@/pages/settings/SettingsPage';
import ProfilePage from '@/pages/profile/ProfilePage';

// Admin Pages
import AdminDashboardPage from '@/pages/admin/AdminDashboardPage';
import UserManagementPage from '@/pages/admin/UserManagementPage';
import PracticeSettingsPage from '@/pages/admin/PracticeSettingsPage';

// Error Pages
import NotFoundPage from '@/pages/error/NotFoundPage';
import UnauthorizedPage from '@/pages/error/UnauthorizedPage';

// Protected Route Component
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  requireAuth?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles = [],
  requireAuth = true,
}) => {
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const userRole = useSelector(selectUserRole);

  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (requiredRoles.length > 0 && userRole && !requiredRoles.includes(userRole as UserRole)) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
};

// Main App Routes Component
const AppRoutes: React.FC = () => {
  const isAuthenticated = useSelector(selectIsAuthenticated);

  return (
    <Routes>
      {/* Public Routes */}
      <Route path="/login" element={
        <AuthLayout>
          <LoginPage />
        </AuthLayout>
      } />
      
      <Route path="/forgot-password" element={
        <AuthLayout>
          <ForgotPasswordPage />
        </AuthLayout>
      } />
      
      <Route path="/reset-password" element={
        <AuthLayout>
          <ResetPasswordPage />
        </AuthLayout>
      } />

      {/* Protected Dashboard Routes */}
      <Route path="/" element={
        <ProtectedRoute>
          <DashboardLayout>
            <DashboardPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/dashboard" element={
        <ProtectedRoute>
          <DashboardLayout>
            <DashboardPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Patient Management Routes */}
      <Route path="/patients" element={
        <ProtectedRoute requiredRoles={[UserRole.PROVIDER, UserRole.STAFF, UserRole.PRACTICE_ADMIN]}>
          <DashboardLayout>
            <PatientsPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/patients/:id" element={
        <ProtectedRoute requiredRoles={[UserRole.PROVIDER, UserRole.STAFF, UserRole.PRACTICE_ADMIN]}>
          <DashboardLayout>
            <PatientDetailPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Prior Authorization Routes */}
      <Route path="/prior-auth" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PriorAuthPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/prior-auth/new" element={
        <ProtectedRoute requiredRoles={[UserRole.PROVIDER, UserRole.STAFF, UserRole.PRACTICE_ADMIN]}>
          <DashboardLayout>
            <CreatePriorAuthPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/prior-auth/:id" element={
        <ProtectedRoute>
          <DashboardLayout>
            <PriorAuthDetailPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Document Management Routes */}
      <Route path="/documents" element={
        <ProtectedRoute>
          <DashboardLayout>
            <DocumentsPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Communications Routes */}
      <Route path="/communications" element={
        <ProtectedRoute>
          <DashboardLayout>
            <CommunicationsPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Reports Routes */}
      <Route path="/reports" element={
        <ProtectedRoute requiredRoles={[UserRole.PROVIDER, UserRole.PRACTICE_ADMIN, UserRole.BILLING]}>
          <DashboardLayout>
            <ReportsPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Settings and Profile Routes */}
      <Route path="/settings" element={
        <ProtectedRoute>
          <DashboardLayout>
            <SettingsPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/profile" element={
        <ProtectedRoute>
          <DashboardLayout>
            <ProfilePage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Admin Routes */}
      <Route path="/admin" element={
        <ProtectedRoute requiredRoles={[UserRole.SYSTEM_ADMIN, UserRole.PRACTICE_ADMIN]}>
          <DashboardLayout>
            <AdminDashboardPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/admin/users" element={
        <ProtectedRoute requiredRoles={[UserRole.SYSTEM_ADMIN, UserRole.PRACTICE_ADMIN]}>
          <DashboardLayout>
            <UserManagementPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      <Route path="/admin/practice" element={
        <ProtectedRoute requiredRoles={[UserRole.SYSTEM_ADMIN, UserRole.PRACTICE_ADMIN]}>
          <DashboardLayout>
            <PracticeSettingsPage />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Error Routes */}
      <Route path="/unauthorized" element={<UnauthorizedPage />} />
      <Route path="/404" element={<NotFoundPage />} />
      <Route path="*" element={<Navigate to="/404" replace />} />

      {/* Redirect to dashboard if authenticated, login if not */}
      <Route path="/" element={
        isAuthenticated ? <Navigate to="/dashboard" replace /> : <Navigate to="/login" replace />
      } />
    </Routes>
  );
};

export default AppRoutes;
