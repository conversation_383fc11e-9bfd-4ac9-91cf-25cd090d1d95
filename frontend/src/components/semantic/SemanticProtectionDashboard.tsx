// OCTAVE Healthcare Platform - Semantic Protection Dashboard
// Comprehensive Dashboard for OCTAVE Semantic Immune System Monitoring

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  LinearProgress,
  Chip,
  IconButton,
  <PERSON>ltip,
  Button,
  Alert,
  useTheme,
} from '@mui/material';
import {
  SecurityRounded,
  ShieldRounded,
  PsychologyRounded,
  TrendingUpRounded,
  AutoFixHighRounded,
  VisibilityRounded,
  BugReportRounded,
  RefreshRounded,
  SettingsRounded,
} from '@mui/icons-material';

import ThreatDetectionIndicators from './ThreatDetectionIndicators';
import { ThreatDetectionStatus, SemanticProfile } from './ThreatDetectionIndicators';

export interface SemanticSystemMetrics {
  overallHealth: number; // 0-100
  adaptiveLearningProgress: number; // 0-100
  antibodyCount: number;
  activeAntibodies: number;
  mutationDetectionRate: number; // 0-100
  falsePositiveRate: number; // 0-100
  systemUptime: number; // hours
  lastTrainingUpdate: Date;
  threatIntelligenceFeeds: number;
  semanticProfiles: SemanticProfile[];
}

export interface AntibodyMetrics {
  id: string;
  name: string;
  type: 'ARTEMIS' | 'ATHENA' | 'APOLLO' | 'ZEUS' | 'HERMES' | 'HEPHAESTUS';
  effectiveness: number; // 0-100
  coverage: number; // 0-100
  mutations: number;
  lastUpdate: Date;
  status: 'active' | 'learning' | 'dormant' | 'updating';
}

interface SemanticProtectionDashboardProps {
  threatStatus: ThreatDetectionStatus;
  systemMetrics: SemanticSystemMetrics;
  antibodyMetrics: AntibodyMetrics[];
  onRefresh?: () => void;
  onConfigure?: () => void;
  onViewDetails?: (profileId: string) => void;
}

export const SemanticProtectionDashboard: React.FC<SemanticProtectionDashboardProps> = ({
  threatStatus,
  systemMetrics,
  antibodyMetrics,
  onRefresh,
  onConfigure,
  onViewDetails,
}) => {
  const theme = useTheme();

  const getHealthColor = (health: number) => {
    if (health >= 90) return theme.palette.success.main;
    if (health >= 70) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return theme.palette.success.main;
      case 'learning':
        return theme.palette.info.main;
      case 'updating':
        return theme.palette.warning.main;
      case 'dormant':
        return theme.palette.grey[500];
      default:
        return theme.palette.grey[500];
    }
  };

  const formatUptime = (hours: number): string => {
    const days = Math.floor(hours / 24);
    const remainingHours = hours % 24;
    return `${days}d ${remainingHours}h`;
  };

  const formatTimestamp = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4">OCTAVE Semantic Protection</Typography>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Tooltip title="Refresh Data">
            <IconButton onClick={onRefresh}>
              <RefreshRounded />
            </IconButton>
          </Tooltip>
          <Button
            variant="outlined"
            startIcon={<SettingsRounded />}
            onClick={onConfigure}
          >
            Configure
          </Button>
        </Box>
      </Box>

      {/* System Health Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                System Health
              </Typography>
              <Typography
                variant="h2"
                sx={{ color: getHealthColor(systemMetrics.overallHealth), mb: 1 }}
              >
                {systemMetrics.overallHealth}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={systemMetrics.overallHealth}
                sx={{
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: getHealthColor(systemMetrics.overallHealth),
                  },
                }}
              />
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Active Antibodies
              </Typography>
              <Typography variant="h2" sx={{ color: 'primary.main', mb: 1 }}>
                {systemMetrics.activeAntibodies}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                of {systemMetrics.antibodyCount} total
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Detection Rate
              </Typography>
              <Typography variant="h2" sx={{ color: 'success.main', mb: 1 }}>
                {systemMetrics.mutationDetectionRate}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Mutation detection
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                System Uptime
              </Typography>
              <Typography variant="h2" sx={{ color: 'info.main', mb: 1 }}>
                {formatUptime(systemMetrics.systemUptime)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Continuous operation
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Threat Detection Status */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={8}>
          <ThreatDetectionIndicators
            status={threatStatus}
            showDetails={true}
          />
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardHeader title="Adaptive Learning" />
            <CardContent>
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" gutterBottom>
                  Learning Progress
                </Typography>
                <LinearProgress
                  variant="determinate"
                  value={systemMetrics.adaptiveLearningProgress}
                  sx={{
                    height: 8,
                    borderRadius: 4,
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: theme.palette.info.main,
                    },
                  }}
                />
                <Typography variant="caption" color="text.secondary">
                  {systemMetrics.adaptiveLearningProgress}% complete
                </Typography>
              </Box>

              <Typography variant="body2" color="text.secondary" gutterBottom>
                Last training update: {formatTimestamp(systemMetrics.lastTrainingUpdate)}
              </Typography>

              <Typography variant="body2" color="text.secondary">
                Threat intelligence feeds: {systemMetrics.threatIntelligenceFeeds}
              </Typography>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  System is continuously learning from new threat patterns
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Antibody Effectiveness */}
      <Card sx={{ mb: 3 }}>
        <CardHeader title="Semantic Antibody Performance" />
        <CardContent>
          <Grid container spacing={2}>
            {antibodyMetrics.map((antibody) => (
              <Grid item xs={12} sm={6} md={4} key={antibody.id}>
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6">{antibody.type}</Typography>
                      <Chip
                        label={antibody.status.toUpperCase()}
                        size="small"
                        sx={{
                          backgroundColor: getStatusColor(antibody.status),
                          color: 'white',
                        }}
                      />
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Effectiveness: {antibody.effectiveness}%
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={antibody.effectiveness}
                        sx={{
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: getHealthColor(antibody.effectiveness),
                          },
                        }}
                      />
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        Coverage: {antibody.coverage}%
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={antibody.coverage}
                        sx={{
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: theme.palette.info.main,
                          },
                        }}
                      />
                    </Box>

                    <Typography variant="body2" color="text.secondary">
                      Mutations detected: {antibody.mutations}
                      <br />
                      Last update: {formatTimestamp(antibody.lastUpdate)}
                    </Typography>

                    <Button
                      size="small"
                      startIcon={<VisibilityRounded />}
                      onClick={() => onViewDetails?.(antibody.id)}
                      sx={{ mt: 1 }}
                    >
                      View Details
                    </Button>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* System Performance Metrics */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Performance Metrics" />
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
                <Typography variant="body2">False Positive Rate</Typography>
                <Typography variant="body2" fontWeight="bold">
                  {systemMetrics.falsePositiveRate}%
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={100 - systemMetrics.falsePositiveRate}
                sx={{
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: systemMetrics.falsePositiveRate < 5 ? 
                      theme.palette.success.main : 
                      systemMetrics.falsePositiveRate < 10 ? 
                        theme.palette.warning.main : 
                        theme.palette.error.main,
                  },
                }}
              />

              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Lower false positive rates indicate better accuracy in threat detection.
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="System Status" />
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <SecurityRounded sx={{ color: 'success.main' }} />
                <Typography variant="body2">
                  All semantic profiles operational
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <PsychologyRounded sx={{ color: 'info.main' }} />
                <Typography variant="body2">
                  Adaptive learning active
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
                <AutoFixHighRounded sx={{ color: 'warning.main' }} />
                <Typography variant="body2">
                  {systemMetrics.activeAntibodies} antibodies updating
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TrendingUpRounded sx={{ color: 'success.main' }} />
                <Typography variant="body2">
                  System performance optimal
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default SemanticProtectionDashboard;
