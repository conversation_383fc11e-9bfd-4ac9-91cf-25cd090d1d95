// OCTAVE Healthcare Platform - Threat Detection Status Indicators
// Real-time Visual Indicators for OCTAVE Semantic Protection System

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  Tooltip,
  IconButton,
  Badge,
  LinearProgress,
  Alert,
  Popover,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
} from '@mui/material';
import {
  SecurityRounded,
  ShieldRounded,
  WarningRounded,
  ErrorRounded,
  CheckCircleRounded,
  TrendingUpRounded,
  TrendingDownRounded,
  VisibilityRounded,
  BugReportRounded,
  PsychologyRounded,
  AutoFixHighRounded,
} from '@mui/icons-material';

export interface ThreatDetectionStatus {
  overallThreatLevel: 'safe' | 'low' | 'medium' | 'high' | 'critical';
  protectionLevel: number; // 0-100
  activeThreats: number;
  blockedThreats: number;
  adaptiveLearningActive: boolean;
  antibodyEffectiveness: number; // 0-100
  lastUpdate: Date;
  semanticProfiles: SemanticProfile[];
  recentDetections: ThreatDetection[];
}

export interface SemanticProfile {
  id: string;
  name: string;
  type: 'ARTEMIS' | 'ATHENA' | 'APOLLO' | 'ZEUS' | 'HERMES' | 'HEPHAESTUS';
  status: 'active' | 'learning' | 'dormant' | 'error';
  effectiveness: number; // 0-100
  threatsDetected: number;
  lastActivity: Date;
}

export interface ThreatDetection {
  id: string;
  type: 'mutation' | 'anomaly' | 'injection' | 'bypass' | 'unknown';
  severity: 'low' | 'medium' | 'high' | 'critical';
  source: string;
  detectedBy: string; // Semantic profile name
  timestamp: Date;
  blocked: boolean;
  description: string;
}

interface ThreatDetectionIndicatorsProps {
  status: ThreatDetectionStatus;
  compact?: boolean;
  showDetails?: boolean;
  onViewThreat?: (threat: ThreatDetection) => void;
}

export const ThreatDetectionIndicators: React.FC<ThreatDetectionIndicatorsProps> = ({
  status,
  compact = false,
  showDetails = true,
  onViewThreat,
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [selectedProfile, setSelectedProfile] = useState<SemanticProfile | null>(null);

  const getThreatLevelColor = (level: string) => {
    switch (level) {
      case 'safe':
        return theme.palette.success.main;
      case 'low':
        return theme.palette.info.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'high':
        return theme.palette.error.main;
      case 'critical':
        return theme.palette.error.dark;
      default:
        return theme.palette.grey[500];
    }
  };

  const getThreatLevelIcon = (level: string) => {
    switch (level) {
      case 'safe':
        return <CheckCircleRounded />;
      case 'low':
        return <SecurityRounded />;
      case 'medium':
        return <WarningRounded />;
      case 'high':
      case 'critical':
        return <ErrorRounded />;
      default:
        return <SecurityRounded />;
    }
  };

  const getProfileIcon = (type: string) => {
    switch (type) {
      case 'ARTEMIS':
        return <ShieldRounded />;
      case 'ATHENA':
        return <PsychologyRounded />;
      case 'APOLLO':
        return <VisibilityRounded />;
      case 'ZEUS':
        return <SecurityRounded />;
      case 'HERMES':
        return <TrendingUpRounded />;
      case 'HEPHAESTUS':
        return <AutoFixHighRounded />;
      default:
        return <SecurityRounded />;
    }
  };

  const getEffectivenessColor = (effectiveness: number) => {
    if (effectiveness >= 90) return theme.palette.success.main;
    if (effectiveness >= 70) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const formatTimestamp = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const handleProfileClick = (event: React.MouseEvent<HTMLElement>, profile: SemanticProfile) => {
    setAnchorEl(event.currentTarget);
    setSelectedProfile(profile);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedProfile(null);
  };

  if (compact) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Tooltip title={`Threat Level: ${status.overallThreatLevel.toUpperCase()}`}>
          <Chip
            icon={getThreatLevelIcon(status.overallThreatLevel)}
            label={status.overallThreatLevel.toUpperCase()}
            size="small"
            sx={{
              backgroundColor: getThreatLevelColor(status.overallThreatLevel),
              color: 'white',
            }}
          />
        </Tooltip>
        
        <Tooltip title={`Protection Level: ${status.protectionLevel}%`}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <ShieldRounded sx={{ fontSize: 16, color: getEffectivenessColor(status.protectionLevel) }} />
            <Typography variant="caption" sx={{ color: getEffectivenessColor(status.protectionLevel) }}>
              {status.protectionLevel}%
            </Typography>
          </Box>
        </Tooltip>

        {status.activeThreats > 0 && (
          <Badge badgeContent={status.activeThreats} color="error">
            <WarningRounded sx={{ fontSize: 16 }} />
          </Badge>
        )}
      </Box>
    );
  }

  return (
    <Card>
      <CardContent>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6">OCTAVE Threat Detection</Typography>
          <Chip
            icon={getThreatLevelIcon(status.overallThreatLevel)}
            label={`${status.overallThreatLevel.toUpperCase()} THREAT LEVEL`}
            sx={{
              backgroundColor: getThreatLevelColor(status.overallThreatLevel),
              color: 'white',
              fontWeight: 'bold',
            }}
          />
        </Box>

        {/* Protection Level */}
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2">Protection Level</Typography>
            <Typography variant="body2" fontWeight="bold">
              {status.protectionLevel}%
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={status.protectionLevel}
            sx={{
              height: 8,
              borderRadius: 4,
              '& .MuiLinearProgress-bar': {
                backgroundColor: getEffectivenessColor(status.protectionLevel),
              },
            }}
          />
        </Box>

        {/* Threat Statistics */}
        <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
          <Box sx={{ textAlign: 'center', flex: 1 }}>
            <Typography variant="h4" color="error.main">
              {status.activeThreats}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Active Threats
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'center', flex: 1 }}>
            <Typography variant="h4" color="success.main">
              {status.blockedThreats}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Blocked Today
            </Typography>
          </Box>
          <Box sx={{ textAlign: 'center', flex: 1 }}>
            <Typography variant="h4" color="info.main">
              {status.antibodyEffectiveness}%
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Antibody Effectiveness
            </Typography>
          </Box>
        </Box>

        {/* Adaptive Learning Status */}
        {status.adaptiveLearningActive && (
          <Alert severity="info" sx={{ mb: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <PsychologyRounded />
              <Typography variant="body2">
                Adaptive learning is active - System is continuously improving threat detection
              </Typography>
            </Box>
          </Alert>
        )}

        {/* Semantic Profiles */}
        {showDetails && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Semantic Protection Profiles
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {status.semanticProfiles.map((profile) => (
                <Tooltip
                  key={profile.id}
                  title={`${profile.name}: ${profile.effectiveness}% effective, ${profile.threatsDetected} threats detected`}
                >
                  <Chip
                    icon={getProfileIcon(profile.type)}
                    label={profile.type}
                    size="small"
                    color={
                      profile.status === 'active' ? 'success' :
                      profile.status === 'learning' ? 'warning' :
                      profile.status === 'error' ? 'error' : 'default'
                    }
                    onClick={(e) => handleProfileClick(e, profile)}
                    sx={{ cursor: 'pointer' }}
                  />
                </Tooltip>
              ))}
            </Box>
          </Box>
        )}

        {/* Recent Detections */}
        {showDetails && status.recentDetections.length > 0 && (
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Recent Threat Detections
            </Typography>
            <List dense>
              {status.recentDetections.slice(0, 3).map((detection) => (
                <ListItem
                  key={detection.id}
                  button
                  onClick={() => onViewThreat?.(detection)}
                  sx={{
                    borderLeft: `3px solid ${getThreatLevelColor(detection.severity)}`,
                    mb: 1,
                    borderRadius: 1,
                  }}
                >
                  <ListItemIcon>
                    <BugReportRounded sx={{ color: getThreatLevelColor(detection.severity) }} />
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2">
                          {detection.type.toUpperCase()} - {detection.description}
                        </Typography>
                        <Chip
                          label={detection.blocked ? 'BLOCKED' : 'DETECTED'}
                          size="small"
                          color={detection.blocked ? 'success' : 'warning'}
                        />
                      </Box>
                    }
                    secondary={
                      <Typography variant="caption" color="text.secondary">
                        {formatTimestamp(detection.timestamp)} • Detected by {detection.detectedBy}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          </Box>
        )}

        {/* Last Update */}
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'right', mt: 2 }}>
          Last updated: {formatTimestamp(status.lastUpdate)}
        </Typography>
      </CardContent>

      {/* Profile Detail Popover */}
      <Popover
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
      >
        {selectedProfile && (
          <Box sx={{ p: 2, maxWidth: 300 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              {getProfileIcon(selectedProfile.type)}
              <Typography variant="h6">{selectedProfile.name}</Typography>
              <Chip
                label={selectedProfile.status.toUpperCase()}
                size="small"
                color={
                  selectedProfile.status === 'active' ? 'success' :
                  selectedProfile.status === 'learning' ? 'warning' : 'error'
                }
              />
            </Box>

            <Typography variant="body2" color="text.secondary" paragraph>
              Semantic protection profile specializing in {selectedProfile.type.toLowerCase()} 
              threat detection and prevention.
            </Typography>

            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" gutterBottom>
                Effectiveness: {selectedProfile.effectiveness}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={selectedProfile.effectiveness}
                sx={{
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: getEffectivenessColor(selectedProfile.effectiveness),
                  },
                }}
              />
            </Box>

            <Typography variant="body2" color="text.secondary">
              Threats detected: {selectedProfile.threatsDetected}
              <br />
              Last activity: {formatTimestamp(selectedProfile.lastActivity)}
            </Typography>
          </Box>
        )}
      </Popover>
    </Card>
  );
};

// Real-time threat alert component
export const ThreatAlert: React.FC<{
  threat: ThreatDetection;
  onDismiss: () => void;
  onViewDetails: () => void;
}> = ({ threat, onDismiss, onViewDetails }) => {
  const theme = useTheme();

  return (
    <Alert
      severity={threat.severity === 'critical' || threat.severity === 'high' ? 'error' : 'warning'}
      onClose={onDismiss}
      action={
        <IconButton size="small" onClick={onViewDetails}>
          <VisibilityRounded />
        </IconButton>
      }
      sx={{
        position: 'fixed',
        top: 16,
        right: 16,
        zIndex: theme.zIndex.snackbar,
        minWidth: 350,
        maxWidth: 500,
      }}
    >
      <Typography variant="body2" fontWeight="bold">
        {threat.blocked ? 'Threat Blocked' : 'Threat Detected'}: {threat.type.toUpperCase()}
      </Typography>
      <Typography variant="body2">
        {threat.description}
      </Typography>
      <Typography variant="caption" display="block">
        Detected by {threat.detectedBy} • {formatTimestamp(threat.timestamp)}
      </Typography>
    </Alert>
  );
};

export default ThreatDetectionIndicators;
