// OCTAVE Healthcare Platform - Password Reset Confirmation Form
// HIPAA Compliant Password Reset with Strength Validation

import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  CircularProgress,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  LockRounded,
  CheckCircleRounded,
  ErrorRounded,
  SecurityRounded,
} from '@mui/icons-material';

import { AuthService } from '@/services/authService';
import { PasswordResetConfirmRequest } from '@/types';

interface PasswordFormData {
  newPassword: string;
  confirmPassword: string;
}

export const PasswordResetConfirmForm: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const resetToken = searchParams.get('token');

  // Form state
  const [formData, setFormData] = useState<PasswordFormData>({
    newPassword: '',
    confirmPassword: '',
  });

  // UI state
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<Partial<PasswordFormData>>({});

  // Password strength state
  const [passwordStrength, setPasswordStrength] = useState({
    isValid: false,
    score: 0,
    feedback: [] as string[],
  });

  useEffect(() => {
    if (!resetToken) {
      setError('Invalid or missing reset token. Please request a new password reset.');
      return;
    }
  }, [resetToken]);

  useEffect(() => {
    // Update password strength when password changes
    if (formData.newPassword) {
      const strength = AuthService.validatePasswordStrength(formData.newPassword);
      setPasswordStrength(strength);
    } else {
      setPasswordStrength({ isValid: false, score: 0, feedback: [] });
    }
  }, [formData.newPassword]);

  const validateForm = (): boolean => {
    const errors: Partial<PasswordFormData> = {};

    if (!formData.newPassword) {
      errors.newPassword = 'New password is required';
    } else if (!passwordStrength.isValid) {
      errors.newPassword = 'Password does not meet security requirements';
    }

    if (!formData.confirmPassword) {
      errors.confirmPassword = 'Please confirm your password';
    } else if (formData.newPassword !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: keyof PasswordFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: undefined }));
    }

    // Clear general error
    if (error) {
      setError(null);
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!resetToken) {
      setError('Invalid reset token');
      return;
    }

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const resetRequest: PasswordResetConfirmRequest = {
        resetToken,
        newPassword: formData.newPassword,
        confirmPassword: formData.confirmPassword,
      };

      await AuthService.confirmPasswordReset(resetRequest);
      setSuccess(true);
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login', { 
          state: { message: 'Password reset successful. Please log in with your new password.' }
        });
      }, 3000);
    } catch (error: any) {
      setError(error.message || 'Failed to reset password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getPasswordStrengthColor = (score: number): string => {
    if (score <= 2) return theme.palette.error.main;
    if (score <= 4) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  const getPasswordStrengthLabel = (score: number): string => {
    if (score <= 2) return 'Weak';
    if (score <= 4) return 'Good';
    return 'Strong';
  };

  if (success) {
    return (
      <Card sx={{ maxWidth: 400, mx: 'auto', mt: 4 }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <CheckCircleRounded sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
            <Typography variant="h5" component="h1" gutterBottom>
              Password Reset Successful
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Your password has been updated successfully. You will be redirected to the login page.
            </Typography>
          </Box>

          <LinearProgress sx={{ mb: 2 }} />
          
          <Typography variant="body2" color="text.secondary" textAlign="center">
            Redirecting to login page...
          </Typography>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ maxWidth: 500, mx: 'auto', mt: 4 }}>
      <CardContent sx={{ p: 4 }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <SecurityRounded sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
          <Typography variant="h5" component="h1" gutterBottom>
            Create New Password
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Please create a strong password for your account
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit}>
          <TextField
            fullWidth
            label="New Password"
            type={showPassword ? 'text' : 'password'}
            value={formData.newPassword}
            onChange={handleInputChange('newPassword')}
            error={!!fieldErrors.newPassword}
            helperText={fieldErrors.newPassword}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <LockRounded />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
            autoComplete="new-password"
            autoFocus
          />

          {formData.newPassword && (
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" sx={{ mr: 1 }}>
                  Password Strength:
                </Typography>
                <Typography 
                  variant="body2" 
                  sx={{ 
                    color: getPasswordStrengthColor(passwordStrength.score),
                    fontWeight: 'bold'
                  }}
                >
                  {getPasswordStrengthLabel(passwordStrength.score)}
                </Typography>
              </Box>
              
              <LinearProgress
                variant="determinate"
                value={(passwordStrength.score / 6) * 100}
                sx={{
                  mb: 1,
                  '& .MuiLinearProgress-bar': {
                    backgroundColor: getPasswordStrengthColor(passwordStrength.score),
                  },
                }}
              />

              {passwordStrength.feedback.length > 0 && (
                <List dense>
                  {passwordStrength.feedback.map((feedback, index) => (
                    <ListItem key={index} sx={{ py: 0 }}>
                      <ListItemIcon sx={{ minWidth: 24 }}>
                        <ErrorRounded sx={{ fontSize: 16, color: 'error.main' }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary={feedback}
                        primaryTypographyProps={{ variant: 'caption' }}
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </Box>
          )}

          <TextField
            fullWidth
            label="Confirm New Password"
            type={showConfirmPassword ? 'text' : 'password'}
            value={formData.confirmPassword}
            onChange={handleInputChange('confirmPassword')}
            error={!!fieldErrors.confirmPassword}
            helperText={fieldErrors.confirmPassword}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <LockRounded />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    edge="end"
                  >
                    {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{ mb: 3 }}
            autoComplete="new-password"
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            size="large"
            disabled={isLoading || !passwordStrength.isValid}
            sx={{ mb: 2 }}
          >
            {isLoading ? <CircularProgress size={24} /> : 'Update Password'}
          </Button>
        </Box>

        <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary">
            <strong>Password Requirements:</strong>
            <br />
            • At least 8 characters long
            <br />
            • Contains uppercase and lowercase letters
            <br />
            • Contains at least one number
            <br />
            • Contains at least one special character
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default PasswordResetConfirmForm;
