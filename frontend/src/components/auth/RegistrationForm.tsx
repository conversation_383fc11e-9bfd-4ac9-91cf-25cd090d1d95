// OCTAVE Healthcare Platform - User Registration Form Component
// HIPAA Compliant Registration with Role-Based Onboarding

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  Stepper,
  Step,
  StepLabel,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  InputAdornment,
  IconButton,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  PersonRounded,
  EmailRounded,
  LockRounded,
  BusinessRounded,
  CheckCircleRounded,
  ErrorRounded,
  SecurityRounded,
} from '@mui/icons-material';

import { AuthService } from '@/services/authService';
import { UserRole } from '@/types';

interface RegistrationFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: UserRole | '';
  practiceId: string;
  practiceName: string;
  agreeToTerms: boolean;
  agreeToHipaa: boolean;
}

interface RegistrationFormProps {
  onBackToLogin?: () => void;
}

const steps = ['Personal Information', 'Account Details', 'Practice Information', 'Review & Submit'];

export const RegistrationForm: React.FC<RegistrationFormProps> = ({
  onBackToLogin,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();

  // Form state
  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState<RegistrationFormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: '',
    practiceId: '',
    practiceName: '',
    agreeToTerms: false,
    agreeToHipaa: false,
  });

  // UI state
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<Partial<RegistrationFormData>>({});

  // Password strength state
  const [passwordStrength, setPasswordStrength] = useState({
    isValid: false,
    score: 0,
    feedback: [] as string[],
  });

  useEffect(() => {
    // Update password strength when password changes
    if (formData.password) {
      const strength = AuthService.validatePasswordStrength(formData.password);
      setPasswordStrength(strength);
    } else {
      setPasswordStrength({ isValid: false, score: 0, feedback: [] });
    }
  }, [formData.password]);

  const handleInputChange = (field: keyof RegistrationFormData) => (
    event: React.ChangeEvent<HTMLInputElement | { value: unknown }>
  ) => {
    const value = event.target.type === 'checkbox' 
      ? (event.target as HTMLInputElement).checked 
      : event.target.value;
    
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: undefined }));
    }

    // Clear general error
    if (error) {
      setError(null);
    }
  };

  const validateStep = (step: number): boolean => {
    const errors: Partial<RegistrationFormData> = {};

    switch (step) {
      case 0: // Personal Information
        if (!formData.firstName.trim()) {
          errors.firstName = 'First name is required';
        }
        if (!formData.lastName.trim()) {
          errors.lastName = 'Last name is required';
        }
        if (!formData.email.trim()) {
          errors.email = 'Email is required';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
          errors.email = 'Please enter a valid email address';
        }
        break;

      case 1: // Account Details
        if (!formData.password) {
          errors.password = 'Password is required';
        } else if (!passwordStrength.isValid) {
          errors.password = 'Password does not meet security requirements';
        }
        if (!formData.confirmPassword) {
          errors.confirmPassword = 'Please confirm your password';
        } else if (formData.password !== formData.confirmPassword) {
          errors.confirmPassword = 'Passwords do not match';
        }
        if (!formData.role) {
          errors.role = 'Please select your role';
        }
        break;

      case 2: // Practice Information
        if (!formData.practiceId.trim()) {
          errors.practiceId = 'Practice ID is required';
        }
        if (!formData.practiceName.trim()) {
          errors.practiceName = 'Practice name is required';
        }
        break;

      case 3: // Review & Submit
        if (!formData.agreeToTerms) {
          errors.agreeToTerms = 'You must agree to the terms of service';
        }
        if (!formData.agreeToHipaa) {
          errors.agreeToHipaa = 'You must agree to HIPAA compliance requirements';
        }
        break;
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleSubmit = async () => {
    if (!validateStep(activeStep)) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // In a real implementation, this would call the registration API
      // For now, we'll simulate the registration process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Show success message and redirect
      navigate('/login', {
        state: {
          message: 'Registration successful! Please check your email to verify your account.',
          email: formData.email,
        },
      });
    } catch (error: any) {
      setError(error.message || 'Registration failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const getPasswordStrengthColor = (score: number): string => {
    if (score <= 2) return theme.palette.error.main;
    if (score <= 4) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  const getPasswordStrengthLabel = (score: number): string => {
    if (score <= 2) return 'Weak';
    if (score <= 4) return 'Good';
    return 'Strong';
  };

  const renderStepContent = (step: number) => {
    switch (step) {
      case 0:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Personal Information
            </Typography>
            <TextField
              fullWidth
              label="First Name"
              value={formData.firstName}
              onChange={handleInputChange('firstName')}
              error={!!fieldErrors.firstName}
              helperText={fieldErrors.firstName}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonRounded />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
              autoFocus
            />
            <TextField
              fullWidth
              label="Last Name"
              value={formData.lastName}
              onChange={handleInputChange('lastName')}
              error={!!fieldErrors.lastName}
              helperText={fieldErrors.lastName}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonRounded />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Email Address"
              type="email"
              value={formData.email}
              onChange={handleInputChange('email')}
              error={!!fieldErrors.email}
              helperText={fieldErrors.email}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailRounded />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Account Details
            </Typography>
            <TextField
              fullWidth
              label="Password"
              type={showPassword ? 'text' : 'password'}
              value={formData.password}
              onChange={handleInputChange('password')}
              error={!!fieldErrors.password}
              helperText={fieldErrors.password}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockRounded />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowPassword(!showPassword)}
                      edge="end"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />

            {formData.password && (
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Typography variant="body2" sx={{ mr: 1 }}>
                    Password Strength:
                  </Typography>
                  <Typography 
                    variant="body2" 
                    sx={{ 
                      color: getPasswordStrengthColor(passwordStrength.score),
                      fontWeight: 'bold'
                    }}
                  >
                    {getPasswordStrengthLabel(passwordStrength.score)}
                  </Typography>
                </Box>
                
                <LinearProgress
                  variant="determinate"
                  value={(passwordStrength.score / 6) * 100}
                  sx={{
                    mb: 1,
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: getPasswordStrengthColor(passwordStrength.score),
                    },
                  }}
                />

                {passwordStrength.feedback.length > 0 && (
                  <List dense>
                    {passwordStrength.feedback.map((feedback, index) => (
                      <ListItem key={index} sx={{ py: 0 }}>
                        <ListItemIcon sx={{ minWidth: 24 }}>
                          <ErrorRounded sx={{ fontSize: 16, color: 'error.main' }} />
                        </ListItemIcon>
                        <ListItemText 
                          primary={feedback}
                          primaryTypographyProps={{ variant: 'caption' }}
                        />
                      </ListItem>
                    ))}
                  </List>
                )}
              </Box>
            )}

            <TextField
              fullWidth
              label="Confirm Password"
              type={showConfirmPassword ? 'text' : 'password'}
              value={formData.confirmPassword}
              onChange={handleInputChange('confirmPassword')}
              error={!!fieldErrors.confirmPassword}
              helperText={fieldErrors.confirmPassword}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockRounded />
                  </InputAdornment>
                ),
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      edge="end"
                    >
                      {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />

            <FormControl fullWidth error={!!fieldErrors.role}>
              <InputLabel>Role</InputLabel>
              <Select
                value={formData.role}
                onChange={handleInputChange('role')}
                label="Role"
              >
                <MenuItem value={UserRole.PROVIDER}>Healthcare Provider</MenuItem>
                <MenuItem value={UserRole.STAFF}>Medical Staff</MenuItem>
                <MenuItem value={UserRole.BILLING}>Billing Specialist</MenuItem>
                <MenuItem value={UserRole.PRACTICE_ADMIN}>Practice Administrator</MenuItem>
              </Select>
              {fieldErrors.role && (
                <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 2 }}>
                  {fieldErrors.role}
                </Typography>
              )}
            </FormControl>
          </Box>
        );

      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Practice Information
            </Typography>
            <TextField
              fullWidth
              label="Practice ID"
              value={formData.practiceId}
              onChange={handleInputChange('practiceId')}
              error={!!fieldErrors.practiceId}
              helperText={fieldErrors.practiceId || 'Contact your practice administrator for this ID'}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <BusinessRounded />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />
            <TextField
              fullWidth
              label="Practice Name"
              value={formData.practiceName}
              onChange={handleInputChange('practiceName')}
              error={!!fieldErrors.practiceName}
              helperText={fieldErrors.practiceName}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <BusinessRounded />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />
          </Box>
        );

      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review & Submit
            </Typography>
            
            <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" gutterBottom>
                <strong>Name:</strong> {formData.firstName} {formData.lastName}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Email:</strong> {formData.email}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Role:</strong> {formData.role}
              </Typography>
              <Typography variant="body2" gutterBottom>
                <strong>Practice:</strong> {formData.practiceName} ({formData.practiceId})
              </Typography>
            </Box>

            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.agreeToTerms}
                  onChange={handleInputChange('agreeToTerms')}
                />
              }
              label="I agree to the Terms of Service and Privacy Policy"
              sx={{ mb: 1 }}
            />
            {fieldErrors.agreeToTerms && (
              <Typography variant="caption" color="error" display="block" sx={{ mb: 1 }}>
                {fieldErrors.agreeToTerms}
              </Typography>
            )}

            <FormControlLabel
              control={
                <Checkbox
                  checked={formData.agreeToHipaa}
                  onChange={handleInputChange('agreeToHipaa')}
                />
              }
              label="I acknowledge and agree to HIPAA compliance requirements"
              sx={{ mb: 1 }}
            />
            {fieldErrors.agreeToHipaa && (
              <Typography variant="caption" color="error" display="block">
                {fieldErrors.agreeToHipaa}
              </Typography>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Card sx={{ maxWidth: 600, mx: 'auto', mt: 4 }}>
      <CardContent sx={{ p: 4 }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <SecurityRounded sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
          <Typography variant="h4" component="h1" gutterBottom>
            Create Account
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Join the OCTAVE Healthcare Platform
          </Typography>
        </Box>

        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {renderStepContent(activeStep)}

        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 3 }}>
          <Button
            onClick={activeStep === 0 ? onBackToLogin : handleBack}
            disabled={isLoading}
          >
            {activeStep === 0 ? 'Back to Login' : 'Back'}
          </Button>
          
          <Button
            variant="contained"
            onClick={activeStep === steps.length - 1 ? handleSubmit : handleNext}
            disabled={isLoading}
          >
            {isLoading ? 'Creating Account...' : 
             activeStep === steps.length - 1 ? 'Create Account' : 'Next'}
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

export default RegistrationForm;
