// OCTAVE Healthcare Platform - Security Notifications Component
// HIPAA Compliant Security Alerts and Account Lockout Notifications

import React, { useState, useEffect } from 'react';
import {
  Alert,
  AlertTitle,
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  IconButton,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Snackbar,
  Typography,
  useTheme,
} from '@mui/material';
import {
  SecurityRounded,
  WarningRounded,
  ErrorRounded,
  InfoRounded,
  CloseRounded,
  LockRounded,
  VpnKeyRounded,
  DevicesRounded,
  LocationOnRounded,
  AccessTimeRounded,
} from '@mui/icons-material';

export interface SecurityNotification {
  id: string;
  type: 'account_locked' | 'suspicious_activity' | 'new_device' | 'password_expiry' | 'mfa_required' | 'breach_alert';
  severity: 'info' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  details?: Record<string, any>;
  actionRequired?: boolean;
  dismissed?: boolean;
}

interface SecurityNotificationsProps {
  notifications: SecurityNotification[];
  onDismiss?: (notificationId: string) => void;
  onAction?: (notificationId: string, action: string) => void;
}

export const SecurityNotifications: React.FC<SecurityNotificationsProps> = ({
  notifications,
  onDismiss,
  onAction,
}) => {
  const theme = useTheme();
  const [selectedNotification, setSelectedNotification] = useState<SecurityNotification | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  // Filter active notifications
  const activeNotifications = notifications.filter(n => !n.dismissed);
  const criticalNotifications = activeNotifications.filter(n => n.severity === 'error');
  const warningNotifications = activeNotifications.filter(n => n.severity === 'warning');

  const getNotificationIcon = (type: SecurityNotification['type']) => {
    switch (type) {
      case 'account_locked':
        return <LockRounded />;
      case 'suspicious_activity':
        return <WarningRounded />;
      case 'new_device':
        return <DevicesRounded />;
      case 'password_expiry':
        return <VpnKeyRounded />;
      case 'mfa_required':
        return <SecurityRounded />;
      case 'breach_alert':
        return <ErrorRounded />;
      default:
        return <InfoRounded />;
    }
  };

  const getNotificationColor = (severity: SecurityNotification['severity']) => {
    switch (severity) {
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'info':
      default:
        return 'info';
    }
  };

  const handleDismiss = (notificationId: string) => {
    onDismiss?.(notificationId);
    setSnackbarMessage('Notification dismissed');
    setSnackbarOpen(true);
  };

  const handleAction = (notificationId: string, action: string) => {
    onAction?.(notificationId, action);
    setSelectedNotification(null);
  };

  const formatTimestamp = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    return 'Just now';
  };

  const renderNotificationDetails = (notification: SecurityNotification) => {
    const { details } = notification;
    if (!details) return null;

    return (
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2" gutterBottom>
          Additional Details:
        </Typography>
        <List dense>
          {details.ipAddress && (
            <ListItem>
              <ListItemIcon>
                <LocationOnRounded fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="IP Address" secondary={details.ipAddress} />
            </ListItem>
          )}
          {details.deviceInfo && (
            <ListItem>
              <ListItemIcon>
                <DevicesRounded fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Device" secondary={details.deviceInfo} />
            </ListItem>
          )}
          {details.location && (
            <ListItem>
              <ListItemIcon>
                <LocationOnRounded fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Location" secondary={details.location} />
            </ListItem>
          )}
          {details.attemptCount && (
            <ListItem>
              <ListItemIcon>
                <AccessTimeRounded fontSize="small" />
              </ListItemIcon>
              <ListItemText 
                primary="Failed Attempts" 
                secondary={`${details.attemptCount} attempts in the last hour`} 
              />
            </ListItem>
          )}
        </List>
      </Box>
    );
  };

  const renderNotificationActions = (notification: SecurityNotification) => {
    switch (notification.type) {
      case 'account_locked':
        return (
          <>
            <Button onClick={() => handleAction(notification.id, 'contact_admin')}>
              Contact Administrator
            </Button>
            <Button onClick={() => handleAction(notification.id, 'reset_password')}>
              Reset Password
            </Button>
          </>
        );
      case 'new_device':
        return (
          <>
            <Button onClick={() => handleAction(notification.id, 'approve_device')}>
              Approve Device
            </Button>
            <Button onClick={() => handleAction(notification.id, 'block_device')}>
              Block Device
            </Button>
          </>
        );
      case 'password_expiry':
        return (
          <Button onClick={() => handleAction(notification.id, 'change_password')}>
            Change Password Now
          </Button>
        );
      case 'mfa_required':
        return (
          <Button onClick={() => handleAction(notification.id, 'setup_mfa')}>
            Set Up MFA
          </Button>
        );
      default:
        return null;
    }
  };

  if (activeNotifications.length === 0) {
    return null;
  }

  return (
    <>
      {/* Critical Notifications Banner */}
      {criticalNotifications.length > 0 && (
        <Alert 
          severity="error" 
          sx={{ mb: 2 }}
          action={
            <IconButton
              color="inherit"
              size="small"
              onClick={() => handleDismiss(criticalNotifications[0].id)}
            >
              <CloseRounded fontSize="inherit" />
            </IconButton>
          }
        >
          <AlertTitle>Security Alert</AlertTitle>
          {criticalNotifications[0].message}
          {criticalNotifications[0].actionRequired && (
            <Button 
              color="inherit" 
              size="small" 
              sx={{ mt: 1 }}
              onClick={() => setSelectedNotification(criticalNotifications[0])}
            >
              Take Action
            </Button>
          )}
        </Alert>
      )}

      {/* Warning Notifications */}
      {warningNotifications.map((notification) => (
        <Alert
          key={notification.id}
          severity="warning"
          sx={{ mb: 1 }}
          action={
            <IconButton
              color="inherit"
              size="small"
              onClick={() => handleDismiss(notification.id)}
            >
              <CloseRounded fontSize="inherit" />
            </IconButton>
          }
        >
          {notification.message}
          {notification.actionRequired && (
            <Button 
              color="inherit" 
              size="small" 
              sx={{ ml: 1 }}
              onClick={() => setSelectedNotification(notification)}
            >
              Details
            </Button>
          )}
        </Alert>
      ))}

      {/* Notification Details Dialog */}
      <Dialog
        open={!!selectedNotification}
        onClose={() => setSelectedNotification(null)}
        maxWidth="sm"
        fullWidth
      >
        {selectedNotification && (
          <>
            <DialogTitle>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getNotificationIcon(selectedNotification.type)}
                <Typography variant="h6">
                  {selectedNotification.title}
                </Typography>
                <Chip
                  label={selectedNotification.severity.toUpperCase()}
                  color={getNotificationColor(selectedNotification.severity)}
                  size="small"
                />
              </Box>
            </DialogTitle>

            <DialogContent>
              <Typography variant="body1" paragraph>
                {selectedNotification.message}
              </Typography>

              <Typography variant="caption" color="text.secondary">
                {formatTimestamp(selectedNotification.timestamp)}
              </Typography>

              {renderNotificationDetails(selectedNotification)}

              {selectedNotification.type === 'breach_alert' && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  <AlertTitle>HIPAA Compliance Notice</AlertTitle>
                  This security incident may require reporting under HIPAA breach notification rules. 
                  Please contact your compliance officer immediately.
                </Alert>
              )}
            </DialogContent>

            <DialogActions>
              <Button onClick={() => setSelectedNotification(null)}>
                Close
              </Button>
              {renderNotificationActions(selectedNotification)}
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Snackbar for feedback */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        onClose={() => setSnackbarOpen(false)}
        message={snackbarMessage}
      />
    </>
  );
};

// Hook for managing security notifications
export const useSecurityNotifications = () => {
  const [notifications, setNotifications] = useState<SecurityNotification[]>([]);

  const addNotification = (notification: Omit<SecurityNotification, 'id' | 'timestamp'>) => {
    const newNotification: SecurityNotification = {
      ...notification,
      id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
    };
    setNotifications(prev => [newNotification, ...prev]);
  };

  const dismissNotification = (notificationId: string) => {
    setNotifications(prev =>
      prev.map(n => n.id === notificationId ? { ...n, dismissed: true } : n)
    );
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  return {
    notifications,
    addNotification,
    dismissNotification,
    clearAllNotifications,
  };
};

export default SecurityNotifications;

// Example usage and notification types for reference
export const createAccountLockedNotification = (details: { attemptCount: number; ipAddress: string }): Omit<SecurityNotification, 'id' | 'timestamp'> => ({
  type: 'account_locked',
  severity: 'error',
  title: 'Account Locked',
  message: 'Your account has been locked due to multiple failed login attempts.',
  details,
  actionRequired: true,
});

export const createSuspiciousActivityNotification = (details: { ipAddress: string; location?: string }): Omit<SecurityNotification, 'id' | 'timestamp'> => ({
  type: 'suspicious_activity',
  severity: 'warning',
  title: 'Suspicious Activity Detected',
  message: 'Unusual login activity has been detected on your account.',
  details,
  actionRequired: true,
});

export const createNewDeviceNotification = (details: { deviceInfo: string; ipAddress: string; location?: string }): Omit<SecurityNotification, 'id' | 'timestamp'> => ({
  type: 'new_device',
  severity: 'warning',
  title: 'New Device Login',
  message: 'A new device has been used to access your account.',
  details,
  actionRequired: true,
});

export const createPasswordExpiryNotification = (daysUntilExpiry: number): Omit<SecurityNotification, 'id' | 'timestamp'> => ({
  type: 'password_expiry',
  severity: daysUntilExpiry <= 3 ? 'error' : 'warning',
  title: 'Password Expiring Soon',
  message: `Your password will expire in ${daysUntilExpiry} day${daysUntilExpiry > 1 ? 's' : ''}.`,
  actionRequired: true,
});

export const createMfaRequiredNotification = (): Omit<SecurityNotification, 'id' | 'timestamp'> => ({
  type: 'mfa_required',
  severity: 'warning',
  title: 'Multi-Factor Authentication Required',
  message: 'Your account requires multi-factor authentication to be enabled.',
  actionRequired: true,
});

export const createBreachAlertNotification = (details: { affectedData: string; incidentId: string }): Omit<SecurityNotification, 'id' | 'timestamp'> => ({
  type: 'breach_alert',
  severity: 'error',
  title: 'Security Breach Alert',
  message: 'A potential security breach has been detected that may affect your data.',
  details,
  actionRequired: true,
});
