// OCTAVE Healthcare Platform - Session Timeout Warning Component
// HIPAA Compliant Session Management

import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  LinearProgress,
  Alert,
  useTheme,
} from '@mui/material';
import {
  AccessTimeRounded,
  WarningRounded,
  SecurityRounded,
} from '@mui/icons-material';

import { selectAuth, logout, refreshTokenSuccess } from '@/store/slices/authSlice';
import { AuthService } from '@/services/authService';

interface SessionTimeoutWarningProps {
  warningTimeMinutes?: number; // Show warning X minutes before expiry
  autoLogoutTimeMinutes?: number; // Auto logout X minutes after warning
}

export const SessionTimeoutWarning: React.FC<SessionTimeoutWarningProps> = ({
  warningTimeMinutes = 5,
  autoLogoutTimeMinutes = 2,
}) => {
  const theme = useTheme();
  const dispatch = useDispatch();
  const auth = useSelector(selectAuth);

  // State
  const [showWarning, setShowWarning] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [isExtending, setIsExtending] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Calculate time until session expires
  const getTimeUntilExpiry = useCallback((): number => {
    if (!auth.expiresAt) return 0;
    const now = new Date().getTime();
    const expiry = new Date(auth.expiresAt).getTime();
    return Math.max(0, expiry - now);
  }, [auth.expiresAt]);

  // Format time remaining for display
  const formatTimeRemaining = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Handle session extension
  const handleExtendSession = async () => {
    setIsExtending(true);
    setError(null);

    try {
      const refreshResult = await AuthService.refreshToken();
      dispatch(refreshTokenSuccess(refreshResult));
      setShowWarning(false);
      setTimeRemaining(0);
    } catch (error: any) {
      setError(error.message || 'Failed to extend session');
      // If refresh fails, logout user
      setTimeout(() => {
        handleLogout();
      }, 2000);
    } finally {
      setIsExtending(false);
    }
  };

  // Handle logout
  const handleLogout = useCallback(async () => {
    try {
      await AuthService.logout();
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      dispatch(logout());
      setShowWarning(false);
    }
  }, [dispatch]);

  // Main timer effect
  useEffect(() => {
    if (!auth.isAuthenticated || !auth.expiresAt) {
      setShowWarning(false);
      return;
    }

    const checkSessionStatus = () => {
      const timeUntilExpiry = getTimeUntilExpiry();
      const warningThreshold = warningTimeMinutes * 60 * 1000;
      const autoLogoutThreshold = autoLogoutTimeMinutes * 60 * 1000;

      if (timeUntilExpiry <= 0) {
        // Session expired
        handleLogout();
        return;
      }

      if (timeUntilExpiry <= autoLogoutThreshold && showWarning) {
        // Auto logout time reached
        handleLogout();
        return;
      }

      if (timeUntilExpiry <= warningThreshold) {
        // Show warning
        setShowWarning(true);
        setTimeRemaining(timeUntilExpiry);
      } else {
        // Session is still valid
        setShowWarning(false);
        setTimeRemaining(0);
      }
    };

    // Check immediately
    checkSessionStatus();

    // Set up interval to check every second
    const interval = setInterval(checkSessionStatus, 1000);

    return () => clearInterval(interval);
  }, [
    auth.isAuthenticated,
    auth.expiresAt,
    warningTimeMinutes,
    autoLogoutTimeMinutes,
    showWarning,
    getTimeUntilExpiry,
    handleLogout,
  ]);

  // Update countdown timer
  useEffect(() => {
    if (!showWarning) return;

    const updateTimer = () => {
      const timeUntilExpiry = getTimeUntilExpiry();
      setTimeRemaining(timeUntilExpiry);

      if (timeUntilExpiry <= 0) {
        handleLogout();
      }
    };

    const interval = setInterval(updateTimer, 1000);
    return () => clearInterval(interval);
  }, [showWarning, getTimeUntilExpiry, handleLogout]);

  if (!showWarning) {
    return null;
  }

  const progressValue = Math.max(0, (timeRemaining / (warningTimeMinutes * 60 * 1000)) * 100);
  const isUrgent = timeRemaining <= autoLogoutTimeMinutes * 60 * 1000;

  return (
    <Dialog
      open={showWarning}
      disableEscapeKeyDown
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          border: `2px solid ${isUrgent ? theme.palette.error.main : theme.palette.warning.main}`,
        },
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          {isUrgent ? (
            <SecurityRounded sx={{ color: 'error.main' }} />
          ) : (
            <AccessTimeRounded sx={{ color: 'warning.main' }} />
          )}
          <Typography variant="h6">
            {isUrgent ? 'Session Expiring Soon' : 'Session Timeout Warning'}
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Alert 
          severity={isUrgent ? 'error' : 'warning'} 
          icon={<WarningRounded />}
          sx={{ mb: 2 }}
        >
          <Typography variant="body2">
            {isUrgent 
              ? 'Your session will expire automatically for security reasons.'
              : 'Your session is about to expire due to inactivity.'
            }
          </Typography>
        </Alert>

        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Time remaining:
          </Typography>
          <Typography 
            variant="h4" 
            sx={{ 
              color: isUrgent ? 'error.main' : 'warning.main',
              fontFamily: 'monospace',
              textAlign: 'center',
              mb: 1,
            }}
          >
            {formatTimeRemaining(timeRemaining)}
          </Typography>
          
          <LinearProgress
            variant="determinate"
            value={progressValue}
            sx={{
              height: 8,
              borderRadius: 4,
              '& .MuiLinearProgress-bar': {
                backgroundColor: isUrgent ? 'error.main' : 'warning.main',
              },
            }}
          />
        </Box>

        <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="body2" color="text.secondary">
            <strong>HIPAA Security Notice:</strong> For your protection and to maintain 
            compliance with healthcare privacy regulations, inactive sessions are 
            automatically terminated.
          </Typography>
        </Box>
      </DialogContent>

      <DialogActions sx={{ p: 3, pt: 1 }}>
        <Button
          onClick={handleLogout}
          variant="outlined"
          disabled={isExtending}
        >
          Logout Now
        </Button>
        
        <Button
          onClick={handleExtendSession}
          variant="contained"
          disabled={isExtending || isUrgent}
          sx={{ ml: 1 }}
        >
          {isExtending ? 'Extending...' : 'Extend Session'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SessionTimeoutWarning;
