// OCTAVE Healthcare Platform - Login Form Component
// HIPAA Compliant Authentication with MFA Support

import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  FormControlLabel,
  Checkbox,
  InputAdornment,
  IconButton,
  CircularProgress,
  Link,
  Divider,
  useTheme,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  SecurityRounded,
  EmailRounded,
  LockRounded,
  BusinessRounded,
} from '@mui/icons-material';

import { loginSuccess } from '@/store/slices/authSlice';
import { AuthService } from '@/services/authService';
import { LoginRequest, MfaVerificationRequest } from '@/types';

interface LoginFormProps {
  onForgotPassword?: () => void;
  onRegister?: () => void;
}

interface LoginFormData {
  email: string;
  password: string;
  practiceId: string;
  rememberMe: boolean;
}

interface MfaFormData {
  mfaCode: string;
  rememberDevice: boolean;
}

export const LoginForm: React.FC<LoginFormProps> = ({
  onForgotPassword,
  onRegister,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();

  // Form state
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    practiceId: '',
    rememberMe: false,
  });

  const [mfaData, setMfaData] = useState<MfaFormData>({
    mfaCode: '',
    rememberDevice: false,
  });

  // UI state
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showMfa, setShowMfa] = useState(false);
  const [mfaToken, setMfaToken] = useState<string | null>(null);

  // Form validation
  const [fieldErrors, setFieldErrors] = useState<Partial<LoginFormData>>({});

  useEffect(() => {
    // Clear error when user starts typing
    if (error) {
      setError(null);
    }
  }, [formData, mfaData]);

  const validateForm = (): boolean => {
    const errors: Partial<LoginFormData> = {};

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.password.trim()) {
      errors.password = 'Password is required';
    }

    if (!formData.practiceId.trim()) {
      errors.practiceId = 'Practice ID is required';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: keyof LoginFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleMfaInputChange = (field: keyof MfaFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setMfaData(prev => ({ ...prev, [field]: value }));
  };

  const handleLogin = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const loginRequest: LoginRequest = {
        email: formData.email.trim(),
        password: formData.password,
        practiceId: formData.practiceId.trim(),
        rememberMe: formData.rememberMe,
      };

      const response = await AuthService.login(loginRequest);

      if (response.requiresMfa && response.mfaToken) {
        // MFA required
        setMfaToken(response.mfaToken);
        setShowMfa(true);
      } else {
        // Login successful
        dispatch(loginSuccess(response));
        
        // Redirect to intended page or dashboard
        const from = (location.state as any)?.from?.pathname || '/dashboard';
        navigate(from, { replace: true });
      }
    } catch (error: any) {
      setError(error.message || 'Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleMfaVerification = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!mfaData.mfaCode.trim() || !mfaToken) {
      setError('Please enter the verification code');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const mfaRequest: MfaVerificationRequest = {
        mfaToken,
        mfaCode: mfaData.mfaCode.trim(),
        rememberDevice: mfaData.rememberDevice,
      };

      const response = await AuthService.verifyMfa(mfaRequest);
      
      // MFA verification successful
      dispatch(loginSuccess(response));
      
      // Redirect to intended page or dashboard
      const from = (location.state as any)?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    } catch (error: any) {
      setError(error.message || 'Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    setShowMfa(false);
    setMfaToken(null);
    setMfaData({ mfaCode: '', rememberDevice: false });
    setError(null);
  };

  if (showMfa) {
    return (
      <Card sx={{ maxWidth: 400, mx: 'auto', mt: 4 }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <SecurityRounded sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
            <Typography variant="h5" component="h1" gutterBottom>
              Two-Factor Authentication
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Enter the verification code sent to your registered device
            </Typography>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleMfaVerification}>
            <TextField
              fullWidth
              label="Verification Code"
              value={mfaData.mfaCode}
              onChange={handleMfaInputChange('mfaCode')}
              placeholder="Enter 6-digit code"
              inputProps={{ maxLength: 6, pattern: '[0-9]*' }}
              sx={{ mb: 2 }}
              autoFocus
            />

            <FormControlLabel
              control={
                <Checkbox
                  checked={mfaData.rememberDevice}
                  onChange={handleMfaInputChange('rememberDevice')}
                />
              }
              label="Remember this device for 30 days"
              sx={{ mb: 3 }}
            />

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              disabled={isLoading}
              sx={{ mb: 2 }}
            >
              {isLoading ? <CircularProgress size={24} /> : 'Verify'}
            </Button>

            <Button
              fullWidth
              variant="text"
              onClick={handleBackToLogin}
              disabled={isLoading}
            >
              Back to Login
            </Button>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ maxWidth: 400, mx: 'auto', mt: 4 }}>
      <CardContent sx={{ p: 4 }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <SecurityRounded sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
          <Typography variant="h4" component="h1" gutterBottom>
            OCTAVE Healthcare
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Secure Prior Authorization Platform
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleLogin}>
          <TextField
            fullWidth
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={handleInputChange('email')}
            error={!!fieldErrors.email}
            helperText={fieldErrors.email}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <EmailRounded />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
            autoComplete="email"
            autoFocus
          />

          <TextField
            fullWidth
            label="Practice ID"
            value={formData.practiceId}
            onChange={handleInputChange('practiceId')}
            error={!!fieldErrors.practiceId}
            helperText={fieldErrors.practiceId}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <BusinessRounded />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
            autoComplete="organization"
          />

          <TextField
            fullWidth
            label="Password"
            type={showPassword ? 'text' : 'password'}
            value={formData.password}
            onChange={handleInputChange('password')}
            error={!!fieldErrors.password}
            helperText={fieldErrors.password}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <LockRounded />
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
            autoComplete="current-password"
          />

          <FormControlLabel
            control={
              <Checkbox
                checked={formData.rememberMe}
                onChange={handleInputChange('rememberMe')}
              />
            }
            label="Remember me"
            sx={{ mb: 3 }}
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            size="large"
            disabled={isLoading}
            sx={{ mb: 2 }}
          >
            {isLoading ? <CircularProgress size={24} /> : 'Sign In'}
          </Button>

          <Box sx={{ textAlign: 'center' }}>
            <Link
              component="button"
              type="button"
              variant="body2"
              onClick={onForgotPassword}
              sx={{ mr: 2 }}
            >
              Forgot Password?
            </Link>
            
            {onRegister && (
              <>
                <Divider sx={{ my: 1 }}>or</Divider>
                <Link
                  component="button"
                  type="button"
                  variant="body2"
                  onClick={onRegister}
                >
                  Create New Account
                </Link>
              </>
            )}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default LoginForm;
