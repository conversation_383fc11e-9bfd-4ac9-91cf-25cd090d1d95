// OCTAVE Healthcare Platform - Security Settings Component
// HIPAA Compliant Security Configuration Dashboard

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Switch,
  FormControlLabel,
  Button,
  Typography,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Chip,
  useTheme,
} from '@mui/material';
import {
  SecurityRounded,
  VpnKeyRounded,
  DevicesRounded,
  AccessTimeRounded,
  DeleteRounded,
  EditRounded,
  PhoneRounded,
  EmailRounded,
  QrCodeRounded,
  FingerprintRounded,
} from '@mui/icons-material';

import { AuthService } from '@/services/authService';
import { SecuritySettings as SecuritySettingsType, MfaMethod, TrustedDevice, SessionInfo } from '@/types';

interface SecuritySettingsProps {
  onPasswordChange?: () => void;
}

export const SecuritySettings: React.FC<SecuritySettingsProps> = ({
  onPasswordChange,
}) => {
  const theme = useTheme();

  // State
  const [settings, setSettings] = useState<SecuritySettingsType | null>(null);
  const [sessions, setSessions] = useState<SessionInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showMfaDialog, setShowMfaDialog] = useState(false);
  const [showSessionDialog, setShowSessionDialog] = useState(false);
  const [selectedSession, setSelectedSession] = useState<SessionInfo | null>(null);

  useEffect(() => {
    loadSecurityData();
  }, []);

  const loadSecurityData = async () => {
    try {
      setLoading(true);
      const [settingsData, sessionsData] = await Promise.all([
        AuthService.getSecuritySettings(),
        AuthService.getActiveSessions(),
      ]);
      setSettings(settingsData);
      setSessions(sessionsData);
    } catch (error: any) {
      setError(error.message || 'Failed to load security settings');
    } finally {
      setLoading(false);
    }
  };

  const handleMfaToggle = async (enabled: boolean) => {
    if (!settings) return;

    try {
      const updatedSettings = await AuthService.updateSecuritySettings({
        mfaEnabled: enabled,
        mfaMethod: enabled ? MfaMethod.EMAIL : MfaMethod.NONE,
      });
      setSettings(updatedSettings);
    } catch (error: any) {
      setError(error.message || 'Failed to update MFA settings');
    }
  };

  const handleSessionTimeoutChange = async (timeout: number) => {
    if (!settings) return;

    try {
      const updatedSettings = await AuthService.updateSecuritySettings({
        sessionTimeout: timeout,
      });
      setSettings(updatedSettings);
    } catch (error: any) {
      setError(error.message || 'Failed to update session timeout');
    }
  };

  const handleTerminateSession = async (sessionId: string) => {
    try {
      await AuthService.terminateSession(sessionId);
      setSessions(prev => prev.filter(s => s.sessionId !== sessionId));
      setSelectedSession(null);
      setShowSessionDialog(false);
    } catch (error: any) {
      setError(error.message || 'Failed to terminate session');
    }
  };

  const getMfaMethodIcon = (method: MfaMethod) => {
    switch (method) {
      case MfaMethod.SMS:
        return <PhoneRounded />;
      case MfaMethod.EMAIL:
        return <EmailRounded />;
      case MfaMethod.AUTHENTICATOR:
        return <QrCodeRounded />;
      case MfaMethod.BIOMETRIC:
        return <FingerprintRounded />;
      default:
        return <SecurityRounded />;
    }
  };

  const formatLastActivity = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} day${days > 1 ? 's' : ''} ago`;
    if (hours > 0) return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    if (minutes > 0) return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    return 'Active now';
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography>Loading security settings...</Typography>
      </Box>
    );
  }

  if (!settings) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">Failed to load security settings</Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Security Settings
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Password Security */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          avatar={<VpnKeyRounded />}
          title="Password Security"
          subheader="Manage your password and authentication settings"
        />
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="body1">Password</Typography>
              <Typography variant="body2" color="text.secondary">
                Last changed: {new Date(settings.lastPasswordChange).toLocaleDateString()}
              </Typography>
            </Box>
            <Button variant="outlined" onClick={onPasswordChange}>
              Change Password
            </Button>
          </Box>

          <Divider sx={{ my: 2 }} />

          <FormControlLabel
            control={
              <Switch
                checked={settings.mfaEnabled}
                onChange={(e) => handleMfaToggle(e.target.checked)}
              />
            }
            label="Multi-Factor Authentication"
          />
          
          {settings.mfaEnabled && (
            <Box sx={{ mt: 2, pl: 4 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                {getMfaMethodIcon(settings.mfaMethod)}
                <Typography variant="body2">
                  Current method: {settings.mfaMethod}
                </Typography>
              </Box>
              <Button size="small" onClick={() => setShowMfaDialog(true)}>
                Configure MFA
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Session Management */}
      <Card sx={{ mb: 3 }}>
        <CardHeader
          avatar={<AccessTimeRounded />}
          title="Session Management"
          subheader="Control session timeout and active sessions"
        />
        <CardContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" gutterBottom>
              Session Timeout
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Automatically log out after inactivity
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
              {[15, 30, 60, 120].map((minutes) => (
                <Button
                  key={minutes}
                  size="small"
                  variant={settings.sessionTimeout === minutes ? 'contained' : 'outlined'}
                  onClick={() => handleSessionTimeoutChange(minutes)}
                >
                  {minutes} min
                </Button>
              ))}
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Typography variant="body1" gutterBottom>
            Active Sessions ({sessions.length})
          </Typography>
          <List>
            {sessions.map((session) => (
              <ListItem key={session.sessionId}>
                <ListItemIcon>
                  <DevicesRounded />
                </ListItemIcon>
                <ListItemText
                  primary={session.deviceInfo}
                  secondary={
                    <Box>
                      <Typography variant="caption" display="block">
                        IP: {session.ipAddress}
                      </Typography>
                      <Typography variant="caption" display="block">
                        Last activity: {formatLastActivity(session.lastActivity)}
                      </Typography>
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={() => {
                      setSelectedSession(session);
                      setShowSessionDialog(true);
                    }}
                  >
                    <DeleteRounded />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>

      {/* Trusted Devices */}
      <Card>
        <CardHeader
          avatar={<DevicesRounded />}
          title="Trusted Devices"
          subheader="Devices that don't require MFA"
        />
        <CardContent>
          {settings.trustedDevices.length === 0 ? (
            <Typography variant="body2" color="text.secondary">
              No trusted devices configured
            </Typography>
          ) : (
            <List>
              {settings.trustedDevices.map((device) => (
                <ListItem key={device.id}>
                  <ListItemIcon>
                    <DevicesRounded />
                  </ListItemIcon>
                  <ListItemText
                    primary={device.deviceName}
                    secondary={
                      <Box>
                        <Typography variant="caption" display="block">
                          {device.deviceType} • Last used: {formatLastActivity(device.lastUsed)}
                        </Typography>
                        <Typography variant="caption" display="block">
                          IP: {device.ipAddress}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton edge="end">
                      <DeleteRounded />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>

      {/* Session Termination Dialog */}
      <Dialog
        open={showSessionDialog}
        onClose={() => setShowSessionDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Terminate Session</DialogTitle>
        <DialogContent>
          {selectedSession && (
            <Box>
              <Typography variant="body1" gutterBottom>
                Are you sure you want to terminate this session?
              </Typography>
              <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="body2">
                  <strong>Device:</strong> {selectedSession.deviceInfo}
                </Typography>
                <Typography variant="body2">
                  <strong>IP Address:</strong> {selectedSession.ipAddress}
                </Typography>
                <Typography variant="body2">
                  <strong>Login Time:</strong> {new Date(selectedSession.loginTime).toLocaleString()}
                </Typography>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSessionDialog(false)}>Cancel</Button>
          <Button
            onClick={() => selectedSession && handleTerminateSession(selectedSession.sessionId)}
            color="error"
            variant="contained"
          >
            Terminate Session
          </Button>
        </DialogActions>
      </Dialog>

      {/* MFA Configuration Dialog */}
      <Dialog
        open={showMfaDialog}
        onClose={() => setShowMfaDialog(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Configure Multi-Factor Authentication</DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Choose your preferred MFA method:
          </Typography>
          
          <List>
            {Object.values(MfaMethod).filter(method => method !== MfaMethod.NONE).map((method) => (
              <ListItem
                key={method}
                button
                selected={settings.mfaMethod === method}
                onClick={() => {
                  // Handle MFA method selection
                  setShowMfaDialog(false);
                }}
              >
                <ListItemIcon>
                  {getMfaMethodIcon(method)}
                </ListItemIcon>
                <ListItemText
                  primary={method}
                  secondary={
                    method === MfaMethod.EMAIL ? 'Send codes to your email' :
                    method === MfaMethod.SMS ? 'Send codes to your phone' :
                    method === MfaMethod.AUTHENTICATOR ? 'Use authenticator app' :
                    'Use biometric authentication'
                  }
                />
                {settings.mfaMethod === method && (
                  <Chip label="Current" size="small" color="primary" />
                )}
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowMfaDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SecuritySettings;
