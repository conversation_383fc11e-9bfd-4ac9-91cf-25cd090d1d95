// OCTAVE Healthcare Platform - Password Reset Form Component
// HIPAA Compliant Password Recovery

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  CircularProgress,
  Link,
  useTheme,
} from '@mui/material';
import {
  EmailRounded,
  LockResetRounded,
  ArrowBackRounded,
  CheckCircleRounded,
  BusinessRounded,
} from '@mui/icons-material';

import { AuthService } from '@/services/authService';
import { PasswordResetRequest } from '@/types';

interface PasswordResetFormProps {
  onBackToLogin?: () => void;
}

interface ResetFormData {
  email: string;
  practiceId: string;
}

export const PasswordResetForm: React.FC<PasswordResetFormProps> = ({
  onBackToLogin,
}) => {
  const theme = useTheme();

  // Form state
  const [formData, setFormData] = useState<ResetFormData>({
    email: '',
    practiceId: '',
  });

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<Partial<ResetFormData>>({});

  const validateForm = (): boolean => {
    const errors: Partial<ResetFormData> = {};

    if (!formData.email.trim()) {
      errors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = 'Please enter a valid email address';
    }

    if (!formData.practiceId.trim()) {
      errors.practiceId = 'Practice ID is required';
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleInputChange = (field: keyof ResetFormData) => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (fieldErrors[field]) {
      setFieldErrors(prev => ({ ...prev, [field]: undefined }));
    }

    // Clear general error
    if (error) {
      setError(null);
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const resetRequest: PasswordResetRequest = {
        email: formData.email.trim(),
        practiceId: formData.practiceId.trim(),
      };

      await AuthService.requestPasswordReset(resetRequest);
      setSuccess(true);
    } catch (error: any) {
      setError(error.message || 'Failed to send password reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTryAgain = () => {
    setSuccess(false);
    setFormData({ email: '', practiceId: '' });
    setError(null);
    setFieldErrors({});
  };

  if (success) {
    return (
      <Card sx={{ maxWidth: 400, mx: 'auto', mt: 4 }}>
        <CardContent sx={{ p: 4 }}>
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            <CheckCircleRounded sx={{ fontSize: 48, color: 'success.main', mb: 2 }} />
            <Typography variant="h5" component="h1" gutterBottom>
              Reset Email Sent
            </Typography>
            <Typography variant="body2" color="text.secondary">
              We've sent password reset instructions to your email address.
            </Typography>
          </Box>

          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              <strong>Next Steps:</strong>
              <br />
              1. Check your email inbox (and spam folder)
              <br />
              2. Click the reset link in the email
              <br />
              3. Follow the instructions to create a new password
              <br />
              <br />
              The reset link will expire in 1 hour for security.
            </Typography>
          </Alert>

          <Button
            fullWidth
            variant="outlined"
            onClick={handleTryAgain}
            sx={{ mb: 2 }}
          >
            Send Another Reset Email
          </Button>

          <Button
            fullWidth
            variant="text"
            onClick={onBackToLogin}
            startIcon={<ArrowBackRounded />}
          >
            Back to Login
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ maxWidth: 400, mx: 'auto', mt: 4 }}>
      <CardContent sx={{ p: 4 }}>
        <Box sx={{ textAlign: 'center', mb: 3 }}>
          <LockResetRounded sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
          <Typography variant="h5" component="h1" gutterBottom>
            Reset Password
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Enter your email address and practice ID to receive password reset instructions
          </Typography>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        <Box component="form" onSubmit={handleSubmit}>
          <TextField
            fullWidth
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={handleInputChange('email')}
            error={!!fieldErrors.email}
            helperText={fieldErrors.email}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <EmailRounded />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 2 }}
            autoComplete="email"
            autoFocus
          />

          <TextField
            fullWidth
            label="Practice ID"
            value={formData.practiceId}
            onChange={handleInputChange('practiceId')}
            error={!!fieldErrors.practiceId}
            helperText={fieldErrors.practiceId}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <BusinessRounded />
                </InputAdornment>
              ),
            }}
            sx={{ mb: 3 }}
            autoComplete="organization"
          />

          <Button
            type="submit"
            fullWidth
            variant="contained"
            size="large"
            disabled={isLoading}
            sx={{ mb: 2 }}
          >
            {isLoading ? <CircularProgress size={24} /> : 'Send Reset Instructions'}
          </Button>

          <Button
            fullWidth
            variant="text"
            onClick={onBackToLogin}
            disabled={isLoading}
            startIcon={<ArrowBackRounded />}
          >
            Back to Login
          </Button>
        </Box>

        <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary">
            <strong>Security Note:</strong> For your protection, password reset links expire after 1 hour. 
            If you don't receive an email within a few minutes, please check your spam folder or contact 
            your system administrator.
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default PasswordResetForm;
