// OCTAVE Healthcare Platform - Patient Dashboard Component
// Patient-facing dashboard based on mockup design

import React from 'react';
import {
  Box,
  Grid,
  CardContent,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Avatar,
  Divider,
  Alert,
  useTheme,
} from '@mui/material';
import {
  CalendarTodayRounded,
  AssignmentRounded,
  DescriptionRounded,
  LocalHospitalRounded,
  PersonRounded,
  PhoneRounded,
  EmailRounded,
  LocationOnRounded,
  CheckCircleRounded,
  PendingRounded,
  InfoRounded,
} from '@mui/icons-material';

import { ResponsiveGrid, ResponsiveCard } from '@/components/common/layout/ResponsiveContainer';

interface AppointmentItemProps {
  date: string;
  time: string;
  doctor: string;
  type: string;
  status: 'upcoming' | 'completed' | 'cancelled';
}

const AppointmentItem: React.FC<AppointmentItemProps> = ({ 
  date, 
  time, 
  doctor, 
  type, 
  status 
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'upcoming': return 'primary';
      case 'completed': return 'success';
      case 'cancelled': return 'error';
      default: return 'default';
    }
  };

  return (
    <ListItem sx={{ px: 0, py: 2, borderBottom: '1px solid #f0f0f0' }}>
      <ListItemIcon>
        <CalendarTodayRounded color="primary" />
      </ListItemIcon>
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" fontWeight={500}>
              {type}
            </Typography>
            <Chip
              label={status}
              size="small"
              color={getStatusColor(status) as any}
              variant="outlined"
            />
          </Box>
        }
        secondary={
          <Box sx={{ mt: 0.5 }}>
            <Typography variant="caption" color="text.secondary">
              {date} at {time} • Dr. {doctor}
            </Typography>
          </Box>
        }
      />
    </ListItem>
  );
};

interface AuthorizationItemProps {
  id: string;
  procedure: string;
  status: 'approved' | 'pending' | 'denied';
  submittedDate: string;
  expiryDate?: string;
}

const AuthorizationItem: React.FC<AuthorizationItemProps> = ({ 
  id, 
  procedure, 
  status, 
  submittedDate, 
  expiryDate 
}) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'approved':
        return { color: 'success', icon: <CheckCircleRounded />, label: 'Approved' };
      case 'pending':
        return { color: 'warning', icon: <PendingRounded />, label: 'Pending' };
      case 'denied':
        return { color: 'error', icon: <InfoRounded />, label: 'Denied' };
      default:
        return { color: 'default', icon: <InfoRounded />, label: status };
    }
  };

  const config = getStatusConfig(status);

  return (
    <ListItem sx={{ px: 0, py: 2, borderBottom: '1px solid #f0f0f0' }}>
      <ListItemIcon>
        <Avatar sx={{ bgcolor: `${config.color}.main`, width: 32, height: 32 }}>
          {React.cloneElement(config.icon, { fontSize: 'small', sx: { color: 'white' } })}
        </Avatar>
      </ListItemIcon>
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" fontWeight={500}>
              {procedure}
            </Typography>
            <Chip
              label={config.label}
              size="small"
              color={config.color as any}
              variant="filled"
            />
          </Box>
        }
        secondary={
          <Box sx={{ mt: 0.5 }}>
            <Typography variant="caption" color="text.secondary">
              ID: {id} • Submitted: {submittedDate}
              {expiryDate && ` • Expires: ${expiryDate}`}
            </Typography>
          </Box>
        }
      />
    </ListItem>
  );
};

const PatientDashboard: React.FC = () => {

  const upcomingAppointments = [
    {
      date: 'Jan 20, 2025',
      time: '10:00 AM',
      doctor: 'Santos',
      type: 'Cardiology Consultation',
      status: 'upcoming' as const,
    },
    {
      date: 'Jan 25, 2025',
      time: '2:00 PM',
      doctor: 'Rodriguez',
      type: 'Follow-up Checkup',
      status: 'upcoming' as const,
    },
  ];

  const recentAuthorizations = [
    {
      id: 'PA-2025-001',
      procedure: 'MRI Brain Scan',
      status: 'approved' as const,
      submittedDate: 'Jan 15, 2025',
      expiryDate: 'Mar 15, 2025',
    },
    {
      id: 'PA-2025-002',
      procedure: 'Physical Therapy Sessions',
      status: 'pending' as const,
      submittedDate: 'Jan 18, 2025',
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight={600} gutterBottom>
          Welcome, Maria Santos
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your healthcare information and appointments
        </Typography>
      </Box>

      {/* Important Alerts */}
      <Box sx={{ mb: 4 }}>
        <Alert severity="info" sx={{ mb: 2 }}>
          <Typography variant="body2">
            <strong>Reminder:</strong> Your upcoming cardiology appointment is on January 20th at 10:00 AM.
          </Typography>
        </Alert>
        <Alert severity="success">
          <Typography variant="body2">
            <strong>Good News:</strong> Your MRI authorization has been approved and is valid until March 15th.
          </Typography>
        </Alert>
      </Box>

      <Grid container spacing={3}>
        {/* Patient Information Card */}
        <Grid item xs={12} md={4}>
          <ResponsiveCard>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Avatar
                  sx={{
                    width: 60,
                    height: 60,
                    bgcolor: 'primary.main',
                    mr: 2,
                    fontSize: '1.5rem',
                  }}
                >
                  MS
                </Avatar>
                <Box>
                  <Typography variant="h6" fontWeight={600}>
                    Maria Santos
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Patient ID: P-2025-001
                  </Typography>
                </Box>
              </Box>

              <Divider sx={{ mb: 2 }} />

              <List dense>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <PersonRounded color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Date of Birth"
                    secondary="March 15, 1985"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <PhoneRounded color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Phone"
                    secondary="+63 ************"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <EmailRounded color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Email"
                    secondary="<EMAIL>"
                  />
                </ListItem>
                <ListItem sx={{ px: 0 }}>
                  <ListItemIcon>
                    <LocationOnRounded color="action" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Address"
                    secondary="123 Main St, Manila"
                  />
                </ListItem>
              </List>

              <Button
                variant="outlined"
                fullWidth
                sx={{ mt: 2 }}
                startIcon={<PersonRounded />}
              >
                Update Profile
              </Button>
            </CardContent>
          </ResponsiveCard>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={8}>
          <Box sx={{ mb: 3 }}>
            <ResponsiveCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Quick Actions
              </Typography>
              <ResponsiveGrid columns={{ xs: 2, sm: 4 }} spacing={2}>
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<CalendarTodayRounded />}
                  sx={{ height: 80, flexDirection: 'column', gap: 1 }}
                  fullWidth
                >
                  <Typography variant="body2">Book Appointment</Typography>
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<DescriptionRounded />}
                  sx={{ height: 80, flexDirection: 'column', gap: 1 }}
                  fullWidth
                >
                  <Typography variant="body2">Medical Records</Typography>
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<AssignmentRounded />}
                  sx={{ height: 80, flexDirection: 'column', gap: 1 }}
                  fullWidth
                >
                  <Typography variant="body2">Authorizations</Typography>
                </Button>
                <Button
                  variant="outlined"
                  size="large"
                  startIcon={<LocalHospitalRounded />}
                  sx={{ height: 80, flexDirection: 'column', gap: 1 }}
                  fullWidth
                >
                  <Typography variant="body2">Insurance</Typography>
                </Button>
              </ResponsiveGrid>
            </CardContent>
          </ResponsiveCard>
          </Box>

          {/* Insurance Information */}
          <ResponsiveCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Insurance Information
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: '#009639',
                    color: 'white',
                    width: 40,
                    height: 40,
                    fontSize: '0.875rem',
                  }}
                >
                  PH
                </Avatar>
                <Box>
                  <Typography variant="body1" fontWeight={500}>
                    PhilHealth
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Member ID: 12-***********
                  </Typography>
                </Box>
                <Chip
                  label="Active"
                  size="small"
                  color="success"
                  variant="filled"
                />
              </Box>
              <Typography variant="body2" color="text.secondary">
                Coverage: Universal Health Care • Valid until: Dec 31, 2025
              </Typography>
            </CardContent>
          </ResponsiveCard>
        </Grid>

        {/* Upcoming Appointments */}
        <Grid item xs={12} md={6}>
          <ResponsiveCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Upcoming Appointments
              </Typography>
              <List>
                {upcomingAppointments.map((appointment, index) => (
                  <AppointmentItem key={index} {...appointment} />
                ))}
              </List>
              <Button
                variant="outlined"
                fullWidth
                sx={{ mt: 2 }}
                startIcon={<CalendarTodayRounded />}
              >
                View All Appointments
              </Button>
            </CardContent>
          </ResponsiveCard>
        </Grid>

        {/* Recent Authorizations */}
        <Grid item xs={12} md={6}>
          <ResponsiveCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Prior Authorizations
              </Typography>
              <List>
                {recentAuthorizations.map((auth) => (
                  <AuthorizationItem key={auth.id} {...auth} />
                ))}
              </List>
              <Button
                variant="outlined"
                fullWidth
                sx={{ mt: 2 }}
                startIcon={<AssignmentRounded />}
              >
                View All Authorizations
              </Button>
            </CardContent>
          </ResponsiveCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PatientDashboard;
