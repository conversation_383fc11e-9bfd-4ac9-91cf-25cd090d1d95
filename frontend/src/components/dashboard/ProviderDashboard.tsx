// OCTAVE Healthcare Platform - Provider Dashboard Component
// Role-specific dashboard for healthcare providers based on mockup design

import React from 'react';
import {
  Box,
  Grid,
  CardContent,
  Typography,
  Button,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Chip,
  useTheme,
} from '@mui/material';
import {
  AddRounded,
  PeopleRounded,
  AssignmentRounded,
  DescriptionRounded,
  MessageRounded,
  AnalyticsRounded,
  CheckCircleRounded,
  PendingRounded,
  ErrorRounded,
  AccessTimeRounded,
} from '@mui/icons-material';

import { ResponsiveGrid, ResponsiveCard } from '@/components/common/layout/ResponsiveContainer';

interface QuickActionProps {
  title: string;
  icon: React.ReactNode;
  onClick: () => void;
  color?: 'primary' | 'secondary' | 'success' | 'warning';
}

const QuickAction: React.FC<QuickActionProps> = ({ title, icon, onClick, color = 'primary' }) => {
  const theme = useTheme();
  
  return (
    <Button
      variant="outlined"
      size="large"
      startIcon={icon}
      onClick={onClick}
      sx={{
        height: 80,
        borderRadius: 2,
        borderWidth: 2,
        flexDirection: 'column',
        gap: 1,
        '&:hover': {
          borderWidth: 2,
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[4],
        },
        transition: 'all 0.3s ease',
      }}
      color={color}
      fullWidth
    >
      <Typography variant="body2" fontWeight={500}>
        {title}
      </Typography>
    </Button>
  );
};

interface ActivityItemProps {
  type: 'approved' | 'pending' | 'urgent' | 'philhealth';
  title: string;
  subtitle: string;
  time: string;
}

const ActivityItem: React.FC<ActivityItemProps> = ({ type, title, subtitle, time }) => {
  const getStatusConfig = (type: string) => {
    switch (type) {
      case 'approved':
        return { color: '#009639', icon: 'A', bgColor: '#E8F5E8' };
      case 'pending':
        return { color: '#F39C12', icon: 'P', bgColor: '#FEF9E7' };
      case 'urgent':
        return { color: '#E74C3C', icon: 'U', bgColor: '#FDEDEC' };
      case 'philhealth':
        return { color: '#0066CC', icon: 'PH', bgColor: '#EBF3FD' };
      default:
        return { color: '#95A5A6', icon: '?', bgColor: '#F8F9FA' };
    }
  };

  const config = getStatusConfig(type);

  return (
    <ListItem sx={{ px: 0 }}>
      <ListItemAvatar>
        <Avatar
          sx={{
            bgcolor: config.color,
            color: 'white',
            width: 40,
            height: 40,
            fontSize: '0.875rem',
            fontWeight: 700,
          }}
        >
          {config.icon}
        </Avatar>
      </ListItemAvatar>
      <ListItemText
        primary={
          <Typography variant="body2" fontWeight={500}>
            {title}
          </Typography>
        }
        secondary={
          <Typography variant="caption" color="text.secondary">
            {subtitle}
          </Typography>
        }
      />
      <Typography variant="caption" color="text.secondary">
        {time}
      </Typography>
    </ListItem>
  );
};

interface ScheduleItemProps {
  time: string;
  patientName: string;
  appointmentType: string;
  philhealthStatus?: boolean;
}

const ScheduleItem: React.FC<ScheduleItemProps> = ({ 
  time, 
  patientName, 
  appointmentType, 
  philhealthStatus 
}) => {
  return (
    <ListItem sx={{ px: 0, alignItems: 'flex-start' }}>
      <Box sx={{ minWidth: 80, pt: 0.5 }}>
        <Typography variant="body2" fontWeight={500} color="primary">
          {time}
        </Typography>
      </Box>
      <Box sx={{ flex: 1, ml: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Typography variant="body2" fontWeight={500}>
            {patientName}
          </Typography>
          {philhealthStatus && (
            <Chip
              label="PhilHealth"
              size="small"
              sx={{
                bgcolor: '#009639',
                color: 'white',
                fontSize: '0.75rem',
                height: 20,
              }}
            />
          )}
        </Box>
        <Typography variant="caption" color="text.secondary">
          {appointmentType}
        </Typography>
      </Box>
    </ListItem>
  );
};

const ProviderDashboard: React.FC = () => {
  const theme = useTheme();

  const handleQuickAction = (action: string) => {
    console.log(`Quick action: ${action}`);
    // TODO: Implement navigation to respective pages
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight={600} gutterBottom>
          Welcome back, Dr. Santos
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Here's what's happening with your practice today
        </Typography>
      </Box>

      {/* Quick Actions */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h6" fontWeight={600} gutterBottom>
          Quick Actions
        </Typography>
        <ResponsiveGrid columns={{ xs: 2, sm: 3, md: 6 }} spacing={2}>
          <QuickAction
            title="New Patient"
            icon={<PeopleRounded />}
            onClick={() => handleQuickAction('new-patient')}
            color="primary"
          />
          <QuickAction
            title="Prior Auth"
            icon={<AssignmentRounded />}
            onClick={() => handleQuickAction('new-prior-auth')}
            color="secondary"
          />
          <QuickAction
            title="Medical Records"
            icon={<DescriptionRounded />}
            onClick={() => handleQuickAction('medical-records')}
            color="success"
          />
          <QuickAction
            title="Messages"
            icon={<MessageRounded />}
            onClick={() => handleQuickAction('messages')}
            color="warning"
          />
          <QuickAction
            title="Reports"
            icon={<AnalyticsRounded />}
            onClick={() => handleQuickAction('reports')}
            color="primary"
          />
          <QuickAction
            title="Add More"
            icon={<AddRounded />}
            onClick={() => handleQuickAction('add-more')}
            color="secondary"
          />
        </ResponsiveGrid>
      </Box>

      {/* Dashboard Content Grid */}
      <Grid container spacing={3}>
        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <ResponsiveCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Recent Activity
              </Typography>
              <List>
                <ActivityItem
                  type="approved"
                  title="Prior Authorization Approved"
                  subtitle="Maria Garcia - Cardiology Consultation"
                  time="2 hours ago"
                />
                <ActivityItem
                  type="pending"
                  title="Pending Review"
                  subtitle="John Smith - MRI Scan Authorization"
                  time="4 hours ago"
                />
                <ActivityItem
                  type="urgent"
                  title="Urgent: Response Required"
                  subtitle="Emergency Surgery Authorization"
                  time="6 hours ago"
                />
                <ActivityItem
                  type="philhealth"
                  title="PhilHealth Claim Processed"
                  subtitle="Ana Rodriguez - Routine Checkup"
                  time="1 day ago"
                />
              </List>
            </CardContent>
          </ResponsiveCard>
        </Grid>

        {/* Today's Schedule */}
        <Grid item xs={12} md={6}>
          <ResponsiveCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Today's Schedule
              </Typography>
              <List>
                <ScheduleItem
                  time="9:00 AM"
                  patientName="Maria Santos"
                  appointmentType="Cardiology Consultation"
                  philhealthStatus={true}
                />
                <ScheduleItem
                  time="10:30 AM"
                  patientName="John Dela Cruz"
                  appointmentType="Follow-up Checkup"
                />
                <ScheduleItem
                  time="2:00 PM"
                  patientName="Ana Rodriguez"
                  appointmentType="Initial Consultation"
                  philhealthStatus={true}
                />
                <ScheduleItem
                  time="3:30 PM"
                  patientName="Carlos Mendoza"
                  appointmentType="Prescription Review"
                />
              </List>
            </CardContent>
          </ResponsiveCard>
        </Grid>

        {/* Statistics Cards */}
        <Grid item xs={12} md={3}>
          <ResponsiveCard>
            <CardContent sx={{ textAlign: 'center' }}>
              <CheckCircleRounded sx={{ fontSize: 48, color: 'success.main', mb: 1 }} />
              <Typography variant="h4" fontWeight={600} color="success.main">
                24
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Approved Today
              </Typography>
            </CardContent>
          </ResponsiveCard>
        </Grid>

        <Grid item xs={12} md={3}>
          <ResponsiveCard>
            <CardContent sx={{ textAlign: 'center' }}>
              <PendingRounded sx={{ fontSize: 48, color: 'warning.main', mb: 1 }} />
              <Typography variant="h4" fontWeight={600} color="warning.main">
                8
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending Review
              </Typography>
            </CardContent>
          </ResponsiveCard>
        </Grid>

        <Grid item xs={12} md={3}>
          <ResponsiveCard>
            <CardContent sx={{ textAlign: 'center' }}>
              <ErrorRounded sx={{ fontSize: 48, color: 'error.main', mb: 1 }} />
              <Typography variant="h4" fontWeight={600} color="error.main">
                3
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Urgent Items
              </Typography>
            </CardContent>
          </ResponsiveCard>
        </Grid>

        <Grid item xs={12} md={3}>
          <ResponsiveCard>
            <CardContent sx={{ textAlign: 'center' }}>
              <AccessTimeRounded sx={{ fontSize: 48, color: 'info.main', mb: 1 }} />
              <Typography variant="h4" fontWeight={600} color="info.main">
                12
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Appointments Today
              </Typography>
            </CardContent>
          </ResponsiveCard>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ProviderDashboard;
