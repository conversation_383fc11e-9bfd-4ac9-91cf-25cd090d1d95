// OCTAVE Healthcare Platform - Staff Dashboard Component
// Role-specific dashboard for healthcare staff based on mockup design

import React from 'react';
import {
  Box,
  Grid as MuiGrid,
  CardContent,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  Chip,
  LinearProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
} from '@mui/material';
import {
  AssignmentRounded,
  PeopleRounded,
  DescriptionRounded,
  PhoneRounded,
  EditRounded,
  VisibilityRounded,
  PriorityHighRounded,
} from '@mui/icons-material';

import { ResponsiveGrid, ResponsiveCard } from '@/components/common/layout/ResponsiveContainer';

interface TaskItemProps {
  id: string;
  title: string;
  patient: string;
  priority: 'high' | 'medium' | 'low';
  dueDate: string;
  status: 'pending' | 'in-progress' | 'completed';
}

const TaskItem: React.FC<TaskItemProps> = ({ title, patient, priority, dueDate, status }) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#E74C3C';
      case 'medium': return '#F39C12';
      case 'low': return '#009639';
      default: return '#95A5A6';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'in-progress': return 'warning';
      case 'pending': return 'error';
      default: return 'default';
    }
  };

  return (
    <ListItem sx={{ px: 0, py: 1, borderBottom: '1px solid #f0f0f0' }}>
      <ListItemText
        primary={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <PriorityHighRounded 
              sx={{ 
                fontSize: 16, 
                color: getPriorityColor(priority) 
              }} 
            />
            <Typography variant="body2" fontWeight={500}>
              {title}
            </Typography>
          </Box>
        }
        secondary={
          <Box sx={{ mt: 0.5 }}>
            <Typography variant="caption" color="text.secondary">
              Patient: {patient} • Due: {dueDate}
            </Typography>
          </Box>
        }
      />
      <Chip
        label={status.replace('-', ' ')}
        size="small"
        color={getStatusColor(status) as any}
        variant="outlined"
      />
    </ListItem>
  );
};

interface PriorAuthRowProps {
  id: string;
  patient: string;
  procedure: string;
  status: string;
  submittedDate: string;
  priority: string;
}

const PriorAuthRow: React.FC<PriorAuthRowProps> = ({ 
  id, 
  patient, 
  procedure, 
  status, 
  submittedDate, 
  priority 
}) => {
  const getStatusChip = (status: string) => {
    const statusConfig = {
      'pending': { color: 'warning', label: 'Pending' },
      'approved': { color: 'success', label: 'Approved' },
      'denied': { color: 'error', label: 'Denied' },
      'in-review': { color: 'info', label: 'In Review' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || 
                  { color: 'default', label: status };
    
    return (
      <Chip
        label={config.label}
        size="small"
        color={config.color as any}
        variant="filled"
      />
    );
  };

  return (
    <TableRow>
      <TableCell>
        <Typography variant="body2" fontWeight={500}>
          {id}
        </Typography>
      </TableCell>
      <TableCell>
        <Typography variant="body2">
          {patient}
        </Typography>
      </TableCell>
      <TableCell>
        <Typography variant="body2">
          {procedure}
        </Typography>
      </TableCell>
      <TableCell>
        {getStatusChip(status)}
      </TableCell>
      <TableCell>
        <Typography variant="body2" color="text.secondary">
          {submittedDate}
        </Typography>
      </TableCell>
      <TableCell>
        <Chip
          label={priority}
          size="small"
          color={priority === 'High' ? 'error' : priority === 'Medium' ? 'warning' : 'success'}
          variant="outlined"
        />
      </TableCell>
      <TableCell>
        <Box sx={{ display: 'flex', gap: 0.5 }}>
          <IconButton size="small" color="primary">
            <VisibilityRounded fontSize="small" />
          </IconButton>
          <IconButton size="small" color="secondary">
            <EditRounded fontSize="small" />
          </IconButton>
        </Box>
      </TableCell>
    </TableRow>
  );
};

const StaffDashboard: React.FC = () => {

  const workloadData = [
    { label: 'Prior Authorizations', completed: 15, total: 20, color: '#3AA6B9' },
    { label: 'Patient Calls', completed: 8, total: 12, color: '#F39C12' },
    { label: 'Document Processing', completed: 22, total: 25, color: '#009639' },
    { label: 'Insurance Verifications', completed: 6, total: 10, color: '#E74C3C' },
  ];

  const recentTasks = [
    {
      id: '1',
      title: 'Process Prior Authorization',
      patient: 'Maria Santos',
      priority: 'high' as const,
      dueDate: 'Today 3:00 PM',
      status: 'pending' as const,
    },
    {
      id: '2',
      title: 'Verify Insurance Coverage',
      patient: 'John Dela Cruz',
      priority: 'medium' as const,
      dueDate: 'Tomorrow',
      status: 'in-progress' as const,
    },
    {
      id: '3',
      title: 'Follow up on Denied Claim',
      patient: 'Ana Rodriguez',
      priority: 'high' as const,
      dueDate: 'Today 5:00 PM',
      status: 'pending' as const,
    },
    {
      id: '4',
      title: 'Update Patient Records',
      patient: 'Carlos Mendoza',
      priority: 'low' as const,
      dueDate: 'This Week',
      status: 'completed' as const,
    },
  ];

  const priorAuthData = [
    {
      id: 'PA-2025-001',
      patient: 'Maria Santos',
      procedure: 'MRI Brain Scan',
      status: 'pending',
      submittedDate: '2025-01-15',
      priority: 'High',
    },
    {
      id: 'PA-2025-002',
      patient: 'John Dela Cruz',
      procedure: 'Cardiac Catheterization',
      status: 'approved',
      submittedDate: '2025-01-14',
      priority: 'Medium',
    },
    {
      id: 'PA-2025-003',
      patient: 'Ana Rodriguez',
      procedure: 'Physical Therapy',
      status: 'in-review',
      submittedDate: '2025-01-13',
      priority: 'Low',
    },
  ];

  return (
    <Box sx={{ p: 3 }}>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" fontWeight={600} gutterBottom>
          Staff Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your daily tasks and track progress
        </Typography>
      </Box>

      {/* Quick Stats */}
      <Box sx={{ mb: 4 }}>
        <ResponsiveGrid columns={{ xs: 2, sm: 4 }} spacing={3}>
        <ResponsiveCard>
          <CardContent sx={{ textAlign: 'center' }}>
            <AssignmentRounded sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
            <Typography variant="h5" fontWeight={600}>
              18
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Active Tasks
            </Typography>
          </CardContent>
        </ResponsiveCard>

        <ResponsiveCard>
          <CardContent sx={{ textAlign: 'center' }}>
            <PeopleRounded sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
            <Typography variant="h5" fontWeight={600}>
              45
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Patients Today
            </Typography>
          </CardContent>
        </ResponsiveCard>

        <ResponsiveCard>
          <CardContent sx={{ textAlign: 'center' }}>
            <PhoneRounded sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
            <Typography variant="h5" fontWeight={600}>
              12
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Pending Calls
            </Typography>
          </CardContent>
        </ResponsiveCard>

        <ResponsiveCard>
          <CardContent sx={{ textAlign: 'center' }}>
            <DescriptionRounded sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
            <Typography variant="h5" fontWeight={600}>
              8
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Documents
            </Typography>
          </CardContent>
        </ResponsiveCard>
      </ResponsiveGrid>
      </Box>

      <MuiGrid container spacing={3}>
        {/* Workload Progress */}
        <MuiGrid item xs={12} md={6}>
          <ResponsiveCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Today's Workload
              </Typography>
              <Box sx={{ mt: 2 }}>
                {workloadData.map((item, index) => (
                  <Box key={index} sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" fontWeight={500}>
                        {item.label}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {item.completed}/{item.total}
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={(item.completed / item.total) * 100}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: 'grey.200',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: item.color,
                          borderRadius: 4,
                        },
                      }}
                    />
                  </Box>
                ))}
              </Box>
            </CardContent>
          </ResponsiveCard>
        </MuiGrid>

        {/* Recent Tasks */}
        <MuiGrid item xs={12} md={6}>
          <ResponsiveCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Recent Tasks
              </Typography>
              <List sx={{ mt: 1 }}>
                {recentTasks.map((task) => (
                  <TaskItem key={task.id} {...task} />
                ))}
              </List>
              <Button
                variant="outlined"
                fullWidth
                sx={{ mt: 2 }}
              >
                View All Tasks
              </Button>
            </CardContent>
          </ResponsiveCard>
        </MuiGrid>

        {/* Prior Authorization Table */}
        <MuiGrid item xs={12}>
          <ResponsiveCard>
            <CardContent>
              <Typography variant="h6" fontWeight={600} gutterBottom>
                Recent Prior Authorizations
              </Typography>
              <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell><strong>ID</strong></TableCell>
                      <TableCell><strong>Patient</strong></TableCell>
                      <TableCell><strong>Procedure</strong></TableCell>
                      <TableCell><strong>Status</strong></TableCell>
                      <TableCell><strong>Submitted</strong></TableCell>
                      <TableCell><strong>Priority</strong></TableCell>
                      <TableCell><strong>Actions</strong></TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {priorAuthData.map((row) => (
                      <PriorAuthRow key={row.id} {...row} />
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </ResponsiveCard>
        </MuiGrid>
      </MuiGrid>
    </Box>
  );
};

export default StaffDashboard;
