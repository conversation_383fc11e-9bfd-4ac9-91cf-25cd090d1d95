{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/common/errorboundary.tsx", "./src/components/common/loadingprovider.tsx", "./src/components/common/notificationprovider.tsx", "./src/components/common/accessibility/accessiblecomponents.tsx", "./src/components/common/icons/healthcareicons.tsx", "./src/components/common/layout/responsivecontainer.tsx", "./src/components/common/theme/themetoggle.tsx", "./src/components/layouts/authlayout.tsx", "./src/components/layouts/dashboardlayout.tsx", "./src/components/navigation/breadcrumbs.tsx", "./src/components/navigation/navigationprovider.tsx", "./src/components/navigation/sidebar.tsx", "./src/components/routing/approutes.tsx", "./src/pages/admin/admindashboardpage.tsx", "./src/pages/admin/practicesettingspage.tsx", "./src/pages/admin/usermanagementpage.tsx", "./src/pages/auth/forgotpasswordpage.tsx", "./src/pages/auth/loginpage.tsx", "./src/pages/auth/resetpasswordpage.tsx", "./src/pages/communications/communicationspage.tsx", "./src/pages/dashboard/dashboardpage.tsx", "./src/pages/documents/documentspage.tsx", "./src/pages/error/notfoundpage.tsx", "./src/pages/error/unauthorizedpage.tsx", "./src/pages/patients/patientdetailpage.tsx", "./src/pages/patients/patientspage.tsx", "./src/pages/prior-auth/createpriorauthpage.tsx", "./src/pages/prior-auth/priorauthdetailpage.tsx", "./src/pages/prior-auth/priorauthpage.tsx", "./src/pages/profile/profilepage.tsx", "./src/pages/reports/reportspage.tsx", "./src/pages/settings/settingspage.tsx", "./src/services/offlineservice.ts", "./src/services/websocketservice.ts", "./src/store/index.ts", "./src/store/api/apislice.ts", "./src/store/api/authapi.ts", "./src/store/api/patientsapi.ts", "./src/store/api/priorauthapi.ts", "./src/store/slices/authslice.ts", "./src/store/slices/uislice.ts", "./src/styles/theme.ts", "./src/styles/typography.ts", "./src/test/setup.ts", "./src/types/index.ts"], "version": "5.8.3"}