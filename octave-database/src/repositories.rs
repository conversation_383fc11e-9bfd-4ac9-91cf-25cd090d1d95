//! Repository traits for database operations

use crate::models::{
    Practice, QueryFilter, StoredLearningEvent, StoredMetric, StoredThreatPattern,
    User, UserActivity, UserRole, UserStatus, PracticeStatus, Patient, PatientStatus,
    PatientSummary, Gender, PriorAuth, PriorAuthStatus, PriorAuthPriority, PriorAuthSummary,
    UrgencyLevel, Document, DocumentType, DocumentClassification, DocumentSummary, VirusScanStatus,
    Communication, CommunicationType, CommunicationDirection, CommunicationOutcome,
    CommunicationPriority, CommunicationSummary, InsuranceRepresentative, InsuranceRepSummary,
    CommunicationTemplate, TemplateCategory, CommunicationTemplateSummary, Reminder, ReminderType,
    ReminderStatus, ReminderPriority, ReminderSummary, EntityType, Notification, NotificationType,
    NotificationChannel, DeliveryStatus, WorkflowState, WorkflowDefinition, StateTransition,
    PendingAction, ActionType, ActionStatus, Template, TemplateType, TemplateSummary, ApprovalStatus,
    TemplateVersion, TemplateUsage, UsageContext, ContextType, AnalyticsMetric, MetricType,
    AggregationPeriod, AnalyticsMetricSummary, Report, ReportType, ReportFormat, ReportStatus,
    ReportSummary, ComplianceViolation, ComplianceViolationType, ComplianceSeverity, ComplianceStatus,
    ComplianceViolationSummary, PerformanceMetric, PerformanceMetricType, PerformanceMetricSummary,
    Dashboard, DashboardSummary, ApiEndpoint, HttpMethod, RateLimit, ApiRequestLog, ResponseTimeCategory,
    ExternalIntegration, IntegrationType, ConnectionStatus, FileStorage, StorageType, SearchIndex,
    IndexType, SearchQuery, Webhook, WebhookEvent, WebhookRetryConfig, WebhookDelivery
};
use async_trait::async_trait;
use chrono::{DateTime, Utc};
use octave_core::error::OctaveResult;
use std::collections::HashMap;
use uuid::Uuid;

/// Repository for threat pattern operations
#[async_trait]
pub trait ThreatPatternRepository: Send + Sync {
    /// Create a new threat pattern
    async fn create(&self, pattern: StoredThreatPattern) -> OctaveResult<StoredThreatPattern>;

    /// Get threat pattern by ID
    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredThreatPattern>>;

    /// Get threat pattern by name
    async fn get_by_name(&self, name: &str) -> OctaveResult<Option<StoredThreatPattern>>;

    /// Update threat pattern
    async fn update(&self, pattern: StoredThreatPattern) -> OctaveResult<StoredThreatPattern>;

    /// Delete threat pattern by ID
    async fn delete(&self, id: &str) -> OctaveResult<bool>;

    /// List threat patterns with filters
    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredThreatPattern>>;

    /// Get patterns by threat type
    async fn get_by_threat_type(&self, threat_type: &str) -> OctaveResult<Vec<StoredThreatPattern>>;

    /// Get active patterns only
    async fn get_active_patterns(&self) -> OctaveResult<Vec<StoredThreatPattern>>;

    /// Update pattern statistics
    async fn update_stats(&self, id: &str, was_false_positive: bool) -> OctaveResult<()>;

    /// Get patterns with low effectiveness (for cleanup)
    async fn get_low_effectiveness_patterns(&self, threshold: f64) -> OctaveResult<Vec<StoredThreatPattern>>;

    /// Get pattern statistics
    async fn get_stats(&self) -> OctaveResult<ThreatPatternStats>;
}

/// Repository for learning data operations
#[async_trait]
pub trait LearningDataRepository: Send + Sync {
    /// Create a new learning event
    async fn create(&self, event: StoredLearningEvent) -> OctaveResult<StoredLearningEvent>;

    /// Get learning event by ID
    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredLearningEvent>>;

    /// Update learning event
    async fn update(&self, event: StoredLearningEvent) -> OctaveResult<StoredLearningEvent>;

    /// Delete learning event by ID
    async fn delete(&self, id: &str) -> OctaveResult<bool>;

    /// List learning events with filters
    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredLearningEvent>>;

    /// Get events by context fingerprint
    async fn get_by_context(&self, fingerprint: &str) -> OctaveResult<Vec<StoredLearningEvent>>;

    /// Get events by user ID
    async fn get_by_user(&self, user_id: i32) -> OctaveResult<Vec<StoredLearningEvent>>;

    /// Get events by threat level
    async fn get_by_threat_level(&self, threat_level: &str) -> OctaveResult<Vec<StoredLearningEvent>>;

    /// Get recent events for pattern analysis
    async fn get_recent_events(&self, hours: u32) -> OctaveResult<Vec<StoredLearningEvent>>;

    /// Get false positive events
    async fn get_false_positives(&self) -> OctaveResult<Vec<StoredLearningEvent>>;

    /// Clean up old events
    async fn cleanup_old_events(&self, retention_days: u32) -> OctaveResult<u64>;

    /// Get learning statistics
    async fn get_stats(&self) -> OctaveResult<LearningDataStats>;
}

/// Repository for metrics operations
#[async_trait]
pub trait MetricsRepository: Send + Sync {
    /// Create a new metric
    async fn create(&self, metric: StoredMetric) -> OctaveResult<StoredMetric>;

    /// Get metric by ID
    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredMetric>>;

    /// Update metric
    async fn update(&self, metric: StoredMetric) -> OctaveResult<StoredMetric>;

    /// Delete metric by ID
    async fn delete(&self, id: &str) -> OctaveResult<bool>;

    /// List metrics with filters
    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredMetric>>;

    /// Get metrics by name
    async fn get_by_name(&self, name: &str) -> OctaveResult<Vec<StoredMetric>>;

    /// Get metrics by category
    async fn get_by_category(&self, category: &str) -> OctaveResult<Vec<StoredMetric>>;

    /// Get metrics in time range
    async fn get_time_range(
        &self,
        name: &str,
        from: DateTime<Utc>,
        to: DateTime<Utc>,
    ) -> OctaveResult<Vec<StoredMetric>>;

    /// Get latest metric value by name
    async fn get_latest_value(&self, name: &str) -> OctaveResult<Option<f64>>;

    /// Get aggregated metrics
    async fn get_aggregated(
        &self,
        name: &str,
        aggregation: MetricAggregation,
        from: DateTime<Utc>,
        to: DateTime<Utc>,
    ) -> OctaveResult<Option<f64>>;

    /// Clean up old metrics
    async fn cleanup_old_metrics(&self) -> OctaveResult<u64>;

    /// Get metrics statistics
    async fn get_stats(&self) -> OctaveResult<MetricsStats>;
}

/// Threat pattern statistics
#[derive(Debug, Clone)]
pub struct ThreatPatternStats {
    /// Total number of patterns
    pub total_patterns: u64,
    /// Active patterns
    pub active_patterns: u64,
    /// Patterns by threat type
    pub patterns_by_type: HashMap<String, u64>,
    /// Average effectiveness
    pub average_effectiveness: f64,
    /// Total matches
    pub total_matches: u64,
    /// Total false positives
    pub total_false_positives: u64,
}

/// Learning data statistics
#[derive(Debug, Clone)]
pub struct LearningDataStats {
    /// Total learning events
    pub total_events: u64,
    /// Events by threat level
    pub events_by_threat_level: HashMap<String, u64>,
    /// Effective responses
    pub effective_responses: u64,
    /// False positives
    pub false_positives: u64,
    /// Events by archetype
    pub events_by_archetype: HashMap<String, u64>,
    /// Recent events (last 24 hours)
    pub recent_events: u64,
}

/// Metrics statistics
#[derive(Debug, Clone)]
pub struct MetricsStats {
    /// Total metrics
    pub total_metrics: u64,
    /// Metrics by category
    pub metrics_by_category: HashMap<String, u64>,
    /// Unique metric names
    pub unique_names: u64,
    /// Latest timestamp
    pub latest_timestamp: Option<DateTime<Utc>>,
    /// Oldest timestamp
    pub oldest_timestamp: Option<DateTime<Utc>>,
}

/// Metric aggregation types
#[derive(Debug, Clone)]
pub enum MetricAggregation {
    /// Average value
    Average,
    /// Sum of values
    Sum,
    /// Minimum value
    Min,
    /// Maximum value
    Max,
    /// Count of records
    Count,
}

impl MetricAggregation {
    /// Get SQL aggregation function name
    pub fn sql_function(&self) -> &'static str {
        match self {
            MetricAggregation::Average => "AVG",
            MetricAggregation::Sum => "SUM",
            MetricAggregation::Min => "MIN",
            MetricAggregation::Max => "MAX",
            MetricAggregation::Count => "COUNT",
        }
    }
}

// ============================================================================
// AUTHTRACKER BUSINESS REPOSITORIES
// ============================================================================

/// Repository for practice operations
#[async_trait]
pub trait PracticeRepository: Send + Sync {
    /// Create a new practice
    async fn create(&self, practice: Practice) -> OctaveResult<Practice>;

    /// Get practice by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<Practice>>;

    /// Get practice by email
    async fn get_by_email(&self, email: &str) -> OctaveResult<Option<Practice>>;

    /// Get practice by NPI
    async fn get_by_npi(&self, npi: &str) -> OctaveResult<Option<Practice>>;

    /// Update practice
    async fn update(&self, practice: Practice) -> OctaveResult<Practice>;

    /// Delete practice by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// List practices with filters
    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<Practice>>;

    /// Get practices by status
    async fn get_by_status(&self, status: PracticeStatus) -> OctaveResult<Vec<Practice>>;

    /// Get practices by type
    async fn get_by_type(&self, practice_type: &str) -> OctaveResult<Vec<Practice>>;

    /// Search practices by name
    async fn search_by_name(&self, name: &str) -> OctaveResult<Vec<Practice>>;

    /// Get practice statistics
    async fn get_stats(&self) -> OctaveResult<PracticeStats>;

    /// Activate practice
    async fn activate(&self, id: Uuid) -> OctaveResult<bool>;

    /// Suspend practice
    async fn suspend(&self, id: Uuid) -> OctaveResult<bool>;
}

/// Repository for user operations
#[async_trait]
pub trait UserRepository: Send + Sync {
    /// Create a new user
    async fn create(&self, user: User) -> OctaveResult<User>;

    /// Get user by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<User>>;

    /// Get user by email
    async fn get_by_email(&self, email: &str) -> OctaveResult<Option<User>>;

    /// Get user by invitation token
    async fn get_by_invitation_token(&self, token: &str) -> OctaveResult<Option<User>>;

    /// Get user by password reset token
    async fn get_by_password_reset_token(&self, token: &str) -> OctaveResult<Option<User>>;

    /// Update user
    async fn update(&self, user: User) -> OctaveResult<User>;

    /// Delete user by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// List users with filters
    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<User>>;

    /// Get users by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<User>>;

    /// Get users by role
    async fn get_by_role(&self, role: UserRole) -> OctaveResult<Vec<User>>;

    /// Get users by status
    async fn get_by_status(&self, status: UserStatus) -> OctaveResult<Vec<User>>;

    /// Search users by name
    async fn search_by_name(&self, practice_id: Uuid, name: &str) -> OctaveResult<Vec<User>>;

    /// Get practice administrators
    async fn get_practice_admins(&self, practice_id: Uuid) -> OctaveResult<Vec<User>>;

    /// Get active users for practice
    async fn get_active_users(&self, practice_id: Uuid) -> OctaveResult<Vec<User>>;

    /// Update last login
    async fn update_last_login(&self, id: Uuid) -> OctaveResult<bool>;

    /// Update last activity
    async fn update_last_activity(&self, id: Uuid) -> OctaveResult<bool>;

    /// Activate user
    async fn activate(&self, id: Uuid, password_hash: String) -> OctaveResult<bool>;

    /// Suspend user
    async fn suspend(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get user statistics
    async fn get_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<UserStats>;
}

/// Repository for user activity operations
#[async_trait]
pub trait UserActivityRepository: Send + Sync {
    /// Create a new user activity
    async fn create(&self, activity: UserActivity) -> OctaveResult<UserActivity>;

    /// Get activity by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<UserActivity>>;

    /// List activities with filters
    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<UserActivity>>;

    /// Get activities by user ID
    async fn get_by_user(&self, user_id: Uuid) -> OctaveResult<Vec<UserActivity>>;

    /// Get activities by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<UserActivity>>;

    /// Get activities by type
    async fn get_by_type(&self, activity_type: &str) -> OctaveResult<Vec<UserActivity>>;

    /// Get PHI-related activities
    async fn get_phi_activities(&self, practice_id: Uuid) -> OctaveResult<Vec<UserActivity>>;

    /// Get security-related activities
    async fn get_security_activities(&self, practice_id: Uuid) -> OctaveResult<Vec<UserActivity>>;

    /// Get failed activities
    async fn get_failed_activities(&self, practice_id: Uuid) -> OctaveResult<Vec<UserActivity>>;

    /// Get recent activities
    async fn get_recent_activities(&self, practice_id: Uuid, hours: u32) -> OctaveResult<Vec<UserActivity>>;

    /// Get user login history
    async fn get_login_history(&self, user_id: Uuid) -> OctaveResult<Vec<UserActivity>>;

    /// Clean up old activities
    async fn cleanup_old_activities(&self, retention_days: u32) -> OctaveResult<u64>;

    /// Get activity statistics
    async fn get_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<UserActivityStats>;
}

/// Practice statistics
#[derive(Debug, Clone)]
pub struct PracticeStats {
    /// Total number of practices
    pub total_practices: u64,
    /// Active practices
    pub active_practices: u64,
    /// Practices by status
    pub practices_by_status: HashMap<String, u64>,
    /// Practices by type
    pub practices_by_type: HashMap<String, u64>,
    /// Average users per practice
    pub average_users_per_practice: f64,
    /// Recently created practices (last 30 days)
    pub recent_practices: u64,
}

/// User statistics
#[derive(Debug, Clone)]
pub struct UserStats {
    /// Total number of users
    pub total_users: u64,
    /// Active users
    pub active_users: u64,
    /// Users by status
    pub users_by_status: HashMap<String, u64>,
    /// Users by role
    pub users_by_role: HashMap<String, u64>,
    /// Recently active users (last 24 hours)
    pub recently_active_users: u64,
    /// Users with pending invitations
    pub pending_invitations: u64,
    /// Average login frequency
    pub average_login_frequency: f64,
}

/// User activity statistics
#[derive(Debug, Clone)]
pub struct UserActivityStats {
    /// Total activities
    pub total_activities: u64,
    /// Activities by type
    pub activities_by_type: HashMap<String, u64>,
    /// Successful activities
    pub successful_activities: u64,
    /// Failed activities
    pub failed_activities: u64,
    /// PHI-related activities
    pub phi_activities: u64,
    /// Security-related activities
    pub security_activities: u64,
    /// Recent activities (last 24 hours)
    pub recent_activities: u64,
    /// Unique active users (last 24 hours)
    pub unique_active_users: u64,
}

// ============================================================================
// PATIENT MANAGEMENT REPOSITORIES
// ============================================================================

/// Repository for patient operations with PHI protection
#[async_trait]
pub trait PatientRepository: Send + Sync {
    /// Create a new patient
    async fn create(&self, patient: Patient) -> OctaveResult<Patient>;

    /// Get patient by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<Patient>>;

    /// Get patient by MRN within practice
    async fn get_by_mrn(&self, practice_id: Uuid, mrn: &str) -> OctaveResult<Option<Patient>>;

    /// Update patient
    async fn update(&self, patient: Patient) -> OctaveResult<Patient>;

    /// Delete patient by ID (soft delete)
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// List patients with filters
    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<Patient>>;

    /// Get patients by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<Patient>>;

    /// Get patients by status
    async fn get_by_status(&self, practice_id: Uuid, status: PatientStatus) -> OctaveResult<Vec<Patient>>;

    /// Search patients by name (encrypted search)
    async fn search_by_name(&self, practice_id: Uuid, name_query: &str) -> OctaveResult<Vec<Patient>>;

    /// Search patients by phone (encrypted search)
    async fn search_by_phone(&self, practice_id: Uuid, phone_query: &str) -> OctaveResult<Vec<Patient>>;

    /// Search patients by date of birth
    async fn search_by_dob(&self, practice_id: Uuid, dob_query: &str) -> OctaveResult<Vec<Patient>>;

    /// Get patient summaries (without PHI)
    async fn get_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<PatientSummary>>;

    /// Find potential duplicates
    async fn find_potential_duplicates(&self, practice_id: Uuid, patient_id: Uuid) -> OctaveResult<Vec<Patient>>;

    /// Update last accessed timestamp
    async fn update_last_accessed(&self, id: Uuid) -> OctaveResult<bool>;

    /// Update last visit timestamp
    async fn update_last_visit(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get patients with recent activity
    async fn get_recently_active(&self, practice_id: Uuid, hours: u32) -> OctaveResult<Vec<Patient>>;

    /// Get patients by age range
    async fn get_by_age_range(&self, practice_id: Uuid, min_age: u32, max_age: u32) -> OctaveResult<Vec<Patient>>;

    /// Get patients by gender
    async fn get_by_gender(&self, practice_id: Uuid, gender: Gender) -> OctaveResult<Vec<Patient>>;

    /// Get patients without insurance
    async fn get_without_insurance(&self, practice_id: Uuid) -> OctaveResult<Vec<Patient>>;

    /// Get patient statistics
    async fn get_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<PatientStats>;

    /// Merge patients (combine records)
    async fn merge_patients(&self, primary_id: Uuid, secondary_id: Uuid) -> OctaveResult<Patient>;

    /// Mark patient as duplicate
    async fn mark_as_duplicate(&self, id: Uuid, primary_id: Uuid) -> OctaveResult<bool>;
}

/// Patient statistics
#[derive(Debug, Clone)]
pub struct PatientStats {
    /// Total number of patients
    pub total_patients: u64,
    /// Active patients
    pub active_patients: u64,
    /// Patients by status
    pub patients_by_status: HashMap<String, u64>,
    /// Patients by gender
    pub patients_by_gender: HashMap<String, u64>,
    /// Patients by age group
    pub patients_by_age_group: HashMap<String, u64>,
    /// Patients with insurance
    pub patients_with_insurance: u64,
    /// Recently active patients (last 30 days)
    pub recently_active_patients: u64,
    /// New patients this month
    pub new_patients_this_month: u64,
    /// Average patient age
    pub average_age: f64,
}

// ============================================================================
// PRIOR AUTHORIZATION REPOSITORIES
// ============================================================================

/// Repository for prior authorization operations
#[async_trait]
pub trait PriorAuthRepository: Send + Sync {
    /// Create a new prior authorization
    async fn create(&self, prior_auth: PriorAuth) -> OctaveResult<PriorAuth>;

    /// Get prior authorization by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<PriorAuth>>;

    /// Get prior authorization by tracking ID
    async fn get_by_tracking_id(&self, tracking_id: &str) -> OctaveResult<Option<PriorAuth>>;

    /// Update prior authorization
    async fn update(&self, prior_auth: PriorAuth) -> OctaveResult<PriorAuth>;

    /// Delete prior authorization by ID (soft delete)
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// List prior authorizations with filters
    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorizations by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorizations by patient ID
    async fn get_by_patient(&self, patient_id: Uuid) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorizations by status
    async fn get_by_status(&self, practice_id: Uuid, status: PriorAuthStatus) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorizations by priority
    async fn get_by_priority(&self, practice_id: Uuid, priority: PriorAuthPriority) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorizations by urgency level
    async fn get_by_urgency(&self, practice_id: Uuid, urgency: UrgencyLevel) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorizations by insurance company
    async fn get_by_insurance(&self, practice_id: Uuid, insurance_company: &str) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorizations by procedure code
    async fn get_by_procedure_code(&self, practice_id: Uuid, procedure_code: &str) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorizations by date range
    async fn get_by_date_range(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> OctaveResult<Vec<PriorAuth>>;

    /// Get expired prior authorizations
    async fn get_expired(&self, practice_id: Uuid) -> OctaveResult<Vec<PriorAuth>>;

    /// Get expiring prior authorizations (within specified days)
    async fn get_expiring_soon(&self, practice_id: Uuid, days: u32) -> OctaveResult<Vec<PriorAuth>>;

    /// Get overdue prior authorizations (past SLA)
    async fn get_overdue(&self, practice_id: Uuid) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorization summaries (without PHI)
    async fn get_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<PriorAuthSummary>>;

    /// Search prior authorizations by tracking ID pattern
    async fn search_by_tracking_id(&self, practice_id: Uuid, pattern: &str) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorizations requiring action
    async fn get_requiring_action(&self, practice_id: Uuid) -> OctaveResult<Vec<PriorAuth>>;

    /// Get prior authorization statistics
    async fn get_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<PriorAuthStats>;

    /// Update status with validation
    async fn update_status(
        &self,
        id: Uuid,
        new_status: PriorAuthStatus,
        updated_by: Uuid,
        reason: Option<String>,
    ) -> OctaveResult<PriorAuth>;

    /// Bulk update statuses
    async fn bulk_update_status(
        &self,
        ids: Vec<Uuid>,
        new_status: PriorAuthStatus,
        updated_by: Uuid,
    ) -> OctaveResult<Vec<PriorAuth>>;

    /// Mark as expired (batch operation)
    async fn mark_expired(&self, practice_id: Uuid) -> OctaveResult<usize>;
}

/// Prior authorization statistics
#[derive(Debug, Clone)]
pub struct PriorAuthStats {
    /// Total number of prior authorizations
    pub total_prior_auths: u64,
    /// Prior auths by status
    pub by_status: HashMap<String, u64>,
    /// Prior auths by priority
    pub by_priority: HashMap<String, u64>,
    /// Prior auths by urgency
    pub by_urgency: HashMap<String, u64>,
    /// Prior auths by insurance company
    pub by_insurance: HashMap<String, u64>,
    /// Average processing time in days
    pub avg_processing_time_days: f64,
    /// SLA compliance rate (percentage)
    pub sla_compliance_rate: f64,
    /// Approval rate (percentage)
    pub approval_rate: f64,
    /// Denial rate (percentage)
    pub denial_rate: f64,
    /// Expired authorizations
    pub expired_count: u64,
    /// Expiring soon (within 30 days)
    pub expiring_soon_count: u64,
    /// Overdue authorizations
    pub overdue_count: u64,
    /// This month's submissions
    pub this_month_submissions: u64,
    /// This month's approvals
    pub this_month_approvals: u64,
}

// ============================================================================
// DOCUMENT MANAGEMENT REPOSITORIES
// ============================================================================

/// Repository for document operations with security and compliance
#[async_trait]
pub trait DocumentRepository: Send + Sync {
    /// Create a new document
    async fn create(&self, document: Document) -> OctaveResult<Document>;

    /// Get document by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<Document>>;

    /// Update document
    async fn update(&self, document: Document) -> OctaveResult<Document>;

    /// Delete document by ID (soft delete)
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// List documents with filters
    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<Document>>;

    /// Get documents by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<Document>>;

    /// Get documents by prior authorization ID
    async fn get_by_prior_auth(&self, prior_auth_id: Uuid) -> OctaveResult<Vec<Document>>;

    /// Get documents by patient ID
    async fn get_by_patient(&self, patient_id: Uuid) -> OctaveResult<Vec<Document>>;

    /// Get documents by type
    async fn get_by_type(&self, practice_id: Uuid, document_type: DocumentType) -> OctaveResult<Vec<Document>>;

    /// Get documents by classification
    async fn get_by_classification(&self, practice_id: Uuid, classification: DocumentClassification) -> OctaveResult<Vec<Document>>;

    /// Get documents by virus scan status
    async fn get_by_virus_scan_status(&self, practice_id: Uuid, status: VirusScanStatus) -> OctaveResult<Vec<Document>>;

    /// Search documents by file name
    async fn search_by_filename(&self, practice_id: Uuid, filename_pattern: &str) -> OctaveResult<Vec<Document>>;

    /// Search documents by tags
    async fn search_by_tags(&self, practice_id: Uuid, tags: Vec<String>) -> OctaveResult<Vec<Document>>;

    /// Get document versions
    async fn get_versions(&self, practice_id: Uuid, base_document_id: Uuid) -> OctaveResult<Vec<Document>>;

    /// Get current version of document
    async fn get_current_version(&self, practice_id: Uuid, base_document_id: Uuid) -> OctaveResult<Option<Document>>;

    /// Get documents requiring retention action
    async fn get_retention_candidates(&self, practice_id: Uuid) -> OctaveResult<Vec<Document>>;

    /// Get expired documents
    async fn get_expired(&self, practice_id: Uuid) -> OctaveResult<Vec<Document>>;

    /// Get documents pending virus scan
    async fn get_pending_virus_scan(&self, practice_id: Uuid) -> OctaveResult<Vec<Document>>;

    /// Get document summaries (without PHI)
    async fn get_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<DocumentSummary>>;

    /// Update virus scan status
    async fn update_virus_scan_status(&self, id: Uuid, status: VirusScanStatus) -> OctaveResult<Document>;

    /// Update last accessed timestamp
    async fn update_last_accessed(&self, id: Uuid) -> OctaveResult<bool>;

    /// Mark document version as not current
    async fn mark_version_not_current(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get document statistics
    async fn get_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<DocumentStats>;

    /// Bulk update retention policies
    async fn bulk_update_retention(&self, practice_id: Uuid, document_type: DocumentType, retention_years: u32) -> OctaveResult<usize>;

    /// Get documents by date range
    async fn get_by_date_range(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> OctaveResult<Vec<Document>>;

    /// Full-text search in document metadata
    async fn full_text_search(&self, practice_id: Uuid, query: &str) -> OctaveResult<Vec<Document>>;
}

/// Document statistics
#[derive(Debug, Clone)]
pub struct DocumentStats {
    /// Total number of documents
    pub total_documents: u64,
    /// Documents by type
    pub by_type: HashMap<String, u64>,
    /// Documents by classification
    pub by_classification: HashMap<String, u64>,
    /// Documents by virus scan status
    pub by_virus_scan_status: HashMap<String, u64>,
    /// Total storage size in bytes
    pub total_storage_size: u64,
    /// Average document size
    pub average_document_size: f64,
    /// Documents uploaded this month
    pub uploaded_this_month: u64,
    /// Documents accessed this month
    pub accessed_this_month: u64,
    /// Documents pending virus scan
    pub pending_virus_scan: u64,
    /// Infected documents
    pub infected_documents: u64,
    /// Expired documents
    pub expired_documents: u64,
    /// Documents requiring retention action
    pub retention_candidates: u64,
    /// Documents by version count
    pub by_version_count: HashMap<u32, u64>,
}

// ============================================================================
// COMMUNICATION TRACKING REPOSITORIES
// ============================================================================

/// Repository for communication operations with healthcare compliance
#[async_trait]
pub trait CommunicationRepository: Send + Sync {
    /// Create a new communication
    async fn create(&self, communication: Communication) -> OctaveResult<Communication>;

    /// Get communication by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<Communication>>;

    /// Update communication
    async fn update(&self, communication: Communication) -> OctaveResult<Communication>;

    /// Delete communication by ID (soft delete)
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// List communications with filters
    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<Communication>>;

    /// Get communications by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<Communication>>;

    /// Get communications by prior authorization ID
    async fn get_by_prior_auth(&self, prior_auth_id: Uuid) -> OctaveResult<Vec<Communication>>;

    /// Get communications by patient ID
    async fn get_by_patient(&self, patient_id: Uuid) -> OctaveResult<Vec<Communication>>;

    /// Get communications by type
    async fn get_by_type(&self, practice_id: Uuid, communication_type: CommunicationType) -> OctaveResult<Vec<Communication>>;

    /// Get communications by outcome
    async fn get_by_outcome(&self, practice_id: Uuid, outcome: CommunicationOutcome) -> OctaveResult<Vec<Communication>>;

    /// Get communications by priority
    async fn get_by_priority(&self, practice_id: Uuid, priority: CommunicationPriority) -> OctaveResult<Vec<Communication>>;

    /// Get communications requiring follow-up
    async fn get_requiring_follow_up(&self, practice_id: Uuid) -> OctaveResult<Vec<Communication>>;

    /// Get overdue communications
    async fn get_overdue(&self, practice_id: Uuid) -> OctaveResult<Vec<Communication>>;

    /// Search communications by content
    async fn search_content(&self, practice_id: Uuid, query: &str) -> OctaveResult<Vec<Communication>>;

    /// Search communications by tags
    async fn search_by_tags(&self, practice_id: Uuid, tags: Vec<String>) -> OctaveResult<Vec<Communication>>;

    /// Get communications by date range
    async fn get_by_date_range(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> OctaveResult<Vec<Communication>>;

    /// Get communications by insurance company
    async fn get_by_insurance_company(&self, practice_id: Uuid, insurance_company: &str) -> OctaveResult<Vec<Communication>>;

    /// Get communications by insurance representative
    async fn get_by_insurance_rep(&self, practice_id: Uuid, rep_id: Uuid) -> OctaveResult<Vec<Communication>>;

    /// Get recent communications
    async fn get_recent(&self, practice_id: Uuid, limit: u32) -> OctaveResult<Vec<Communication>>;

    /// Get communication summaries (without PHI)
    async fn get_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<CommunicationSummary>>;

    /// Update communication outcome
    async fn update_outcome(&self, id: Uuid, outcome: CommunicationOutcome) -> OctaveResult<Communication>;

    /// Mark follow-up as completed
    async fn complete_follow_up(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get communication statistics
    async fn get_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<CommunicationStats>;

    /// Get communications by created user
    async fn get_by_created_user(&self, practice_id: Uuid, user_id: Uuid) -> OctaveResult<Vec<Communication>>;
}

/// Repository for insurance representative management
#[async_trait]
pub trait InsuranceRepRepository: Send + Sync {
    /// Create a new insurance representative
    async fn create(&self, rep: InsuranceRepresentative) -> OctaveResult<InsuranceRepresentative>;

    /// Get insurance representative by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<InsuranceRepresentative>>;

    /// Update insurance representative
    async fn update(&self, rep: InsuranceRepresentative) -> OctaveResult<InsuranceRepresentative>;

    /// Delete insurance representative by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get representatives by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<InsuranceRepresentative>>;

    /// Get representatives by insurance company
    async fn get_by_insurance_company(&self, practice_id: Uuid, company: &str) -> OctaveResult<Vec<InsuranceRepresentative>>;

    /// Search representatives by name
    async fn search_by_name(&self, practice_id: Uuid, name_pattern: &str) -> OctaveResult<Vec<InsuranceRepresentative>>;

    /// Get active representatives
    async fn get_active(&self, practice_id: Uuid) -> OctaveResult<Vec<InsuranceRepresentative>>;

    /// Get top performing representatives
    async fn get_top_performers(&self, practice_id: Uuid, limit: u32) -> OctaveResult<Vec<InsuranceRepresentative>>;

    /// Update performance metrics
    async fn update_performance(&self, id: Uuid, response_time_hours: f32, helpfulness_rating: f32) -> OctaveResult<InsuranceRepresentative>;

    /// Get representative summaries
    async fn get_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<InsuranceRepSummary>>;

    /// Get representatives by department
    async fn get_by_department(&self, practice_id: Uuid, department: &str) -> OctaveResult<Vec<InsuranceRepresentative>>;
}

/// Repository for communication template management
#[async_trait]
pub trait CommunicationTemplateRepository: Send + Sync {
    /// Create a new communication template
    async fn create(&self, template: CommunicationTemplate) -> OctaveResult<CommunicationTemplate>;

    /// Get communication template by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<CommunicationTemplate>>;

    /// Update communication template
    async fn update(&self, template: CommunicationTemplate) -> OctaveResult<CommunicationTemplate>;

    /// Delete communication template by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get templates by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<CommunicationTemplate>>;

    /// Get templates by category
    async fn get_by_category(&self, practice_id: Uuid, category: TemplateCategory) -> OctaveResult<Vec<CommunicationTemplate>>;

    /// Get templates by communication type
    async fn get_by_communication_type(&self, practice_id: Uuid, comm_type: CommunicationType) -> OctaveResult<Vec<CommunicationTemplate>>;

    /// Search templates by name
    async fn search_by_name(&self, practice_id: Uuid, name_pattern: &str) -> OctaveResult<Vec<CommunicationTemplate>>;

    /// Get active templates
    async fn get_active(&self, practice_id: Uuid) -> OctaveResult<Vec<CommunicationTemplate>>;

    /// Get shared templates
    async fn get_shared(&self, practice_id: Uuid) -> OctaveResult<Vec<CommunicationTemplate>>;

    /// Get most used templates
    async fn get_most_used(&self, practice_id: Uuid, limit: u32) -> OctaveResult<Vec<CommunicationTemplate>>;

    /// Record template usage
    async fn record_usage(&self, id: Uuid, effectiveness_rating: Option<f32>) -> OctaveResult<CommunicationTemplate>;

    /// Get template summaries
    async fn get_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<CommunicationTemplateSummary>>;

    /// Get templates by created user
    async fn get_by_created_user(&self, practice_id: Uuid, user_id: Uuid) -> OctaveResult<Vec<CommunicationTemplate>>;
}

/// Communication statistics
#[derive(Debug, Clone)]
pub struct CommunicationStats {
    /// Total number of communications
    pub total_communications: u64,
    /// Communications by type
    pub by_type: HashMap<String, u64>,
    /// Communications by direction
    pub by_direction: HashMap<String, u64>,
    /// Communications by outcome
    pub by_outcome: HashMap<String, u64>,
    /// Communications by priority
    pub by_priority: HashMap<String, u64>,
    /// Average response time in hours
    pub avg_response_time_hours: f64,
    /// Communications requiring follow-up
    pub requiring_follow_up: u64,
    /// Overdue communications
    pub overdue_communications: u64,
    /// Communications this month
    pub this_month_count: u64,
    /// Most active insurance companies
    pub top_insurance_companies: Vec<(String, u64)>,
    /// Most used templates
    pub top_templates: Vec<(String, u64)>,
    /// Success rate (successful outcomes / total)
    pub success_rate: f64,
}

// ============================================================================
// REMINDER & NOTIFICATION REPOSITORIES
// ============================================================================

/// Repository for reminder operations with comprehensive scheduling
#[async_trait]
pub trait ReminderRepository: Send + Sync {
    /// Create a new reminder
    async fn create(&self, reminder: Reminder) -> OctaveResult<Reminder>;

    /// Get reminder by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<Reminder>>;

    /// Update reminder
    async fn update(&self, reminder: Reminder) -> OctaveResult<Reminder>;

    /// Delete reminder by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get reminders by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<Reminder>>;

    /// Get reminders by assigned user
    async fn get_by_assigned_user(&self, practice_id: Uuid, user_id: Uuid) -> OctaveResult<Vec<Reminder>>;

    /// Get reminders by type
    async fn get_by_type(&self, practice_id: Uuid, reminder_type: ReminderType) -> OctaveResult<Vec<Reminder>>;

    /// Get reminders by status
    async fn get_by_status(&self, practice_id: Uuid, status: ReminderStatus) -> OctaveResult<Vec<Reminder>>;

    /// Get reminders by priority
    async fn get_by_priority(&self, practice_id: Uuid, priority: ReminderPriority) -> OctaveResult<Vec<Reminder>>;

    /// Get reminders by related entity
    async fn get_by_related_entity(&self, practice_id: Uuid, entity_type: EntityType, entity_id: Uuid) -> OctaveResult<Vec<Reminder>>;

    /// Get due reminders
    async fn get_due_reminders(&self, practice_id: Uuid) -> OctaveResult<Vec<Reminder>>;

    /// Get overdue reminders
    async fn get_overdue_reminders(&self, practice_id: Uuid) -> OctaveResult<Vec<Reminder>>;

    /// Get reminders due in next N hours
    async fn get_upcoming_reminders(&self, practice_id: Uuid, hours: u32) -> OctaveResult<Vec<Reminder>>;

    /// Get reminders requiring escalation
    async fn get_escalation_candidates(&self, practice_id: Uuid) -> OctaveResult<Vec<Reminder>>;

    /// Search reminders by title or description
    async fn search_reminders(&self, practice_id: Uuid, query: &str) -> OctaveResult<Vec<Reminder>>;

    /// Get reminders by date range
    async fn get_by_date_range(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> OctaveResult<Vec<Reminder>>;

    /// Get reminder summaries (without PHI)
    async fn get_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<ReminderSummary>>;

    /// Update reminder status
    async fn update_status(&self, id: Uuid, status: ReminderStatus) -> OctaveResult<Reminder>;

    /// Snooze reminder
    async fn snooze_reminder(&self, id: Uuid, new_due_time: DateTime<Utc>) -> OctaveResult<Reminder>;

    /// Complete reminder
    async fn complete_reminder(&self, id: Uuid, completion_notes: Option<String>) -> OctaveResult<Reminder>;

    /// Get reminder statistics
    async fn get_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<ReminderStats>;

    /// Bulk update reminders
    async fn bulk_update_status(&self, practice_id: Uuid, reminder_ids: Vec<Uuid>, status: ReminderStatus) -> OctaveResult<usize>;
}

/// Repository for notification tracking and delivery
#[async_trait]
pub trait NotificationRepository: Send + Sync {
    /// Create a new notification
    async fn create(&self, notification: Notification) -> OctaveResult<Notification>;

    /// Get notification by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<Notification>>;

    /// Update notification
    async fn update(&self, notification: Notification) -> OctaveResult<Notification>;

    /// Get notifications by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<Notification>>;

    /// Get notifications by recipient
    async fn get_by_recipient(&self, practice_id: Uuid, recipient_id: Uuid) -> OctaveResult<Vec<Notification>>;

    /// Get notifications by type
    async fn get_by_type(&self, practice_id: Uuid, notification_type: NotificationType) -> OctaveResult<Vec<Notification>>;

    /// Get notifications by channel
    async fn get_by_channel(&self, practice_id: Uuid, channel: NotificationChannel) -> OctaveResult<Vec<Notification>>;

    /// Get notifications by delivery status
    async fn get_by_delivery_status(&self, practice_id: Uuid, status: DeliveryStatus) -> OctaveResult<Vec<Notification>>;

    /// Get pending notifications
    async fn get_pending_notifications(&self, practice_id: Uuid) -> OctaveResult<Vec<Notification>>;

    /// Get failed notifications
    async fn get_failed_notifications(&self, practice_id: Uuid) -> OctaveResult<Vec<Notification>>;

    /// Update delivery status
    async fn update_delivery_status(&self, id: Uuid, status: DeliveryStatus, error_message: Option<String>) -> OctaveResult<Notification>;

    /// Mark notification as read
    async fn mark_as_read(&self, id: Uuid) -> OctaveResult<Notification>;

    /// Get notification statistics
    async fn get_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<NotificationStats>;

    /// Get notifications by date range
    async fn get_by_date_range(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> OctaveResult<Vec<Notification>>;
}

/// Repository for workflow state management
#[async_trait]
pub trait WorkflowRepository: Send + Sync {
    /// Create a new workflow state
    async fn create_workflow_state(&self, workflow_state: WorkflowState) -> OctaveResult<WorkflowState>;

    /// Get workflow state by ID
    async fn get_workflow_state_by_id(&self, id: Uuid) -> OctaveResult<Option<WorkflowState>>;

    /// Get workflow state by entity
    async fn get_workflow_state_by_entity(&self, practice_id: Uuid, entity_type: EntityType, entity_id: Uuid) -> OctaveResult<Option<WorkflowState>>;

    /// Update workflow state
    async fn update_workflow_state(&self, workflow_state: WorkflowState) -> OctaveResult<WorkflowState>;

    /// Add state transition
    async fn add_state_transition(&self, workflow_state_id: Uuid, transition: StateTransition) -> OctaveResult<WorkflowState>;

    /// Add pending action
    async fn add_pending_action(&self, workflow_state_id: Uuid, action: PendingAction) -> OctaveResult<WorkflowState>;

    /// Complete pending action
    async fn complete_pending_action(&self, workflow_state_id: Uuid, action_id: Uuid) -> OctaveResult<WorkflowState>;

    /// Get workflow states by practice
    async fn get_workflow_states_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<WorkflowState>>;

    /// Get workflow states by status
    async fn get_workflow_states_by_status(&self, practice_id: Uuid, status: &str) -> OctaveResult<Vec<WorkflowState>>;

    /// Get workflow states with pending actions
    async fn get_states_with_pending_actions(&self, practice_id: Uuid) -> OctaveResult<Vec<WorkflowState>>;

    /// Create workflow definition
    async fn create_workflow_definition(&self, definition: WorkflowDefinition) -> OctaveResult<WorkflowDefinition>;

    /// Get workflow definition by ID
    async fn get_workflow_definition_by_id(&self, id: Uuid) -> OctaveResult<Option<WorkflowDefinition>>;

    /// Get workflow definitions by practice
    async fn get_workflow_definitions_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<WorkflowDefinition>>;

    /// Update workflow definition
    async fn update_workflow_definition(&self, definition: WorkflowDefinition) -> OctaveResult<WorkflowDefinition>;

    /// Get workflow statistics
    async fn get_workflow_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<WorkflowStats>;
}

/// Reminder statistics
#[derive(Debug, Clone)]
pub struct ReminderStats {
    /// Total number of reminders
    pub total_reminders: u64,
    /// Reminders by type
    pub by_type: HashMap<String, u64>,
    /// Reminders by status
    pub by_status: HashMap<String, u64>,
    /// Reminders by priority
    pub by_priority: HashMap<String, u64>,
    /// Average completion time in hours
    pub avg_completion_time_hours: f64,
    /// Completion rate
    pub completion_rate: f64,
    /// Overdue reminders count
    pub overdue_count: u64,
    /// Escalated reminders count
    pub escalated_count: u64,
    /// Average snooze count
    pub avg_snooze_count: f64,
    /// Reminders created this month
    pub created_this_month: u64,
    /// Reminders completed this month
    pub completed_this_month: u64,
}

/// Notification statistics
#[derive(Debug, Clone)]
pub struct NotificationStats {
    /// Total notifications sent
    pub total_notifications: u64,
    /// Notifications by type
    pub by_type: HashMap<String, u64>,
    /// Notifications by channel
    pub by_channel: HashMap<String, u64>,
    /// Notifications by delivery status
    pub by_delivery_status: HashMap<String, u64>,
    /// Delivery success rate
    pub delivery_success_rate: f64,
    /// Average delivery time in minutes
    pub avg_delivery_time_minutes: f64,
    /// Read rate for delivered notifications
    pub read_rate: f64,
    /// Failed notifications count
    pub failed_count: u64,
    /// Notifications sent this month
    pub sent_this_month: u64,
}

/// Workflow statistics
#[derive(Debug, Clone)]
pub struct WorkflowStats {
    /// Total workflow states
    pub total_workflow_states: u64,
    /// States by current status
    pub by_current_status: HashMap<String, u64>,
    /// States by entity type
    pub by_entity_type: HashMap<String, u64>,
    /// Average processing time by status
    pub avg_processing_time_by_status: HashMap<String, f64>,
    /// Pending actions count
    pub pending_actions_count: u64,
    /// Overdue actions count
    pub overdue_actions_count: u64,
    /// Workflow bottlenecks (status with longest avg time)
    pub bottlenecks: Vec<(String, f64)>,
    /// Workflow efficiency score
    pub efficiency_score: f64,
}

// ============================================================================
// TEMPLATE & AUTOMATION REPOSITORIES
// ============================================================================

/// Repository for template management with version control
#[async_trait]
pub trait TemplateRepository: Send + Sync {
    /// Create a new template
    async fn create(&self, template: Template) -> OctaveResult<Template>;

    /// Get template by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<Template>>;

    /// Update template
    async fn update(&self, template: Template) -> OctaveResult<Template>;

    /// Delete template by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get templates by practice ID
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<Template>>;

    /// Get templates by type
    async fn get_by_type(&self, practice_id: Uuid, template_type: TemplateType) -> OctaveResult<Vec<Template>>;

    /// Get templates by category
    async fn get_by_category(&self, practice_id: Uuid, category: TemplateCategory) -> OctaveResult<Vec<Template>>;

    /// Get templates by approval status
    async fn get_by_approval_status(&self, practice_id: Uuid, status: ApprovalStatus) -> OctaveResult<Vec<Template>>;

    /// Search templates by name or description
    async fn search_templates(&self, practice_id: Uuid, query: &str) -> OctaveResult<Vec<Template>>;

    /// Get templates by tags
    async fn get_by_tags(&self, practice_id: Uuid, tags: Vec<String>) -> OctaveResult<Vec<Template>>;

    /// Get active templates
    async fn get_active_templates(&self, practice_id: Uuid) -> OctaveResult<Vec<Template>>;

    /// Get shared templates
    async fn get_shared_templates(&self, practice_id: Uuid) -> OctaveResult<Vec<Template>>;

    /// Get most used templates
    async fn get_most_used(&self, practice_id: Uuid, limit: u32) -> OctaveResult<Vec<Template>>;

    /// Get templates by effectiveness score
    async fn get_by_effectiveness(&self, practice_id: Uuid, min_score: f32) -> OctaveResult<Vec<Template>>;

    /// Get templates by created user
    async fn get_by_created_user(&self, practice_id: Uuid, user_id: Uuid) -> OctaveResult<Vec<Template>>;

    /// Get template summaries (without PHI)
    async fn get_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<TemplateSummary>>;

    /// Update template approval status
    async fn update_approval_status(&self, id: Uuid, status: ApprovalStatus, approved_by: Option<Uuid>) -> OctaveResult<Template>;

    /// Record template usage
    async fn record_usage(&self, id: Uuid, effectiveness_score: Option<f32>) -> OctaveResult<Template>;

    /// Get template statistics
    async fn get_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<TemplateStats>;

    /// Bulk update template status
    async fn bulk_update_status(&self, practice_id: Uuid, template_ids: Vec<Uuid>, is_active: bool) -> OctaveResult<usize>;

    /// Get templates requiring approval
    async fn get_pending_approval(&self, practice_id: Uuid) -> OctaveResult<Vec<Template>>;
}

/// Repository for template version control
#[async_trait]
pub trait TemplateVersionRepository: Send + Sync {
    /// Create a new template version
    async fn create_version(&self, version: TemplateVersion) -> OctaveResult<TemplateVersion>;

    /// Get template version by ID
    async fn get_version_by_id(&self, id: Uuid) -> OctaveResult<Option<TemplateVersion>>;

    /// Get all versions for a template
    async fn get_versions_by_template(&self, template_id: Uuid) -> OctaveResult<Vec<TemplateVersion>>;

    /// Get specific version of template
    async fn get_template_version(&self, template_id: Uuid, version: u32) -> OctaveResult<Option<TemplateVersion>>;

    /// Get latest version of template
    async fn get_latest_version(&self, template_id: Uuid) -> OctaveResult<Option<TemplateVersion>>;

    /// Delete template version
    async fn delete_version(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get version history for template
    async fn get_version_history(&self, template_id: Uuid) -> OctaveResult<Vec<TemplateVersion>>;
}

/// Repository for template usage analytics
#[async_trait]
pub trait TemplateUsageRepository: Send + Sync {
    /// Record template usage
    async fn record_usage(&self, usage: TemplateUsage) -> OctaveResult<TemplateUsage>;

    /// Get usage records by template
    async fn get_usage_by_template(&self, template_id: Uuid) -> OctaveResult<Vec<TemplateUsage>>;

    /// Get usage records by practice
    async fn get_usage_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<TemplateUsage>>;

    /// Get usage records by user
    async fn get_usage_by_user(&self, practice_id: Uuid, user_id: Uuid) -> OctaveResult<Vec<TemplateUsage>>;

    /// Get usage records by context
    async fn get_usage_by_context(&self, practice_id: Uuid, context_type: ContextType) -> OctaveResult<Vec<TemplateUsage>>;

    /// Get usage records by date range
    async fn get_usage_by_date_range(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> OctaveResult<Vec<TemplateUsage>>;

    /// Get usage statistics
    async fn get_usage_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<TemplateUsageStats>;

    /// Get template effectiveness metrics
    async fn get_effectiveness_metrics(&self, template_id: Uuid) -> OctaveResult<EffectivenessMetrics>;
}

/// Template statistics
#[derive(Debug, Clone)]
pub struct TemplateStats {
    /// Total number of templates
    pub total_templates: u64,
    /// Templates by type
    pub by_type: HashMap<String, u64>,
    /// Templates by category
    pub by_category: HashMap<String, u64>,
    /// Templates by approval status
    pub by_approval_status: HashMap<String, u64>,
    /// Average effectiveness score
    pub avg_effectiveness_score: f64,
    /// Most used templates
    pub most_used_templates: Vec<(String, u64)>,
    /// Templates created this month
    pub created_this_month: u64,
    /// Templates used this month
    pub used_this_month: u64,
    /// Active templates count
    pub active_templates: u64,
    /// Shared templates count
    pub shared_templates: u64,
    /// Templates pending approval
    pub pending_approval: u64,
}

/// Template usage statistics
#[derive(Debug, Clone)]
pub struct TemplateUsageStats {
    /// Total usage count
    pub total_usage: u64,
    /// Usage by template type
    pub by_template_type: HashMap<String, u64>,
    /// Usage by context type
    pub by_context_type: HashMap<String, u64>,
    /// Usage by user
    pub by_user: HashMap<Uuid, u64>,
    /// Average effectiveness score
    pub avg_effectiveness: f64,
    /// Usage this month
    pub usage_this_month: u64,
    /// Most effective templates
    pub most_effective_templates: Vec<(String, f32)>,
    /// Usage trends over time
    pub usage_trends: Vec<(DateTime<Utc>, u64)>,
}

/// Template effectiveness metrics
#[derive(Debug, Clone)]
pub struct EffectivenessMetrics {
    /// Template ID
    pub template_id: Uuid,
    /// Total usage count
    pub usage_count: u64,
    /// Average effectiveness score
    pub avg_effectiveness: f64,
    /// Effectiveness by context
    pub effectiveness_by_context: HashMap<String, f64>,
    /// Effectiveness trend over time
    pub effectiveness_trend: Vec<(DateTime<Utc>, f64)>,
    /// Success rate (effectiveness > 0.7)
    pub success_rate: f64,
    /// User satisfaction score
    pub user_satisfaction: f64,
}

// ============================================================================
// ANALYTICS & REPORTING REPOSITORIES
// ============================================================================

/// Repository for analytics metrics collection and querying
#[async_trait]
pub trait AnalyticsRepository: Send + Sync {
    /// Record a new analytics metric
    async fn record_metric(&self, metric: AnalyticsMetric) -> OctaveResult<AnalyticsMetric>;

    /// Get metrics by practice and type
    async fn get_metrics_by_type(
        &self,
        practice_id: Uuid,
        metric_type: MetricType,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<AnalyticsMetric>>;

    /// Get metrics by aggregation period
    async fn get_metrics_by_period(
        &self,
        practice_id: Uuid,
        aggregation_period: AggregationPeriod,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<AnalyticsMetric>>;

    /// Get metrics by entity
    async fn get_metrics_by_entity(
        &self,
        practice_id: Uuid,
        entity_type: EntityType,
        entity_id: Uuid,
    ) -> OctaveResult<Vec<AnalyticsMetric>>;

    /// Get metric summaries (without PHI)
    async fn get_metric_summaries(
        &self,
        practice_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<AnalyticsMetricSummary>>;

    /// Get aggregated metrics
    async fn get_aggregated_metrics(
        &self,
        practice_id: Uuid,
        metric_type: MetricType,
        aggregation_period: AggregationPeriod,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<AnalyticsMetric>>;

    /// Get real-time metrics
    async fn get_real_time_metrics(&self, practice_id: Uuid) -> OctaveResult<Vec<AnalyticsMetric>>;

    /// Get metrics by dimensions
    async fn get_metrics_by_dimensions(
        &self,
        practice_id: Uuid,
        dimensions: HashMap<String, String>,
    ) -> OctaveResult<Vec<AnalyticsMetric>>;

    /// Delete old metrics (for retention)
    async fn delete_old_metrics(&self, practice_id: Uuid, before_date: DateTime<Utc>) -> OctaveResult<usize>;

    /// Get analytics statistics
    async fn get_analytics_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<AnalyticsStats>;
}

/// Repository for report management
#[async_trait]
pub trait ReportRepository: Send + Sync {
    /// Create a new report
    async fn create(&self, report: Report) -> OctaveResult<Report>;

    /// Get report by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<Report>>;

    /// Update report
    async fn update(&self, report: Report) -> OctaveResult<Report>;

    /// Delete report by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get reports by practice
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<Report>>;

    /// Get reports by type
    async fn get_by_type(&self, practice_id: Uuid, report_type: ReportType) -> OctaveResult<Vec<Report>>;

    /// Get reports by status
    async fn get_by_status(&self, practice_id: Uuid, status: ReportStatus) -> OctaveResult<Vec<Report>>;

    /// Get reports by user
    async fn get_by_user(&self, practice_id: Uuid, user_id: Uuid) -> OctaveResult<Vec<Report>>;

    /// Get report summaries (without PHI)
    async fn get_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<ReportSummary>>;

    /// Get scheduled reports
    async fn get_scheduled_reports(&self, practice_id: Uuid) -> OctaveResult<Vec<Report>>;

    /// Get expired reports
    async fn get_expired_reports(&self, practice_id: Uuid) -> OctaveResult<Vec<Report>>;

    /// Update report status
    async fn update_status(&self, id: Uuid, status: ReportStatus) -> OctaveResult<Report>;

    /// Record report access
    async fn record_access(&self, id: Uuid) -> OctaveResult<Report>;

    /// Get report statistics
    async fn get_report_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<ReportStats>;

    /// Clean up expired reports
    async fn cleanup_expired_reports(&self, practice_id: Uuid) -> OctaveResult<usize>;
}

/// Repository for compliance monitoring
#[async_trait]
pub trait ComplianceRepository: Send + Sync {
    /// Record a compliance violation
    async fn record_violation(&self, violation: ComplianceViolation) -> OctaveResult<ComplianceViolation>;

    /// Get violation by ID
    async fn get_violation_by_id(&self, id: Uuid) -> OctaveResult<Option<ComplianceViolation>>;

    /// Update violation
    async fn update_violation(&self, violation: ComplianceViolation) -> OctaveResult<ComplianceViolation>;

    /// Get violations by practice
    async fn get_violations_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<ComplianceViolation>>;

    /// Get violations by type
    async fn get_violations_by_type(
        &self,
        practice_id: Uuid,
        violation_type: ComplianceViolationType,
    ) -> OctaveResult<Vec<ComplianceViolation>>;

    /// Get violations by severity
    async fn get_violations_by_severity(
        &self,
        practice_id: Uuid,
        severity: ComplianceSeverity,
    ) -> OctaveResult<Vec<ComplianceViolation>>;

    /// Get violations by status
    async fn get_violations_by_status(
        &self,
        practice_id: Uuid,
        status: ComplianceStatus,
    ) -> OctaveResult<Vec<ComplianceViolation>>;

    /// Get violations by date range
    async fn get_violations_by_date_range(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> OctaveResult<Vec<ComplianceViolation>>;

    /// Get violation summaries (without PHI)
    async fn get_violation_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<ComplianceViolationSummary>>;

    /// Get unresolved violations
    async fn get_unresolved_violations(&self, practice_id: Uuid) -> OctaveResult<Vec<ComplianceViolation>>;

    /// Get critical violations
    async fn get_critical_violations(&self, practice_id: Uuid) -> OctaveResult<Vec<ComplianceViolation>>;

    /// Get compliance statistics
    async fn get_compliance_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<ComplianceStats>;

    /// Get compliance score
    async fn get_compliance_score(&self, practice_id: Uuid) -> OctaveResult<f64>;
}

/// Repository for performance monitoring
#[async_trait]
pub trait PerformanceRepository: Send + Sync {
    /// Record a performance metric
    async fn record_metric(&self, metric: PerformanceMetric) -> OctaveResult<PerformanceMetric>;

    /// Get metrics by type
    async fn get_metrics_by_type(
        &self,
        practice_id: Uuid,
        metric_type: PerformanceMetricType,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<PerformanceMetric>>;

    /// Get metrics exceeding threshold
    async fn get_metrics_exceeding_threshold(&self, practice_id: Uuid) -> OctaveResult<Vec<PerformanceMetric>>;

    /// Get metric summaries
    async fn get_metric_summaries(
        &self,
        practice_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<PerformanceMetricSummary>>;

    /// Get real-time performance metrics
    async fn get_real_time_metrics(&self, practice_id: Uuid) -> OctaveResult<Vec<PerformanceMetric>>;

    /// Get performance trends
    async fn get_performance_trends(
        &self,
        practice_id: Uuid,
        metric_type: PerformanceMetricType,
        days: u32,
    ) -> OctaveResult<Vec<PerformanceMetric>>;

    /// Get system health metrics
    async fn get_system_health_metrics(&self, practice_id: Uuid) -> OctaveResult<Vec<PerformanceMetric>>;

    /// Delete old metrics (for retention)
    async fn delete_old_metrics(&self, practice_id: Uuid, before_date: DateTime<Utc>) -> OctaveResult<usize>;

    /// Get performance statistics
    async fn get_performance_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<PerformanceStats>;
}

/// Repository for dashboard management
#[async_trait]
pub trait DashboardRepository: Send + Sync {
    /// Create a new dashboard
    async fn create(&self, dashboard: Dashboard) -> OctaveResult<Dashboard>;

    /// Get dashboard by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<Dashboard>>;

    /// Update dashboard
    async fn update(&self, dashboard: Dashboard) -> OctaveResult<Dashboard>;

    /// Delete dashboard by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get dashboards by practice
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<Dashboard>>;

    /// Get dashboards by user
    async fn get_by_user(&self, practice_id: Uuid, user_id: Uuid) -> OctaveResult<Vec<Dashboard>>;

    /// Get shared dashboards
    async fn get_shared_dashboards(&self, practice_id: Uuid) -> OctaveResult<Vec<Dashboard>>;

    /// Get default dashboard
    async fn get_default_dashboard(&self, practice_id: Uuid) -> OctaveResult<Option<Dashboard>>;

    /// Get dashboard summaries
    async fn get_summaries(&self, practice_id: Uuid) -> OctaveResult<Vec<DashboardSummary>>;

    /// Record dashboard access
    async fn record_access(&self, id: Uuid) -> OctaveResult<Dashboard>;

    /// Get dashboard statistics
    async fn get_dashboard_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<DashboardStats>;
}

/// Analytics statistics
#[derive(Debug, Clone)]
pub struct AnalyticsStats {
    /// Total metrics recorded
    pub total_metrics: u64,
    /// Metrics by type
    pub by_type: HashMap<String, u64>,
    /// Metrics by aggregation period
    pub by_period: HashMap<String, u64>,
    /// Average metric value by type
    pub avg_values: HashMap<String, f64>,
    /// Metrics recorded today
    pub metrics_today: u64,
    /// Metrics recorded this week
    pub metrics_this_week: u64,
    /// Metrics recorded this month
    pub metrics_this_month: u64,
    /// Real-time metrics count
    pub real_time_metrics: u64,
}

/// Report statistics
#[derive(Debug, Clone)]
pub struct ReportStats {
    /// Total reports generated
    pub total_reports: u64,
    /// Reports by type
    pub by_type: HashMap<String, u64>,
    /// Reports by status
    pub by_status: HashMap<String, u64>,
    /// Reports by format
    pub by_format: HashMap<String, u64>,
    /// Average generation time
    pub avg_generation_time: f64,
    /// Reports generated today
    pub reports_today: u64,
    /// Reports generated this week
    pub reports_this_week: u64,
    /// Reports generated this month
    pub reports_this_month: u64,
    /// Failed reports count
    pub failed_reports: u64,
    /// Total report size
    pub total_size_bytes: u64,
}

/// Compliance statistics
#[derive(Debug, Clone)]
pub struct ComplianceStats {
    /// Total violations detected
    pub total_violations: u64,
    /// Violations by type
    pub by_type: HashMap<String, u64>,
    /// Violations by severity
    pub by_severity: HashMap<String, u64>,
    /// Violations by status
    pub by_status: HashMap<String, u64>,
    /// Unresolved violations
    pub unresolved_violations: u64,
    /// Critical violations
    pub critical_violations: u64,
    /// Violations detected today
    pub violations_today: u64,
    /// Violations detected this week
    pub violations_this_week: u64,
    /// Violations detected this month
    pub violations_this_month: u64,
    /// Average resolution time (hours)
    pub avg_resolution_time: f64,
    /// Compliance score (0.0 - 1.0)
    pub compliance_score: f64,
}

/// Performance statistics
#[derive(Debug, Clone)]
pub struct PerformanceStats {
    /// Total metrics recorded
    pub total_metrics: u64,
    /// Metrics by type
    pub by_type: HashMap<String, u64>,
    /// Average values by type
    pub avg_values: HashMap<String, f64>,
    /// Threshold violations
    pub threshold_violations: u64,
    /// System health score (0.0 - 1.0)
    pub system_health_score: f64,
    /// Performance trends
    pub performance_trends: HashMap<String, f64>,
    /// Metrics recorded today
    pub metrics_today: u64,
    /// Critical performance issues
    pub critical_issues: u64,
}

/// Dashboard statistics
#[derive(Debug, Clone)]
pub struct DashboardStats {
    /// Total dashboards
    pub total_dashboards: u64,
    /// Shared dashboards
    pub shared_dashboards: u64,
    /// Default dashboards
    pub default_dashboards: u64,
    /// Total dashboard accesses
    pub total_accesses: u64,
    /// Average widgets per dashboard
    pub avg_widgets_per_dashboard: f64,
    /// Most accessed dashboards
    pub most_accessed: Vec<(String, u64)>,
    /// Dashboards created this month
    pub created_this_month: u64,
}

// ============================================================================
// API & INTEGRATION REPOSITORIES
// ============================================================================

/// Repository for API endpoint management
#[async_trait]
pub trait ApiEndpointRepository: Send + Sync {
    /// Create a new API endpoint
    async fn create(&self, endpoint: ApiEndpoint) -> OctaveResult<ApiEndpoint>;

    /// Get endpoint by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<ApiEndpoint>>;

    /// Update endpoint
    async fn update(&self, endpoint: ApiEndpoint) -> OctaveResult<ApiEndpoint>;

    /// Delete endpoint by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get endpoints by practice
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<ApiEndpoint>>;

    /// Get endpoints by version
    async fn get_by_version(&self, practice_id: Uuid, version: &str) -> OctaveResult<Vec<ApiEndpoint>>;

    /// Get active endpoints
    async fn get_active_endpoints(&self, practice_id: Uuid) -> OctaveResult<Vec<ApiEndpoint>>;

    /// Find endpoint by path and method
    async fn find_endpoint(&self, practice_id: Uuid, path: &str, method: HttpMethod) -> OctaveResult<Option<ApiEndpoint>>;
}

/// Repository for API request logging and monitoring
#[async_trait]
pub trait ApiRequestLogRepository: Send + Sync {
    /// Log an API request
    async fn log_request(&self, log: ApiRequestLog) -> OctaveResult<ApiRequestLog>;

    /// Get request logs by practice
    async fn get_by_practice(
        &self,
        practice_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<ApiRequestLog>>;

    /// Get request logs by user
    async fn get_by_user(
        &self,
        practice_id: Uuid,
        user_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<ApiRequestLog>>;

    /// Get request logs by endpoint
    async fn get_by_endpoint(
        &self,
        practice_id: Uuid,
        endpoint: &str,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<ApiRequestLog>>;

    /// Get error logs
    async fn get_error_logs(
        &self,
        practice_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<ApiRequestLog>>;

    /// Get slow requests
    async fn get_slow_requests(
        &self,
        practice_id: Uuid,
        min_processing_time_ms: u32,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<ApiRequestLog>>;

    /// Get API usage statistics
    async fn get_usage_stats(&self, practice_id: Uuid) -> OctaveResult<ApiUsageStats>;

    /// Clean up old logs
    async fn cleanup_old_logs(&self, practice_id: Uuid, before_date: DateTime<Utc>) -> OctaveResult<usize>;
}

/// Repository for external integration management
#[async_trait]
pub trait ExternalIntegrationRepository: Send + Sync {
    /// Create a new integration
    async fn create(&self, integration: ExternalIntegration) -> OctaveResult<ExternalIntegration>;

    /// Get integration by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<ExternalIntegration>>;

    /// Update integration
    async fn update(&self, integration: ExternalIntegration) -> OctaveResult<ExternalIntegration>;

    /// Delete integration by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get integrations by practice
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<ExternalIntegration>>;

    /// Get integrations by type
    async fn get_by_type(&self, practice_id: Uuid, integration_type: IntegrationType) -> OctaveResult<Vec<ExternalIntegration>>;

    /// Get active integrations
    async fn get_active_integrations(&self, practice_id: Uuid) -> OctaveResult<Vec<ExternalIntegration>>;

    /// Get integrations by connection status
    async fn get_by_connection_status(&self, practice_id: Uuid, status: ConnectionStatus) -> OctaveResult<Vec<ExternalIntegration>>;

    /// Update connection status
    async fn update_connection_status(&self, id: Uuid, status: ConnectionStatus, error_message: Option<String>) -> OctaveResult<ExternalIntegration>;

    /// Test integration connection
    async fn test_connection(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get integration statistics
    async fn get_integration_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<IntegrationStats>;
}

/// Repository for file storage management
#[async_trait]
pub trait FileStorageRepository: Send + Sync {
    /// Create a new file storage configuration
    async fn create(&self, storage: FileStorage) -> OctaveResult<FileStorage>;

    /// Get storage by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<FileStorage>>;

    /// Update storage
    async fn update(&self, storage: FileStorage) -> OctaveResult<FileStorage>;

    /// Delete storage by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get storage configurations by practice
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<FileStorage>>;

    /// Get default storage for practice
    async fn get_default_storage(&self, practice_id: Uuid) -> OctaveResult<Option<FileStorage>>;

    /// Get storage by type
    async fn get_by_type(&self, practice_id: Uuid, storage_type: StorageType) -> OctaveResult<Vec<FileStorage>>;

    /// Update storage usage
    async fn update_usage(&self, id: Uuid, used_bytes: u64) -> OctaveResult<FileStorage>;

    /// Get storage statistics
    async fn get_storage_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<StorageStats>;
}

/// Repository for search index management
#[async_trait]
pub trait SearchIndexRepository: Send + Sync {
    /// Create a new search index
    async fn create(&self, index: SearchIndex) -> OctaveResult<SearchIndex>;

    /// Get index by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<SearchIndex>>;

    /// Update index
    async fn update(&self, index: SearchIndex) -> OctaveResult<SearchIndex>;

    /// Delete index by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get indices by practice
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<SearchIndex>>;

    /// Get indices by type
    async fn get_by_type(&self, practice_id: Uuid, index_type: IndexType) -> OctaveResult<Vec<SearchIndex>>;

    /// Get active indices
    async fn get_active_indices(&self, practice_id: Uuid) -> OctaveResult<Vec<SearchIndex>>;

    /// Update index statistics
    async fn update_stats(&self, id: Uuid, document_count: u64, size_bytes: u64) -> OctaveResult<SearchIndex>;

    /// Mark index as indexed
    async fn mark_indexed(&self, id: Uuid) -> OctaveResult<SearchIndex>;

    /// Get search statistics
    async fn get_search_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<SearchStats>;
}

/// Repository for search query logging
#[async_trait]
pub trait SearchQueryRepository: Send + Sync {
    /// Log a search query
    async fn log_query(&self, query: SearchQuery) -> OctaveResult<SearchQuery>;

    /// Get queries by practice
    async fn get_by_practice(
        &self,
        practice_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<SearchQuery>>;

    /// Get queries by user
    async fn get_by_user(
        &self,
        practice_id: Uuid,
        user_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<SearchQuery>>;

    /// Get popular queries
    async fn get_popular_queries(
        &self,
        practice_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: u32,
    ) -> OctaveResult<Vec<(String, u32)>>;

    /// Get slow queries
    async fn get_slow_queries(
        &self,
        practice_id: Uuid,
        min_processing_time_ms: u32,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<SearchQuery>>;

    /// Get search analytics
    async fn get_search_analytics(&self, practice_id: Uuid) -> OctaveResult<SearchAnalytics>;

    /// Clean up old queries
    async fn cleanup_old_queries(&self, practice_id: Uuid, before_date: DateTime<Utc>) -> OctaveResult<usize>;
}

/// Repository for webhook management
#[async_trait]
pub trait WebhookRepository: Send + Sync {
    /// Create a new webhook
    async fn create(&self, webhook: Webhook) -> OctaveResult<Webhook>;

    /// Get webhook by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<Webhook>>;

    /// Update webhook
    async fn update(&self, webhook: Webhook) -> OctaveResult<Webhook>;

    /// Delete webhook by ID
    async fn delete(&self, id: Uuid) -> OctaveResult<bool>;

    /// Get webhooks by practice
    async fn get_by_practice(&self, practice_id: Uuid) -> OctaveResult<Vec<Webhook>>;

    /// Get webhooks by event
    async fn get_by_event(&self, practice_id: Uuid, event: WebhookEvent) -> OctaveResult<Vec<Webhook>>;

    /// Get active webhooks
    async fn get_active_webhooks(&self, practice_id: Uuid) -> OctaveResult<Vec<Webhook>>;

    /// Update delivery status
    async fn update_delivery_status(&self, id: Uuid, last_delivery: Option<DateTime<Utc>>, success: bool) -> OctaveResult<Webhook>;

    /// Get webhook statistics
    async fn get_webhook_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<WebhookStats>;
}

/// Repository for webhook delivery logging
#[async_trait]
pub trait WebhookDeliveryRepository: Send + Sync {
    /// Create a new delivery log
    async fn create(&self, delivery: WebhookDelivery) -> OctaveResult<WebhookDelivery>;

    /// Get delivery by ID
    async fn get_by_id(&self, id: Uuid) -> OctaveResult<Option<WebhookDelivery>>;

    /// Update delivery
    async fn update(&self, delivery: WebhookDelivery) -> OctaveResult<WebhookDelivery>;

    /// Get deliveries by webhook
    async fn get_by_webhook(
        &self,
        webhook_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<WebhookDelivery>>;

    /// Get deliveries by practice
    async fn get_by_practice(
        &self,
        practice_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> OctaveResult<Vec<WebhookDelivery>>;

    /// Get failed deliveries
    async fn get_failed_deliveries(&self, practice_id: Uuid) -> OctaveResult<Vec<WebhookDelivery>>;

    /// Get deliveries ready for retry
    async fn get_retry_deliveries(&self) -> OctaveResult<Vec<WebhookDelivery>>;

    /// Mark delivery as successful
    async fn mark_success(&self, id: Uuid, status_code: u16, response_body: Option<String>) -> OctaveResult<WebhookDelivery>;

    /// Mark delivery as failed
    async fn mark_failure(&self, id: Uuid, error_message: String, next_retry_at: Option<DateTime<Utc>>) -> OctaveResult<WebhookDelivery>;

    /// Get delivery statistics
    async fn get_delivery_stats(&self, practice_id: Option<Uuid>) -> OctaveResult<DeliveryStats>;

    /// Clean up old deliveries
    async fn cleanup_old_deliveries(&self, practice_id: Uuid, before_date: DateTime<Utc>) -> OctaveResult<usize>;
}

// ============================================================================
// API & INTEGRATION STATISTICS
// ============================================================================

/// API usage statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiUsageStats {
    /// Total number of requests
    pub total_requests: u64,
    /// Successful requests
    pub successful_requests: u64,
    /// Failed requests
    pub failed_requests: u64,
    /// Average response time in milliseconds
    pub avg_response_time_ms: f64,
    /// Requests per hour
    pub requests_per_hour: f64,
    /// Most used endpoints
    pub top_endpoints: Vec<(String, u64)>,
    /// Error rate percentage
    pub error_rate: f64,
    /// PHI requests count
    pub phi_requests: u64,
    /// High threat score requests
    pub high_threat_requests: u64,
}

/// Integration statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IntegrationStats {
    /// Total number of integrations
    pub total_integrations: u64,
    /// Active integrations
    pub active_integrations: u64,
    /// Connected integrations
    pub connected_integrations: u64,
    /// Failed integrations
    pub failed_integrations: u64,
    /// Integrations by type
    pub integrations_by_type: HashMap<String, u64>,
    /// Connection success rate
    pub connection_success_rate: f64,
    /// Average connection time
    pub avg_connection_time_ms: f64,
}

/// Storage statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageStats {
    /// Total storage configurations
    pub total_storages: u64,
    /// Active storages
    pub active_storages: u64,
    /// Total capacity in bytes
    pub total_capacity_bytes: u64,
    /// Total used bytes
    pub total_used_bytes: u64,
    /// Storage utilization percentage
    pub utilization_percentage: f64,
    /// Storage by type
    pub storage_by_type: HashMap<String, u64>,
    /// Average file size
    pub avg_file_size_bytes: u64,
}

/// Search statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchStats {
    /// Total search indices
    pub total_indices: u64,
    /// Active indices
    pub active_indices: u64,
    /// Total documents indexed
    pub total_documents: u64,
    /// Total index size in bytes
    pub total_size_bytes: u64,
    /// Indices by type
    pub indices_by_type: HashMap<String, u64>,
    /// Average index size
    pub avg_index_size_bytes: u64,
    /// Last indexing time
    pub last_indexed_at: Option<DateTime<Utc>>,
}

/// Search analytics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchAnalytics {
    /// Total queries
    pub total_queries: u64,
    /// Average query time
    pub avg_query_time_ms: f64,
    /// Queries per hour
    pub queries_per_hour: f64,
    /// Most popular queries
    pub popular_queries: Vec<(String, u32)>,
    /// Average results per query
    pub avg_results_per_query: f64,
    /// PHI queries count
    pub phi_queries: u64,
    /// Zero result queries
    pub zero_result_queries: u64,
}

/// Webhook statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebhookStats {
    /// Total webhooks
    pub total_webhooks: u64,
    /// Active webhooks
    pub active_webhooks: u64,
    /// Webhooks by event type
    pub webhooks_by_event: HashMap<String, u64>,
    /// Average delivery time
    pub avg_delivery_time_ms: f64,
    /// Delivery success rate
    pub delivery_success_rate: f64,
    /// Total deliveries
    pub total_deliveries: u64,
    /// Failed deliveries
    pub failed_deliveries: u64,
}

/// Delivery statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeliveryStats {
    /// Total deliveries
    pub total_deliveries: u64,
    /// Successful deliveries
    pub successful_deliveries: u64,
    /// Failed deliveries
    pub failed_deliveries: u64,
    /// Pending retries
    pub pending_retries: u64,
    /// Average delivery time
    pub avg_delivery_time_ms: f64,
    /// Deliveries by event
    pub deliveries_by_event: HashMap<String, u64>,
    /// Success rate percentage
    pub success_rate: f64,
    /// Average retry count
    pub avg_retry_count: f64,
}
