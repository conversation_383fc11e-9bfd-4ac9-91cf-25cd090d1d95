//! Database models for OCTAVE semantic protection data and AuthTracker business logic

use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
// Note: FromRow will be implemented manually for SQLite compatibility
use std::collections::HashMap;
use uuid::Uuid;
use octave_core::error::{OctaveError, OctaveResult};
use base64::{Engine as _, engine::general_purpose};

/// Stored threat pattern for pattern matching and learning
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoredThreatPattern {
    /// Unique pattern ID
    pub id: String,
    /// Pattern name/identifier
    pub name: String,
    /// Threat type (SQL injection, XSS, etc.)
    pub threat_type: String,
    /// Pattern content/signature
    pub pattern: String,
    /// Pattern confidence score (0.0 - 1.0)
    pub confidence: f64,
    /// Severity level (None, Low, Medium, High, Critical)
    pub severity: String,
    /// Pattern description
    pub description: String,
    /// Pattern metadata as JSON
    pub metadata: String,
    /// Number of times this pattern was matched
    pub match_count: i64,
    /// Number of false positives
    pub false_positive_count: i64,
    /// Pattern effectiveness score
    pub effectiveness: f64,
    /// Pattern is active/enabled
    pub is_active: bool,
    /// Pattern creation timestamp
    pub created_at: DateTime<Utc>,
    /// Pattern last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// Pattern last matched timestamp
    pub last_matched_at: Option<DateTime<Utc>>,
}

impl StoredThreatPattern {
    /// Create a new stored threat pattern
    pub fn new(
        name: String,
        threat_type: String,
        pattern: String,
        confidence: f64,
        severity: String,
        description: String,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            name,
            threat_type,
            pattern,
            confidence,
            severity,
            description,
            metadata: "{}".to_string(),
            match_count: 0,
            false_positive_count: 0,
            effectiveness: 1.0,
            is_active: true,
            created_at: now,
            updated_at: now,
            last_matched_at: None,
        }
    }

    /// Update pattern statistics
    pub fn update_stats(&mut self, was_false_positive: bool) {
        self.match_count += 1;
        if was_false_positive {
            self.false_positive_count += 1;
        }
        
        // Update effectiveness score
        if self.match_count > 0 {
            self.effectiveness = 1.0 - (self.false_positive_count as f64 / self.match_count as f64);
        }
        
        self.last_matched_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Get metadata as HashMap
    pub fn get_metadata(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.metadata).unwrap_or_default()
    }

    /// Set metadata from HashMap
    pub fn set_metadata(&mut self, metadata: HashMap<String, String>) {
        self.metadata = serde_json::to_string(&metadata).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }
}

/// Learning event for adaptive learning system
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoredLearningEvent {
    /// Unique event ID
    pub id: String,
    /// Context fingerprint that triggered this event
    pub context_fingerprint: String,
    /// Detected threat patterns as JSON
    pub threat_patterns: String,
    /// Whether the response was effective
    pub response_effective: bool,
    /// Threat level detected
    pub threat_level: String,
    /// Antibodies that responded as JSON
    pub antibody_responses: String,
    /// Selected archetype (if any)
    pub archetype: Option<String>,
    /// Whether the request was blocked
    pub was_blocked: bool,
    /// User ID associated with the event
    pub user_id: Option<i32>,
    /// Session ID
    pub session_id: Option<String>,
    /// Client IP address
    pub client_ip: Option<String>,
    /// User agent
    pub user_agent: Option<String>,
    /// Request endpoint
    pub endpoint: Option<String>,
    /// Request method
    pub method: Option<String>,
    /// Event metadata as JSON
    pub metadata: String,
    /// Event timestamp
    pub created_at: DateTime<Utc>,
}

impl StoredLearningEvent {
    /// Create a new learning event
    pub fn new(
        context_fingerprint: String,
        threat_patterns: Vec<String>,
        response_effective: bool,
        threat_level: String,
    ) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            context_fingerprint,
            threat_patterns: serde_json::to_string(&threat_patterns).unwrap_or_default(),
            response_effective,
            threat_level,
            antibody_responses: "[]".to_string(),
            archetype: None,
            was_blocked: false,
            user_id: None,
            session_id: None,
            client_ip: None,
            user_agent: None,
            endpoint: None,
            method: None,
            metadata: "{}".to_string(),
            created_at: Utc::now(),
        }
    }

    /// Get threat patterns as vector
    pub fn get_threat_patterns(&self) -> Vec<String> {
        serde_json::from_str(&self.threat_patterns).unwrap_or_default()
    }

    /// Get antibody responses as vector
    pub fn get_antibody_responses(&self) -> Vec<String> {
        serde_json::from_str(&self.antibody_responses).unwrap_or_default()
    }

    /// Get metadata as HashMap
    pub fn get_metadata(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.metadata).unwrap_or_default()
    }
}

/// Metrics data for performance monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoredMetric {
    /// Unique metric ID
    pub id: String,
    /// Metric name/type
    pub name: String,
    /// Metric category (performance, security, compliance, etc.)
    pub category: String,
    /// Metric value
    pub value: f64,
    /// Metric unit (ms, count, percentage, etc.)
    pub unit: String,
    /// Associated labels as JSON
    pub labels: String,
    /// Metric description
    pub description: String,
    /// Metric timestamp
    pub timestamp: DateTime<Utc>,
    /// Metric retention period in days
    pub retention_days: i32,
}

impl StoredMetric {
    /// Create a new metric
    pub fn new(
        name: String,
        category: String,
        value: f64,
        unit: String,
        description: String,
    ) -> Self {
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            name,
            category,
            value,
            unit,
            labels: "{}".to_string(),
            description,
            timestamp: Utc::now(),
            retention_days: 90, // Default 90 days retention
        }
    }

    /// Create a performance metric
    pub fn performance(name: String, value: f64, unit: String) -> Self {
        Self::new(name, "performance".to_string(), value, unit, "Performance metric".to_string())
    }

    /// Create a security metric
    pub fn security(name: String, value: f64, unit: String) -> Self {
        Self::new(name, "security".to_string(), value, unit, "Security metric".to_string())
    }

    /// Create a compliance metric
    pub fn compliance(name: String, value: f64, unit: String) -> Self {
        Self::new(name, "compliance".to_string(), value, unit, "Compliance metric".to_string())
    }

    /// Get labels as HashMap
    pub fn get_labels(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.labels).unwrap_or_default()
    }

    /// Set labels from HashMap
    pub fn set_labels(&mut self, labels: HashMap<String, String>) {
        self.labels = serde_json::to_string(&labels).unwrap_or_else(|_| "{}".to_string());
    }

    /// Add a label
    pub fn add_label(&mut self, key: String, value: String) {
        let mut labels = self.get_labels();
        labels.insert(key, value);
        self.set_labels(labels);
    }
}

/// Query filters for database operations
#[derive(Debug, Clone, Default)]
pub struct QueryFilter {
    /// Limit number of results
    pub limit: Option<u32>,
    /// Offset for pagination
    pub offset: Option<u32>,
    /// Order by field
    pub order_by: Option<String>,
    /// Order direction (ASC/DESC)
    pub order_desc: bool,
    /// Filter by date range
    pub date_from: Option<DateTime<Utc>>,
    pub date_to: Option<DateTime<Utc>>,
    /// Additional filters
    pub filters: HashMap<String, String>,
}

impl QueryFilter {
    /// Create a new query filter
    pub fn new() -> Self {
        Self::default()
    }

    /// Set limit
    pub fn limit(mut self, limit: u32) -> Self {
        self.limit = Some(limit);
        self
    }

    /// Set offset
    pub fn offset(mut self, offset: u32) -> Self {
        self.offset = Some(offset);
        self
    }

    /// Set order by
    pub fn order_by(mut self, field: String, desc: bool) -> Self {
        self.order_by = Some(field);
        self.order_desc = desc;
        self
    }

    /// Set date range
    pub fn date_range(mut self, from: DateTime<Utc>, to: DateTime<Utc>) -> Self {
        self.date_from = Some(from);
        self.date_to = Some(to);
        self
    }

    /// Add filter
    pub fn filter(mut self, key: String, value: String) -> Self {
        self.filters.insert(key, value);
        self
    }
}

// ============================================================================
// AUTHTRACKER BUSINESS MODELS
// ============================================================================

/// User roles in the system
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub enum UserRole {
    /// System administrator with full access
    SystemAdmin,
    /// Practice administrator
    PracticeAdmin,
    /// Practice manager
    PracticeManager,
    /// Healthcare provider (doctor, nurse, etc.)
    Provider,
    /// Administrative staff
    Staff,
    /// Billing specialist
    Billing,
    /// Read-only user
    ReadOnly,
}

impl UserRole {
    /// Get role as string
    pub fn as_str(&self) -> &'static str {
        match self {
            UserRole::SystemAdmin => "system_admin",
            UserRole::PracticeAdmin => "practice_admin",
            UserRole::PracticeManager => "practice_manager",
            UserRole::Provider => "provider",
            UserRole::Staff => "staff",
            UserRole::Billing => "billing",
            UserRole::ReadOnly => "read_only",
        }
    }

    /// Parse role from string
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "system_admin" => Some(UserRole::SystemAdmin),
            "practice_admin" => Some(UserRole::PracticeAdmin),
            "practice_manager" => Some(UserRole::PracticeManager),
            "provider" => Some(UserRole::Provider),
            "staff" => Some(UserRole::Staff),
            "billing" => Some(UserRole::Billing),
            "read_only" => Some(UserRole::ReadOnly),
            _ => None,
        }
    }

    /// Check if role has administrative privileges
    pub fn is_admin(&self) -> bool {
        matches!(self, UserRole::SystemAdmin | UserRole::PracticeAdmin)
    }

    /// Check if role can manage users
    pub fn can_manage_users(&self) -> bool {
        matches!(self, UserRole::SystemAdmin | UserRole::PracticeAdmin | UserRole::PracticeManager)
    }

    /// Check if role can access PHI
    pub fn can_access_phi(&self) -> bool {
        matches!(self, UserRole::SystemAdmin | UserRole::PracticeAdmin | UserRole::Provider | UserRole::Staff)
    }
}

/// Practice status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum PracticeStatus {
    /// Practice is being set up
    Setup,
    /// Practice is active
    Active,
    /// Practice is suspended
    Suspended,
    /// Practice is inactive/closed
    Inactive,
}

impl PracticeStatus {
    /// Get status as string
    pub fn as_str(&self) -> &'static str {
        match self {
            PracticeStatus::Setup => "setup",
            PracticeStatus::Active => "active",
            PracticeStatus::Suspended => "suspended",
            PracticeStatus::Inactive => "inactive",
        }
    }

    /// Parse status from string
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "setup" => Some(PracticeStatus::Setup),
            "active" => Some(PracticeStatus::Active),
            "suspended" => Some(PracticeStatus::Suspended),
            "inactive" => Some(PracticeStatus::Inactive),
            _ => None,
        }
    }
}

/// Practice model representing a healthcare practice/organization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Practice {
    /// Unique practice identifier
    pub id: Uuid,
    /// Practice name
    pub name: String,
    /// Practice type (clinic, hospital, etc.)
    pub practice_type: String,
    /// Tax ID/EIN
    pub tax_id: Option<String>,
    /// NPI (National Provider Identifier)
    pub npi: Option<String>,
    /// Practice status
    pub status: PracticeStatus,
    /// Primary contact email
    pub email: String,
    /// Primary phone number
    pub phone: Option<String>,
    /// Practice address (JSON)
    pub address: String,
    /// Practice settings (JSON)
    pub settings: String,
    /// Practice metadata (JSON)
    pub metadata: String,
    /// Practice creation timestamp
    pub created_at: DateTime<Utc>,
    /// Practice last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// Practice activation timestamp
    pub activated_at: Option<DateTime<Utc>>,
}

impl Practice {
    /// Create a new practice
    pub fn new(name: String, practice_type: String, email: String) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            name,
            practice_type,
            tax_id: None,
            npi: None,
            status: PracticeStatus::Setup,
            email,
            phone: None,
            address: "{}".to_string(),
            settings: "{}".to_string(),
            metadata: "{}".to_string(),
            created_at: now,
            updated_at: now,
            activated_at: None,
        }
    }

    /// Activate the practice
    pub fn activate(&mut self) {
        self.status = PracticeStatus::Active;
        self.activated_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Suspend the practice
    pub fn suspend(&mut self) {
        self.status = PracticeStatus::Suspended;
        self.updated_at = Utc::now();
    }

    /// Get address as HashMap
    pub fn get_address(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.address).unwrap_or_default()
    }

    /// Set address from HashMap
    pub fn set_address(&mut self, address: HashMap<String, String>) {
        self.address = serde_json::to_string(&address).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }

    /// Get settings as HashMap
    pub fn get_settings(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.settings).unwrap_or_default()
    }

    /// Set settings from HashMap
    pub fn set_settings(&mut self, settings: HashMap<String, String>) {
        self.settings = serde_json::to_string(&settings).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }

    /// Get metadata as HashMap
    pub fn get_metadata(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.metadata).unwrap_or_default()
    }

    /// Set metadata from HashMap
    pub fn set_metadata(&mut self, metadata: HashMap<String, String>) {
        self.metadata = serde_json::to_string(&metadata).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }

    /// Check if practice is active
    pub fn is_active(&self) -> bool {
        self.status == PracticeStatus::Active
    }

    /// Check if practice can be used
    pub fn is_usable(&self) -> bool {
        matches!(self.status, PracticeStatus::Active | PracticeStatus::Setup)
    }
}

/// User status
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum UserStatus {
    /// User invitation sent, pending acceptance
    Invited,
    /// User is active
    Active,
    /// User is suspended
    Suspended,
    /// User is inactive/disabled
    Inactive,
}

impl UserStatus {
    /// Get status as string
    pub fn as_str(&self) -> &'static str {
        match self {
            UserStatus::Invited => "invited",
            UserStatus::Active => "active",
            UserStatus::Suspended => "suspended",
            UserStatus::Inactive => "inactive",
        }
    }

    /// Parse status from string
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "invited" => Some(UserStatus::Invited),
            "active" => Some(UserStatus::Active),
            "suspended" => Some(UserStatus::Suspended),
            "inactive" => Some(UserStatus::Inactive),
            _ => None,
        }
    }
}

/// User model representing a system user
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    /// Unique user identifier
    pub id: Uuid,
    /// Practice this user belongs to
    pub practice_id: Uuid,
    /// User's email address (unique)
    pub email: String,
    /// User's first name
    pub first_name: String,
    /// User's last name
    pub last_name: String,
    /// User's role in the system
    pub role: UserRole,
    /// User status
    pub status: UserStatus,
    /// Password hash (bcrypt)
    pub password_hash: Option<String>,
    /// User's phone number
    pub phone: Option<String>,
    /// User's title/position
    pub title: Option<String>,
    /// User's department
    pub department: Option<String>,
    /// User's license number (for providers)
    pub license_number: Option<String>,
    /// User's NPI (for providers)
    pub npi: Option<String>,
    /// User preferences (JSON)
    pub preferences: String,
    /// User metadata (JSON)
    pub metadata: String,
    /// Email verification status
    pub email_verified: bool,
    /// Email verification token
    pub email_verification_token: Option<String>,
    /// Password reset token
    pub password_reset_token: Option<String>,
    /// Password reset token expiry
    pub password_reset_expires: Option<DateTime<Utc>>,
    /// Invitation token
    pub invitation_token: Option<String>,
    /// Invitation token expiry
    pub invitation_expires: Option<DateTime<Utc>>,
    /// Last login timestamp
    pub last_login_at: Option<DateTime<Utc>>,
    /// Last activity timestamp
    pub last_activity_at: Option<DateTime<Utc>>,
    /// User creation timestamp
    pub created_at: DateTime<Utc>,
    /// User last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// User activation timestamp
    pub activated_at: Option<DateTime<Utc>>,
}

impl User {
    /// Create a new user
    pub fn new(
        practice_id: Uuid,
        email: String,
        first_name: String,
        last_name: String,
        role: UserRole,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            practice_id,
            email,
            first_name,
            last_name,
            role,
            status: UserStatus::Invited,
            password_hash: None,
            phone: None,
            title: None,
            department: None,
            license_number: None,
            npi: None,
            preferences: "{}".to_string(),
            metadata: "{}".to_string(),
            email_verified: false,
            email_verification_token: None,
            password_reset_token: None,
            password_reset_expires: None,
            invitation_token: Some(Uuid::new_v4().to_string()),
            invitation_expires: Some(now + chrono::Duration::days(7)), // 7 days to accept invitation
            last_login_at: None,
            last_activity_at: None,
            created_at: now,
            updated_at: now,
            activated_at: None,
        }
    }

    /// Get user's full name
    pub fn full_name(&self) -> String {
        format!("{} {}", self.first_name, self.last_name)
    }

    /// Get user's display name (includes title if available)
    pub fn display_name(&self) -> String {
        if let Some(ref title) = self.title {
            format!("{} {} {}", title, self.first_name, self.last_name)
        } else {
            self.full_name()
        }
    }

    /// Activate user account
    pub fn activate(&mut self, password_hash: String) {
        self.status = UserStatus::Active;
        self.password_hash = Some(password_hash);
        self.email_verified = true;
        self.invitation_token = None;
        self.invitation_expires = None;
        self.activated_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Suspend user account
    pub fn suspend(&mut self) {
        self.status = UserStatus::Suspended;
        self.updated_at = Utc::now();
    }

    /// Update last login
    pub fn update_last_login(&mut self) {
        self.last_login_at = Some(Utc::now());
        self.last_activity_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Update last activity
    pub fn update_last_activity(&mut self) {
        self.last_activity_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Generate password reset token
    pub fn generate_password_reset_token(&mut self) -> String {
        let token = Uuid::new_v4().to_string();
        self.password_reset_token = Some(token.clone());
        self.password_reset_expires = Some(Utc::now() + chrono::Duration::hours(24)); // 24 hours
        self.updated_at = Utc::now();
        token
    }

    /// Clear password reset token
    pub fn clear_password_reset_token(&mut self) {
        self.password_reset_token = None;
        self.password_reset_expires = None;
        self.updated_at = Utc::now();
    }

    /// Check if password reset token is valid
    pub fn is_password_reset_token_valid(&self, token: &str) -> bool {
        if let (Some(ref stored_token), Some(expires)) = (&self.password_reset_token, self.password_reset_expires) {
            stored_token == token && Utc::now() < expires
        } else {
            false
        }
    }

    /// Check if invitation is valid
    pub fn is_invitation_valid(&self) -> bool {
        if let (Some(_), Some(expires)) = (&self.invitation_token, self.invitation_expires) {
            Utc::now() < expires && self.status == UserStatus::Invited
        } else {
            false
        }
    }

    /// Get preferences as HashMap
    pub fn get_preferences(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.preferences).unwrap_or_default()
    }

    /// Set preferences from HashMap
    pub fn set_preferences(&mut self, preferences: HashMap<String, String>) {
        self.preferences = serde_json::to_string(&preferences).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }

    /// Get metadata as HashMap
    pub fn get_metadata(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.metadata).unwrap_or_default()
    }

    /// Set metadata from HashMap
    pub fn set_metadata(&mut self, metadata: HashMap<String, String>) {
        self.metadata = serde_json::to_string(&metadata).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }

    /// Check if user is active
    pub fn is_active(&self) -> bool {
        self.status == UserStatus::Active
    }

    /// Check if user can login
    pub fn can_login(&self) -> bool {
        self.status == UserStatus::Active && self.password_hash.is_some()
    }

    /// Check if user has administrative privileges
    pub fn is_admin(&self) -> bool {
        self.role.is_admin()
    }

    /// Check if user can manage other users
    pub fn can_manage_users(&self) -> bool {
        self.role.can_manage_users()
    }

    /// Check if user can access PHI
    pub fn can_access_phi(&self) -> bool {
        self.role.can_access_phi()
    }
}

/// User activity types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum ActivityType {
    /// User login
    Login,
    /// User logout
    Logout,
    /// Password change
    PasswordChange,
    /// Profile update
    ProfileUpdate,
    /// Practice settings change
    PracticeSettingsChange,
    /// User management action
    UserManagement,
    /// PHI access
    PhiAccess,
    /// Document access
    DocumentAccess,
    /// Prior authorization action
    PriorAuthAction,
    /// System configuration change
    SystemConfig,
    /// Security event
    SecurityEvent,
    /// Security settings change
    SecuritySettingsChange,
    /// Patient management action
    PatientManagement,
    /// Custom activity
    Custom(String),
}

impl ActivityType {
    /// Get activity type as string
    pub fn as_str(&self) -> String {
        match self {
            ActivityType::Login => "login".to_string(),
            ActivityType::Logout => "logout".to_string(),
            ActivityType::PasswordChange => "password_change".to_string(),
            ActivityType::ProfileUpdate => "profile_update".to_string(),
            ActivityType::PracticeSettingsChange => "practice_settings_change".to_string(),
            ActivityType::UserManagement => "user_management".to_string(),
            ActivityType::PhiAccess => "phi_access".to_string(),
            ActivityType::DocumentAccess => "document_access".to_string(),
            ActivityType::PriorAuthAction => "prior_auth_action".to_string(),
            ActivityType::SystemConfig => "system_config".to_string(),
            ActivityType::SecurityEvent => "security_event".to_string(),
            ActivityType::SecuritySettingsChange => "security_settings_change".to_string(),
            ActivityType::PatientManagement => "patient_management".to_string(),
            ActivityType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse activity type from string
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "login" => Some(ActivityType::Login),
            "logout" => Some(ActivityType::Logout),
            "password_change" => Some(ActivityType::PasswordChange),
            "profile_update" => Some(ActivityType::ProfileUpdate),
            "practice_settings_change" => Some(ActivityType::PracticeSettingsChange),
            "user_management" => Some(ActivityType::UserManagement),
            "phi_access" => Some(ActivityType::PhiAccess),
            "document_access" => Some(ActivityType::DocumentAccess),
            "prior_auth_action" => Some(ActivityType::PriorAuthAction),
            "system_config" => Some(ActivityType::SystemConfig),
            "security_event" => Some(ActivityType::SecurityEvent),
            "security_settings_change" => Some(ActivityType::SecuritySettingsChange),
            "patient_management" => Some(ActivityType::PatientManagement),
            s if s.starts_with("custom:") => Some(ActivityType::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Check if activity involves PHI
    pub fn involves_phi(&self) -> bool {
        matches!(self,
            ActivityType::PhiAccess |
            ActivityType::DocumentAccess |
            ActivityType::PriorAuthAction |
            ActivityType::PatientManagement
        )
    }

    /// Check if activity is security-related
    pub fn is_security_related(&self) -> bool {
        matches!(self,
            ActivityType::Login |
            ActivityType::Logout |
            ActivityType::PasswordChange |
            ActivityType::SecurityEvent |
            ActivityType::SecuritySettingsChange
        )
    }
}

/// User activity tracking model
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserActivity {
    /// Unique activity identifier
    pub id: Uuid,
    /// User who performed the activity
    pub user_id: Uuid,
    /// Practice context
    pub practice_id: Uuid,
    /// Activity type
    pub activity_type: ActivityType,
    /// Activity description
    pub description: String,
    /// Resource affected (if any)
    pub resource_type: Option<String>,
    /// Resource ID (if any)
    pub resource_id: Option<String>,
    /// IP address
    pub ip_address: Option<String>,
    /// User agent
    pub user_agent: Option<String>,
    /// Session ID
    pub session_id: Option<String>,
    /// Activity metadata (JSON)
    pub metadata: String,
    /// Whether activity was successful
    pub success: bool,
    /// Error message (if failed)
    pub error_message: Option<String>,
    /// Activity timestamp
    pub created_at: DateTime<Utc>,
}

impl UserActivity {
    /// Create a new user activity
    pub fn new(
        user_id: Uuid,
        practice_id: Uuid,
        activity_type: ActivityType,
        description: String,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            user_id,
            practice_id,
            activity_type,
            description,
            resource_type: None,
            resource_id: None,
            ip_address: None,
            user_agent: None,
            session_id: None,
            metadata: "{}".to_string(),
            success: true,
            error_message: None,
            created_at: Utc::now(),
        }
    }

    /// Create a login activity
    pub fn login(user_id: Uuid, practice_id: Uuid, ip_address: Option<String>) -> Self {
        let mut activity = Self::new(
            user_id,
            practice_id,
            ActivityType::Login,
            "User logged in".to_string(),
        );
        activity.ip_address = ip_address;
        activity
    }

    /// Create a logout activity
    pub fn logout(user_id: Uuid, practice_id: Uuid) -> Self {
        Self::new(
            user_id,
            practice_id,
            ActivityType::Logout,
            "User logged out".to_string(),
        )
    }

    /// Create a PHI access activity
    pub fn phi_access(
        user_id: Uuid,
        practice_id: Uuid,
        resource_type: String,
        resource_id: String,
        description: String,
    ) -> Self {
        let mut activity = Self::new(
            user_id,
            practice_id,
            ActivityType::PhiAccess,
            description,
        );
        activity.resource_type = Some(resource_type);
        activity.resource_id = Some(resource_id);
        activity
    }

    /// Create a failed activity
    pub fn failed(
        user_id: Uuid,
        practice_id: Uuid,
        activity_type: ActivityType,
        description: String,
        error_message: String,
    ) -> Self {
        let mut activity = Self::new(user_id, practice_id, activity_type, description);
        activity.success = false;
        activity.error_message = Some(error_message);
        activity
    }

    /// Set session information
    pub fn with_session_info(
        mut self,
        session_id: Option<String>,
        ip_address: Option<String>,
        user_agent: Option<String>,
    ) -> Self {
        self.session_id = session_id;
        self.ip_address = ip_address;
        self.user_agent = user_agent;
        self
    }

    /// Set resource information
    pub fn with_resource(mut self, resource_type: String, resource_id: String) -> Self {
        self.resource_type = Some(resource_type);
        self.resource_id = Some(resource_id);
        self
    }

    /// Get metadata as HashMap
    pub fn get_metadata(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.metadata).unwrap_or_default()
    }

    /// Set metadata from HashMap
    pub fn set_metadata(&mut self, metadata: HashMap<String, String>) {
        self.metadata = serde_json::to_string(&metadata).unwrap_or_else(|_| "{}".to_string());
    }

    /// Add metadata field
    pub fn add_metadata(&mut self, key: String, value: String) {
        let mut metadata = self.get_metadata();
        metadata.insert(key, value);
        self.set_metadata(metadata);
    }

    /// Check if activity involves PHI
    pub fn involves_phi(&self) -> bool {
        self.activity_type.involves_phi()
    }

    /// Check if activity is security-related
    pub fn is_security_related(&self) -> bool {
        self.activity_type.is_security_related()
    }
}

// ============================================================================
// PATIENT MANAGEMENT MODELS
// ============================================================================

/// Encrypted PHI wrapper for secure storage of protected health information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptedPhi {
    /// Encrypted data
    pub encrypted_data: String,
    /// Encryption metadata
    pub encryption_metadata: String,
    /// PHI type for compliance tracking
    pub phi_type: PhiType,
}

impl EncryptedPhi {
    /// Create new encrypted PHI
    pub fn new(data: String, phi_type: PhiType) -> Self {
        // TODO: Implement actual encryption using octave-healthcare
        // For now, we'll use a placeholder
        Self {
            encrypted_data: format!("ENC:{}", general_purpose::STANDARD.encode(&data)),
            encryption_metadata: "{}".to_string(),
            phi_type,
        }
    }

    /// Decrypt PHI data (requires proper authorization)
    pub fn decrypt(&self) -> OctaveResult<String> {
        // TODO: Implement actual decryption using octave-healthcare
        // For now, we'll use a placeholder
        if self.encrypted_data.starts_with("ENC:") {
            let encoded = &self.encrypted_data[4..];
            match general_purpose::STANDARD.decode(encoded) {
                Ok(decoded) => Ok(String::from_utf8_lossy(&decoded).to_string()),
                Err(_) => Err(OctaveError::validation("encrypted_data", "Invalid encrypted data")),
            }
        } else {
            Err(OctaveError::validation("encrypted_data", "Invalid encrypted data format"))
        }
    }

    /// Get masked version for display
    pub fn masked(&self) -> String {
        match self.phi_type {
            PhiType::Name => "***".to_string(),
            PhiType::Ssn => "***-**-****".to_string(),
            PhiType::PhoneNumber => "(***) ***-****".to_string(),
            PhiType::EmailAddress => "***@***.***".to_string(),
            PhiType::Address => "*** *** ***".to_string(),
            PhiType::Date => "**/**/****".to_string(),
            _ => "***".to_string(),
        }
    }
}

/// PHI type enumeration for compliance tracking
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq, Hash)]
pub enum PhiType {
    Name,
    Ssn,
    PhoneNumber,
    EmailAddress,
    Address,
    Date,
    Mrn,
    ZipCode,
    Other(String),
}

/// Patient gender enumeration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum Gender {
    Male,
    Female,
    Other,
    Unknown,
    PreferNotToSay,
}

/// Patient status enumeration
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq, Eq)]
pub enum PatientStatus {
    Active,
    Inactive,
    Deceased,
    Merged,
    Duplicate,
}

/// Emergency contact information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmergencyContact {
    pub name: EncryptedPhi,
    pub relationship: String,
    pub phone: EncryptedPhi,
    pub email: Option<EncryptedPhi>,
}

/// Patient address with PHI protection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatientAddress {
    pub street: EncryptedPhi,
    pub city: EncryptedPhi,
    pub state: String,
    pub zip_code: EncryptedPhi,
    pub country: String,
}

/// Insurance information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InsuranceInfo {
    pub id: Uuid,
    pub provider_name: String,
    pub policy_number: EncryptedPhi,
    pub group_number: Option<String>,
    pub subscriber_id: EncryptedPhi,
    pub subscriber_name: Option<EncryptedPhi>,
    pub relationship_to_subscriber: String,
    pub effective_date: Option<DateTime<Utc>>,
    pub expiration_date: Option<DateTime<Utc>>,
    pub is_primary: bool,
    pub copay_amount: Option<f64>,
    pub deductible_amount: Option<f64>,
    pub metadata: String, // JSON
}

/// Patient model with comprehensive PHI protection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Patient {
    /// Unique patient identifier
    pub id: Uuid,
    /// Practice this patient belongs to
    pub practice_id: Uuid,
    /// Medical Record Number (unique within practice)
    pub mrn: String,
    /// Patient's first name (PHI-protected)
    pub first_name: EncryptedPhi,
    /// Patient's last name (PHI-protected)
    pub last_name: EncryptedPhi,
    /// Patient's middle name (PHI-protected)
    pub middle_name: Option<EncryptedPhi>,
    /// Date of birth (PHI-protected)
    pub date_of_birth: EncryptedPhi,
    /// Patient gender
    pub gender: Gender,
    /// Social Security Number (PHI-protected)
    pub ssn: Option<EncryptedPhi>,
    /// Primary phone number (PHI-protected)
    pub phone: Option<EncryptedPhi>,
    /// Email address (PHI-protected)
    pub email: Option<EncryptedPhi>,
    /// Patient address (PHI-protected)
    pub address: Option<PatientAddress>,
    /// Emergency contact information
    pub emergency_contact: Option<EmergencyContact>,
    /// Insurance information
    pub insurance_info: Vec<InsuranceInfo>,
    /// Patient status
    pub status: PatientStatus,
    /// Patient preferences and metadata (JSON)
    pub metadata: String,
    /// Patient creation timestamp
    pub created_at: DateTime<Utc>,
    /// Patient last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// Last time patient data was accessed
    pub last_accessed_at: Option<DateTime<Utc>>,
    /// Last time patient visited practice
    pub last_visit_at: Option<DateTime<Utc>>,
}

impl Patient {
    /// Create a new patient
    pub fn new(
        practice_id: Uuid,
        mrn: String,
        first_name: String,
        last_name: String,
        date_of_birth: String,
        gender: Gender,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            practice_id,
            mrn,
            first_name: EncryptedPhi::new(first_name, PhiType::Name),
            last_name: EncryptedPhi::new(last_name, PhiType::Name),
            middle_name: None,
            date_of_birth: EncryptedPhi::new(date_of_birth, PhiType::Date),
            gender,
            ssn: None,
            phone: None,
            email: None,
            address: None,
            emergency_contact: None,
            insurance_info: Vec::new(),
            status: PatientStatus::Active,
            metadata: "{}".to_string(),
            created_at: now,
            updated_at: now,
            last_accessed_at: None,
            last_visit_at: None,
        }
    }

    /// Get patient's full name (decrypted)
    pub fn full_name(&self) -> OctaveResult<String> {
        let first = self.first_name.decrypt()?;
        let last = self.last_name.decrypt()?;

        if let Some(ref middle) = self.middle_name {
            let middle_name = middle.decrypt()?;
            Ok(format!("{} {} {}", first, middle_name, last))
        } else {
            Ok(format!("{} {}", first, last))
        }
    }

    /// Get patient's masked full name for display
    pub fn masked_full_name(&self) -> String {
        let first = self.first_name.masked();
        let last = self.last_name.masked();

        if let Some(ref middle) = self.middle_name {
            let middle_name = middle.masked();
            format!("{} {} {}", first, middle_name, last)
        } else {
            format!("{} {}", first, last)
        }
    }

    /// Update last accessed timestamp
    pub fn update_last_accessed(&mut self) {
        self.last_accessed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Add insurance information
    pub fn add_insurance(&mut self, insurance: InsuranceInfo) {
        // If this is primary insurance, make others non-primary
        if insurance.is_primary {
            for existing in &mut self.insurance_info {
                existing.is_primary = false;
            }
        }
        self.insurance_info.push(insurance);
        self.updated_at = Utc::now();
    }

    /// Get primary insurance
    pub fn primary_insurance(&self) -> Option<&InsuranceInfo> {
        self.insurance_info.iter().find(|ins| ins.is_primary)
    }

    /// Update patient status
    pub fn update_status(&mut self, status: PatientStatus) {
        self.status = status;
        self.updated_at = Utc::now();
    }

    /// Check if patient is active
    pub fn is_active(&self) -> bool {
        matches!(self.status, PatientStatus::Active)
    }

    /// Get metadata as HashMap
    pub fn get_metadata(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.metadata).unwrap_or_default()
    }

    /// Set metadata from HashMap
    pub fn set_metadata(&mut self, metadata: HashMap<String, String>) {
        self.metadata = serde_json::to_string(&metadata).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }

    /// Calculate patient age
    pub fn age(&self) -> OctaveResult<u32> {
        let dob_str = self.date_of_birth.decrypt()?;
        // Parse date string (assuming YYYY-MM-DD format)
        let dob = chrono::NaiveDate::parse_from_str(&dob_str, "%Y-%m-%d")
            .map_err(|_| OctaveError::validation("date_of_birth", "Invalid date format"))?;

        let today = Utc::now().date_naive();
        let age = today.years_since(dob).unwrap_or(0);
        Ok(age)
    }

    /// Generate patient summary for display
    pub fn summary(&self) -> PatientSummary {
        PatientSummary {
            id: self.id,
            practice_id: self.practice_id,
            mrn: self.mrn.clone(),
            masked_name: self.masked_full_name(),
            gender: self.gender.clone(),
            status: self.status.clone(),
            has_insurance: !self.insurance_info.is_empty(),
            last_visit: self.last_visit_at,
            created_at: self.created_at,
        }
    }
}

/// Patient summary for display without PHI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PatientSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub mrn: String,
    pub masked_name: String,
    pub gender: Gender,
    pub status: PatientStatus,
    pub has_insurance: bool,
    pub last_visit: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
}

// ============================================================================
// PRIOR AUTHORIZATION MODELS
// ============================================================================

/// Prior authorization status enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum PriorAuthStatus {
    /// Request submitted, pending initial review
    Pending,
    /// Under medical review
    UnderReview,
    /// Additional information required from provider
    InformationRequired,
    /// Authorization approved
    Approved,
    /// Authorization denied
    Denied,
    /// Authorization has expired
    Expired,
    /// Request cancelled by submitter
    Cancelled,
}

impl PriorAuthStatus {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            PriorAuthStatus::Pending => "pending",
            PriorAuthStatus::UnderReview => "under_review",
            PriorAuthStatus::InformationRequired => "information_required",
            PriorAuthStatus::Approved => "approved",
            PriorAuthStatus::Denied => "denied",
            PriorAuthStatus::Expired => "expired",
            PriorAuthStatus::Cancelled => "cancelled",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "pending" => Some(PriorAuthStatus::Pending),
            "under_review" => Some(PriorAuthStatus::UnderReview),
            "information_required" => Some(PriorAuthStatus::InformationRequired),
            "approved" => Some(PriorAuthStatus::Approved),
            "denied" => Some(PriorAuthStatus::Denied),
            "expired" => Some(PriorAuthStatus::Expired),
            "cancelled" => Some(PriorAuthStatus::Cancelled),
            _ => None,
        }
    }

    /// Check if status is final (no further transitions expected)
    pub fn is_final(&self) -> bool {
        matches!(self,
            PriorAuthStatus::Approved |
            PriorAuthStatus::Denied |
            PriorAuthStatus::Expired |
            PriorAuthStatus::Cancelled
        )
    }

    /// Check if status allows updates
    pub fn allows_updates(&self) -> bool {
        matches!(self,
            PriorAuthStatus::Pending |
            PriorAuthStatus::UnderReview |
            PriorAuthStatus::InformationRequired
        )
    }

    /// Get valid next statuses
    pub fn valid_transitions(&self) -> Vec<PriorAuthStatus> {
        match self {
            PriorAuthStatus::Pending => vec![
                PriorAuthStatus::UnderReview,
                PriorAuthStatus::InformationRequired,
                PriorAuthStatus::Approved,
                PriorAuthStatus::Denied,
                PriorAuthStatus::Cancelled,
            ],
            PriorAuthStatus::UnderReview => vec![
                PriorAuthStatus::InformationRequired,
                PriorAuthStatus::Approved,
                PriorAuthStatus::Denied,
                PriorAuthStatus::Cancelled,
            ],
            PriorAuthStatus::InformationRequired => vec![
                PriorAuthStatus::UnderReview,
                PriorAuthStatus::Approved,
                PriorAuthStatus::Denied,
                PriorAuthStatus::Cancelled,
            ],
            PriorAuthStatus::Approved => vec![
                PriorAuthStatus::Expired,
                PriorAuthStatus::Cancelled,
            ],
            _ => vec![], // Final statuses have no valid transitions
        }
    }
}

/// Prior authorization priority enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum PriorAuthPriority {
    /// Standard processing (5-7 business days)
    Normal,
    /// Expedited processing (2-3 business days)
    Urgent,
    /// Emergency processing (same day)
    Stat,
}

impl PriorAuthPriority {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            PriorAuthPriority::Normal => "normal",
            PriorAuthPriority::Urgent => "urgent",
            PriorAuthPriority::Stat => "stat",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "normal" => Some(PriorAuthPriority::Normal),
            "urgent" => Some(PriorAuthPriority::Urgent),
            "stat" => Some(PriorAuthPriority::Stat),
            _ => None,
        }
    }

    /// Get processing SLA in business days
    pub fn sla_days(&self) -> u32 {
        match self {
            PriorAuthPriority::Normal => 7,
            PriorAuthPriority::Urgent => 3,
            PriorAuthPriority::Stat => 1,
        }
    }

    /// Get priority score for sorting (higher = more urgent)
    pub fn priority_score(&self) -> u32 {
        match self {
            PriorAuthPriority::Stat => 100,
            PriorAuthPriority::Urgent => 50,
            PriorAuthPriority::Normal => 10,
        }
    }
}

/// Urgency level for medical procedures
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum UrgencyLevel {
    /// Routine procedure
    Routine,
    /// Urgent but not emergency
    Urgent,
    /// Emergency procedure
    Emergency,
}

/// Compliance result for prior authorization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceResult {
    pub rule_name: String,
    pub passed: bool,
    pub message: String,
    pub severity: ComplianceSeverity,
}

/// Compliance severity levels
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ComplianceSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

impl ComplianceSeverity {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            ComplianceSeverity::Info => "info",
            ComplianceSeverity::Warning => "warning",
            ComplianceSeverity::Error => "error",
            ComplianceSeverity::Critical => "critical",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "info" => Some(ComplianceSeverity::Info),
            "warning" => Some(ComplianceSeverity::Warning),
            "error" => Some(ComplianceSeverity::Error),
            "critical" => Some(ComplianceSeverity::Critical),
            _ => None,
        }
    }

    /// Get numeric severity level
    pub fn level(&self) -> u8 {
        match self {
            ComplianceSeverity::Info => 1,
            ComplianceSeverity::Warning => 2,
            ComplianceSeverity::Error => 3,
            ComplianceSeverity::Critical => 4,
        }
    }

    /// Create from numeric level
    pub fn from_level(level: u8) -> Self {
        match level {
            1 => ComplianceSeverity::Info,
            2 => ComplianceSeverity::Warning,
            3 => ComplianceSeverity::Error,
            _ => ComplianceSeverity::Critical,
        }
    }
}

/// Prior authorization model with comprehensive workflow support
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriorAuth {
    /// Unique prior authorization identifier
    pub id: Uuid,
    /// Practice this prior auth belongs to
    pub practice_id: Uuid,
    /// Patient this prior auth is for
    pub patient_id: Uuid,
    /// Human-readable tracking identifier
    pub tracking_id: String,
    /// Procedure code (CPT, HCPCS, etc.)
    pub procedure_code: String,
    /// Human-readable procedure description
    pub procedure_description: Option<String>,
    /// Diagnosis codes (ICD-10)
    pub diagnosis_codes: Vec<String>,
    /// Requesting provider information (PHI-protected)
    pub requesting_provider: EncryptedPhi,
    /// Insurance company name
    pub insurance_company: String,
    /// Current authorization status
    pub status: PriorAuthStatus,
    /// Processing priority
    pub priority: PriorAuthPriority,
    /// Medical urgency level
    pub urgency_level: UrgencyLevel,
    /// Clinical justification for the procedure
    pub clinical_justification: String,
    /// Supporting document IDs
    pub supporting_documents: Vec<Uuid>,
    /// Expected approval date
    pub expected_approval_date: Option<DateTime<Utc>>,
    /// Actual approval date
    pub actual_approval_date: Option<DateTime<Utc>>,
    /// Authorization expiration date
    pub expiration_date: Option<DateTime<Utc>>,
    /// Insurance approval number
    pub approval_number: Option<String>,
    /// Reason for denial (if denied)
    pub denial_reason: Option<String>,
    /// Medical necessity score (0.0 - 100.0)
    pub medical_necessity_score: Option<f64>,
    /// Compliance check results
    pub compliance_results: Vec<ComplianceResult>,
    /// Associated PhilHealth claim ID
    pub philhealth_claim_id: Option<String>,
    /// Additional metadata (JSON)
    pub metadata: String,
    /// Creation timestamp
    pub created_at: DateTime<Utc>,
    /// Last update timestamp
    pub updated_at: DateTime<Utc>,
    /// User who submitted the request
    pub submitted_by: Uuid,
    /// User who last updated the request
    pub last_updated_by: Uuid,
    /// Notes and comments
    pub notes: Option<String>,
}

impl PriorAuth {
    /// Create a new prior authorization request
    pub fn new(
        practice_id: Uuid,
        patient_id: Uuid,
        procedure_code: String,
        requesting_provider: String,
        insurance_company: String,
        priority: PriorAuthPriority,
        submitted_by: Uuid,
    ) -> Self {
        let now = Utc::now();
        let tracking_id = Self::generate_tracking_id();

        Self {
            id: Uuid::new_v4(),
            practice_id,
            patient_id,
            tracking_id,
            procedure_code,
            procedure_description: None,
            diagnosis_codes: Vec::new(),
            requesting_provider: EncryptedPhi::new(requesting_provider, PhiType::Name),
            insurance_company,
            status: PriorAuthStatus::Pending,
            priority,
            urgency_level: UrgencyLevel::Routine,
            clinical_justification: String::new(),
            supporting_documents: Vec::new(),
            expected_approval_date: None,
            actual_approval_date: None,
            expiration_date: None,
            approval_number: None,
            denial_reason: None,
            medical_necessity_score: None,
            compliance_results: Vec::new(),
            philhealth_claim_id: None,
            metadata: "{}".to_string(),
            created_at: now,
            updated_at: now,
            submitted_by,
            last_updated_by: submitted_by,
            notes: None,
        }
    }

    /// Generate a unique tracking ID
    pub fn generate_tracking_id() -> String {
        let timestamp = Utc::now().timestamp();
        let random_suffix: u32 = rand::random();
        format!("PA{}{:04}", timestamp, random_suffix % 10000)
    }

    /// Update the status with validation
    pub fn update_status(&mut self, new_status: PriorAuthStatus, updated_by: Uuid) -> OctaveResult<()> {
        let valid_transitions = self.status.valid_transitions();

        if !valid_transitions.contains(&new_status) {
            return Err(OctaveError::validation(
                "status",
                &format!("Invalid status transition from {:?} to {:?}", self.status, new_status)
            ));
        }

        self.status = new_status;
        self.last_updated_by = updated_by;
        self.updated_at = Utc::now();

        // Set approval date if approved
        if self.status == PriorAuthStatus::Approved && self.actual_approval_date.is_none() {
            self.actual_approval_date = Some(Utc::now());

            // Set expiration date (default 1 year from approval)
            if self.expiration_date.is_none() {
                self.expiration_date = Some(Utc::now() + chrono::Duration::days(365));
            }
        }

        Ok(())
    }

    /// Check if the authorization is expired
    pub fn is_expired(&self) -> bool {
        if let Some(expiration_date) = self.expiration_date {
            Utc::now() > expiration_date
        } else {
            false
        }
    }

    /// Check if the authorization is active (approved and not expired)
    pub fn is_active(&self) -> bool {
        self.status == PriorAuthStatus::Approved && !self.is_expired()
    }

    /// Get days until expiration (if applicable)
    pub fn days_until_expiration(&self) -> Option<i64> {
        if let Some(expiration_date) = self.expiration_date {
            let days = (expiration_date - Utc::now()).num_days();
            Some(days.max(0))
        } else {
            None
        }
    }

    /// Calculate processing time in business days
    pub fn processing_time_days(&self) -> Option<i64> {
        if let Some(approval_date) = self.actual_approval_date {
            Some((approval_date - self.created_at).num_days())
        } else {
            None
        }
    }

    /// Check if SLA is met
    pub fn is_sla_met(&self) -> bool {
        let sla_days = self.priority.sla_days() as i64;
        let elapsed_days = (Utc::now() - self.created_at).num_days();

        if self.status.is_final() {
            // If completed, check if it was done within SLA
            if let Some(processing_days) = self.processing_time_days() {
                processing_days <= sla_days
            } else {
                elapsed_days <= sla_days
            }
        } else {
            // If still in progress, check if we're within SLA
            elapsed_days <= sla_days
        }
    }

    /// Add a compliance result
    pub fn add_compliance_result(&mut self, result: ComplianceResult) {
        self.compliance_results.push(result);
        self.updated_at = Utc::now();
    }

    /// Get metadata as HashMap
    pub fn get_metadata(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.metadata).unwrap_or_default()
    }

    /// Set metadata from HashMap
    pub fn set_metadata(&mut self, metadata: HashMap<String, String>) {
        self.metadata = serde_json::to_string(&metadata).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }

    /// Generate summary for display
    pub fn summary(&self) -> PriorAuthSummary {
        PriorAuthSummary {
            id: self.id,
            practice_id: self.practice_id,
            patient_id: self.patient_id,
            tracking_id: self.tracking_id.clone(),
            procedure_code: self.procedure_code.clone(),
            procedure_description: self.procedure_description.clone(),
            insurance_company: self.insurance_company.clone(),
            status: self.status.clone(),
            priority: self.priority.clone(),
            urgency_level: self.urgency_level.clone(),
            expected_approval_date: self.expected_approval_date,
            actual_approval_date: self.actual_approval_date,
            expiration_date: self.expiration_date,
            is_expired: self.is_expired(),
            is_active: self.is_active(),
            days_until_expiration: self.days_until_expiration(),
            is_sla_met: self.is_sla_met(),
            created_at: self.created_at,
            updated_at: self.updated_at,
        }
    }
}

/// Prior authorization summary for display
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PriorAuthSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub patient_id: Uuid,
    pub tracking_id: String,
    pub procedure_code: String,
    pub procedure_description: Option<String>,
    pub insurance_company: String,
    pub status: PriorAuthStatus,
    pub priority: PriorAuthPriority,
    pub urgency_level: UrgencyLevel,
    pub expected_approval_date: Option<DateTime<Utc>>,
    pub actual_approval_date: Option<DateTime<Utc>>,
    pub expiration_date: Option<DateTime<Utc>>,
    pub is_expired: bool,
    pub is_active: bool,
    pub days_until_expiration: Option<i64>,
    pub is_sla_met: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// ============================================================================
// DOCUMENT MANAGEMENT MODELS
// ============================================================================

/// Document type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DocumentType {
    /// Medical records and clinical documents
    MedicalRecord,
    /// Insurance-related documents
    Insurance,
    /// Prior authorization supporting documents
    PriorAuthSupport,
    /// Lab results and test reports
    LabResult,
    /// Imaging studies (X-ray, MRI, etc.)
    ImagingStudy,
    /// Prescription and medication documents
    Prescription,
    /// Consent forms and legal documents
    ConsentForm,
    /// Administrative documents
    Administrative,
    /// Billing and financial documents
    Billing,
    /// Other document types
    Other(String),
}

impl DocumentType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            DocumentType::MedicalRecord => "medical_record".to_string(),
            DocumentType::Insurance => "insurance".to_string(),
            DocumentType::PriorAuthSupport => "prior_auth_support".to_string(),
            DocumentType::LabResult => "lab_result".to_string(),
            DocumentType::ImagingStudy => "imaging_study".to_string(),
            DocumentType::Prescription => "prescription".to_string(),
            DocumentType::ConsentForm => "consent_form".to_string(),
            DocumentType::Administrative => "administrative".to_string(),
            DocumentType::Billing => "billing".to_string(),
            DocumentType::Other(ref s) => format!("other:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "medical_record" => Some(DocumentType::MedicalRecord),
            "insurance" => Some(DocumentType::Insurance),
            "prior_auth_support" => Some(DocumentType::PriorAuthSupport),
            "lab_result" => Some(DocumentType::LabResult),
            "imaging_study" => Some(DocumentType::ImagingStudy),
            "prescription" => Some(DocumentType::Prescription),
            "consent_form" => Some(DocumentType::ConsentForm),
            "administrative" => Some(DocumentType::Administrative),
            "billing" => Some(DocumentType::Billing),
            s if s.starts_with("other:") => Some(DocumentType::Other(s[6..].to_string())),
            _ => None,
        }
    }

    /// Check if document type contains PHI
    pub fn contains_phi(&self) -> bool {
        matches!(self,
            DocumentType::MedicalRecord |
            DocumentType::LabResult |
            DocumentType::ImagingStudy |
            DocumentType::Prescription |
            DocumentType::ConsentForm
        )
    }
}

/// Document classification for security and access control
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DocumentClassification {
    /// Public documents (no PHI)
    Public,
    /// Internal documents (practice-specific)
    Internal,
    /// Confidential documents (contains PHI)
    Confidential,
    /// Restricted documents (highly sensitive PHI)
    Restricted,
}

impl DocumentClassification {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            DocumentClassification::Public => "public",
            DocumentClassification::Internal => "internal",
            DocumentClassification::Confidential => "confidential",
            DocumentClassification::Restricted => "restricted",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "public" => Some(DocumentClassification::Public),
            "internal" => Some(DocumentClassification::Internal),
            "confidential" => Some(DocumentClassification::Confidential),
            "restricted" => Some(DocumentClassification::Restricted),
            _ => None,
        }
    }

    /// Get minimum required role for access
    pub fn minimum_role(&self) -> UserRole {
        match self {
            DocumentClassification::Public => UserRole::ReadOnly,
            DocumentClassification::Internal => UserRole::Staff,
            DocumentClassification::Confidential => UserRole::Staff,
            DocumentClassification::Restricted => UserRole::PracticeManager,
        }
    }
}

/// Document retention policy
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetentionPolicy {
    /// Retention period in years
    pub retention_years: u32,
    /// Auto-delete after retention period
    pub auto_delete: bool,
    /// Legal hold (prevents deletion)
    pub legal_hold: bool,
    /// Retention reason/justification
    pub reason: String,
}

impl Default for RetentionPolicy {
    fn default() -> Self {
        Self {
            retention_years: 7, // Default HIPAA retention period
            auto_delete: false,
            legal_hold: false,
            reason: "HIPAA compliance".to_string(),
        }
    }
}

/// Document model with comprehensive security and compliance features
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Document {
    /// Unique document identifier
    pub id: Uuid,
    /// Practice this document belongs to
    pub practice_id: Uuid,
    /// Associated prior authorization (if applicable)
    pub prior_auth_id: Option<Uuid>,
    /// Associated patient (if applicable)
    pub patient_id: Option<Uuid>,
    /// Original file name (PHI-protected)
    pub file_name: EncryptedPhi,
    /// MIME type of the file
    pub file_type: String,
    /// File size in bytes
    pub file_size: u64,
    /// Storage path/key in object storage
    pub storage_path: String,
    /// Encryption key identifier
    pub encryption_key_id: String,
    /// File checksum for integrity verification
    pub checksum: String,
    /// Document version number
    pub version: u32,
    /// Whether this is the current version
    pub is_current: bool,
    /// Document type classification
    pub document_type: DocumentType,
    /// Security classification
    pub classification: DocumentClassification,
    /// Retention policy for this document
    pub retention_policy: RetentionPolicy,
    /// Additional metadata (JSON)
    pub metadata: String,
    /// User who uploaded the document
    pub uploaded_by: Uuid,
    /// Document creation timestamp
    pub created_at: DateTime<Utc>,
    /// Document last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// Last access timestamp
    pub accessed_at: Option<DateTime<Utc>>,
    /// Document expiration timestamp (if applicable)
    pub expires_at: Option<DateTime<Utc>>,
    /// Virus scan status
    pub virus_scan_status: VirusScanStatus,
    /// Virus scan timestamp
    pub virus_scan_at: Option<DateTime<Utc>>,
    /// Document tags for organization
    pub tags: Vec<String>,
}

/// Virus scan status enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum VirusScanStatus {
    /// Scan pending
    Pending,
    /// Scan in progress
    Scanning,
    /// File is clean
    Clean,
    /// Virus/malware detected
    Infected,
    /// Scan failed
    ScanFailed,
    /// Scan skipped (trusted source)
    Skipped,
}

impl VirusScanStatus {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            VirusScanStatus::Pending => "pending",
            VirusScanStatus::Scanning => "scanning",
            VirusScanStatus::Clean => "clean",
            VirusScanStatus::Infected => "infected",
            VirusScanStatus::ScanFailed => "scan_failed",
            VirusScanStatus::Skipped => "skipped",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "pending" => Some(VirusScanStatus::Pending),
            "scanning" => Some(VirusScanStatus::Scanning),
            "clean" => Some(VirusScanStatus::Clean),
            "infected" => Some(VirusScanStatus::Infected),
            "scan_failed" => Some(VirusScanStatus::ScanFailed),
            "skipped" => Some(VirusScanStatus::Skipped),
            _ => None,
        }
    }

    /// Check if document is safe to access
    pub fn is_safe(&self) -> bool {
        matches!(self, VirusScanStatus::Clean | VirusScanStatus::Skipped)
    }
}

impl Document {
    /// Create a new document
    pub fn new(
        practice_id: Uuid,
        file_name: String,
        file_type: String,
        file_size: u64,
        storage_path: String,
        document_type: DocumentType,
        uploaded_by: Uuid,
    ) -> Self {
        let now = Utc::now();
        let classification = if document_type.contains_phi() {
            DocumentClassification::Confidential
        } else {
            DocumentClassification::Internal
        };

        Self {
            id: Uuid::new_v4(),
            practice_id,
            prior_auth_id: None,
            patient_id: None,
            file_name: EncryptedPhi::new(file_name, PhiType::Other("filename".to_string())),
            file_type,
            file_size,
            storage_path,
            encryption_key_id: Uuid::new_v4().to_string(),
            checksum: String::new(), // Will be set after upload
            version: 1,
            is_current: true,
            document_type,
            classification,
            retention_policy: RetentionPolicy::default(),
            metadata: "{}".to_string(),
            uploaded_by,
            created_at: now,
            updated_at: now,
            accessed_at: None,
            expires_at: None,
            virus_scan_status: VirusScanStatus::Pending,
            virus_scan_at: None,
            tags: Vec::new(),
        }
    }

    /// Update last accessed timestamp
    pub fn update_last_accessed(&mut self) {
        self.accessed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Check if document is expired
    pub fn is_expired(&self) -> bool {
        if let Some(expires_at) = self.expires_at {
            Utc::now() > expires_at
        } else {
            false
        }
    }

    /// Check if document should be retained
    pub fn should_be_retained(&self) -> bool {
        if self.retention_policy.legal_hold {
            return true;
        }

        let retention_end = self.created_at + chrono::Duration::days(
            (self.retention_policy.retention_years * 365) as i64
        );

        Utc::now() < retention_end
    }

    /// Check if document is safe to access
    pub fn is_safe_to_access(&self) -> bool {
        self.virus_scan_status.is_safe() && !self.is_expired()
    }

    /// Get file extension from file name
    pub fn file_extension(&self) -> OctaveResult<String> {
        let file_name = self.file_name.decrypt()?;
        if let Some(ext) = file_name.split('.').last() {
            Ok(ext.to_lowercase())
        } else {
            Ok(String::new())
        }
    }

    /// Check if user has access to this document
    pub fn user_has_access(&self, user_role: &UserRole) -> bool {
        let required_role = self.classification.minimum_role();
        user_role >= &required_role
    }

    /// Create new version of document
    pub fn create_new_version(&self, new_storage_path: String, uploaded_by: Uuid) -> Self {
        let mut new_doc = self.clone();
        new_doc.id = Uuid::new_v4();
        new_doc.storage_path = new_storage_path;
        new_doc.version = self.version + 1;
        new_doc.uploaded_by = uploaded_by;
        new_doc.created_at = Utc::now();
        new_doc.updated_at = Utc::now();
        new_doc.accessed_at = None;
        new_doc.virus_scan_status = VirusScanStatus::Pending;
        new_doc.virus_scan_at = None;
        new_doc.encryption_key_id = Uuid::new_v4().to_string();
        new_doc.checksum = String::new();
        new_doc
    }

    /// Update virus scan status
    pub fn update_virus_scan_status(&mut self, status: VirusScanStatus) {
        self.virus_scan_status = status;
        self.virus_scan_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Add tag to document
    pub fn add_tag(&mut self, tag: String) {
        if !self.tags.contains(&tag) {
            self.tags.push(tag);
            self.updated_at = Utc::now();
        }
    }

    /// Remove tag from document
    pub fn remove_tag(&mut self, tag: &str) {
        self.tags.retain(|t| t != tag);
        self.updated_at = Utc::now();
    }

    /// Get metadata as HashMap
    pub fn get_metadata(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.metadata).unwrap_or_default()
    }

    /// Set metadata from HashMap
    pub fn set_metadata(&mut self, metadata: HashMap<String, String>) {
        self.metadata = serde_json::to_string(&metadata).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }

    /// Generate document summary for display
    pub fn summary(&self) -> DocumentSummary {
        DocumentSummary {
            id: self.id,
            practice_id: self.practice_id,
            prior_auth_id: self.prior_auth_id,
            patient_id: self.patient_id,
            masked_file_name: self.file_name.masked(),
            file_type: self.file_type.clone(),
            file_size: self.file_size,
            version: self.version,
            is_current: self.is_current,
            document_type: self.document_type.clone(),
            classification: self.classification.clone(),
            virus_scan_status: self.virus_scan_status.clone(),
            is_safe: self.is_safe_to_access(),
            is_expired: self.is_expired(),
            should_retain: self.should_be_retained(),
            tags: self.tags.clone(),
            uploaded_by: self.uploaded_by,
            created_at: self.created_at,
            updated_at: self.updated_at,
            accessed_at: self.accessed_at,
        }
    }
}

/// Document summary for display without PHI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub prior_auth_id: Option<Uuid>,
    pub patient_id: Option<Uuid>,
    pub masked_file_name: String,
    pub file_type: String,
    pub file_size: u64,
    pub version: u32,
    pub is_current: bool,
    pub document_type: DocumentType,
    pub classification: DocumentClassification,
    pub virus_scan_status: VirusScanStatus,
    pub is_safe: bool,
    pub is_expired: bool,
    pub should_retain: bool,
    pub tags: Vec<String>,
    pub uploaded_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub accessed_at: Option<DateTime<Utc>>,
}

// ============================================================================
// COMMUNICATION TRACKING MODELS
// ============================================================================

/// Communication type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CommunicationType {
    /// Phone call communication
    PhoneCall,
    /// Email communication
    Email,
    /// Fax communication
    Fax,
    /// Insurance portal message
    PortalMessage,
    /// In-person meeting or consultation
    InPerson,
    /// Text message or SMS
    TextMessage,
    /// Video call or telehealth
    VideoCall,
    /// Letter or postal mail
    Letter,
    /// Internal note or memo
    InternalNote,
    /// Other communication type
    Other(String),
}

impl CommunicationType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            CommunicationType::PhoneCall => "phone_call".to_string(),
            CommunicationType::Email => "email".to_string(),
            CommunicationType::Fax => "fax".to_string(),
            CommunicationType::PortalMessage => "portal_message".to_string(),
            CommunicationType::InPerson => "in_person".to_string(),
            CommunicationType::TextMessage => "text_message".to_string(),
            CommunicationType::VideoCall => "video_call".to_string(),
            CommunicationType::Letter => "letter".to_string(),
            CommunicationType::InternalNote => "internal_note".to_string(),
            CommunicationType::Other(ref s) => format!("other:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "phone_call" => Some(CommunicationType::PhoneCall),
            "email" => Some(CommunicationType::Email),
            "fax" => Some(CommunicationType::Fax),
            "portal_message" => Some(CommunicationType::PortalMessage),
            "in_person" => Some(CommunicationType::InPerson),
            "text_message" => Some(CommunicationType::TextMessage),
            "video_call" => Some(CommunicationType::VideoCall),
            "letter" => Some(CommunicationType::Letter),
            "internal_note" => Some(CommunicationType::InternalNote),
            s if s.starts_with("other:") => Some(CommunicationType::Other(s[6..].to_string())),
            _ => None,
        }
    }

    /// Check if communication type requires immediate response
    pub fn requires_immediate_response(&self) -> bool {
        matches!(self,
            CommunicationType::PhoneCall |
            CommunicationType::VideoCall |
            CommunicationType::InPerson
        )
    }

    /// Check if communication type is electronic
    pub fn is_electronic(&self) -> bool {
        matches!(self,
            CommunicationType::Email |
            CommunicationType::PortalMessage |
            CommunicationType::TextMessage |
            CommunicationType::VideoCall
        )
    }
}

/// Communication direction enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CommunicationDirection {
    /// Incoming communication (received)
    Incoming,
    /// Outgoing communication (sent)
    Outgoing,
    /// Internal communication (within practice)
    Internal,
}

impl CommunicationDirection {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            CommunicationDirection::Incoming => "incoming",
            CommunicationDirection::Outgoing => "outgoing",
            CommunicationDirection::Internal => "internal",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "incoming" => Some(CommunicationDirection::Incoming),
            "outgoing" => Some(CommunicationDirection::Outgoing),
            "internal" => Some(CommunicationDirection::Internal),
            _ => None,
        }
    }
}

/// Communication outcome enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum CommunicationOutcome {
    /// Information was successfully received
    InformationReceived,
    /// Follow-up is required
    FollowUpRequired,
    /// Issue was escalated to supervisor
    Escalated,
    /// Issue was completely resolved
    Resolved,
    /// Request was denied
    Denied,
    /// Request was approved
    Approved,
    /// Waiting for additional information
    Pending,
    /// No response received
    NoResponse,
    /// Left voicemail or message
    LeftMessage,
    /// Scheduled callback or meeting
    ScheduledCallback,
    /// Transferred to another department
    Transferred,
    /// Communication failed (busy, disconnected, etc.)
    Failed,
    /// Other outcome
    Other(String),
}

impl CommunicationOutcome {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            CommunicationOutcome::InformationReceived => "information_received".to_string(),
            CommunicationOutcome::FollowUpRequired => "follow_up_required".to_string(),
            CommunicationOutcome::Escalated => "escalated".to_string(),
            CommunicationOutcome::Resolved => "resolved".to_string(),
            CommunicationOutcome::Denied => "denied".to_string(),
            CommunicationOutcome::Approved => "approved".to_string(),
            CommunicationOutcome::Pending => "pending".to_string(),
            CommunicationOutcome::NoResponse => "no_response".to_string(),
            CommunicationOutcome::LeftMessage => "left_message".to_string(),
            CommunicationOutcome::ScheduledCallback => "scheduled_callback".to_string(),
            CommunicationOutcome::Transferred => "transferred".to_string(),
            CommunicationOutcome::Failed => "failed".to_string(),
            CommunicationOutcome::Other(ref s) => format!("other:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "information_received" => Some(CommunicationOutcome::InformationReceived),
            "follow_up_required" => Some(CommunicationOutcome::FollowUpRequired),
            "escalated" => Some(CommunicationOutcome::Escalated),
            "resolved" => Some(CommunicationOutcome::Resolved),
            "denied" => Some(CommunicationOutcome::Denied),
            "approved" => Some(CommunicationOutcome::Approved),
            "pending" => Some(CommunicationOutcome::Pending),
            "no_response" => Some(CommunicationOutcome::NoResponse),
            "left_message" => Some(CommunicationOutcome::LeftMessage),
            "scheduled_callback" => Some(CommunicationOutcome::ScheduledCallback),
            "transferred" => Some(CommunicationOutcome::Transferred),
            "failed" => Some(CommunicationOutcome::Failed),
            s if s.starts_with("other:") => Some(CommunicationOutcome::Other(s[6..].to_string())),
            _ => None,
        }
    }

    /// Check if outcome requires follow-up action
    pub fn requires_follow_up(&self) -> bool {
        matches!(self,
            CommunicationOutcome::FollowUpRequired |
            CommunicationOutcome::Pending |
            CommunicationOutcome::NoResponse |
            CommunicationOutcome::LeftMessage |
            CommunicationOutcome::ScheduledCallback |
            CommunicationOutcome::Transferred
        )
    }

    /// Check if outcome indicates successful completion
    pub fn is_successful(&self) -> bool {
        matches!(self,
            CommunicationOutcome::InformationReceived |
            CommunicationOutcome::Resolved |
            CommunicationOutcome::Approved
        )
    }
}

/// Communication priority enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize, PartialOrd, Ord)]
pub enum CommunicationPriority {
    /// Low priority - routine communication
    Low,
    /// Normal priority - standard communication
    Normal,
    /// High priority - urgent communication
    High,
    /// Critical priority - emergency communication
    Critical,
}

impl CommunicationPriority {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            CommunicationPriority::Low => "low",
            CommunicationPriority::Normal => "normal",
            CommunicationPriority::High => "high",
            CommunicationPriority::Critical => "critical",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "low" => Some(CommunicationPriority::Low),
            "normal" => Some(CommunicationPriority::Normal),
            "high" => Some(CommunicationPriority::High),
            "critical" => Some(CommunicationPriority::Critical),
            _ => None,
        }
    }

    /// Get recommended response time in hours
    pub fn response_time_hours(&self) -> u32 {
        match self {
            CommunicationPriority::Low => 72,      // 3 days
            CommunicationPriority::Normal => 24,   // 1 day
            CommunicationPriority::High => 4,      // 4 hours
            CommunicationPriority::Critical => 1,  // 1 hour
        }
    }
}

impl Default for CommunicationPriority {
    fn default() -> Self {
        CommunicationPriority::Normal
    }
}

/// Communication participant
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommunicationParticipant {
    /// Participant type
    pub participant_type: ParticipantType,
    /// Participant name (PHI-protected if patient)
    pub name: EncryptedPhi,
    /// Contact information (phone, email, etc.)
    pub contact_info: Option<String>,
    /// Role in the communication
    pub role: String,
    /// Organization or company
    pub organization: Option<String>,
    /// Department within organization
    pub department: Option<String>,
    /// Whether this participant initiated the communication
    pub is_initiator: bool,
}

/// Participant type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ParticipantType {
    /// Internal staff member
    Staff,
    /// Patient
    Patient,
    /// Insurance company representative
    InsuranceRep,
    /// Healthcare provider
    Provider,
    /// External vendor or service
    Vendor,
    /// Government agency
    Government,
    /// Other participant type
    Other(String),
}

impl ParticipantType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            ParticipantType::Staff => "staff".to_string(),
            ParticipantType::Patient => "patient".to_string(),
            ParticipantType::InsuranceRep => "insurance_rep".to_string(),
            ParticipantType::Provider => "provider".to_string(),
            ParticipantType::Vendor => "vendor".to_string(),
            ParticipantType::Government => "government".to_string(),
            ParticipantType::Other(ref s) => format!("other:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "staff" => Some(ParticipantType::Staff),
            "patient" => Some(ParticipantType::Patient),
            "insurance_rep" => Some(ParticipantType::InsuranceRep),
            "provider" => Some(ParticipantType::Provider),
            "vendor" => Some(ParticipantType::Vendor),
            "government" => Some(ParticipantType::Government),
            s if s.starts_with("other:") => Some(ParticipantType::Other(s[6..].to_string())),
            _ => None,
        }
    }

    /// Check if participant type involves PHI
    pub fn involves_phi(&self) -> bool {
        matches!(self, ParticipantType::Patient | ParticipantType::Provider)
    }
}

/// Communication model for tracking all interactions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Communication {
    /// Unique communication identifier
    pub id: Uuid,
    /// Practice this communication belongs to
    pub practice_id: Uuid,
    /// Associated prior authorization (if applicable)
    pub prior_auth_id: Option<Uuid>,
    /// Associated patient (if applicable)
    pub patient_id: Option<Uuid>,
    /// Type of communication
    pub communication_type: CommunicationType,
    /// Direction of communication
    pub direction: CommunicationDirection,
    /// Communication subject/title
    pub subject: String,
    /// Communication content/body
    pub content: String,
    /// List of participants in the communication
    pub participants: Vec<CommunicationParticipant>,
    /// Insurance company name (if applicable)
    pub insurance_company: Option<String>,
    /// Insurance representative ID (if applicable)
    pub insurance_rep_id: Option<Uuid>,
    /// Communication outcome
    pub outcome: CommunicationOutcome,
    /// Whether follow-up is required
    pub follow_up_required: bool,
    /// Scheduled follow-up date
    pub follow_up_date: Option<DateTime<Utc>>,
    /// Communication priority
    pub priority: CommunicationPriority,
    /// Tags for categorization
    pub tags: Vec<String>,
    /// Attached document IDs
    pub attachments: Vec<Uuid>,
    /// Template used (if any)
    pub template_used: Option<Uuid>,
    /// Duration of communication (in minutes)
    pub duration_minutes: Option<u32>,
    /// Cost of communication (if applicable)
    pub cost: Option<f64>,
    /// External reference ID (insurance portal ID, etc.)
    pub external_reference: Option<String>,
    /// Communication metadata (JSON)
    pub metadata: String,
    /// User who created/logged this communication
    pub created_by: Uuid,
    /// Communication creation timestamp
    pub created_at: DateTime<Utc>,
    /// Communication last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// Actual communication timestamp (may differ from created_at)
    pub communication_at: DateTime<Utc>,
}

impl Communication {
    /// Create a new communication
    pub fn new(
        practice_id: Uuid,
        communication_type: CommunicationType,
        direction: CommunicationDirection,
        subject: String,
        content: String,
        created_by: Uuid,
    ) -> Self {
        let now = Utc::now();

        Self {
            id: Uuid::new_v4(),
            practice_id,
            prior_auth_id: None,
            patient_id: None,
            communication_type,
            direction,
            subject,
            content,
            participants: Vec::new(),
            insurance_company: None,
            insurance_rep_id: None,
            outcome: CommunicationOutcome::Pending,
            follow_up_required: false,
            follow_up_date: None,
            priority: CommunicationPriority::default(),
            tags: Vec::new(),
            attachments: Vec::new(),
            template_used: None,
            duration_minutes: None,
            cost: None,
            external_reference: None,
            metadata: "{}".to_string(),
            created_by,
            created_at: now,
            updated_at: now,
            communication_at: now,
        }
    }

    /// Add a participant to the communication
    pub fn add_participant(&mut self, participant: CommunicationParticipant) {
        self.participants.push(participant);
        self.updated_at = Utc::now();
    }

    /// Set communication outcome and update follow-up requirements
    pub fn set_outcome(&mut self, outcome: CommunicationOutcome) {
        self.outcome = outcome.clone();
        self.follow_up_required = outcome.requires_follow_up();
        self.updated_at = Utc::now();

        // Set default follow-up date based on priority if follow-up is required
        if self.follow_up_required && self.follow_up_date.is_none() {
            let hours = self.priority.response_time_hours();
            self.follow_up_date = Some(Utc::now() + chrono::Duration::hours(hours as i64));
        }
    }

    /// Check if communication is overdue for follow-up
    pub fn is_overdue(&self) -> bool {
        if let Some(follow_up_date) = self.follow_up_date {
            Utc::now() > follow_up_date && self.follow_up_required
        } else {
            false
        }
    }

    /// Get time until follow-up is due
    pub fn time_until_follow_up(&self) -> Option<chrono::Duration> {
        if let Some(follow_up_date) = self.follow_up_date {
            if self.follow_up_required {
                Some(follow_up_date - Utc::now())
            } else {
                None
            }
        } else {
            None
        }
    }

    /// Add tag to communication
    pub fn add_tag(&mut self, tag: String) {
        if !self.tags.contains(&tag) {
            self.tags.push(tag);
            self.updated_at = Utc::now();
        }
    }

    /// Remove tag from communication
    pub fn remove_tag(&mut self, tag: &str) {
        self.tags.retain(|t| t != tag);
        self.updated_at = Utc::now();
    }

    /// Get metadata as HashMap
    pub fn get_metadata(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.metadata).unwrap_or_default()
    }

    /// Set metadata from HashMap
    pub fn set_metadata(&mut self, metadata: HashMap<String, String>) {
        self.metadata = serde_json::to_string(&metadata).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }

    /// Check if communication involves PHI
    pub fn involves_phi(&self) -> bool {
        self.participants.iter().any(|p| p.participant_type.involves_phi()) ||
        self.patient_id.is_some()
    }

    /// Generate communication summary for display
    pub fn summary(&self) -> CommunicationSummary {
        CommunicationSummary {
            id: self.id,
            practice_id: self.practice_id,
            prior_auth_id: self.prior_auth_id,
            patient_id: self.patient_id,
            communication_type: self.communication_type.clone(),
            direction: self.direction.clone(),
            subject: self.subject.clone(),
            outcome: self.outcome.clone(),
            priority: self.priority.clone(),
            follow_up_required: self.follow_up_required,
            follow_up_date: self.follow_up_date,
            is_overdue: self.is_overdue(),
            insurance_company: self.insurance_company.clone(),
            tags: self.tags.clone(),
            duration_minutes: self.duration_minutes,
            created_by: self.created_by,
            created_at: self.created_at,
            communication_at: self.communication_at,
        }
    }
}

/// Communication summary for display without PHI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommunicationSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub prior_auth_id: Option<Uuid>,
    pub patient_id: Option<Uuid>,
    pub communication_type: CommunicationType,
    pub direction: CommunicationDirection,
    pub subject: String,
    pub outcome: CommunicationOutcome,
    pub priority: CommunicationPriority,
    pub follow_up_required: bool,
    pub follow_up_date: Option<DateTime<Utc>>,
    pub is_overdue: bool,
    pub insurance_company: Option<String>,
    pub tags: Vec<String>,
    pub duration_minutes: Option<u32>,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub communication_at: DateTime<Utc>,
}

/// Insurance representative model for contact management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InsuranceRepresentative {
    /// Unique representative identifier
    pub id: Uuid,
    /// Practice this representative is associated with
    pub practice_id: Uuid,
    /// Representative name (PHI-protected)
    pub name: EncryptedPhi,
    /// Insurance company name
    pub insurance_company: String,
    /// Department within insurance company
    pub department: Option<String>,
    /// Job title or role
    pub title: Option<String>,
    /// Primary phone number
    pub phone_number: Option<String>,
    /// Email address
    pub email: Option<String>,
    /// Extension number
    pub extension: Option<String>,
    /// Direct line number
    pub direct_line: Option<String>,
    /// Fax number
    pub fax_number: Option<String>,
    /// Preferred communication method
    pub preferred_contact_method: CommunicationType,
    /// Best time to contact (e.g., "9 AM - 5 PM EST")
    pub best_contact_time: Option<String>,
    /// Languages spoken
    pub languages: Vec<String>,
    /// Specialties or areas of expertise
    pub specialties: Vec<String>,
    /// Performance rating (1-5 stars)
    pub performance_rating: Option<f32>,
    /// Average response time in hours
    pub avg_response_time_hours: Option<f32>,
    /// Helpfulness rating (1-5 stars)
    pub helpfulness_rating: Option<f32>,
    /// Notes about this representative
    pub notes: Option<String>,
    /// Whether this representative is currently active
    pub is_active: bool,
    /// Supervisor or escalation contact
    pub supervisor_id: Option<Uuid>,
    /// Tags for categorization
    pub tags: Vec<String>,
    /// Last communication date
    pub last_contact_date: Option<DateTime<Utc>>,
    /// Total number of communications
    pub total_communications: u32,
    /// User who created this record
    pub created_by: Uuid,
    /// Record creation timestamp
    pub created_at: DateTime<Utc>,
    /// Record last updated timestamp
    pub updated_at: DateTime<Utc>,
}

impl InsuranceRepresentative {
    /// Create a new insurance representative
    pub fn new(
        practice_id: Uuid,
        name: String,
        insurance_company: String,
        created_by: Uuid,
    ) -> Self {
        let now = Utc::now();

        Self {
            id: Uuid::new_v4(),
            practice_id,
            name: EncryptedPhi::new(name, PhiType::Other("insurance_rep_name".to_string())),
            insurance_company,
            department: None,
            title: None,
            phone_number: None,
            email: None,
            extension: None,
            direct_line: None,
            fax_number: None,
            preferred_contact_method: CommunicationType::PhoneCall,
            best_contact_time: None,
            languages: vec!["English".to_string()],
            specialties: Vec::new(),
            performance_rating: None,
            avg_response_time_hours: None,
            helpfulness_rating: None,
            notes: None,
            is_active: true,
            supervisor_id: None,
            tags: Vec::new(),
            last_contact_date: None,
            total_communications: 0,
            created_by,
            created_at: now,
            updated_at: now,
        }
    }

    /// Update performance metrics
    pub fn update_performance(&mut self, response_time_hours: f32, helpfulness_rating: f32) {
        // Update average response time
        if let Some(current_avg) = self.avg_response_time_hours {
            self.avg_response_time_hours = Some(
                (current_avg * self.total_communications as f32 + response_time_hours) /
                (self.total_communications + 1) as f32
            );
        } else {
            self.avg_response_time_hours = Some(response_time_hours);
        }

        // Update helpfulness rating
        if let Some(current_rating) = self.helpfulness_rating {
            self.helpfulness_rating = Some(
                (current_rating * self.total_communications as f32 + helpfulness_rating) /
                (self.total_communications + 1) as f32
            );
        } else {
            self.helpfulness_rating = Some(helpfulness_rating);
        }

        self.total_communications += 1;
        self.last_contact_date = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Get overall performance score (0.0 - 5.0)
    pub fn overall_performance_score(&self) -> f32 {
        let mut score = 0.0;
        let mut factors = 0;

        if let Some(rating) = self.performance_rating {
            score += rating;
            factors += 1;
        }

        if let Some(helpfulness) = self.helpfulness_rating {
            score += helpfulness;
            factors += 1;
        }

        // Response time factor (faster = better, max 5.0)
        if let Some(response_time) = self.avg_response_time_hours {
            let response_score = (48.0 - response_time.min(48.0)) / 48.0 * 5.0;
            score += response_score.max(0.0);
            factors += 1;
        }

        if factors > 0 {
            score / factors as f32
        } else {
            0.0
        }
    }

    /// Generate representative summary
    pub fn summary(&self) -> InsuranceRepSummary {
        InsuranceRepSummary {
            id: self.id,
            practice_id: self.practice_id,
            masked_name: self.name.masked(),
            insurance_company: self.insurance_company.clone(),
            department: self.department.clone(),
            title: self.title.clone(),
            preferred_contact_method: self.preferred_contact_method.clone(),
            performance_rating: self.performance_rating,
            helpfulness_rating: self.helpfulness_rating,
            avg_response_time_hours: self.avg_response_time_hours,
            overall_score: self.overall_performance_score(),
            is_active: self.is_active,
            total_communications: self.total_communications,
            last_contact_date: self.last_contact_date,
            tags: self.tags.clone(),
        }
    }
}

/// Insurance representative summary for display
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InsuranceRepSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub masked_name: String,
    pub insurance_company: String,
    pub department: Option<String>,
    pub title: Option<String>,
    pub preferred_contact_method: CommunicationType,
    pub performance_rating: Option<f32>,
    pub helpfulness_rating: Option<f32>,
    pub avg_response_time_hours: Option<f32>,
    pub overall_score: f32,
    pub is_active: bool,
    pub total_communications: u32,
    pub last_contact_date: Option<DateTime<Utc>>,
    pub tags: Vec<String>,
}

/// Communication template for standardized communications
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommunicationTemplate {
    /// Unique template identifier
    pub id: Uuid,
    /// Practice this template belongs to
    pub practice_id: Uuid,
    /// Template name
    pub name: String,
    /// Template description
    pub description: Option<String>,
    /// Template category
    pub category: TemplateCategory,
    /// Communication type this template is for
    pub communication_type: CommunicationType,
    /// Template subject
    pub subject_template: String,
    /// Template content with placeholders
    pub content_template: String,
    /// Available placeholders and their descriptions
    pub placeholders: HashMap<String, String>,
    /// Default tags to apply when using this template
    pub default_tags: Vec<String>,
    /// Default priority for communications using this template
    pub default_priority: CommunicationPriority,
    /// Whether follow-up is typically required
    pub default_follow_up_required: bool,
    /// Default follow-up time in hours
    pub default_follow_up_hours: Option<u32>,
    /// Template usage statistics
    pub usage_count: u32,
    /// Template effectiveness rating (1-5 stars)
    pub effectiveness_rating: Option<f32>,
    /// Whether template is active
    pub is_active: bool,
    /// Whether template is shared across practice
    pub is_shared: bool,
    /// User who created this template
    pub created_by: Uuid,
    /// Template creation timestamp
    pub created_at: DateTime<Utc>,
    /// Template last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// Last time template was used
    pub last_used_at: Option<DateTime<Utc>>,
}

/// Template category enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TemplateCategory {
    /// Prior authorization related
    PriorAuth,
    /// Insurance inquiry
    InsuranceInquiry,
    /// Follow-up communication
    FollowUp,
    /// Escalation communication
    Escalation,
    /// Patient communication
    PatientCommunication,
    /// Internal communication
    Internal,
    /// Billing related
    Billing,
    /// Appointment related
    Appointment,
    /// General inquiry
    General,
    /// Documentation templates
    Documentation,
    /// Administrative templates
    Administrative,
    /// Legal templates
    Legal,
    /// Marketing templates
    Marketing,
    /// Custom category
    Custom(String),
}

impl TemplateCategory {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            TemplateCategory::PriorAuth => "prior_auth".to_string(),
            TemplateCategory::InsuranceInquiry => "insurance_inquiry".to_string(),
            TemplateCategory::FollowUp => "follow_up".to_string(),
            TemplateCategory::Escalation => "escalation".to_string(),
            TemplateCategory::PatientCommunication => "patient_communication".to_string(),
            TemplateCategory::Internal => "internal".to_string(),
            TemplateCategory::Billing => "billing".to_string(),
            TemplateCategory::Appointment => "appointment".to_string(),
            TemplateCategory::General => "general".to_string(),
            TemplateCategory::Documentation => "documentation".to_string(),
            TemplateCategory::Administrative => "administrative".to_string(),
            TemplateCategory::Legal => "legal".to_string(),
            TemplateCategory::Marketing => "marketing".to_string(),
            TemplateCategory::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "prior_auth" => Some(TemplateCategory::PriorAuth),
            "insurance_inquiry" => Some(TemplateCategory::InsuranceInquiry),
            "follow_up" => Some(TemplateCategory::FollowUp),
            "escalation" => Some(TemplateCategory::Escalation),
            "patient_communication" => Some(TemplateCategory::PatientCommunication),
            "internal" => Some(TemplateCategory::Internal),
            "billing" => Some(TemplateCategory::Billing),
            "appointment" => Some(TemplateCategory::Appointment),
            "general" => Some(TemplateCategory::General),
            "documentation" => Some(TemplateCategory::Documentation),
            "administrative" => Some(TemplateCategory::Administrative),
            "legal" => Some(TemplateCategory::Legal),
            "marketing" => Some(TemplateCategory::Marketing),
            s if s.starts_with("custom:") => Some(TemplateCategory::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Check if category involves PHI
    pub fn involves_phi(&self) -> bool {
        matches!(self,
            TemplateCategory::PatientCommunication |
            TemplateCategory::PriorAuth |
            TemplateCategory::InsuranceInquiry |
            TemplateCategory::Documentation
        )
    }
}

impl CommunicationTemplate {
    /// Create a new communication template
    pub fn new(
        practice_id: Uuid,
        name: String,
        category: TemplateCategory,
        communication_type: CommunicationType,
        subject_template: String,
        content_template: String,
        created_by: Uuid,
    ) -> Self {
        let now = Utc::now();

        Self {
            id: Uuid::new_v4(),
            practice_id,
            name,
            description: None,
            category,
            communication_type,
            subject_template,
            content_template,
            placeholders: HashMap::new(),
            default_tags: Vec::new(),
            default_priority: CommunicationPriority::Normal,
            default_follow_up_required: false,
            default_follow_up_hours: None,
            usage_count: 0,
            effectiveness_rating: None,
            is_active: true,
            is_shared: false,
            created_by,
            created_at: now,
            updated_at: now,
            last_used_at: None,
        }
    }

    /// Render template with provided values
    pub fn render(&self, values: &HashMap<String, String>) -> OctaveResult<(String, String)> {
        let mut subject = self.subject_template.clone();
        let mut content = self.content_template.clone();

        // Replace placeholders in subject and content
        for (placeholder, value) in values {
            let placeholder_pattern = format!("{{{{{}}}}}", placeholder);
            subject = subject.replace(&placeholder_pattern, value);
            content = content.replace(&placeholder_pattern, value);
        }

        // Check for unreplaced placeholders
        if subject.contains("{{") || content.contains("{{") {
            return Err(OctaveError::validation("template", "Template contains unreplaced placeholders"));
        }

        Ok((subject, content))
    }

    /// Record template usage
    pub fn record_usage(&mut self, effectiveness_rating: Option<f32>) {
        self.usage_count += 1;
        self.last_used_at = Some(Utc::now());

        if let Some(rating) = effectiveness_rating {
            if let Some(current_rating) = self.effectiveness_rating {
                self.effectiveness_rating = Some(
                    (current_rating * (self.usage_count - 1) as f32 + rating) / self.usage_count as f32
                );
            } else {
                self.effectiveness_rating = Some(rating);
            }
        }

        self.updated_at = Utc::now();
    }

    /// Get template summary
    pub fn summary(&self) -> CommunicationTemplateSummary {
        CommunicationTemplateSummary {
            id: self.id,
            practice_id: self.practice_id,
            name: self.name.clone(),
            description: self.description.clone(),
            category: self.category.clone(),
            communication_type: self.communication_type.clone(),
            default_priority: self.default_priority.clone(),
            usage_count: self.usage_count,
            effectiveness_rating: self.effectiveness_rating,
            is_active: self.is_active,
            is_shared: self.is_shared,
            created_by: self.created_by,
            created_at: self.created_at,
            last_used_at: self.last_used_at,
        }
    }
}

/// Communication template summary for display
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CommunicationTemplateSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub category: TemplateCategory,
    pub communication_type: CommunicationType,
    pub default_priority: CommunicationPriority,
    pub usage_count: u32,
    pub effectiveness_rating: Option<f32>,
    pub is_active: bool,
    pub is_shared: bool,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub last_used_at: Option<DateTime<Utc>>,
}

// ============================================================================
// REMINDER & NOTIFICATION SYSTEM MODELS
// ============================================================================

/// Reminder type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ReminderType {
    /// Follow-up reminder for communications
    FollowUp,
    /// Deadline reminder for prior authorizations
    Deadline,
    /// Escalation reminder for overdue items
    Escalation,
    /// Appointment reminder
    Appointment,
    /// Document submission reminder
    DocumentSubmission,
    /// Insurance follow-up reminder
    InsuranceFollowUp,
    /// Patient contact reminder
    PatientContact,
    /// Internal task reminder
    InternalTask,
    /// Custom reminder type
    Custom(String),
}

impl ReminderType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            ReminderType::FollowUp => "follow_up".to_string(),
            ReminderType::Deadline => "deadline".to_string(),
            ReminderType::Escalation => "escalation".to_string(),
            ReminderType::Appointment => "appointment".to_string(),
            ReminderType::DocumentSubmission => "document_submission".to_string(),
            ReminderType::InsuranceFollowUp => "insurance_follow_up".to_string(),
            ReminderType::PatientContact => "patient_contact".to_string(),
            ReminderType::InternalTask => "internal_task".to_string(),
            ReminderType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "follow_up" => Some(ReminderType::FollowUp),
            "deadline" => Some(ReminderType::Deadline),
            "escalation" => Some(ReminderType::Escalation),
            "appointment" => Some(ReminderType::Appointment),
            "document_submission" => Some(ReminderType::DocumentSubmission),
            "insurance_follow_up" => Some(ReminderType::InsuranceFollowUp),
            "patient_contact" => Some(ReminderType::PatientContact),
            "internal_task" => Some(ReminderType::InternalTask),
            s if s.starts_with("custom:") => Some(ReminderType::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Check if reminder type involves PHI
    pub fn involves_phi(&self) -> bool {
        matches!(self,
            ReminderType::PatientContact |
            ReminderType::Appointment |
            ReminderType::DocumentSubmission
        )
    }
}

/// Reminder status enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ReminderStatus {
    /// Reminder is pending/scheduled
    Pending,
    /// Reminder has been sent/triggered
    Triggered,
    /// Reminder has been completed
    Completed,
    /// Reminder has been snoozed
    Snoozed,
    /// Reminder has been cancelled
    Cancelled,
    /// Reminder is overdue
    Overdue,
    /// Reminder has been escalated
    Escalated,
}

impl ReminderStatus {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            ReminderStatus::Pending => "pending",
            ReminderStatus::Triggered => "triggered",
            ReminderStatus::Completed => "completed",
            ReminderStatus::Snoozed => "snoozed",
            ReminderStatus::Cancelled => "cancelled",
            ReminderStatus::Overdue => "overdue",
            ReminderStatus::Escalated => "escalated",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "pending" => Some(ReminderStatus::Pending),
            "triggered" => Some(ReminderStatus::Triggered),
            "completed" => Some(ReminderStatus::Completed),
            "snoozed" => Some(ReminderStatus::Snoozed),
            "cancelled" => Some(ReminderStatus::Cancelled),
            "overdue" => Some(ReminderStatus::Overdue),
            "escalated" => Some(ReminderStatus::Escalated),
            _ => None,
        }
    }

    /// Check if reminder is active (needs attention)
    pub fn is_active(&self) -> bool {
        matches!(self,
            ReminderStatus::Pending |
            ReminderStatus::Triggered |
            ReminderStatus::Snoozed |
            ReminderStatus::Overdue
        )
    }
}

/// Reminder priority enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize, PartialOrd, Ord)]
pub enum ReminderPriority {
    /// Low priority reminder
    Low,
    /// Normal priority reminder
    Normal,
    /// High priority reminder
    High,
    /// Critical priority reminder
    Critical,
}

impl ReminderPriority {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            ReminderPriority::Low => "low",
            ReminderPriority::Normal => "normal",
            ReminderPriority::High => "high",
            ReminderPriority::Critical => "critical",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "low" => Some(ReminderPriority::Low),
            "normal" => Some(ReminderPriority::Normal),
            "high" => Some(ReminderPriority::High),
            "critical" => Some(ReminderPriority::Critical),
            _ => None,
        }
    }

    /// Get escalation time in hours
    pub fn escalation_hours(&self) -> u32 {
        match self {
            ReminderPriority::Low => 72,      // 3 days
            ReminderPriority::Normal => 24,   // 1 day
            ReminderPriority::High => 4,      // 4 hours
            ReminderPriority::Critical => 1,  // 1 hour
        }
    }
}

impl Default for ReminderPriority {
    fn default() -> Self {
        ReminderPriority::Normal
    }
}

/// Entity type for reminder associations
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum EntityType {
    /// Prior authorization entity
    PriorAuth,
    /// Patient entity
    Patient,
    /// Communication entity
    Communication,
    /// Document entity
    Document,
    /// User entity
    User,
    /// Practice entity
    Practice,
    /// Insurance representative entity
    InsuranceRep,
    /// Custom entity type
    Custom(String),
}

impl EntityType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            EntityType::PriorAuth => "prior_auth".to_string(),
            EntityType::Patient => "patient".to_string(),
            EntityType::Communication => "communication".to_string(),
            EntityType::Document => "document".to_string(),
            EntityType::User => "user".to_string(),
            EntityType::Practice => "practice".to_string(),
            EntityType::InsuranceRep => "insurance_rep".to_string(),
            EntityType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "prior_auth" => Some(EntityType::PriorAuth),
            "patient" => Some(EntityType::Patient),
            "communication" => Some(EntityType::Communication),
            "document" => Some(EntityType::Document),
            "user" => Some(EntityType::User),
            "practice" => Some(EntityType::Practice),
            "insurance_rep" => Some(EntityType::InsuranceRep),
            s if s.starts_with("custom:") => Some(EntityType::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Check if entity type involves PHI
    pub fn involves_phi(&self) -> bool {
        matches!(self, EntityType::Patient | EntityType::PriorAuth)
    }
}

/// Recurrence pattern for recurring reminders
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecurrencePattern {
    /// Recurrence type
    pub pattern_type: RecurrenceType,
    /// Interval between recurrences
    pub interval: u32,
    /// Days of week for weekly recurrence
    pub days_of_week: Vec<u8>, // 0 = Sunday, 1 = Monday, etc.
    /// Day of month for monthly recurrence
    pub day_of_month: Option<u8>,
    /// End date for recurrence
    pub end_date: Option<DateTime<Utc>>,
    /// Maximum number of occurrences
    pub max_occurrences: Option<u32>,
    /// Current occurrence count
    pub occurrence_count: u32,
}

/// Recurrence type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum RecurrenceType {
    /// Daily recurrence
    Daily,
    /// Weekly recurrence
    Weekly,
    /// Monthly recurrence
    Monthly,
    /// Yearly recurrence
    Yearly,
    /// Custom interval in hours
    Custom(u32),
}

impl RecurrenceType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            RecurrenceType::Daily => "daily".to_string(),
            RecurrenceType::Weekly => "weekly".to_string(),
            RecurrenceType::Monthly => "monthly".to_string(),
            RecurrenceType::Yearly => "yearly".to_string(),
            RecurrenceType::Custom(hours) => format!("custom:{}", hours),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "daily" => Some(RecurrenceType::Daily),
            "weekly" => Some(RecurrenceType::Weekly),
            "monthly" => Some(RecurrenceType::Monthly),
            "yearly" => Some(RecurrenceType::Yearly),
            s if s.starts_with("custom:") => {
                if let Ok(hours) = s[7..].parse::<u32>() {
                    Some(RecurrenceType::Custom(hours))
                } else {
                    None
                }
            },
            _ => None,
        }
    }
}

/// Escalation rule for reminder escalation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EscalationRule {
    /// Escalation level (1, 2, 3, etc.)
    pub level: u32,
    /// Hours after due time to escalate
    pub escalate_after_hours: u32,
    /// User to escalate to
    pub escalate_to_user_id: Uuid,
    /// Escalation message template
    pub escalation_message: String,
    /// Whether this escalation has been triggered
    pub triggered: bool,
    /// When this escalation was triggered
    pub triggered_at: Option<DateTime<Utc>>,
}

/// Notification preferences for reminders
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NotificationPreferences {
    /// Enable email notifications
    pub email_enabled: bool,
    /// Enable SMS notifications
    pub sms_enabled: bool,
    /// Enable in-app notifications
    pub in_app_enabled: bool,
    /// Enable push notifications
    pub push_enabled: bool,
    /// Minutes before due time to send notification
    pub advance_notice_minutes: u32,
    /// Send reminder on due time
    pub send_on_due: bool,
    /// Send overdue notifications
    pub send_overdue: bool,
    /// Quiet hours start (24-hour format)
    pub quiet_hours_start: Option<u8>,
    /// Quiet hours end (24-hour format)
    pub quiet_hours_end: Option<u8>,
}

impl Default for NotificationPreferences {
    fn default() -> Self {
        Self {
            email_enabled: true,
            sms_enabled: false,
            in_app_enabled: true,
            push_enabled: true,
            advance_notice_minutes: 15,
            send_on_due: true,
            send_overdue: true,
            quiet_hours_start: Some(22), // 10 PM
            quiet_hours_end: Some(8),    // 8 AM
        }
    }
}

/// Reminder model for comprehensive reminder management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Reminder {
    /// Unique reminder identifier
    pub id: Uuid,
    /// Practice this reminder belongs to
    pub practice_id: Uuid,
    /// Type of reminder
    pub reminder_type: ReminderType,
    /// Reminder title
    pub title: String,
    /// Reminder description (PHI-protected)
    pub description: EncryptedPhi,
    /// When the reminder is scheduled for
    pub scheduled_for: DateTime<Utc>,
    /// Current reminder status
    pub status: ReminderStatus,
    /// Reminder priority
    pub priority: ReminderPriority,
    /// User assigned to this reminder
    pub assigned_to: Uuid,
    /// User who created this reminder
    pub created_by: Uuid,
    /// Type of related entity
    pub related_entity_type: EntityType,
    /// ID of related entity
    pub related_entity_id: Uuid,
    /// Escalation rules for this reminder
    pub escalation_rules: Vec<EscalationRule>,
    /// Notification preferences
    pub notification_preferences: NotificationPreferences,
    /// Recurrence pattern (if recurring)
    pub recurrence_pattern: Option<RecurrencePattern>,
    /// Completion notes
    pub completion_notes: Option<String>,
    /// Number of times snoozed
    pub snooze_count: u32,
    /// Last snooze timestamp
    pub last_snoozed_at: Option<DateTime<Utc>>,
    /// Original due date (before snoozing)
    pub original_due_date: DateTime<Utc>,
    /// Tags for categorization
    pub tags: Vec<String>,
    /// Reminder metadata (JSON)
    pub metadata: String,
    /// Reminder creation timestamp
    pub created_at: DateTime<Utc>,
    /// Reminder last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// Reminder completion timestamp
    pub completed_at: Option<DateTime<Utc>>,
    /// Last notification sent timestamp
    pub last_notification_at: Option<DateTime<Utc>>,
}

impl Reminder {
    /// Create a new reminder
    pub fn new(
        practice_id: Uuid,
        reminder_type: ReminderType,
        title: String,
        description: String,
        scheduled_for: DateTime<Utc>,
        assigned_to: Uuid,
        created_by: Uuid,
        related_entity_type: EntityType,
        related_entity_id: Uuid,
    ) -> Self {
        let now = Utc::now();

        Self {
            id: Uuid::new_v4(),
            practice_id,
            reminder_type: reminder_type.clone(),
            title,
            description: EncryptedPhi::new(description, PhiType::Other("reminder_description".to_string())),
            scheduled_for,
            status: ReminderStatus::Pending,
            priority: ReminderPriority::default(),
            assigned_to,
            created_by,
            related_entity_type,
            related_entity_id,
            escalation_rules: Vec::new(),
            notification_preferences: NotificationPreferences::default(),
            recurrence_pattern: None,
            completion_notes: None,
            snooze_count: 0,
            last_snoozed_at: None,
            original_due_date: scheduled_for,
            tags: Vec::new(),
            metadata: "{}".to_string(),
            created_at: now,
            updated_at: now,
            completed_at: None,
            last_notification_at: None,
        }
    }

    /// Check if reminder is overdue
    pub fn is_overdue(&self) -> bool {
        Utc::now() > self.scheduled_for && self.status.is_active()
    }

    /// Get time until due
    pub fn time_until_due(&self) -> chrono::Duration {
        self.scheduled_for - Utc::now()
    }

    /// Snooze reminder for specified duration
    pub fn snooze(&mut self, duration: chrono::Duration) {
        self.scheduled_for = Utc::now() + duration;
        self.status = ReminderStatus::Snoozed;
        self.snooze_count += 1;
        self.last_snoozed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Complete reminder
    pub fn complete(&mut self, completion_notes: Option<String>) {
        self.status = ReminderStatus::Completed;
        self.completion_notes = completion_notes;
        self.completed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Cancel reminder
    pub fn cancel(&mut self) {
        self.status = ReminderStatus::Cancelled;
        self.updated_at = Utc::now();
    }

    /// Mark as triggered
    pub fn trigger(&mut self) {
        self.status = ReminderStatus::Triggered;
        self.last_notification_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Mark as overdue
    pub fn mark_overdue(&mut self) {
        if self.status.is_active() {
            self.status = ReminderStatus::Overdue;
            self.updated_at = Utc::now();
        }
    }

    /// Add escalation rule
    pub fn add_escalation_rule(&mut self, rule: EscalationRule) {
        self.escalation_rules.push(rule);
        self.updated_at = Utc::now();
    }

    /// Check if any escalation should be triggered
    pub fn check_escalations(&mut self) -> Vec<&mut EscalationRule> {
        let now = Utc::now();
        let mut triggered_rules = Vec::new();

        for rule in &mut self.escalation_rules {
            if !rule.triggered {
                let escalation_time = self.scheduled_for + chrono::Duration::hours(rule.escalate_after_hours as i64);
                if now >= escalation_time {
                    rule.triggered = true;
                    rule.triggered_at = Some(now);
                    triggered_rules.push(rule);
                }
            }
        }

        if !triggered_rules.is_empty() {
            self.status = ReminderStatus::Escalated;
            self.updated_at = now;
        }

        triggered_rules
    }

    /// Add tag to reminder
    pub fn add_tag(&mut self, tag: String) {
        if !self.tags.contains(&tag) {
            self.tags.push(tag);
            self.updated_at = Utc::now();
        }
    }

    /// Get metadata as HashMap
    pub fn get_metadata(&self) -> HashMap<String, String> {
        serde_json::from_str(&self.metadata).unwrap_or_default()
    }

    /// Set metadata from HashMap
    pub fn set_metadata(&mut self, metadata: HashMap<String, String>) {
        self.metadata = serde_json::to_string(&metadata).unwrap_or_else(|_| "{}".to_string());
        self.updated_at = Utc::now();
    }

    /// Check if reminder involves PHI
    pub fn involves_phi(&self) -> bool {
        self.reminder_type.involves_phi() || self.related_entity_type.involves_phi()
    }

    /// Generate reminder summary
    pub fn summary(&self) -> ReminderSummary {
        ReminderSummary {
            id: self.id,
            practice_id: self.practice_id,
            reminder_type: self.reminder_type.clone(),
            title: self.title.clone(),
            masked_description: self.description.masked(),
            scheduled_for: self.scheduled_for,
            status: self.status.clone(),
            priority: self.priority.clone(),
            assigned_to: self.assigned_to,
            related_entity_type: self.related_entity_type.clone(),
            related_entity_id: self.related_entity_id,
            is_overdue: self.is_overdue(),
            snooze_count: self.snooze_count,
            tags: self.tags.clone(),
            created_by: self.created_by,
            created_at: self.created_at,
            completed_at: self.completed_at,
        }
    }
}

/// Reminder summary for display without PHI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReminderSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub reminder_type: ReminderType,
    pub title: String,
    pub masked_description: String,
    pub scheduled_for: DateTime<Utc>,
    pub status: ReminderStatus,
    pub priority: ReminderPriority,
    pub assigned_to: Uuid,
    pub related_entity_type: EntityType,
    pub related_entity_id: Uuid,
    pub is_overdue: bool,
    pub snooze_count: u32,
    pub tags: Vec<String>,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// Notification model for tracking all notifications sent
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Notification {
    /// Unique notification identifier
    pub id: Uuid,
    /// Practice this notification belongs to
    pub practice_id: Uuid,
    /// Notification type
    pub notification_type: NotificationType,
    /// Notification channel
    pub channel: NotificationChannel,
    /// Recipient user ID
    pub recipient_id: Uuid,
    /// Notification subject
    pub subject: String,
    /// Notification content (PHI-protected)
    pub content: EncryptedPhi,
    /// Delivery status
    pub delivery_status: DeliveryStatus,
    /// Related entity type
    pub related_entity_type: Option<EntityType>,
    /// Related entity ID
    pub related_entity_id: Option<Uuid>,
    /// External message ID (from email/SMS service)
    pub external_message_id: Option<String>,
    /// Delivery attempts
    pub delivery_attempts: u32,
    /// Last delivery attempt timestamp
    pub last_attempt_at: Option<DateTime<Utc>>,
    /// Delivery timestamp
    pub delivered_at: Option<DateTime<Utc>>,
    /// Read timestamp
    pub read_at: Option<DateTime<Utc>>,
    /// Error message (if delivery failed)
    pub error_message: Option<String>,
    /// Notification metadata (JSON)
    pub metadata: String,
    /// Notification creation timestamp
    pub created_at: DateTime<Utc>,
    /// Notification last updated timestamp
    pub updated_at: DateTime<Utc>,
}

/// Notification type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NotificationType {
    /// Reminder notification
    Reminder,
    /// Status change notification
    StatusChange,
    /// Escalation notification
    Escalation,
    /// Deadline notification
    Deadline,
    /// Approval notification
    Approval,
    /// Denial notification
    Denial,
    /// System notification
    System,
    /// Custom notification
    Custom(String),
}

/// Notification channel enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum NotificationChannel {
    /// Email notification
    Email,
    /// SMS notification
    SMS,
    /// In-app notification
    InApp,
    /// Push notification
    Push,
    /// Phone call
    Phone,
}

/// Delivery status enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum DeliveryStatus {
    /// Notification is pending delivery
    Pending,
    /// Notification is being sent
    Sending,
    /// Notification was delivered successfully
    Delivered,
    /// Notification delivery failed
    Failed,
    /// Notification was read by recipient
    Read,
    /// Notification bounced
    Bounced,
    /// Notification was rejected
    Rejected,
}

// ============================================================================
// WORKFLOW ENGINE MODELS
// ============================================================================

/// Workflow state for managing entity state transitions
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowState {
    /// Unique workflow state identifier
    pub id: Uuid,
    /// Practice this workflow belongs to
    pub practice_id: Uuid,
    /// Type of entity being managed
    pub entity_type: EntityType,
    /// ID of entity being managed
    pub entity_id: Uuid,
    /// Current status/state
    pub current_status: String,
    /// Previous status/state
    pub previous_status: Option<String>,
    /// Workflow definition ID
    pub workflow_definition_id: Uuid,
    /// State-specific data (JSON)
    pub state_data: String,
    /// Transition history
    pub transition_history: Vec<StateTransition>,
    /// Pending actions
    pub pending_actions: Vec<PendingAction>,
    /// Workflow metadata (JSON)
    pub metadata: String,
    /// Workflow creation timestamp
    pub created_at: DateTime<Utc>,
    /// Workflow last updated timestamp
    pub updated_at: DateTime<Utc>,
}

/// State transition record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StateTransition {
    /// Transition ID
    pub id: Uuid,
    /// From status
    pub from_status: String,
    /// To status
    pub to_status: String,
    /// User who triggered the transition
    pub triggered_by: Uuid,
    /// Transition reason
    pub reason: Option<String>,
    /// Transition notes
    pub notes: Option<String>,
    /// Transition timestamp
    pub transitioned_at: DateTime<Utc>,
    /// Whether transition was automatic
    pub is_automatic: bool,
    /// Transition metadata (JSON)
    pub metadata: String,
}

/// Pending action in workflow
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PendingAction {
    /// Action ID
    pub id: Uuid,
    /// Action type
    pub action_type: ActionType,
    /// User assigned to action
    pub assigned_to: Uuid,
    /// Action due date
    pub due_date: Option<DateTime<Utc>>,
    /// Action description
    pub description: String,
    /// Action status
    pub status: ActionStatus,
    /// Action creation timestamp
    pub created_at: DateTime<Utc>,
    /// Action completion timestamp
    pub completed_at: Option<DateTime<Utc>>,
}

/// Action type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ActionType {
    /// Approval action
    Approval,
    /// Review action
    Review,
    /// Documentation action
    Documentation,
    /// Contact action
    Contact,
    /// Follow-up action
    FollowUp,
    /// Escalation action
    Escalation,
    /// Custom action
    Custom(String),
}

/// Action status enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ActionStatus {
    /// Action is pending
    Pending,
    /// Action is in progress
    InProgress,
    /// Action is completed
    Completed,
    /// Action is cancelled
    Cancelled,
    /// Action is overdue
    Overdue,
}

/// Workflow definition for configurable workflows
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowDefinition {
    /// Unique workflow definition identifier
    pub id: Uuid,
    /// Practice this workflow belongs to
    pub practice_id: Uuid,
    /// Workflow name
    pub name: String,
    /// Workflow description
    pub description: Option<String>,
    /// Entity type this workflow applies to
    pub entity_type: EntityType,
    /// Workflow version
    pub version: u32,
    /// Whether this workflow is active
    pub is_active: bool,
    /// Workflow states
    pub states: Vec<WorkflowStateDefinition>,
    /// Valid transitions
    pub transitions: Vec<WorkflowTransition>,
    /// Workflow rules
    pub rules: Vec<WorkflowRule>,
    /// Workflow creation timestamp
    pub created_at: DateTime<Utc>,
    /// Workflow last updated timestamp
    pub updated_at: DateTime<Utc>,
}

/// Workflow state definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowStateDefinition {
    /// State name
    pub name: String,
    /// State display name
    pub display_name: String,
    /// State description
    pub description: Option<String>,
    /// Whether this is an initial state
    pub is_initial: bool,
    /// Whether this is a final state
    pub is_final: bool,
    /// State color for UI
    pub color: Option<String>,
    /// Actions available in this state
    pub available_actions: Vec<ActionType>,
}

/// Workflow transition definition
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowTransition {
    /// From state
    pub from_state: String,
    /// To state
    pub to_state: String,
    /// Transition name
    pub name: String,
    /// Required permissions
    pub required_permissions: Vec<String>,
    /// Transition conditions
    pub conditions: Vec<String>,
    /// Automatic transition rules
    pub auto_transition_rules: Option<AutoTransitionRule>,
}

/// Workflow rule for business logic
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorkflowRule {
    /// Rule name
    pub name: String,
    /// Rule condition
    pub condition: String,
    /// Rule action
    pub action: String,
    /// Rule priority
    pub priority: u32,
    /// Whether rule is active
    pub is_active: bool,
}

/// Auto transition rule
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AutoTransitionRule {
    /// Trigger type
    pub trigger_type: TriggerType,
    /// Trigger condition
    pub condition: String,
    /// Delay before transition (in hours)
    pub delay_hours: Option<u32>,
}

/// Trigger type for auto transitions
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TriggerType {
    /// Time-based trigger
    Time,
    /// Event-based trigger
    Event,
    /// Condition-based trigger
    Condition,
    /// External trigger
    External,
}

// ============================================================================
// TEMPLATE & AUTOMATION SYSTEM MODELS
// ============================================================================

/// Template type enumeration for different template purposes
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TemplateType {
    /// Email template
    Email,
    /// SMS/Text message template
    SMS,
    /// Document template
    Document,
    /// Letter template
    Letter,
    /// Fax template
    Fax,
    /// In-app notification template
    InAppNotification,
    /// Prior authorization template
    PriorAuth,
    /// Insurance communication template
    Insurance,
    /// Patient communication template
    Patient,
    /// Internal memo template
    InternalMemo,
    /// Custom template type
    Custom(String),
}

impl TemplateType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            TemplateType::Email => "email".to_string(),
            TemplateType::SMS => "sms".to_string(),
            TemplateType::Document => "document".to_string(),
            TemplateType::Letter => "letter".to_string(),
            TemplateType::Fax => "fax".to_string(),
            TemplateType::InAppNotification => "in_app_notification".to_string(),
            TemplateType::PriorAuth => "prior_auth".to_string(),
            TemplateType::Insurance => "insurance".to_string(),
            TemplateType::Patient => "patient".to_string(),
            TemplateType::InternalMemo => "internal_memo".to_string(),
            TemplateType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "email" => Some(TemplateType::Email),
            "sms" => Some(TemplateType::SMS),
            "document" => Some(TemplateType::Document),
            "letter" => Some(TemplateType::Letter),
            "fax" => Some(TemplateType::Fax),
            "in_app_notification" => Some(TemplateType::InAppNotification),
            "prior_auth" => Some(TemplateType::PriorAuth),
            "insurance" => Some(TemplateType::Insurance),
            "patient" => Some(TemplateType::Patient),
            "internal_memo" => Some(TemplateType::InternalMemo),
            s if s.starts_with("custom:") => Some(TemplateType::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Check if template type involves PHI
    pub fn involves_phi(&self) -> bool {
        matches!(self,
            TemplateType::Patient |
            TemplateType::PriorAuth |
            TemplateType::Insurance |
            TemplateType::Document
        )
    }

    /// Get default merge fields for template type
    pub fn default_merge_fields(&self) -> Vec<String> {
        match self {
            TemplateType::Email | TemplateType::SMS => vec![
                "recipient_name".to_string(),
                "sender_name".to_string(),
                "practice_name".to_string(),
                "date".to_string(),
            ],
            TemplateType::Patient => vec![
                "patient_name".to_string(),
                "patient_dob".to_string(),
                "practice_name".to_string(),
                "provider_name".to_string(),
                "appointment_date".to_string(),
            ],
            TemplateType::PriorAuth => vec![
                "patient_name".to_string(),
                "tracking_id".to_string(),
                "procedure_code".to_string(),
                "insurance_company".to_string(),
                "status".to_string(),
            ],
            TemplateType::Insurance => vec![
                "insurance_company".to_string(),
                "representative_name".to_string(),
                "reference_number".to_string(),
                "practice_name".to_string(),
            ],
            _ => vec![
                "practice_name".to_string(),
                "date".to_string(),
                "user_name".to_string(),
            ],
        }
    }
}

/// Merge field definition for template personalization
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MergeField {
    /// Field name/key
    pub name: String,
    /// Field display name
    pub display_name: String,
    /// Field description
    pub description: Option<String>,
    /// Field data type
    pub field_type: MergeFieldType,
    /// Whether field contains PHI
    pub is_phi: bool,
    /// Whether field is required
    pub is_required: bool,
    /// Default value if not provided
    pub default_value: Option<String>,
    /// Validation rules
    pub validation_rules: Vec<ValidationRule>,
    /// Field source (where data comes from)
    pub data_source: DataSource,
    /// Field formatting options
    pub formatting: Option<FieldFormatting>,
}

/// Merge field data type
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum MergeFieldType {
    /// Text field
    Text,
    /// Number field
    Number,
    /// Date field
    Date,
    /// DateTime field
    DateTime,
    /// Boolean field
    Boolean,
    /// Email field
    Email,
    /// Phone field
    Phone,
    /// Currency field
    Currency,
    /// List/Array field
    List,
    /// Object field
    Object,
}

/// Data source for merge fields
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataSource {
    /// Source type
    pub source_type: DataSourceType,
    /// Source identifier
    pub source_id: String,
    /// Field path within source
    pub field_path: String,
    /// Fallback value if source unavailable
    pub fallback_value: Option<String>,
}

/// Data source type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum DataSourceType {
    /// Patient data
    Patient,
    /// Prior authorization data
    PriorAuth,
    /// Practice data
    Practice,
    /// User data
    User,
    /// Communication data
    Communication,
    /// Document data
    Document,
    /// Insurance representative data
    InsuranceRep,
    /// Static value
    Static,
    /// Computed value
    Computed,
}

/// Field formatting options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FieldFormatting {
    /// Date format string
    pub date_format: Option<String>,
    /// Number format (decimal places, etc.)
    pub number_format: Option<String>,
    /// Text case transformation
    pub text_case: Option<TextCase>,
    /// Maximum length
    pub max_length: Option<usize>,
    /// Prefix to add
    pub prefix: Option<String>,
    /// Suffix to add
    pub suffix: Option<String>,
}

/// Text case transformation
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum TextCase {
    /// Uppercase
    Upper,
    /// Lowercase
    Lower,
    /// Title case
    Title,
    /// Sentence case
    Sentence,
}

/// Validation rule for merge fields
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ValidationRule {
    /// Rule type
    pub rule_type: ValidationRuleType,
    /// Rule parameters
    pub parameters: HashMap<String, String>,
    /// Error message if validation fails
    pub error_message: String,
}

/// Validation rule type
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ValidationRuleType {
    /// Required field
    Required,
    /// Minimum length
    MinLength,
    /// Maximum length
    MaxLength,
    /// Regular expression pattern
    Pattern,
    /// Email format
    Email,
    /// Phone format
    Phone,
    /// Date range
    DateRange,
    /// Number range
    NumberRange,
    /// Custom validation
    Custom,
}

/// Conditional rule for template logic
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConditionalRule {
    /// Rule ID
    pub id: Uuid,
    /// Rule name
    pub name: String,
    /// Condition expression
    pub condition: String,
    /// Action to take if condition is true
    pub action: ConditionalAction,
    /// Priority/order of execution
    pub priority: u32,
    /// Whether rule is active
    pub is_active: bool,
}

/// Conditional action for template rules
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConditionalAction {
    /// Show/hide content
    ShowContent(String),
    /// Set field value
    SetField { field_name: String, value: String },
    /// Include another template
    IncludeTemplate(Uuid),
    /// Apply formatting
    ApplyFormatting(FieldFormatting),
    /// Send notification
    SendNotification { message: String, recipients: Vec<Uuid> },
    /// Custom action
    Custom { action_type: String, parameters: HashMap<String, String> },
}



/// Main template model for comprehensive template management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Template {
    /// Unique template identifier
    pub id: Uuid,
    /// Practice this template belongs to
    pub practice_id: Uuid,
    /// Template type
    pub template_type: TemplateType,
    /// Template name
    pub name: String,
    /// Template description
    pub description: Option<String>,
    /// Template category
    pub category: TemplateCategory,
    /// Template subject (for emails, etc.)
    pub subject: Option<String>,
    /// Template content (PHI-protected)
    pub content: EncryptedPhi,
    /// Available merge fields
    pub merge_fields: Vec<MergeField>,
    /// Conditional logic rules
    pub conditional_logic: Vec<ConditionalRule>,
    /// Template version
    pub version: u32,
    /// Whether template is active
    pub is_active: bool,
    /// Whether template is shared across practice
    pub is_shared: bool,
    /// Template usage count
    pub usage_count: u64,
    /// Template effectiveness score (0.0 - 1.0)
    pub effectiveness_score: Option<f32>,
    /// Template tags for organization
    pub tags: Vec<String>,
    /// Template metadata (JSON)
    pub metadata: String,
    /// User who created this template
    pub created_by: Uuid,
    /// Template creation timestamp
    pub created_at: DateTime<Utc>,
    /// Template last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// Last time template was used
    pub last_used_at: Option<DateTime<Utc>>,
    /// Template approval status
    pub approval_status: ApprovalStatus,
    /// User who approved template
    pub approved_by: Option<Uuid>,
    /// Template approval timestamp
    pub approved_at: Option<DateTime<Utc>>,
}

/// Template approval status
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ApprovalStatus {
    /// Template is in draft state
    Draft,
    /// Template is pending approval
    PendingApproval,
    /// Template is approved for use
    Approved,
    /// Template approval was rejected
    Rejected,
    /// Template is deprecated
    Deprecated,
}

impl ApprovalStatus {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            ApprovalStatus::Draft => "draft",
            ApprovalStatus::PendingApproval => "pending_approval",
            ApprovalStatus::Approved => "approved",
            ApprovalStatus::Rejected => "rejected",
            ApprovalStatus::Deprecated => "deprecated",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "draft" => Some(ApprovalStatus::Draft),
            "pending_approval" => Some(ApprovalStatus::PendingApproval),
            "approved" => Some(ApprovalStatus::Approved),
            "rejected" => Some(ApprovalStatus::Rejected),
            "deprecated" => Some(ApprovalStatus::Deprecated),
            _ => None,
        }
    }

    /// Check if template can be used
    pub fn is_usable(&self) -> bool {
        matches!(self, ApprovalStatus::Approved)
    }
}

impl Template {
    /// Create a new template
    pub fn new(
        practice_id: Uuid,
        template_type: TemplateType,
        name: String,
        content: String,
        category: TemplateCategory,
        created_by: Uuid,
    ) -> Self {
        let now = Utc::now();
        let merge_fields = template_type.default_merge_fields()
            .into_iter()
            .map(|field_name| MergeField {
                name: field_name.clone(),
                display_name: field_name.replace('_', " ").to_title_case(),
                description: None,
                field_type: MergeFieldType::Text,
                is_phi: field_name.contains("patient") || field_name.contains("dob"),
                is_required: false,
                default_value: None,
                validation_rules: Vec::new(),
                data_source: DataSource {
                    source_type: DataSourceType::Static,
                    source_id: "default".to_string(),
                    field_path: field_name.clone(),
                    fallback_value: None,
                },
                formatting: None,
            })
            .collect();

        Self {
            id: Uuid::new_v4(),
            practice_id,
            template_type,
            name,
            description: None,
            category,
            subject: None,
            content: EncryptedPhi::new(content, PhiType::Other("template_content".to_string())),
            merge_fields,
            conditional_logic: Vec::new(),
            version: 1,
            is_active: true,
            is_shared: false,
            usage_count: 0,
            effectiveness_score: None,
            tags: Vec::new(),
            metadata: "{}".to_string(),
            created_by,
            created_at: now,
            updated_at: now,
            last_used_at: None,
            approval_status: ApprovalStatus::Draft,
            approved_by: None,
            approved_at: None,
        }
    }

    /// Render template with merge field values
    pub fn render(&self, merge_values: &HashMap<String, String>) -> OctaveResult<String> {
        if !self.approval_status.is_usable() {
            return Err(OctaveError::validation("approval_status", "Template is not approved for use"));
        }

        let content = self.content.decrypt()?;
        let mut rendered_content = content;

        // Replace merge fields
        for field in &self.merge_fields {
            let field_value = merge_values.get(&field.name)
                .or(field.default_value.as_ref())
                .cloned()
                .unwrap_or_default();

            // Apply formatting if specified
            let formatted_value = if let Some(formatting) = &field.formatting {
                self.apply_formatting(&field_value, formatting)
            } else {
                field_value
            };

            // Replace field placeholder
            let placeholder = format!("{{{{{}}}}}", field.name);
            rendered_content = rendered_content.replace(&placeholder, &formatted_value);
        }

        // Process conditional logic
        for rule in &self.conditional_logic {
            if rule.is_active && self.evaluate_condition(&rule.condition, merge_values)? {
                rendered_content = self.apply_conditional_action(&rendered_content, &rule.action, merge_values)?;
            }
        }

        Ok(rendered_content)
    }

    /// Record template usage
    pub fn record_usage(&mut self, effectiveness_score: Option<f32>) {
        self.usage_count += 1;
        self.last_used_at = Some(Utc::now());

        if let Some(score) = effectiveness_score {
            if let Some(current_score) = self.effectiveness_score {
                // Calculate weighted average
                let total_weight = self.usage_count as f32;
                let new_weight = 1.0;
                let old_weight = total_weight - new_weight;

                self.effectiveness_score = Some(
                    (current_score * old_weight + score * new_weight) / total_weight
                );
            } else {
                self.effectiveness_score = Some(score);
            }
        }

        self.updated_at = Utc::now();
    }

    /// Add merge field
    pub fn add_merge_field(&mut self, field: MergeField) {
        self.merge_fields.push(field);
        self.updated_at = Utc::now();
    }

    /// Add conditional rule
    pub fn add_conditional_rule(&mut self, rule: ConditionalRule) {
        self.conditional_logic.push(rule);
        self.updated_at = Utc::now();
    }

    /// Update approval status
    pub fn update_approval_status(&mut self, status: ApprovalStatus, approved_by: Option<Uuid>) {
        let is_approved = status == ApprovalStatus::Approved;
        self.approval_status = status;
        self.approved_by = approved_by;
        if is_approved {
            self.approved_at = Some(Utc::now());
        }
        self.updated_at = Utc::now();
    }

    /// Check if template involves PHI
    pub fn involves_phi(&self) -> bool {
        self.template_type.involves_phi() ||
        self.category.involves_phi() ||
        self.merge_fields.iter().any(|field| field.is_phi)
    }

    /// Get template summary
    pub fn summary(&self) -> TemplateSummary {
        TemplateSummary {
            id: self.id,
            practice_id: self.practice_id,
            template_type: self.template_type.clone(),
            name: self.name.clone(),
            description: self.description.clone(),
            category: self.category.clone(),
            version: self.version,
            is_active: self.is_active,
            is_shared: self.is_shared,
            usage_count: self.usage_count,
            effectiveness_score: self.effectiveness_score,
            approval_status: self.approval_status.clone(),
            tags: self.tags.clone(),
            created_by: self.created_by,
            created_at: self.created_at,
            last_used_at: self.last_used_at,
        }
    }

    /// Apply formatting to field value
    fn apply_formatting(&self, value: &str, formatting: &FieldFormatting) -> String {
        let mut formatted = value.to_string();

        // Apply text case transformation
        if let Some(text_case) = &formatting.text_case {
            formatted = match text_case {
                TextCase::Upper => formatted.to_uppercase(),
                TextCase::Lower => formatted.to_lowercase(),
                TextCase::Title => formatted.to_title_case(),
                TextCase::Sentence => {
                    let mut chars: Vec<char> = formatted.chars().collect();
                    if let Some(first_char) = chars.get_mut(0) {
                        *first_char = first_char.to_uppercase().next().unwrap_or(*first_char);
                    }
                    chars.into_iter().collect()
                }
            };
        }

        // Apply length limit
        if let Some(max_length) = formatting.max_length {
            if formatted.len() > max_length {
                formatted.truncate(max_length);
            }
        }

        // Apply prefix and suffix
        if let Some(prefix) = &formatting.prefix {
            formatted = format!("{}{}", prefix, formatted);
        }
        if let Some(suffix) = &formatting.suffix {
            formatted = format!("{}{}", formatted, suffix);
        }

        formatted
    }

    /// Evaluate conditional expression
    fn evaluate_condition(&self, condition: &str, merge_values: &HashMap<String, String>) -> OctaveResult<bool> {
        // Simple condition evaluation - in production, use a proper expression parser
        // For now, support basic equality checks
        if condition.contains("==") {
            let parts: Vec<&str> = condition.split("==").collect();
            if parts.len() == 2 {
                let left = parts[0].trim();
                let right = parts[1].trim().trim_matches('"');

                if let Some(value) = merge_values.get(left) {
                    return Ok(value == right);
                }
            }
        }

        // Default to false for unsupported conditions
        Ok(false)
    }

    /// Apply conditional action
    fn apply_conditional_action(
        &self,
        content: &str,
        action: &ConditionalAction,
        _merge_values: &HashMap<String, String>,
    ) -> OctaveResult<String> {
        match action {
            ConditionalAction::ShowContent(show_content) => {
                Ok(format!("{}\n{}", content, show_content))
            }
            ConditionalAction::SetField { field_name: _, value: _ } => {
                // Field setting would be handled during merge field processing
                Ok(content.to_string())
            }
            _ => {
                // Other actions not implemented in basic version
                Ok(content.to_string())
            }
        }
    }
}

/// Template summary for display without PHI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub template_type: TemplateType,
    pub name: String,
    pub description: Option<String>,
    pub category: TemplateCategory,
    pub version: u32,
    pub is_active: bool,
    pub is_shared: bool,
    pub usage_count: u64,
    pub effectiveness_score: Option<f32>,
    pub approval_status: ApprovalStatus,
    pub tags: Vec<String>,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub last_used_at: Option<DateTime<Utc>>,
}

/// Template version for version control
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateVersion {
    /// Version ID
    pub id: Uuid,
    /// Template ID this version belongs to
    pub template_id: Uuid,
    /// Version number
    pub version: u32,
    /// Version content
    pub content: EncryptedPhi,
    /// Version merge fields
    pub merge_fields: Vec<MergeField>,
    /// Version conditional logic
    pub conditional_logic: Vec<ConditionalRule>,
    /// Change description
    pub change_description: Option<String>,
    /// User who created this version
    pub created_by: Uuid,
    /// Version creation timestamp
    pub created_at: DateTime<Utc>,
}

/// Template usage record for analytics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemplateUsage {
    /// Usage ID
    pub id: Uuid,
    /// Template ID
    pub template_id: Uuid,
    /// Practice ID
    pub practice_id: Uuid,
    /// User who used the template
    pub used_by: Uuid,
    /// Context where template was used
    pub usage_context: UsageContext,
    /// Related entity type
    pub related_entity_type: Option<EntityType>,
    /// Related entity ID
    pub related_entity_id: Option<Uuid>,
    /// Effectiveness score for this usage
    pub effectiveness_score: Option<f32>,
    /// Usage notes
    pub notes: Option<String>,
    /// Usage timestamp
    pub used_at: DateTime<Utc>,
}

/// Usage context for template analytics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UsageContext {
    /// Context type
    pub context_type: ContextType,
    /// Context description
    pub description: String,
    /// Additional context metadata
    pub metadata: HashMap<String, String>,
}

/// Context type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ContextType {
    /// Email communication
    Email,
    /// SMS communication
    SMS,
    /// Document generation
    Document,
    /// Prior authorization
    PriorAuth,
    /// Patient communication
    Patient,
    /// Insurance communication
    Insurance,
    /// Internal communication
    Internal,
    /// Automated response
    Automated,
    /// Manual usage
    Manual,
}

// Helper trait for string case conversion
trait ToTitleCase {
    fn to_title_case(&self) -> String;
}

impl ToTitleCase for str {
    fn to_title_case(&self) -> String {
        self.split_whitespace()
            .map(|word| {
                let mut chars: Vec<char> = word.chars().collect();
                if let Some(first_char) = chars.get_mut(0) {
                    *first_char = first_char.to_uppercase().next().unwrap_or(*first_char);
                }
                chars.into_iter().collect::<String>()
            })
            .collect::<Vec<String>>()
            .join(" ")
    }
}

// ============================================================================
// ANALYTICS & REPORTING SYSTEM MODELS
// ============================================================================

/// Analytics metric type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum MetricType {
    /// Prior authorization approval rate
    ApprovalRate,
    /// Processing time metrics
    ProcessingTime,
    /// Provider performance metrics
    ProviderPerformance,
    /// Revenue impact metrics
    RevenueImpact,
    /// User activity metrics
    UserActivity,
    /// System performance metrics
    SystemPerformance,
    /// Compliance metrics
    Compliance,
    /// Communication effectiveness
    CommunicationEffectiveness,
    /// Template usage metrics
    TemplateUsage,
    /// Workflow efficiency
    WorkflowEfficiency,
    /// Error rate metrics
    ErrorRate,
    /// Custom metric type
    Custom(String),
}

impl MetricType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            MetricType::ApprovalRate => "approval_rate".to_string(),
            MetricType::ProcessingTime => "processing_time".to_string(),
            MetricType::ProviderPerformance => "provider_performance".to_string(),
            MetricType::RevenueImpact => "revenue_impact".to_string(),
            MetricType::UserActivity => "user_activity".to_string(),
            MetricType::SystemPerformance => "system_performance".to_string(),
            MetricType::Compliance => "compliance".to_string(),
            MetricType::CommunicationEffectiveness => "communication_effectiveness".to_string(),
            MetricType::TemplateUsage => "template_usage".to_string(),
            MetricType::WorkflowEfficiency => "workflow_efficiency".to_string(),
            MetricType::ErrorRate => "error_rate".to_string(),
            MetricType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "approval_rate" => Some(MetricType::ApprovalRate),
            "processing_time" => Some(MetricType::ProcessingTime),
            "provider_performance" => Some(MetricType::ProviderPerformance),
            "revenue_impact" => Some(MetricType::RevenueImpact),
            "user_activity" => Some(MetricType::UserActivity),
            "system_performance" => Some(MetricType::SystemPerformance),
            "compliance" => Some(MetricType::Compliance),
            "communication_effectiveness" => Some(MetricType::CommunicationEffectiveness),
            "template_usage" => Some(MetricType::TemplateUsage),
            "workflow_efficiency" => Some(MetricType::WorkflowEfficiency),
            "error_rate" => Some(MetricType::ErrorRate),
            s if s.starts_with("custom:") => Some(MetricType::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Check if metric type involves PHI
    pub fn involves_phi(&self) -> bool {
        matches!(self,
            MetricType::ProviderPerformance |
            MetricType::UserActivity |
            MetricType::CommunicationEffectiveness
        )
    }
}

/// Aggregation period for analytics metrics
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum AggregationPeriod {
    /// Real-time (no aggregation)
    RealTime,
    /// Hourly aggregation
    Hourly,
    /// Daily aggregation
    Daily,
    /// Weekly aggregation
    Weekly,
    /// Monthly aggregation
    Monthly,
    /// Quarterly aggregation
    Quarterly,
    /// Yearly aggregation
    Yearly,
    /// Custom period in minutes
    Custom(u32),
}

impl AggregationPeriod {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            AggregationPeriod::RealTime => "real_time".to_string(),
            AggregationPeriod::Hourly => "hourly".to_string(),
            AggregationPeriod::Daily => "daily".to_string(),
            AggregationPeriod::Weekly => "weekly".to_string(),
            AggregationPeriod::Monthly => "monthly".to_string(),
            AggregationPeriod::Quarterly => "quarterly".to_string(),
            AggregationPeriod::Yearly => "yearly".to_string(),
            AggregationPeriod::Custom(minutes) => format!("custom:{}", minutes),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "real_time" => Some(AggregationPeriod::RealTime),
            "hourly" => Some(AggregationPeriod::Hourly),
            "daily" => Some(AggregationPeriod::Daily),
            "weekly" => Some(AggregationPeriod::Weekly),
            "monthly" => Some(AggregationPeriod::Monthly),
            "quarterly" => Some(AggregationPeriod::Quarterly),
            "yearly" => Some(AggregationPeriod::Yearly),
            s if s.starts_with("custom:") => {
                s[7..].parse::<u32>().ok().map(AggregationPeriod::Custom)
            }
            _ => None,
        }
    }

    /// Get duration in minutes
    pub fn duration_minutes(&self) -> Option<u32> {
        match self {
            AggregationPeriod::RealTime => None,
            AggregationPeriod::Hourly => Some(60),
            AggregationPeriod::Daily => Some(24 * 60),
            AggregationPeriod::Weekly => Some(7 * 24 * 60),
            AggregationPeriod::Monthly => Some(30 * 24 * 60),
            AggregationPeriod::Quarterly => Some(90 * 24 * 60),
            AggregationPeriod::Yearly => Some(365 * 24 * 60),
            AggregationPeriod::Custom(minutes) => Some(*minutes),
        }
    }
}

/// Analytics metric for comprehensive data collection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyticsMetric {
    /// Unique metric identifier
    pub id: Uuid,
    /// Practice this metric belongs to
    pub practice_id: Uuid,
    /// Type of metric
    pub metric_type: MetricType,
    /// Metric name/identifier
    pub metric_name: String,
    /// Metric value
    pub metric_value: f64,
    /// Additional metric dimensions
    pub dimensions: HashMap<String, String>,
    /// Metric timestamp
    pub timestamp: DateTime<Utc>,
    /// Aggregation period
    pub aggregation_period: AggregationPeriod,
    /// Related entity type
    pub related_entity_type: Option<EntityType>,
    /// Related entity ID
    pub related_entity_id: Option<Uuid>,
    /// Metric metadata (JSON)
    pub metadata: String,
    /// User who generated this metric
    pub created_by: Option<Uuid>,
    /// Metric creation timestamp
    pub created_at: DateTime<Utc>,
}

impl AnalyticsMetric {
    /// Create a new analytics metric
    pub fn new(
        practice_id: Uuid,
        metric_type: MetricType,
        metric_name: String,
        metric_value: f64,
        aggregation_period: AggregationPeriod,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            practice_id,
            metric_type,
            metric_name,
            metric_value,
            dimensions: HashMap::new(),
            timestamp: now,
            aggregation_period,
            related_entity_type: None,
            related_entity_id: None,
            metadata: "{}".to_string(),
            created_by: None,
            created_at: now,
        }
    }

    /// Add dimension to metric
    pub fn add_dimension(&mut self, key: String, value: String) {
        self.dimensions.insert(key, value);
    }

    /// Set related entity
    pub fn set_related_entity(&mut self, entity_type: EntityType, entity_id: Uuid) {
        self.related_entity_type = Some(entity_type);
        self.related_entity_id = Some(entity_id);
    }

    /// Check if metric involves PHI
    pub fn involves_phi(&self) -> bool {
        self.metric_type.involves_phi() ||
        self.dimensions.keys().any(|key| key.contains("patient") || key.contains("user"))
    }

    /// Get metric summary without PHI
    pub fn summary(&self) -> AnalyticsMetricSummary {
        let sanitized_dimensions = if self.involves_phi() {
            // Remove PHI-containing dimensions
            self.dimensions.iter()
                .filter(|(key, _)| !key.contains("patient") && !key.contains("user"))
                .map(|(k, v)| (k.clone(), v.clone()))
                .collect()
        } else {
            self.dimensions.clone()
        };

        AnalyticsMetricSummary {
            id: self.id,
            practice_id: self.practice_id,
            metric_type: self.metric_type.clone(),
            metric_name: self.metric_name.clone(),
            metric_value: self.metric_value,
            dimensions: sanitized_dimensions,
            timestamp: self.timestamp,
            aggregation_period: self.aggregation_period.clone(),
            related_entity_type: self.related_entity_type.clone(),
        }
    }
}

/// Analytics metric summary without PHI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AnalyticsMetricSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub metric_type: MetricType,
    pub metric_name: String,
    pub metric_value: f64,
    pub dimensions: HashMap<String, String>,
    pub timestamp: DateTime<Utc>,
    pub aggregation_period: AggregationPeriod,
    pub related_entity_type: Option<EntityType>,
}

/// Report type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ReportType {
    /// Prior authorization analytics report
    PriorAuthAnalytics,
    /// Provider performance report
    ProviderPerformance,
    /// Insurance performance report
    InsurancePerformance,
    /// Financial impact report
    FinancialImpact,
    /// Compliance report
    Compliance,
    /// Audit trail report
    AuditTrail,
    /// System performance report
    SystemPerformance,
    /// User activity report
    UserActivity,
    /// Communication effectiveness report
    CommunicationEffectiveness,
    /// Template usage report
    TemplateUsage,
    /// Workflow efficiency report
    WorkflowEfficiency,
    /// Custom report type
    Custom(String),
}

impl ReportType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            ReportType::PriorAuthAnalytics => "prior_auth_analytics".to_string(),
            ReportType::ProviderPerformance => "provider_performance".to_string(),
            ReportType::InsurancePerformance => "insurance_performance".to_string(),
            ReportType::FinancialImpact => "financial_impact".to_string(),
            ReportType::Compliance => "compliance".to_string(),
            ReportType::AuditTrail => "audit_trail".to_string(),
            ReportType::SystemPerformance => "system_performance".to_string(),
            ReportType::UserActivity => "user_activity".to_string(),
            ReportType::CommunicationEffectiveness => "communication_effectiveness".to_string(),
            ReportType::TemplateUsage => "template_usage".to_string(),
            ReportType::WorkflowEfficiency => "workflow_efficiency".to_string(),
            ReportType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "prior_auth_analytics" => Some(ReportType::PriorAuthAnalytics),
            "provider_performance" => Some(ReportType::ProviderPerformance),
            "insurance_performance" => Some(ReportType::InsurancePerformance),
            "financial_impact" => Some(ReportType::FinancialImpact),
            "compliance" => Some(ReportType::Compliance),
            "audit_trail" => Some(ReportType::AuditTrail),
            "system_performance" => Some(ReportType::SystemPerformance),
            "user_activity" => Some(ReportType::UserActivity),
            "communication_effectiveness" => Some(ReportType::CommunicationEffectiveness),
            "template_usage" => Some(ReportType::TemplateUsage),
            "workflow_efficiency" => Some(ReportType::WorkflowEfficiency),
            s if s.starts_with("custom:") => Some(ReportType::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Check if report type involves PHI
    pub fn involves_phi(&self) -> bool {
        matches!(self,
            ReportType::ProviderPerformance |
            ReportType::UserActivity |
            ReportType::AuditTrail |
            ReportType::CommunicationEffectiveness
        )
    }

    /// Get default parameters for report type
    pub fn default_parameters(&self) -> HashMap<String, String> {
        let mut params = HashMap::new();

        match self {
            ReportType::PriorAuthAnalytics => {
                params.insert("date_range".to_string(), "30_days".to_string());
                params.insert("group_by".to_string(), "insurance_company".to_string());
            }
            ReportType::ProviderPerformance => {
                params.insert("date_range".to_string(), "30_days".to_string());
                params.insert("metrics".to_string(), "productivity,quality".to_string());
            }
            ReportType::Compliance => {
                params.insert("compliance_type".to_string(), "hipaa".to_string());
                params.insert("include_violations".to_string(), "true".to_string());
            }
            _ => {
                params.insert("date_range".to_string(), "30_days".to_string());
            }
        }

        params
    }
}

/// Report format enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ReportFormat {
    /// PDF format
    PDF,
    /// CSV format
    CSV,
    /// Excel format
    Excel,
    /// JSON format
    JSON,
    /// HTML format
    HTML,
}

impl ReportFormat {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            ReportFormat::PDF => "pdf",
            ReportFormat::CSV => "csv",
            ReportFormat::Excel => "excel",
            ReportFormat::JSON => "json",
            ReportFormat::HTML => "html",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "pdf" => Some(ReportFormat::PDF),
            "csv" => Some(ReportFormat::CSV),
            "excel" => Some(ReportFormat::Excel),
            "json" => Some(ReportFormat::JSON),
            "html" => Some(ReportFormat::HTML),
            _ => None,
        }
    }

    /// Get MIME type for format
    pub fn mime_type(&self) -> &'static str {
        match self {
            ReportFormat::PDF => "application/pdf",
            ReportFormat::CSV => "text/csv",
            ReportFormat::Excel => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ReportFormat::JSON => "application/json",
            ReportFormat::HTML => "text/html",
        }
    }

    /// Get file extension for format
    pub fn file_extension(&self) -> &'static str {
        match self {
            ReportFormat::PDF => "pdf",
            ReportFormat::CSV => "csv",
            ReportFormat::Excel => "xlsx",
            ReportFormat::JSON => "json",
            ReportFormat::HTML => "html",
        }
    }
}

/// Report status enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ReportStatus {
    /// Report is queued for generation
    Queued,
    /// Report is being generated
    Generating,
    /// Report generation completed successfully
    Completed,
    /// Report generation failed
    Failed,
    /// Report was cancelled
    Cancelled,
    /// Report is scheduled for future generation
    Scheduled,
}

impl ReportStatus {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            ReportStatus::Queued => "queued",
            ReportStatus::Generating => "generating",
            ReportStatus::Completed => "completed",
            ReportStatus::Failed => "failed",
            ReportStatus::Cancelled => "cancelled",
            ReportStatus::Scheduled => "scheduled",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "queued" => Some(ReportStatus::Queued),
            "generating" => Some(ReportStatus::Generating),
            "completed" => Some(ReportStatus::Completed),
            "failed" => Some(ReportStatus::Failed),
            "cancelled" => Some(ReportStatus::Cancelled),
            "scheduled" => Some(ReportStatus::Scheduled),
            _ => None,
        }
    }

    /// Check if report is in final state
    pub fn is_final(&self) -> bool {
        matches!(self,
            ReportStatus::Completed |
            ReportStatus::Failed |
            ReportStatus::Cancelled
        )
    }
}

/// Main report model for comprehensive reporting
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Report {
    /// Unique report identifier
    pub id: Uuid,
    /// Practice this report belongs to
    pub practice_id: Uuid,
    /// Report type
    pub report_type: ReportType,
    /// Report name/title
    pub name: String,
    /// Report description
    pub description: Option<String>,
    /// Report parameters (JSON)
    pub parameters: String,
    /// Report format
    pub format: ReportFormat,
    /// Report status
    pub status: ReportStatus,
    /// Report content (PHI-protected)
    pub content: Option<EncryptedPhi>,
    /// Report file path (if saved to file)
    pub file_path: Option<String>,
    /// Report generation start time
    pub generation_started_at: Option<DateTime<Utc>>,
    /// Report generation completion time
    pub generation_completed_at: Option<DateTime<Utc>>,
    /// Report generation error message
    pub error_message: Option<String>,
    /// Report size in bytes
    pub size_bytes: Option<u64>,
    /// Report metadata (JSON)
    pub metadata: String,
    /// User who requested this report
    pub requested_by: Uuid,
    /// Report creation timestamp
    pub created_at: DateTime<Utc>,
    /// Report last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// Report expiration timestamp
    pub expires_at: Option<DateTime<Utc>>,
    /// Whether report is shared
    pub is_shared: bool,
    /// Report access count
    pub access_count: u64,
    /// Last time report was accessed
    pub last_accessed_at: Option<DateTime<Utc>>,
}

impl Report {
    /// Create a new report
    pub fn new(
        practice_id: Uuid,
        report_type: ReportType,
        name: String,
        format: ReportFormat,
        parameters: HashMap<String, String>,
        requested_by: Uuid,
    ) -> Self {
        let now = Utc::now();
        let parameters_json = serde_json::to_string(&parameters).unwrap_or_else(|_| "{}".to_string());

        Self {
            id: Uuid::new_v4(),
            practice_id,
            report_type,
            name,
            description: None,
            parameters: parameters_json,
            format,
            status: ReportStatus::Queued,
            content: None,
            file_path: None,
            generation_started_at: None,
            generation_completed_at: None,
            error_message: None,
            size_bytes: None,
            metadata: "{}".to_string(),
            requested_by,
            created_at: now,
            updated_at: now,
            expires_at: Some(now + chrono::Duration::days(30)), // Default 30-day expiration
            is_shared: false,
            access_count: 0,
            last_accessed_at: None,
        }
    }

    /// Start report generation
    pub fn start_generation(&mut self) {
        self.status = ReportStatus::Generating;
        self.generation_started_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Complete report generation
    pub fn complete_generation(&mut self, content: String, size_bytes: u64) {
        self.status = ReportStatus::Completed;
        self.content = Some(EncryptedPhi::new(content, PhiType::Other("report_content".to_string())));
        self.size_bytes = Some(size_bytes);
        self.generation_completed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Fail report generation
    pub fn fail_generation(&mut self, error_message: String) {
        self.status = ReportStatus::Failed;
        self.error_message = Some(error_message);
        self.generation_completed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Record report access
    pub fn record_access(&mut self) {
        self.access_count += 1;
        self.last_accessed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Check if report involves PHI
    pub fn involves_phi(&self) -> bool {
        self.report_type.involves_phi()
    }

    /// Get report summary
    pub fn summary(&self) -> ReportSummary {
        ReportSummary {
            id: self.id,
            practice_id: self.practice_id,
            report_type: self.report_type.clone(),
            name: self.name.clone(),
            description: self.description.clone(),
            format: self.format.clone(),
            status: self.status.clone(),
            size_bytes: self.size_bytes,
            requested_by: self.requested_by,
            created_at: self.created_at,
            generation_completed_at: self.generation_completed_at,
            expires_at: self.expires_at,
            is_shared: self.is_shared,
            access_count: self.access_count,
        }
    }

    /// Get generation duration
    pub fn generation_duration(&self) -> Option<chrono::Duration> {
        if let (Some(start), Some(end)) = (self.generation_started_at, self.generation_completed_at) {
            Some(end - start)
        } else {
            None
        }
    }
}

/// Report summary without PHI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReportSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub report_type: ReportType,
    pub name: String,
    pub description: Option<String>,
    pub format: ReportFormat,
    pub status: ReportStatus,
    pub size_bytes: Option<u64>,
    pub requested_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub generation_completed_at: Option<DateTime<Utc>>,
    pub expires_at: Option<DateTime<Utc>>,
    pub is_shared: bool,
    pub access_count: u64,
}

/// Compliance violation type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum ComplianceViolationType {
    /// Unauthorized PHI access
    UnauthorizedAccess,
    /// PHI disclosure violation
    ImproperDisclosure,
    /// Data retention violation
    RetentionViolation,
    /// Access control violation
    AccessControlViolation,
    /// Audit trail violation
    AuditTrailViolation,
    /// Encryption violation
    EncryptionViolation,
    /// Minimum necessary violation
    MinimumNecessaryViolation,
    /// Business associate violation
    BusinessAssociateViolation,
    /// Patient rights violation
    PatientRightsViolation,
    /// Security incident
    SecurityIncident,
    /// Custom violation type
    Custom(String),
}

impl ComplianceViolationType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            ComplianceViolationType::UnauthorizedAccess => "unauthorized_access".to_string(),
            ComplianceViolationType::ImproperDisclosure => "improper_disclosure".to_string(),
            ComplianceViolationType::RetentionViolation => "retention_violation".to_string(),
            ComplianceViolationType::AccessControlViolation => "access_control_violation".to_string(),
            ComplianceViolationType::AuditTrailViolation => "audit_trail_violation".to_string(),
            ComplianceViolationType::EncryptionViolation => "encryption_violation".to_string(),
            ComplianceViolationType::MinimumNecessaryViolation => "minimum_necessary_violation".to_string(),
            ComplianceViolationType::BusinessAssociateViolation => "business_associate_violation".to_string(),
            ComplianceViolationType::PatientRightsViolation => "patient_rights_violation".to_string(),
            ComplianceViolationType::SecurityIncident => "security_incident".to_string(),
            ComplianceViolationType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "unauthorized_access" => Some(ComplianceViolationType::UnauthorizedAccess),
            "improper_disclosure" => Some(ComplianceViolationType::ImproperDisclosure),
            "retention_violation" => Some(ComplianceViolationType::RetentionViolation),
            "access_control_violation" => Some(ComplianceViolationType::AccessControlViolation),
            "audit_trail_violation" => Some(ComplianceViolationType::AuditTrailViolation),
            "encryption_violation" => Some(ComplianceViolationType::EncryptionViolation),
            "minimum_necessary_violation" => Some(ComplianceViolationType::MinimumNecessaryViolation),
            "business_associate_violation" => Some(ComplianceViolationType::BusinessAssociateViolation),
            "patient_rights_violation" => Some(ComplianceViolationType::PatientRightsViolation),
            "security_incident" => Some(ComplianceViolationType::SecurityIncident),
            s if s.starts_with("custom:") => Some(ComplianceViolationType::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Get severity level (1-5, 5 being most severe)
    pub fn severity_level(&self) -> u8 {
        match self {
            ComplianceViolationType::UnauthorizedAccess => 5,
            ComplianceViolationType::ImproperDisclosure => 5,
            ComplianceViolationType::SecurityIncident => 4,
            ComplianceViolationType::EncryptionViolation => 4,
            ComplianceViolationType::AccessControlViolation => 3,
            ComplianceViolationType::MinimumNecessaryViolation => 3,
            ComplianceViolationType::AuditTrailViolation => 2,
            ComplianceViolationType::RetentionViolation => 2,
            ComplianceViolationType::BusinessAssociateViolation => 3,
            ComplianceViolationType::PatientRightsViolation => 3,
            ComplianceViolationType::Custom(_) => 2,
        }
    }

    /// Check if violation requires immediate notification
    pub fn requires_immediate_notification(&self) -> bool {
        self.severity_level() >= 4
    }
}



/// Compliance violation status
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ComplianceStatus {
    /// Violation detected
    Detected,
    /// Under investigation
    Investigating,
    /// Resolved
    Resolved,
    /// False positive
    FalsePositive,
    /// Acknowledged but not resolved
    Acknowledged,
}

impl ComplianceStatus {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            ComplianceStatus::Detected => "detected",
            ComplianceStatus::Investigating => "investigating",
            ComplianceStatus::Resolved => "resolved",
            ComplianceStatus::FalsePositive => "false_positive",
            ComplianceStatus::Acknowledged => "acknowledged",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "detected" => Some(ComplianceStatus::Detected),
            "investigating" => Some(ComplianceStatus::Investigating),
            "resolved" => Some(ComplianceStatus::Resolved),
            "false_positive" => Some(ComplianceStatus::FalsePositive),
            "acknowledged" => Some(ComplianceStatus::Acknowledged),
            _ => None,
        }
    }

    /// Check if status is final
    pub fn is_final(&self) -> bool {
        matches!(self,
            ComplianceStatus::Resolved |
            ComplianceStatus::FalsePositive
        )
    }
}

/// Compliance violation record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceViolation {
    /// Unique violation identifier
    pub id: Uuid,
    /// Practice this violation belongs to
    pub practice_id: Uuid,
    /// Type of violation
    pub violation_type: ComplianceViolationType,
    /// Violation severity
    pub severity: ComplianceSeverity,
    /// Violation status
    pub status: ComplianceStatus,
    /// Violation title
    pub title: String,
    /// Violation description
    pub description: String,
    /// Related entity type
    pub related_entity_type: Option<EntityType>,
    /// Related entity ID
    pub related_entity_id: Option<Uuid>,
    /// User involved in violation
    pub involved_user_id: Option<Uuid>,
    /// Detection method
    pub detection_method: String,
    /// Evidence data (PHI-protected)
    pub evidence: Option<EncryptedPhi>,
    /// Remediation actions taken
    pub remediation_actions: Vec<String>,
    /// Violation metadata (JSON)
    pub metadata: String,
    /// User who detected this violation
    pub detected_by: Option<Uuid>,
    /// Violation detection timestamp
    pub detected_at: DateTime<Utc>,
    /// User who resolved this violation
    pub resolved_by: Option<Uuid>,
    /// Violation resolution timestamp
    pub resolved_at: Option<DateTime<Utc>>,
    /// Violation creation timestamp
    pub created_at: DateTime<Utc>,
    /// Violation last updated timestamp
    pub updated_at: DateTime<Utc>,
}

impl ComplianceViolation {
    /// Create a new compliance violation
    pub fn new(
        practice_id: Uuid,
        violation_type: ComplianceViolationType,
        title: String,
        description: String,
        detection_method: String,
    ) -> Self {
        let now = Utc::now();
        let severity = ComplianceSeverity::from_level(violation_type.severity_level());

        Self {
            id: Uuid::new_v4(),
            practice_id,
            violation_type,
            severity,
            status: ComplianceStatus::Detected,
            title,
            description,
            related_entity_type: None,
            related_entity_id: None,
            involved_user_id: None,
            detection_method,
            evidence: None,
            remediation_actions: Vec::new(),
            metadata: "{}".to_string(),
            detected_by: None,
            detected_at: now,
            resolved_by: None,
            resolved_at: None,
            created_at: now,
            updated_at: now,
        }
    }

    /// Add remediation action
    pub fn add_remediation_action(&mut self, action: String) {
        self.remediation_actions.push(action);
        self.updated_at = Utc::now();
    }

    /// Resolve violation
    pub fn resolve(&mut self, resolved_by: Uuid, resolution_notes: String) {
        self.status = ComplianceStatus::Resolved;
        self.resolved_by = Some(resolved_by);
        self.resolved_at = Some(Utc::now());
        self.add_remediation_action(format!("Resolved: {}", resolution_notes));
    }

    /// Mark as false positive
    pub fn mark_false_positive(&mut self, marked_by: Uuid, reason: String) {
        self.status = ComplianceStatus::FalsePositive;
        self.resolved_by = Some(marked_by);
        self.resolved_at = Some(Utc::now());
        self.add_remediation_action(format!("Marked as false positive: {}", reason));
    }

    /// Get violation summary
    pub fn summary(&self) -> ComplianceViolationSummary {
        ComplianceViolationSummary {
            id: self.id,
            practice_id: self.practice_id,
            violation_type: self.violation_type.clone(),
            severity: self.severity.clone(),
            status: self.status.clone(),
            title: self.title.clone(),
            description: self.description.clone(),
            detection_method: self.detection_method.clone(),
            detected_at: self.detected_at,
            resolved_at: self.resolved_at,
            remediation_count: self.remediation_actions.len(),
        }
    }
}

/// Compliance violation summary without PHI
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ComplianceViolationSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub violation_type: ComplianceViolationType,
    pub severity: ComplianceSeverity,
    pub status: ComplianceStatus,
    pub title: String,
    pub description: String,
    pub detection_method: String,
    pub detected_at: DateTime<Utc>,
    pub resolved_at: Option<DateTime<Utc>>,
    pub remediation_count: usize,
}

/// Performance metric type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum PerformanceMetricType {
    /// Response time metric
    ResponseTime,
    /// Throughput metric
    Throughput,
    /// Error rate metric
    ErrorRate,
    /// CPU utilization
    CpuUtilization,
    /// Memory utilization
    MemoryUtilization,
    /// Database query performance
    DatabaseQueryPerformance,
    /// API endpoint performance
    ApiEndpointPerformance,
    /// User experience metric
    UserExperience,
    /// System health metric
    SystemHealth,
    /// Security performance metric
    SecurityPerformance,
    /// Custom performance metric
    Custom(String),
}

impl PerformanceMetricType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            PerformanceMetricType::ResponseTime => "response_time".to_string(),
            PerformanceMetricType::Throughput => "throughput".to_string(),
            PerformanceMetricType::ErrorRate => "error_rate".to_string(),
            PerformanceMetricType::CpuUtilization => "cpu_utilization".to_string(),
            PerformanceMetricType::MemoryUtilization => "memory_utilization".to_string(),
            PerformanceMetricType::DatabaseQueryPerformance => "database_query_performance".to_string(),
            PerformanceMetricType::ApiEndpointPerformance => "api_endpoint_performance".to_string(),
            PerformanceMetricType::UserExperience => "user_experience".to_string(),
            PerformanceMetricType::SystemHealth => "system_health".to_string(),
            PerformanceMetricType::SecurityPerformance => "security_performance".to_string(),
            PerformanceMetricType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "response_time" => Some(PerformanceMetricType::ResponseTime),
            "throughput" => Some(PerformanceMetricType::Throughput),
            "error_rate" => Some(PerformanceMetricType::ErrorRate),
            "cpu_utilization" => Some(PerformanceMetricType::CpuUtilization),
            "memory_utilization" => Some(PerformanceMetricType::MemoryUtilization),
            "database_query_performance" => Some(PerformanceMetricType::DatabaseQueryPerformance),
            "api_endpoint_performance" => Some(PerformanceMetricType::ApiEndpointPerformance),
            "user_experience" => Some(PerformanceMetricType::UserExperience),
            "system_health" => Some(PerformanceMetricType::SystemHealth),
            "security_performance" => Some(PerformanceMetricType::SecurityPerformance),
            s if s.starts_with("custom:") => Some(PerformanceMetricType::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Get metric unit
    pub fn unit(&self) -> &'static str {
        match self {
            PerformanceMetricType::ResponseTime => "ms",
            PerformanceMetricType::Throughput => "req/s",
            PerformanceMetricType::ErrorRate => "%",
            PerformanceMetricType::CpuUtilization => "%",
            PerformanceMetricType::MemoryUtilization => "%",
            PerformanceMetricType::DatabaseQueryPerformance => "ms",
            PerformanceMetricType::ApiEndpointPerformance => "ms",
            PerformanceMetricType::UserExperience => "score",
            PerformanceMetricType::SystemHealth => "score",
            PerformanceMetricType::SecurityPerformance => "score",
            PerformanceMetricType::Custom(_) => "value",
        }
    }
}

/// Performance metric record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetric {
    /// Unique metric identifier
    pub id: Uuid,
    /// Practice this metric belongs to
    pub practice_id: Uuid,
    /// Performance metric type
    pub metric_type: PerformanceMetricType,
    /// Metric name
    pub metric_name: String,
    /// Metric value
    pub metric_value: f64,
    /// Metric threshold (for alerting)
    pub threshold: Option<f64>,
    /// Whether metric exceeded threshold
    pub threshold_exceeded: bool,
    /// Metric dimensions
    pub dimensions: HashMap<String, String>,
    /// Metric timestamp
    pub timestamp: DateTime<Utc>,
    /// Metric metadata (JSON)
    pub metadata: String,
    /// Metric creation timestamp
    pub created_at: DateTime<Utc>,
}

impl PerformanceMetric {
    /// Create a new performance metric
    pub fn new(
        practice_id: Uuid,
        metric_type: PerformanceMetricType,
        metric_name: String,
        metric_value: f64,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            practice_id,
            metric_type,
            metric_name,
            metric_value,
            threshold: None,
            threshold_exceeded: false,
            dimensions: HashMap::new(),
            timestamp: now,
            metadata: "{}".to_string(),
            created_at: now,
        }
    }

    /// Set threshold for alerting
    pub fn set_threshold(&mut self, threshold: f64) {
        self.threshold = Some(threshold);
        self.check_threshold();
    }

    /// Check if threshold is exceeded
    pub fn check_threshold(&mut self) {
        if let Some(threshold) = self.threshold {
            self.threshold_exceeded = self.metric_value > threshold;
        }
    }

    /// Add dimension to metric
    pub fn add_dimension(&mut self, key: String, value: String) {
        self.dimensions.insert(key, value);
    }

    /// Get metric summary
    pub fn summary(&self) -> PerformanceMetricSummary {
        PerformanceMetricSummary {
            id: self.id,
            practice_id: self.practice_id,
            metric_type: self.metric_type.clone(),
            metric_name: self.metric_name.clone(),
            metric_value: self.metric_value,
            threshold: self.threshold,
            threshold_exceeded: self.threshold_exceeded,
            timestamp: self.timestamp,
            unit: self.metric_type.unit().to_string(),
        }
    }
}

/// Performance metric summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetricSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub metric_type: PerformanceMetricType,
    pub metric_name: String,
    pub metric_value: f64,
    pub threshold: Option<f64>,
    pub threshold_exceeded: bool,
    pub timestamp: DateTime<Utc>,
    pub unit: String,
}

/// Dashboard configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Dashboard {
    /// Unique dashboard identifier
    pub id: Uuid,
    /// Practice this dashboard belongs to
    pub practice_id: Uuid,
    /// Dashboard name
    pub name: String,
    /// Dashboard description
    pub description: Option<String>,
    /// Dashboard configuration (JSON)
    pub configuration: String,
    /// Dashboard widgets
    pub widgets: Vec<DashboardWidget>,
    /// Whether dashboard is shared
    pub is_shared: bool,
    /// Whether dashboard is default
    pub is_default: bool,
    /// Dashboard access count
    pub access_count: u64,
    /// User who created this dashboard
    pub created_by: Uuid,
    /// Dashboard creation timestamp
    pub created_at: DateTime<Utc>,
    /// Dashboard last updated timestamp
    pub updated_at: DateTime<Utc>,
    /// Last time dashboard was accessed
    pub last_accessed_at: Option<DateTime<Utc>>,
}

/// Dashboard widget configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardWidget {
    /// Widget identifier
    pub id: Uuid,
    /// Widget type
    pub widget_type: String,
    /// Widget title
    pub title: String,
    /// Widget configuration (JSON)
    pub configuration: String,
    /// Widget position
    pub position: WidgetPosition,
    /// Widget size
    pub size: WidgetSize,
    /// Whether widget is visible
    pub is_visible: bool,
}

/// Widget position on dashboard
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WidgetPosition {
    pub x: u32,
    pub y: u32,
}

/// Widget size on dashboard
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WidgetSize {
    pub width: u32,
    pub height: u32,
}

impl Dashboard {
    /// Create a new dashboard
    pub fn new(
        practice_id: Uuid,
        name: String,
        created_by: Uuid,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            practice_id,
            name,
            description: None,
            configuration: "{}".to_string(),
            widgets: Vec::new(),
            is_shared: false,
            is_default: false,
            access_count: 0,
            created_by,
            created_at: now,
            updated_at: now,
            last_accessed_at: None,
        }
    }

    /// Add widget to dashboard
    pub fn add_widget(&mut self, widget: DashboardWidget) {
        self.widgets.push(widget);
        self.updated_at = Utc::now();
    }

    /// Record dashboard access
    pub fn record_access(&mut self) {
        self.access_count += 1;
        self.last_accessed_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// Get dashboard summary
    pub fn summary(&self) -> DashboardSummary {
        DashboardSummary {
            id: self.id,
            practice_id: self.practice_id,
            name: self.name.clone(),
            description: self.description.clone(),
            widget_count: self.widgets.len(),
            is_shared: self.is_shared,
            is_default: self.is_default,
            access_count: self.access_count,
            created_by: self.created_by,
            created_at: self.created_at,
            last_accessed_at: self.last_accessed_at,
        }
    }
}

/// Dashboard summary
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardSummary {
    pub id: Uuid,
    pub practice_id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub widget_count: usize,
    pub is_shared: bool,
    pub is_default: bool,
    pub access_count: u64,
    pub created_by: Uuid,
    pub created_at: DateTime<Utc>,
    pub last_accessed_at: Option<DateTime<Utc>>,
}

// ============================================================================
// API & INTEGRATION SYSTEM MODELS
// ============================================================================

/// API endpoint definition for comprehensive API management
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiEndpoint {
    /// Unique endpoint identifier
    pub id: Uuid,
    /// Practice this endpoint belongs to
    pub practice_id: Uuid,
    /// Endpoint path
    pub path: String,
    /// HTTP method
    pub method: HttpMethod,
    /// Endpoint description
    pub description: String,
    /// API version
    pub version: String,
    /// Whether endpoint is active
    pub is_active: bool,
    /// Whether endpoint requires authentication
    pub requires_auth: bool,
    /// Required permissions for access
    pub required_permissions: Vec<String>,
    /// Rate limit configuration
    pub rate_limit: Option<RateLimit>,
    /// Endpoint metadata (JSON)
    pub metadata: String,
    /// Endpoint creation timestamp
    pub created_at: DateTime<Utc>,
    /// Endpoint last updated timestamp
    pub updated_at: DateTime<Utc>,
}

/// HTTP method enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum HttpMethod {
    GET,
    POST,
    PUT,
    PATCH,
    DELETE,
    HEAD,
    OPTIONS,
}

impl HttpMethod {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            HttpMethod::GET => "GET",
            HttpMethod::POST => "POST",
            HttpMethod::PUT => "PUT",
            HttpMethod::PATCH => "PATCH",
            HttpMethod::DELETE => "DELETE",
            HttpMethod::HEAD => "HEAD",
            HttpMethod::OPTIONS => "OPTIONS",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_uppercase().as_str() {
            "GET" => Some(HttpMethod::GET),
            "POST" => Some(HttpMethod::POST),
            "PUT" => Some(HttpMethod::PUT),
            "PATCH" => Some(HttpMethod::PATCH),
            "DELETE" => Some(HttpMethod::DELETE),
            "HEAD" => Some(HttpMethod::HEAD),
            "OPTIONS" => Some(HttpMethod::OPTIONS),
            _ => None,
        }
    }
}

/// Rate limit configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RateLimit {
    /// Maximum requests per time window
    pub max_requests: u32,
    /// Time window in seconds
    pub window_seconds: u32,
    /// Burst allowance
    pub burst_allowance: Option<u32>,
}

impl RateLimit {
    /// Create a new rate limit configuration
    pub fn new(max_requests: u32, window_seconds: u32) -> Self {
        Self {
            max_requests,
            window_seconds,
            burst_allowance: None,
        }
    }

    /// Set burst allowance
    pub fn with_burst(mut self, burst_allowance: u32) -> Self {
        self.burst_allowance = Some(burst_allowance);
        self
    }

    /// Get requests per second
    pub fn requests_per_second(&self) -> f64 {
        self.max_requests as f64 / self.window_seconds as f64
    }
}

/// API request log for comprehensive API monitoring
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiRequestLog {
    /// Unique log identifier
    pub id: Uuid,
    /// Practice this request belongs to
    pub practice_id: Uuid,
    /// User who made the request
    pub user_id: Option<Uuid>,
    /// Request endpoint
    pub endpoint: String,
    /// HTTP method
    pub method: HttpMethod,
    /// Request IP address
    pub ip_address: String,
    /// User agent
    pub user_agent: Option<String>,
    /// Request headers (sanitized)
    pub request_headers: String,
    /// Request body size
    pub request_size: u64,
    /// Response status code
    pub response_status: u16,
    /// Response body size
    pub response_size: u64,
    /// Request processing time in milliseconds
    pub processing_time_ms: u32,
    /// Whether request contained PHI
    pub contains_phi: bool,
    /// OCTAVE threat score
    pub threat_score: Option<f32>,
    /// Request timestamp
    pub timestamp: DateTime<Utc>,
    /// Request metadata (JSON)
    pub metadata: String,
}

impl ApiRequestLog {
    /// Create a new API request log
    pub fn new(
        practice_id: Uuid,
        endpoint: String,
        method: HttpMethod,
        ip_address: String,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            practice_id,
            user_id: None,
            endpoint,
            method,
            ip_address,
            user_agent: None,
            request_headers: "{}".to_string(),
            request_size: 0,
            response_status: 0,
            response_size: 0,
            processing_time_ms: 0,
            contains_phi: false,
            threat_score: None,
            timestamp: Utc::now(),
            metadata: "{}".to_string(),
        }
    }

    /// Set user information
    pub fn with_user(mut self, user_id: Uuid) -> Self {
        self.user_id = Some(user_id);
        self
    }

    /// Set request details
    pub fn with_request_details(
        mut self,
        user_agent: Option<String>,
        request_size: u64,
    ) -> Self {
        self.user_agent = user_agent;
        self.request_size = request_size;
        self
    }

    /// Set response details
    pub fn with_response_details(
        mut self,
        status: u16,
        response_size: u64,
        processing_time_ms: u32,
    ) -> Self {
        self.response_status = status;
        self.response_size = response_size;
        self.processing_time_ms = processing_time_ms;
        self
    }

    /// Set PHI and security information
    pub fn with_security_info(mut self, contains_phi: bool, threat_score: Option<f32>) -> Self {
        self.contains_phi = contains_phi;
        self.threat_score = threat_score;
        self
    }

    /// Check if request was successful
    pub fn is_successful(&self) -> bool {
        self.response_status >= 200 && self.response_status < 300
    }

    /// Check if request was an error
    pub fn is_error(&self) -> bool {
        self.response_status >= 400
    }

    /// Get response time category
    pub fn response_time_category(&self) -> ResponseTimeCategory {
        match self.processing_time_ms {
            0..=100 => ResponseTimeCategory::Fast,
            101..=500 => ResponseTimeCategory::Normal,
            501..=2000 => ResponseTimeCategory::Slow,
            _ => ResponseTimeCategory::VerySlow,
        }
    }
}

/// Response time category for analytics
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ResponseTimeCategory {
    Fast,
    Normal,
    Slow,
    VerySlow,
}

impl ResponseTimeCategory {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            ResponseTimeCategory::Fast => "fast",
            ResponseTimeCategory::Normal => "normal",
            ResponseTimeCategory::Slow => "slow",
            ResponseTimeCategory::VerySlow => "very_slow",
        }
    }
}

/// External integration configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExternalIntegration {
    /// Unique integration identifier
    pub id: Uuid,
    /// Practice this integration belongs to
    pub practice_id: Uuid,
    /// Integration type
    pub integration_type: IntegrationType,
    /// Integration name
    pub name: String,
    /// Integration description
    pub description: Option<String>,
    /// Integration configuration (encrypted)
    pub configuration: EncryptedPhi,
    /// Whether integration is active
    pub is_active: bool,
    /// Last successful connection
    pub last_successful_connection: Option<DateTime<Utc>>,
    /// Last connection attempt
    pub last_connection_attempt: Option<DateTime<Utc>>,
    /// Connection status
    pub connection_status: ConnectionStatus,
    /// Error message if connection failed
    pub error_message: Option<String>,
    /// Integration metadata (JSON)
    pub metadata: String,
    /// User who created this integration
    pub created_by: Uuid,
    /// Integration creation timestamp
    pub created_at: DateTime<Utc>,
    /// Integration last updated timestamp
    pub updated_at: DateTime<Utc>,
}

/// Integration type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum IntegrationType {
    /// Insurance portal integration
    InsurancePortal,
    /// EHR system integration
    EHR,
    /// Email service integration
    EmailService,
    /// SMS service integration
    SMSService,
    /// Calendar integration
    Calendar,
    /// File storage integration
    FileStorage,
    /// Search engine integration
    SearchEngine,
    /// CDN integration
    CDN,
    /// Webhook integration
    Webhook,
    /// Custom integration
    Custom(String),
}

impl IntegrationType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            IntegrationType::InsurancePortal => "insurance_portal".to_string(),
            IntegrationType::EHR => "ehr".to_string(),
            IntegrationType::EmailService => "email_service".to_string(),
            IntegrationType::SMSService => "sms_service".to_string(),
            IntegrationType::Calendar => "calendar".to_string(),
            IntegrationType::FileStorage => "file_storage".to_string(),
            IntegrationType::SearchEngine => "search_engine".to_string(),
            IntegrationType::CDN => "cdn".to_string(),
            IntegrationType::Webhook => "webhook".to_string(),
            IntegrationType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "insurance_portal" => Some(IntegrationType::InsurancePortal),
            "ehr" => Some(IntegrationType::EHR),
            "email_service" => Some(IntegrationType::EmailService),
            "sms_service" => Some(IntegrationType::SMSService),
            "calendar" => Some(IntegrationType::Calendar),
            "file_storage" => Some(IntegrationType::FileStorage),
            "search_engine" => Some(IntegrationType::SearchEngine),
            "cdn" => Some(IntegrationType::CDN),
            "webhook" => Some(IntegrationType::Webhook),
            s if s.starts_with("custom:") => Some(IntegrationType::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Check if integration type involves PHI
    pub fn involves_phi(&self) -> bool {
        matches!(self,
            IntegrationType::InsurancePortal |
            IntegrationType::EHR |
            IntegrationType::EmailService |
            IntegrationType::SMSService
        )
    }
}

/// Connection status enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum ConnectionStatus {
    /// Connection is healthy
    Connected,
    /// Connection is disconnected
    Disconnected,
    /// Connection is experiencing issues
    Degraded,
    /// Connection has failed
    Failed,
    /// Connection is being tested
    Testing,
}

impl ConnectionStatus {
    /// Convert to string representation
    pub fn as_str(&self) -> &'static str {
        match self {
            ConnectionStatus::Connected => "connected",
            ConnectionStatus::Disconnected => "disconnected",
            ConnectionStatus::Degraded => "degraded",
            ConnectionStatus::Failed => "failed",
            ConnectionStatus::Testing => "testing",
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "connected" => Some(ConnectionStatus::Connected),
            "disconnected" => Some(ConnectionStatus::Disconnected),
            "degraded" => Some(ConnectionStatus::Degraded),
            "failed" => Some(ConnectionStatus::Failed),
            "testing" => Some(ConnectionStatus::Testing),
            _ => None,
        }
    }

    /// Check if connection is healthy
    pub fn is_healthy(&self) -> bool {
        matches!(self, ConnectionStatus::Connected)
    }
}

/// File storage configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileStorage {
    /// Unique storage identifier
    pub id: Uuid,
    /// Practice this storage belongs to
    pub practice_id: Uuid,
    /// Storage type
    pub storage_type: StorageType,
    /// Storage name
    pub name: String,
    /// Storage configuration (encrypted)
    pub configuration: EncryptedPhi,
    /// Whether storage is active
    pub is_active: bool,
    /// Whether this is the default storage
    pub is_default: bool,
    /// Storage capacity in bytes
    pub capacity_bytes: Option<u64>,
    /// Used storage in bytes
    pub used_bytes: u64,
    /// Storage metadata (JSON)
    pub metadata: String,
    /// Storage creation timestamp
    pub created_at: DateTime<Utc>,
    /// Storage last updated timestamp
    pub updated_at: DateTime<Utc>,
}

/// Storage type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum StorageType {
    /// AWS S3
    S3,
    /// MinIO
    MinIO,
    /// Azure Blob Storage
    AzureBlob,
    /// Google Cloud Storage
    GoogleCloud,
    /// Local file system
    Local,
    /// Custom storage
    Custom(String),
}

impl StorageType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            StorageType::S3 => "s3".to_string(),
            StorageType::MinIO => "minio".to_string(),
            StorageType::AzureBlob => "azure_blob".to_string(),
            StorageType::GoogleCloud => "google_cloud".to_string(),
            StorageType::Local => "local".to_string(),
            StorageType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "s3" => Some(StorageType::S3),
            "minio" => Some(StorageType::MinIO),
            "azure_blob" => Some(StorageType::AzureBlob),
            "google_cloud" => Some(StorageType::GoogleCloud),
            "local" => Some(StorageType::Local),
            s if s.starts_with("custom:") => Some(StorageType::Custom(s[7..].to_string())),
            _ => None,
        }
    }
}

/// Search index configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchIndex {
    /// Unique index identifier
    pub id: Uuid,
    /// Practice this index belongs to
    pub practice_id: Uuid,
    /// Index name
    pub name: String,
    /// Index type
    pub index_type: IndexType,
    /// Entity types included in this index
    pub entity_types: Vec<EntityType>,
    /// Index configuration (JSON)
    pub configuration: String,
    /// Whether index is active
    pub is_active: bool,
    /// Last indexing timestamp
    pub last_indexed_at: Option<DateTime<Utc>>,
    /// Number of documents in index
    pub document_count: u64,
    /// Index size in bytes
    pub size_bytes: u64,
    /// Index metadata (JSON)
    pub metadata: String,
    /// Index creation timestamp
    pub created_at: DateTime<Utc>,
    /// Index last updated timestamp
    pub updated_at: DateTime<Utc>,
}

/// Index type enumeration
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum IndexType {
    /// Full-text search index
    FullText,
    /// Faceted search index
    Faceted,
    /// Autocomplete index
    Autocomplete,
    /// Analytics index
    Analytics,
    /// Custom index type
    Custom(String),
}

impl IndexType {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            IndexType::FullText => "full_text".to_string(),
            IndexType::Faceted => "faceted".to_string(),
            IndexType::Autocomplete => "autocomplete".to_string(),
            IndexType::Analytics => "analytics".to_string(),
            IndexType::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "full_text" => Some(IndexType::FullText),
            "faceted" => Some(IndexType::Faceted),
            "autocomplete" => Some(IndexType::Autocomplete),
            "analytics" => Some(IndexType::Analytics),
            s if s.starts_with("custom:") => Some(IndexType::Custom(s[7..].to_string())),
            _ => None,
        }
    }
}

/// Search query log for analytics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchQuery {
    /// Unique query identifier
    pub id: Uuid,
    /// Practice this query belongs to
    pub practice_id: Uuid,
    /// User who performed the search
    pub user_id: Uuid,
    /// Search query text
    pub query_text: String,
    /// Search filters applied
    pub filters: String, // JSON
    /// Number of results returned
    pub result_count: u32,
    /// Query processing time in milliseconds
    pub processing_time_ms: u32,
    /// Whether query contained PHI
    pub contains_phi: bool,
    /// Search index used
    pub index_name: String,
    /// Query metadata (JSON)
    pub metadata: String,
    /// Query timestamp
    pub timestamp: DateTime<Utc>,
}

/// Webhook configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Webhook {
    /// Unique webhook identifier
    pub id: Uuid,
    /// Practice this webhook belongs to
    pub practice_id: Uuid,
    /// Webhook name
    pub name: String,
    /// Webhook URL
    pub url: String,
    /// HTTP method for webhook
    pub method: HttpMethod,
    /// Webhook headers (JSON)
    pub headers: String,
    /// Events that trigger this webhook
    pub events: Vec<WebhookEvent>,
    /// Whether webhook is active
    pub is_active: bool,
    /// Webhook secret for signature verification
    pub secret: Option<String>,
    /// Retry configuration
    pub retry_config: WebhookRetryConfig,
    /// Last successful delivery
    pub last_successful_delivery: Option<DateTime<Utc>>,
    /// Last delivery attempt
    pub last_delivery_attempt: Option<DateTime<Utc>>,
    /// Webhook metadata (JSON)
    pub metadata: String,
    /// User who created this webhook
    pub created_by: Uuid,
    /// Webhook creation timestamp
    pub created_at: DateTime<Utc>,
    /// Webhook last updated timestamp
    pub updated_at: DateTime<Utc>,
}

/// Webhook event enumeration
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum WebhookEvent {
    /// Prior authorization status change
    PriorAuthStatusChange,
    /// Patient created
    PatientCreated,
    /// Patient updated
    PatientUpdated,
    /// Document uploaded
    DocumentUploaded,
    /// Communication sent
    CommunicationSent,
    /// Reminder triggered
    ReminderTriggered,
    /// Workflow state change
    WorkflowStateChange,
    /// Compliance violation detected
    ComplianceViolation,
    /// Custom event
    Custom(String),
}

impl WebhookEvent {
    /// Convert to string representation
    pub fn as_str(&self) -> String {
        match self {
            WebhookEvent::PriorAuthStatusChange => "prior_auth_status_change".to_string(),
            WebhookEvent::PatientCreated => "patient_created".to_string(),
            WebhookEvent::PatientUpdated => "patient_updated".to_string(),
            WebhookEvent::DocumentUploaded => "document_uploaded".to_string(),
            WebhookEvent::CommunicationSent => "communication_sent".to_string(),
            WebhookEvent::ReminderTriggered => "reminder_triggered".to_string(),
            WebhookEvent::WorkflowStateChange => "workflow_state_change".to_string(),
            WebhookEvent::ComplianceViolation => "compliance_violation".to_string(),
            WebhookEvent::Custom(ref s) => format!("custom:{}", s),
        }
    }

    /// Parse from string representation
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "prior_auth_status_change" => Some(WebhookEvent::PriorAuthStatusChange),
            "patient_created" => Some(WebhookEvent::PatientCreated),
            "patient_updated" => Some(WebhookEvent::PatientUpdated),
            "document_uploaded" => Some(WebhookEvent::DocumentUploaded),
            "communication_sent" => Some(WebhookEvent::CommunicationSent),
            "reminder_triggered" => Some(WebhookEvent::ReminderTriggered),
            "workflow_state_change" => Some(WebhookEvent::WorkflowStateChange),
            "compliance_violation" => Some(WebhookEvent::ComplianceViolation),
            s if s.starts_with("custom:") => Some(WebhookEvent::Custom(s[7..].to_string())),
            _ => None,
        }
    }

    /// Check if event involves PHI
    pub fn involves_phi(&self) -> bool {
        matches!(self,
            WebhookEvent::PatientCreated |
            WebhookEvent::PatientUpdated |
            WebhookEvent::PriorAuthStatusChange |
            WebhookEvent::CommunicationSent
        )
    }
}

/// Webhook retry configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebhookRetryConfig {
    /// Maximum number of retries
    pub max_retries: u32,
    /// Initial retry delay in seconds
    pub initial_delay_seconds: u32,
    /// Maximum retry delay in seconds
    pub max_delay_seconds: u32,
    /// Backoff multiplier
    pub backoff_multiplier: f32,
}

impl Default for WebhookRetryConfig {
    fn default() -> Self {
        Self {
            max_retries: 3,
            initial_delay_seconds: 1,
            max_delay_seconds: 300,
            backoff_multiplier: 2.0,
        }
    }
}

/// Webhook delivery log
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebhookDelivery {
    /// Unique delivery identifier
    pub id: Uuid,
    /// Webhook that was triggered
    pub webhook_id: Uuid,
    /// Practice this delivery belongs to
    pub practice_id: Uuid,
    /// Event that triggered the webhook
    pub event: WebhookEvent,
    /// Payload sent to webhook
    pub payload: String, // JSON
    /// HTTP status code received
    pub status_code: Option<u16>,
    /// Response body received
    pub response_body: Option<String>,
    /// Error message if delivery failed
    pub error_message: Option<String>,
    /// Number of retry attempts
    pub retry_count: u32,
    /// Whether delivery was successful
    pub success: bool,
    /// Delivery timestamp
    pub timestamp: DateTime<Utc>,
    /// Next retry timestamp (if applicable)
    pub next_retry_at: Option<DateTime<Utc>>,
}

impl WebhookDelivery {
    /// Create a new webhook delivery
    pub fn new(
        webhook_id: Uuid,
        practice_id: Uuid,
        event: WebhookEvent,
        payload: String,
    ) -> Self {
        Self {
            id: Uuid::new_v4(),
            webhook_id,
            practice_id,
            event,
            payload,
            status_code: None,
            response_body: None,
            error_message: None,
            retry_count: 0,
            success: false,
            timestamp: Utc::now(),
            next_retry_at: None,
        }
    }

    /// Mark delivery as successful
    pub fn mark_success(&mut self, status_code: u16, response_body: Option<String>) {
        self.status_code = Some(status_code);
        self.response_body = response_body;
        self.success = true;
        self.next_retry_at = None;
    }

    /// Mark delivery as failed
    pub fn mark_failure(&mut self, error_message: String, next_retry_at: Option<DateTime<Utc>>) {
        self.error_message = Some(error_message);
        self.retry_count += 1;
        self.next_retry_at = next_retry_at;
    }
}
