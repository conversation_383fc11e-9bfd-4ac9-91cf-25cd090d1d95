//! Business logic services for AuthTracker

use crate::models::{Practice, User, UserActivity, UserRole, UserStatus, PracticeStatus, ActivityType,
    Patient, PatientStatus, PatientSummary, Gender, EncryptedPhi, PhiType, PriorAuth, PriorAuthStatus,
    PriorAuthPriority, PriorAuthSummary, UrgencyLevel, Document, DocumentType, DocumentClassification,
    DocumentSummary, VirusScanStatus, Communication, CommunicationType, CommunicationDirection,
    CommunicationOutcome, CommunicationPriority, CommunicationSummary, CommunicationParticipant,
    InsuranceRepresentative, InsuranceRepSummary, CommunicationTemplate, TemplateCategory,
    CommunicationTemplateSummary, Reminder, ReminderType, ReminderStatus, ReminderPriority,
    ReminderSummary, EntityType, Notification, NotificationType, NotificationChannel, DeliveryStatus,
    WorkflowState, WorkflowDefinition, StateTransition, PendingAction, ActionType, ActionStatus,
    RecurrencePattern, NotificationPreferences, Template, TemplateType, TemplateSummary, ApprovalStatus,
    TemplateVersion, TemplateUsage, UsageContext, ContextType, AnalyticsMetric, MetricType,
    AggregationPeriod, AnalyticsMetricSummary, Report, ReportType, ReportFormat, ReportStatus,
    ReportSummary, ComplianceViolation, ComplianceViolationType, ComplianceSeverity, ComplianceStatus,
    ComplianceViolationSummary, PerformanceMetric, PerformanceMetricType, PerformanceMetricSummary,
    Dashboard, DashboardSummary};
use crate::repositories::{PracticeRepository, UserRepository, UserActivityRepository, PatientRepository,
    PriorAuthRepository, DocumentRepository, CommunicationRepository, InsuranceRepRepository,
    CommunicationTemplateRepository, ReminderRepository, NotificationRepository, WorkflowRepository,
    TemplateRepository, TemplateVersionRepository, TemplateUsageRepository, AnalyticsRepository,
    ReportRepository, ComplianceRepository, PerformanceRepository, DashboardRepository};
// Removed unused async_trait import
use chrono::{DateTime, Utc, Timelike};
use octave_core::error::{OctaveError, OctaveResult};
use std::collections::HashMap;
use uuid::Uuid;

/// Practice onboarding workflow data
#[derive(Debug, Clone)]
pub struct OnboardingData {
    /// Practice information
    pub practice_name: String,
    pub practice_type: String,
    pub email: String,
    pub phone: Option<String>,
    pub address: Option<String>,
    pub tax_id: Option<String>,
    pub npi: Option<String>,

    /// Primary admin user information
    pub admin_first_name: String,
    pub admin_last_name: String,
    pub admin_email: String,
    pub admin_title: Option<String>,

    /// Initial settings
    pub initial_settings: HashMap<String, String>,
}

/// Onboarding step status
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum OnboardingStep {
    PracticeInfo,
    AdminUser,
    Settings,
    Verification,
    Complete,
}

/// Onboarding workflow result
#[derive(Debug, Clone)]
pub struct OnboardingResult {
    pub practice: Practice,
    pub admin_user: User,
    pub onboarding_complete: bool,
    pub next_steps: Vec<String>,
}

/// Practice management service
pub struct PracticeService<P: PracticeRepository> {
    practice_repo: P,
}

impl<P: PracticeRepository> PracticeService<P> {
    /// Create a new practice service
    pub fn new(practice_repo: P) -> Self {
        Self { practice_repo }
    }

    /// Create a new practice
    pub async fn create_practice(
        &self,
        name: String,
        practice_type: String,
        email: String,
        tax_id: Option<String>,
        npi: Option<String>,
    ) -> OctaveResult<Practice> {
        // Validate email is unique
        if let Some(_) = self.practice_repo.get_by_email(&email).await? {
            return Err(OctaveError::validation("email", "Email already exists"));
        }

        // Validate NPI is unique if provided
        if let Some(ref npi_value) = npi {
            if let Some(_) = self.practice_repo.get_by_npi(npi_value).await? {
                return Err(OctaveError::validation("npi", "NPI already exists"));
            }
        }

        let mut practice = Practice::new(name, practice_type, email);
        practice.tax_id = tax_id;
        practice.npi = npi;

        self.practice_repo.create(practice).await
    }

    /// Get practice by ID
    pub async fn get_practice(&self, id: Uuid) -> OctaveResult<Option<Practice>> {
        self.practice_repo.get_by_id(id).await
    }

    /// Update practice
    pub async fn update_practice(&self, mut practice: Practice) -> OctaveResult<Practice> {
        practice.updated_at = Utc::now();
        self.practice_repo.update(practice).await
    }

    /// Activate practice
    pub async fn activate_practice(&self, id: Uuid) -> OctaveResult<Practice> {
        let mut practice = self.practice_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("practice_id", "Practice not found"))?;
        
        practice.activate();
        self.practice_repo.update(practice).await
    }

    /// Suspend practice
    pub async fn suspend_practice(&self, id: Uuid) -> OctaveResult<Practice> {
        let mut practice = self.practice_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("practice_id", "Practice not found"))?;
        
        practice.suspend();
        self.practice_repo.update(practice).await
    }

    /// Update practice settings
    pub async fn update_practice_settings(
        &self,
        id: Uuid,
        settings: HashMap<String, String>,
    ) -> OctaveResult<Practice> {
        let mut practice = self.practice_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("practice_id", "Practice not found"))?;
        
        practice.set_settings(settings);
        self.practice_repo.update(practice).await
    }

    /// Search practices by name
    pub async fn search_practices(&self, name: &str) -> OctaveResult<Vec<Practice>> {
        self.practice_repo.search_by_name(name).await
    }

    /// Get practices by status
    pub async fn get_practices_by_status(&self, status: PracticeStatus) -> OctaveResult<Vec<Practice>> {
        self.practice_repo.get_by_status(status).await
    }
}

/// User management service
pub struct UserService<U: UserRepository, A: UserActivityRepository> {
    user_repo: U,
    activity_repo: A,
}

impl<U: UserRepository, A: UserActivityRepository> UserService<U, A> {
    /// Create a new user service
    pub fn new(user_repo: U, activity_repo: A) -> Self {
        Self { user_repo, activity_repo }
    }

    /// Create a new user (send invitation)
    pub async fn create_user(
        &self,
        practice_id: Uuid,
        email: String,
        first_name: String,
        last_name: String,
        role: UserRole,
        title: Option<String>,
        department: Option<String>,
    ) -> OctaveResult<User> {
        // Validate email is unique
        if let Some(_) = self.user_repo.get_by_email(&email).await? {
            return Err(OctaveError::validation("email", "Email already exists"));
        }

        let mut user = User::new(practice_id, email, first_name, last_name, role);
        user.title = title;
        user.department = department;

        let created_user = self.user_repo.create(user).await?;

        // Log user creation activity
        let activity = UserActivity::new(
            created_user.id,
            practice_id,
            ActivityType::UserManagement,
            format!("User {} invited", created_user.full_name()),
        );
        self.activity_repo.create(activity).await?;

        Ok(created_user)
    }

    /// Accept user invitation and activate account
    pub async fn accept_invitation(
        &self,
        invitation_token: &str,
        password_hash: String,
    ) -> OctaveResult<User> {
        let mut user = self.user_repo.get_by_invitation_token(invitation_token).await?
            .ok_or_else(|| OctaveError::validation("token", "Invalid invitation token"))?;

        if !user.is_invitation_valid() {
            return Err(OctaveError::validation("token", "Invitation token has expired"));
        }

        user.activate(password_hash);
        let updated_user = self.user_repo.update(user).await?;

        // Log activation activity
        let activity = UserActivity::new(
            updated_user.id,
            updated_user.practice_id,
            ActivityType::Login,
            "User activated account".to_string(),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_user)
    }

    /// Authenticate user login
    pub async fn authenticate_user(
        &self,
        email: &str,
        password_hash: &str,
        ip_address: Option<String>,
    ) -> OctaveResult<User> {
        let mut user = self.user_repo.get_by_email(email).await?
            .ok_or_else(|| OctaveError::authentication("Invalid credentials"))?;

        if !user.can_login() {
            return Err(OctaveError::authentication("Account not active"));
        }

        // In a real implementation, you would verify the password hash here
        if user.password_hash.as_ref() != Some(&password_hash.to_string()) {
            // Log failed login attempt
            let activity = UserActivity::failed(
                user.id,
                user.practice_id,
                ActivityType::Login,
                "Failed login attempt".to_string(),
                "Invalid password".to_string(),
            ).with_session_info(None, ip_address.clone(), None);
            self.activity_repo.create(activity).await?;

            return Err(OctaveError::authentication("Invalid credentials"));
        }

        user.update_last_login();
        let updated_user = self.user_repo.update(user).await?;

        // Log successful login
        let activity = UserActivity::login(updated_user.id, updated_user.practice_id, ip_address);
        self.activity_repo.create(activity).await?;

        Ok(updated_user)
    }

    /// Update user profile
    pub async fn update_user_profile(
        &self,
        user_id: Uuid,
        first_name: Option<String>,
        last_name: Option<String>,
        phone: Option<String>,
        title: Option<String>,
        department: Option<String>,
    ) -> OctaveResult<User> {
        let mut user = self.user_repo.get_by_id(user_id).await?
            .ok_or_else(|| OctaveError::validation("user_id", "User not found"))?;

        let mut changes = Vec::new();

        if let Some(name) = first_name {
            user.first_name = name;
            changes.push("first_name");
        }
        if let Some(name) = last_name {
            user.last_name = name;
            changes.push("last_name");
        }
        if let Some(phone_num) = phone {
            user.phone = Some(phone_num);
            changes.push("phone");
        }
        if let Some(user_title) = title {
            user.title = Some(user_title);
            changes.push("title");
        }
        if let Some(dept) = department {
            user.department = Some(dept);
            changes.push("department");
        }

        user.updated_at = Utc::now();
        let updated_user = self.user_repo.update(user).await?;

        // Log profile update activity
        let activity = UserActivity::new(
            updated_user.id,
            updated_user.practice_id,
            ActivityType::ProfileUpdate,
            format!("Profile updated: {}", changes.join(", ")),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_user)
    }

    /// Get user by ID
    pub async fn get_user(&self, id: Uuid) -> OctaveResult<Option<User>> {
        self.user_repo.get_by_id(id).await
    }

    /// Get users by practice
    pub async fn get_practice_users(&self, practice_id: Uuid) -> OctaveResult<Vec<User>> {
        self.user_repo.get_by_practice(practice_id).await
    }

    /// Suspend user
    pub async fn suspend_user(&self, user_id: Uuid, suspended_by: Uuid) -> OctaveResult<User> {
        let mut user = self.user_repo.get_by_id(user_id).await?
            .ok_or_else(|| OctaveError::validation("user_id", "User not found"))?;

        user.suspend();
        let updated_user = self.user_repo.update(user).await?;

        // Log suspension activity
        let activity = UserActivity::new(
            suspended_by,
            updated_user.practice_id,
            ActivityType::UserManagement,
            format!("User {} suspended", updated_user.full_name()),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_user)
    }

    /// Generate password reset token
    pub async fn generate_password_reset_token(&self, email: &str) -> OctaveResult<String> {
        let mut user = self.user_repo.get_by_email(email).await?
            .ok_or_else(|| OctaveError::validation("user_id", "User not found"))?;

        let token = user.generate_password_reset_token();
        self.user_repo.update(user).await?;

        Ok(token)
    }

    /// Reset password using token
    pub async fn reset_password(
        &self,
        token: &str,
        new_password_hash: String,
    ) -> OctaveResult<User> {
        let mut user = self.user_repo.get_by_password_reset_token(token).await?
            .ok_or_else(|| OctaveError::validation("token", "Invalid reset token"))?;

        if !user.is_password_reset_token_valid(token) {
            return Err(OctaveError::validation("token", "Reset token has expired"));
        }

        user.password_hash = Some(new_password_hash);
        user.clear_password_reset_token();
        let updated_user = self.user_repo.update(user).await?;

        // Log password change activity
        let activity = UserActivity::new(
            updated_user.id,
            updated_user.practice_id,
            ActivityType::PasswordChange,
            "Password reset via token".to_string(),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_user)
    }

    /// Update user activity
    pub async fn update_user_activity(&self, user_id: Uuid) -> OctaveResult<()> {
        self.user_repo.update_last_activity(user_id).await?;
        Ok(())
    }

    /// Search users by name
    pub async fn search_users(&self, practice_id: Uuid, name: &str) -> OctaveResult<Vec<User>> {
        self.user_repo.search_by_name(practice_id, name).await
    }
}

/// Practice onboarding service
pub struct OnboardingService<P: PracticeRepository, U: UserRepository, A: UserActivityRepository> {
    practice_service: PracticeService<P>,
    user_service: UserService<U, A>,
}

impl<P: PracticeRepository, U: UserRepository, A: UserActivityRepository> OnboardingService<P, U, A> {
    /// Create a new onboarding service
    pub fn new(practice_repo: P, user_repo: U, activity_repo: A) -> Self {
        Self {
            practice_service: PracticeService::new(practice_repo),
            user_service: UserService::new(user_repo, activity_repo),
        }
    }

    /// Start practice onboarding workflow
    pub async fn start_onboarding(&self, data: OnboardingData) -> OctaveResult<OnboardingResult> {
        // Step 1: Create practice
        let practice = self.practice_service.create_practice(
            data.practice_name,
            data.practice_type,
            data.email,
            data.tax_id,
            data.npi,
        ).await?;

        // Step 2: Create admin user
        let admin_user = self.user_service.create_user(
            practice.id,
            data.admin_email,
            data.admin_first_name,
            data.admin_last_name,
            UserRole::PracticeAdmin,
            data.admin_title,
            Some("Administration".to_string()),
        ).await?;

        // Step 3: Apply initial settings
        let mut settings = data.initial_settings;
        settings.insert("onboarding_completed".to_string(), "false".to_string());
        settings.insert("onboarding_step".to_string(), "verification".to_string());

        let updated_practice = self.practice_service.update_practice_settings(
            practice.id,
            settings,
        ).await?;

        // Generate next steps
        let next_steps = vec![
            "Check your email for the admin user invitation".to_string(),
            "Complete the admin user account setup".to_string(),
            "Configure practice-specific settings".to_string(),
            "Invite additional users to your practice".to_string(),
        ];

        Ok(OnboardingResult {
            practice: updated_practice,
            admin_user,
            onboarding_complete: false,
            next_steps,
        })
    }

    /// Complete onboarding workflow
    pub async fn complete_onboarding(&self, practice_id: Uuid) -> OctaveResult<Practice> {
        let mut settings = HashMap::new();
        settings.insert("onboarding_completed".to_string(), "true".to_string());
        settings.insert("onboarding_step".to_string(), "complete".to_string());
        settings.insert("onboarding_completed_at".to_string(), Utc::now().to_rfc3339());

        self.practice_service.update_practice_settings(practice_id, settings).await
    }

    /// Get onboarding status
    pub async fn get_onboarding_status(&self, practice_id: Uuid) -> OctaveResult<OnboardingStep> {
        let practice = self.practice_service.get_practice(practice_id).await?
            .ok_or_else(|| OctaveError::validation("practice_id", "Practice not found"))?;

        let settings = practice.get_settings();

        if settings.get("onboarding_completed").map_or(false, |v| v == "true") {
            return Ok(OnboardingStep::Complete);
        }

        match settings.get("onboarding_step").map(|s| s.as_str()) {
            Some("practice_info") => Ok(OnboardingStep::PracticeInfo),
            Some("admin_user") => Ok(OnboardingStep::AdminUser),
            Some("settings") => Ok(OnboardingStep::Settings),
            Some("verification") => Ok(OnboardingStep::Verification),
            _ => Ok(OnboardingStep::PracticeInfo),
        }
    }

    /// Update onboarding step
    pub async fn update_onboarding_step(
        &self,
        practice_id: Uuid,
        step: OnboardingStep,
    ) -> OctaveResult<Practice> {
        let step_name = match step {
            OnboardingStep::PracticeInfo => "practice_info",
            OnboardingStep::AdminUser => "admin_user",
            OnboardingStep::Settings => "settings",
            OnboardingStep::Verification => "verification",
            OnboardingStep::Complete => "complete",
        };

        let mut settings = HashMap::new();
        settings.insert("onboarding_step".to_string(), step_name.to_string());

        if step == OnboardingStep::Complete {
            settings.insert("onboarding_completed".to_string(), "true".to_string());
            settings.insert("onboarding_completed_at".to_string(), Utc::now().to_rfc3339());
        }

        self.practice_service.update_practice_settings(practice_id, settings).await
    }
}

/// User invitation data
#[derive(Debug, Clone)]
pub struct InvitationData {
    pub email: String,
    pub first_name: String,
    pub last_name: String,
    pub role: UserRole,
    pub title: Option<String>,
    pub department: Option<String>,
    pub send_email: bool,
}

/// Bulk invitation result
#[derive(Debug, Clone)]
pub struct BulkInvitationResult {
    pub successful_invitations: Vec<User>,
    pub failed_invitations: Vec<(InvitationData, String)>,
    pub total_sent: usize,
    pub total_failed: usize,
}

/// Enhanced user invitation service
pub struct InvitationService<U: UserRepository, A: UserActivityRepository> {
    user_service: UserService<U, A>,
}

impl<U: UserRepository, A: UserActivityRepository> InvitationService<U, A> {
    /// Create a new invitation service
    pub fn new(user_repo: U, activity_repo: A) -> Self {
        Self {
            user_service: UserService::new(user_repo, activity_repo),
        }
    }

    /// Send single user invitation
    pub async fn send_invitation(
        &self,
        practice_id: Uuid,
        invitation_data: InvitationData,
        _invited_by: Uuid,
    ) -> OctaveResult<User> {
        let user = self.user_service.create_user(
            practice_id,
            invitation_data.email,
            invitation_data.first_name,
            invitation_data.last_name,
            invitation_data.role,
            invitation_data.title,
            invitation_data.department,
        ).await?;

        // TODO: Integrate with email service when available
        if invitation_data.send_email {
            self.send_invitation_email(&user).await?;
        }

        Ok(user)
    }

    /// Send bulk user invitations
    pub async fn send_bulk_invitations(
        &self,
        practice_id: Uuid,
        invitations: Vec<InvitationData>,
        invited_by: Uuid,
    ) -> OctaveResult<BulkInvitationResult> {
        let mut successful_invitations = Vec::new();
        let mut failed_invitations = Vec::new();

        for invitation_data in invitations {
            match self.send_invitation(practice_id, invitation_data.clone(), invited_by).await {
                Ok(user) => successful_invitations.push(user),
                Err(error) => failed_invitations.push((invitation_data, error.to_string())),
            }
        }

        Ok(BulkInvitationResult {
            total_sent: successful_invitations.len(),
            total_failed: failed_invitations.len(),
            successful_invitations,
            failed_invitations,
        })
    }

    /// Resend invitation
    pub async fn resend_invitation(&self, user_id: Uuid) -> OctaveResult<User> {
        let user = self.user_service.get_user(user_id).await?
            .ok_or_else(|| OctaveError::validation("user_id", "User not found"))?;

        if user.status != UserStatus::Invited {
            return Err(OctaveError::validation("status", "User is not in invited status"));
        }

        // Generate new invitation token (would need to implement this method)
        let updated_user = user.clone();
        // TODO: Implement generate_invitation_token method on User model
        // For now, we'll just update the user as-is
        let user_with_new_token = self.user_service.user_repo.update(updated_user).await?;

        // Send email with new token
        self.send_invitation_email(&user_with_new_token).await?;

        Ok(user_with_new_token)
    }

    /// Cancel invitation
    pub async fn cancel_invitation(&self, user_id: Uuid, _cancelled_by: Uuid) -> OctaveResult<()> {
        let user = self.user_service.get_user(user_id).await?
            .ok_or_else(|| OctaveError::validation("user_id", "User not found"))?;

        if user.status != UserStatus::Invited {
            return Err(OctaveError::validation("status", "User is not in invited status"));
        }

        // Delete the user record (since they haven't activated yet)
        self.user_service.user_repo.delete(user_id).await?;

        Ok(())
    }

    /// Get pending invitations for practice
    pub async fn get_pending_invitations(&self, practice_id: Uuid) -> OctaveResult<Vec<User>> {
        let users = self.user_service.get_practice_users(practice_id).await?;
        Ok(users.into_iter()
            .filter(|u| u.status == UserStatus::Invited)
            .collect())
    }

    /// Get expired invitations
    pub async fn get_expired_invitations(&self, practice_id: Uuid) -> OctaveResult<Vec<User>> {
        let users = self.user_service.get_practice_users(practice_id).await?;
        Ok(users.into_iter()
            .filter(|u| u.status == UserStatus::Invited && !u.is_invitation_valid())
            .collect())
    }

    /// Clean up expired invitations
    pub async fn cleanup_expired_invitations(&self, practice_id: Uuid) -> OctaveResult<usize> {
        let expired_users = self.get_expired_invitations(practice_id).await?;
        let count = expired_users.len();

        for user in expired_users {
            self.user_service.user_repo.delete(user.id).await?;
        }

        Ok(count)
    }

    /// Send invitation email (placeholder for email service integration)
    async fn send_invitation_email(&self, user: &User) -> OctaveResult<()> {
        // TODO: Integrate with actual email service
        // For now, this is a placeholder that would:
        // 1. Generate invitation email content
        // 2. Include invitation token and setup link
        // 3. Send via email service
        // 4. Log email sending activity

        println!("Sending invitation email to: {} (Token: {:?})",
                user.email, user.invitation_token);

        Ok(())
    }
}

/// User profile preferences
#[derive(Debug, Clone)]
pub struct UserPreferences {
    pub theme: Option<String>,
    pub language: Option<String>,
    pub timezone: Option<String>,
    pub email_notifications: bool,
    pub sms_notifications: bool,
    pub dashboard_layout: Option<String>,
    pub default_view: Option<String>,
}

/// Enhanced user profile service
pub struct ProfileService<U: UserRepository, A: UserActivityRepository> {
    user_service: UserService<U, A>,
}

impl<U: UserRepository, A: UserActivityRepository> ProfileService<U, A> {
    /// Create a new profile service
    pub fn new(user_repo: U, activity_repo: A) -> Self {
        Self {
            user_service: UserService::new(user_repo, activity_repo),
        }
    }

    /// Update user preferences
    pub async fn update_preferences(
        &self,
        user_id: Uuid,
        preferences: UserPreferences,
    ) -> OctaveResult<User> {
        let mut user = self.user_service.get_user(user_id).await?
            .ok_or_else(|| OctaveError::validation("user_id", "User not found"))?;

        let mut prefs_map = user.get_preferences();

        if let Some(theme) = preferences.theme {
            prefs_map.insert("theme".to_string(), theme);
        }
        if let Some(language) = preferences.language {
            prefs_map.insert("language".to_string(), language);
        }
        if let Some(timezone) = preferences.timezone {
            prefs_map.insert("timezone".to_string(), timezone);
        }
        prefs_map.insert("email_notifications".to_string(), preferences.email_notifications.to_string());
        prefs_map.insert("sms_notifications".to_string(), preferences.sms_notifications.to_string());

        if let Some(layout) = preferences.dashboard_layout {
            prefs_map.insert("dashboard_layout".to_string(), layout);
        }
        if let Some(view) = preferences.default_view {
            prefs_map.insert("default_view".to_string(), view);
        }

        user.set_preferences(prefs_map);
        let updated_user = self.user_service.user_repo.update(user).await?;

        // Log preference update
        let activity = UserActivity::new(
            updated_user.id,
            updated_user.practice_id,
            ActivityType::ProfileUpdate,
            "User preferences updated".to_string(),
        );
        self.user_service.activity_repo.create(activity).await?;

        Ok(updated_user)
    }

    /// Update security settings
    pub async fn update_security_settings(
        &self,
        user_id: Uuid,
        require_2fa: bool,
        session_timeout: Option<u32>,
        allowed_ip_ranges: Option<Vec<String>>,
    ) -> OctaveResult<User> {
        let mut user = self.user_service.get_user(user_id).await?
            .ok_or_else(|| OctaveError::validation("user_id", "User not found"))?;

        let mut metadata = user.get_metadata();
        metadata.insert("require_2fa".to_string(), require_2fa.to_string());

        if let Some(timeout) = session_timeout {
            metadata.insert("session_timeout".to_string(), timeout.to_string());
        }

        if let Some(ip_ranges) = allowed_ip_ranges {
            metadata.insert("allowed_ip_ranges".to_string(), ip_ranges.join(","));
        }

        user.set_metadata(metadata);
        let updated_user = self.user_service.user_repo.update(user).await?;

        // Log security settings update
        let activity = UserActivity::new(
            updated_user.id,
            updated_user.practice_id,
            ActivityType::SecuritySettingsChange,
            "Security settings updated".to_string(),
        );
        self.user_service.activity_repo.create(activity).await?;

        Ok(updated_user)
    }

    /// Change user password
    pub async fn change_password(
        &self,
        user_id: Uuid,
        current_password_hash: &str,
        new_password_hash: String,
    ) -> OctaveResult<User> {
        let mut user = self.user_service.get_user(user_id).await?
            .ok_or_else(|| OctaveError::validation("user_id", "User not found"))?;

        // Verify current password
        if user.password_hash.as_ref() != Some(&current_password_hash.to_string()) {
            return Err(OctaveError::authentication("Current password is incorrect"));
        }

        user.password_hash = Some(new_password_hash);
        user.updated_at = Utc::now();
        let updated_user = self.user_service.user_repo.update(user).await?;

        // Log password change
        let activity = UserActivity::new(
            updated_user.id,
            updated_user.practice_id,
            ActivityType::PasswordChange,
            "Password changed by user".to_string(),
        );
        self.user_service.activity_repo.create(activity).await?;

        Ok(updated_user)
    }

    /// Update profile picture (placeholder for file storage integration)
    pub async fn update_profile_picture(
        &self,
        user_id: Uuid,
        picture_url: String,
    ) -> OctaveResult<User> {
        let mut user = self.user_service.get_user(user_id).await?
            .ok_or_else(|| OctaveError::validation("user_id", "User not found"))?;

        let mut metadata = user.get_metadata();
        metadata.insert("profile_picture_url".to_string(), picture_url);
        user.set_metadata(metadata);

        let updated_user = self.user_service.user_repo.update(user).await?;

        // Log profile picture update
        let activity = UserActivity::new(
            updated_user.id,
            updated_user.practice_id,
            ActivityType::ProfileUpdate,
            "Profile picture updated".to_string(),
        );
        self.user_service.activity_repo.create(activity).await?;

        Ok(updated_user)
    }

    /// Get user profile summary
    pub async fn get_profile_summary(&self, user_id: Uuid) -> OctaveResult<UserProfileSummary> {
        let user = self.user_service.get_user(user_id).await?
            .ok_or_else(|| OctaveError::validation("user_id", "User not found"))?;

        let preferences = user.get_preferences();
        let metadata = user.get_metadata();

        Ok(UserProfileSummary {
            id: user.id,
            full_name: user.full_name(),
            email: user.email,
            role: user.role,
            status: user.status,
            title: user.title,
            department: user.department,
            phone: user.phone,
            profile_picture_url: metadata.get("profile_picture_url").cloned(),
            last_login: user.last_login_at,
            last_activity: user.last_activity_at,
            preferences: UserPreferences {
                theme: preferences.get("theme").cloned(),
                language: preferences.get("language").cloned(),
                timezone: preferences.get("timezone").cloned(),
                email_notifications: preferences.get("email_notifications")
                    .map_or(true, |v| v.parse().unwrap_or(true)),
                sms_notifications: preferences.get("sms_notifications")
                    .map_or(false, |v| v.parse().unwrap_or(false)),
                dashboard_layout: preferences.get("dashboard_layout").cloned(),
                default_view: preferences.get("default_view").cloned(),
            },
        })
    }
}

/// User profile summary
#[derive(Debug, Clone)]
pub struct UserProfileSummary {
    pub id: Uuid,
    pub full_name: String,
    pub email: String,
    pub role: UserRole,
    pub status: UserStatus,
    pub title: Option<String>,
    pub department: Option<String>,
    pub phone: Option<String>,
    pub profile_picture_url: Option<String>,
    pub last_login: Option<DateTime<Utc>>,
    pub last_activity: Option<DateTime<Utc>>,
    pub preferences: UserPreferences,
}

/// Activity analytics data
#[derive(Debug, Clone)]
pub struct ActivityAnalytics {
    pub total_activities: usize,
    pub activities_by_type: HashMap<ActivityType, usize>,
    pub activities_by_user: HashMap<Uuid, usize>,
    pub activities_by_day: HashMap<String, usize>,
    pub security_events: usize,
    pub phi_access_events: usize,
    pub most_active_users: Vec<(Uuid, String, usize)>,
    pub peak_activity_hours: Vec<u8>,
}

/// User behavior pattern
#[derive(Debug, Clone)]
pub struct UserBehaviorPattern {
    pub user_id: Uuid,
    pub typical_login_hours: Vec<u8>,
    pub average_session_duration: f64,
    pub common_activities: Vec<ActivityType>,
    pub unusual_activity_detected: bool,
    pub risk_score: f64,
}

/// Activity analytics service
pub struct ActivityAnalyticsService<A: UserActivityRepository> {
    activity_repo: A,
}

impl<A: UserActivityRepository> ActivityAnalyticsService<A> {
    /// Create a new activity analytics service
    pub fn new(activity_repo: A) -> Self {
        Self { activity_repo }
    }

    /// Get practice activity analytics
    pub async fn get_practice_analytics(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> OctaveResult<ActivityAnalytics> {
        let all_activities = self.activity_repo.get_by_practice(practice_id).await?;

        // Filter by date range
        let activities: Vec<_> = all_activities.into_iter()
            .filter(|a| a.created_at >= start_date && a.created_at <= end_date)
            .collect();

        let total_activities = activities.len();
        let mut activities_by_type = HashMap::new();
        let mut activities_by_user = HashMap::new();
        let mut activities_by_day = HashMap::new();
        let mut security_events = 0;
        let mut phi_access_events = 0;

        for activity in &activities {
            *activities_by_type.entry(activity.activity_type.clone()).or_insert(0) += 1;
            *activities_by_user.entry(activity.user_id).or_insert(0) += 1;

            let day_key = activity.created_at.format("%Y-%m-%d").to_string();
            *activities_by_day.entry(day_key).or_insert(0) += 1;

            if activity.activity_type.is_security_related() {
                security_events += 1;
            }
            if activity.activity_type.involves_phi() {
                phi_access_events += 1;
            }
        }

        // Find most active users (top 10)
        let mut user_activity_vec: Vec<_> = activities_by_user.iter().collect();
        user_activity_vec.sort_by(|a, b| b.1.cmp(a.1));
        let most_active_users = user_activity_vec.into_iter()
            .take(10)
            .map(|(user_id, count)| (*user_id, "User Name".to_string(), *count)) // TODO: Get actual user names
            .collect();

        // Calculate peak activity hours
        let mut hour_counts = HashMap::new();
        for activity in &activities {
            let hour = activity.created_at.hour() as u8;
            *hour_counts.entry(hour).or_insert(0) += 1;
        }
        let mut peak_hours: Vec<_> = hour_counts.iter().collect();
        peak_hours.sort_by(|a, b| b.1.cmp(a.1));
        let peak_activity_hours = peak_hours.into_iter()
            .take(3)
            .map(|(hour, _)| *hour)
            .collect();

        Ok(ActivityAnalytics {
            total_activities,
            activities_by_type,
            activities_by_user,
            activities_by_day,
            security_events,
            phi_access_events,
            most_active_users,
            peak_activity_hours,
        })
    }

    /// Analyze user behavior patterns
    pub async fn analyze_user_behavior(
        &self,
        user_id: Uuid,
        days_back: u32,
    ) -> OctaveResult<UserBehaviorPattern> {
        let start_date = Utc::now() - chrono::Duration::days(days_back as i64);
        let all_activities = self.activity_repo.get_by_user(user_id).await?;

        // Filter by date range
        let activities: Vec<_> = all_activities.into_iter()
            .filter(|a| a.created_at >= start_date)
            .collect();

        // Analyze login patterns
        let login_activities: Vec<_> = activities.iter()
            .filter(|a| a.activity_type == ActivityType::Login)
            .collect();

        let mut hour_counts = HashMap::new();
        for activity in &login_activities {
            let hour = activity.created_at.hour() as u8;
            *hour_counts.entry(hour).or_insert(0) += 1;
        }

        let typical_login_hours: Vec<u8> = hour_counts.iter()
            .filter(|(_, &count)| count > 1) // Hours with more than 1 login
            .map(|(&hour, _)| hour)
            .collect();

        // Calculate average session duration (simplified)
        let session_durations: Vec<_> = login_activities.iter()
            .zip(activities.iter().filter(|a| a.activity_type == ActivityType::Logout))
            .map(|(login, logout)| (logout.created_at - login.created_at).num_minutes() as f64)
            .collect();

        let average_session_duration = if session_durations.is_empty() {
            0.0
        } else {
            session_durations.iter().sum::<f64>() / session_durations.len() as f64
        };

        // Find common activities
        let mut activity_counts = HashMap::new();
        for activity in &activities {
            *activity_counts.entry(activity.activity_type.clone()).or_insert(0) += 1;
        }
        let mut common_activities: Vec<_> = activity_counts.iter()
            .filter(|(_, &count)| count > 2)
            .map(|(activity_type, _)| activity_type.clone())
            .collect();
        common_activities.sort_by(|a, b| {
            activity_counts.get(b).unwrap_or(&0).cmp(activity_counts.get(a).unwrap_or(&0))
        });

        // Simple risk scoring based on unusual patterns
        let mut risk_score = 0.0;
        let unusual_activity_detected = false;

        // Check for unusual login times
        if typical_login_hours.iter().any(|&hour| hour < 6 || hour > 22) {
            risk_score += 1.0;
        }

        // Check for excessive activity
        if activities.len() > (days_back as usize * 50) {
            risk_score += 2.0;
        }

        // Check for security-related activities
        let security_activity_count = activities.iter()
            .filter(|a| a.activity_type.is_security_related())
            .count();
        if security_activity_count > 5 {
            risk_score += 1.5;
        }

        Ok(UserBehaviorPattern {
            user_id,
            typical_login_hours,
            average_session_duration,
            common_activities,
            unusual_activity_detected,
            risk_score,
        })
    }

    /// Get security events for practice
    pub async fn get_security_events(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> OctaveResult<Vec<UserActivity>> {
        let all_activities = self.activity_repo.get_by_practice(practice_id).await?;

        let activities: Vec<_> = all_activities.into_iter()
            .filter(|a| a.created_at >= start_date && a.created_at <= end_date)
            .collect();

        Ok(activities.into_iter()
            .filter(|a| a.activity_type.is_security_related())
            .collect())
    }

    /// Get compliance-related activities
    pub async fn get_compliance_activities(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
    ) -> OctaveResult<Vec<UserActivity>> {
        let all_activities = self.activity_repo.get_by_practice(practice_id).await?;

        let activities: Vec<_> = all_activities.into_iter()
            .filter(|a| a.created_at >= start_date && a.created_at <= end_date)
            .collect();

        Ok(activities.into_iter()
            .filter(|a| a.activity_type.involves_phi() || a.activity_type.is_security_related())
            .collect())
    }
}

// ============================================================================
// PATIENT MANAGEMENT SERVICES
// ============================================================================

/// Patient registration data
#[derive(Debug, Clone)]
pub struct PatientRegistrationData {
    pub first_name: String,
    pub last_name: String,
    pub middle_name: Option<String>,
    pub date_of_birth: String,
    pub gender: Gender,
    pub ssn: Option<String>,
    pub phone: Option<String>,
    pub email: Option<String>,
    pub address: Option<PatientAddressData>,
    pub emergency_contact: Option<EmergencyContactData>,
}

/// Patient address data for registration
#[derive(Debug, Clone)]
pub struct PatientAddressData {
    pub street: String,
    pub city: String,
    pub state: String,
    pub zip_code: String,
    pub country: String,
}

/// Emergency contact data for registration
#[derive(Debug, Clone)]
pub struct EmergencyContactData {
    pub name: String,
    pub relationship: String,
    pub phone: String,
    pub email: Option<String>,
}

/// Patient search criteria
#[derive(Debug, Clone)]
pub struct PatientSearchCriteria {
    pub name: Option<String>,
    pub phone: Option<String>,
    pub date_of_birth: Option<String>,
    pub mrn: Option<String>,
    pub status: Option<PatientStatus>,
    pub gender: Option<Gender>,
    pub min_age: Option<u32>,
    pub max_age: Option<u32>,
}

/// Patient service for core patient management
pub struct PatientService<P: PatientRepository, A: UserActivityRepository> {
    patient_repo: P,
    activity_repo: A,
}

impl<P: PatientRepository, A: UserActivityRepository> PatientService<P, A> {
    /// Create a new patient service
    pub fn new(patient_repo: P, activity_repo: A) -> Self {
        Self { patient_repo, activity_repo }
    }

    /// Create a new patient
    pub async fn create_patient(
        &self,
        practice_id: Uuid,
        mrn: String,
        registration_data: PatientRegistrationData,
        created_by: Uuid,
    ) -> OctaveResult<Patient> {
        // Validate MRN is unique within practice
        if let Some(_) = self.patient_repo.get_by_mrn(practice_id, &mrn).await? {
            return Err(OctaveError::validation("mrn", "MRN already exists in this practice"));
        }

        let mut patient = Patient::new(
            practice_id,
            mrn,
            registration_data.first_name,
            registration_data.last_name,
            registration_data.date_of_birth,
            registration_data.gender,
        );

        // Add optional fields
        if let Some(middle_name) = registration_data.middle_name {
            patient.middle_name = Some(EncryptedPhi::new(middle_name, PhiType::Name));
        }

        if let Some(ssn) = registration_data.ssn {
            patient.ssn = Some(EncryptedPhi::new(ssn, PhiType::Ssn));
        }

        if let Some(phone) = registration_data.phone {
            patient.phone = Some(EncryptedPhi::new(phone, PhiType::PhoneNumber));
        }

        if let Some(email) = registration_data.email {
            patient.email = Some(EncryptedPhi::new(email, PhiType::EmailAddress));
        }

        let created_patient = self.patient_repo.create(patient).await?;

        // Log patient creation activity
        let activity = UserActivity::new(
            created_by,
            practice_id,
            ActivityType::PatientManagement,
            format!("Patient created: MRN {}", created_patient.mrn),
        );
        self.activity_repo.create(activity).await?;

        Ok(created_patient)
    }

    /// Get patient by ID with access logging
    pub async fn get_patient(&self, id: Uuid, accessed_by: Uuid) -> OctaveResult<Option<Patient>> {
        let patient = self.patient_repo.get_by_id(id).await?;

        if let Some(ref p) = patient {
            // Update last accessed timestamp
            self.patient_repo.update_last_accessed(id).await?;

            // Log PHI access
            let activity = UserActivity::new(
                accessed_by,
                p.practice_id,
                ActivityType::PhiAccess,
                format!("Patient record accessed: MRN {}", p.mrn),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(patient)
    }

    /// Search patients with privacy protection
    pub async fn search_patients(
        &self,
        practice_id: Uuid,
        criteria: PatientSearchCriteria,
        searched_by: Uuid,
    ) -> OctaveResult<Vec<PatientSummary>> {
        let mut results = Vec::new();

        // Search by different criteria
        if let Some(name) = criteria.name {
            let patients = self.patient_repo.search_by_name(practice_id, &name).await?;
            results.extend(patients);
        }

        if let Some(phone) = criteria.phone {
            let patients = self.patient_repo.search_by_phone(practice_id, &phone).await?;
            results.extend(patients);
        }

        if let Some(dob) = criteria.date_of_birth {
            let patients = self.patient_repo.search_by_dob(practice_id, &dob).await?;
            results.extend(patients);
        }

        if let Some(mrn) = criteria.mrn {
            if let Some(patient) = self.patient_repo.get_by_mrn(practice_id, &mrn).await? {
                results.push(patient);
            }
        }

        // Remove duplicates and apply filters
        results.sort_by_key(|p| p.id);
        results.dedup_by_key(|p| p.id);

        // Apply status filter
        if let Some(status) = criteria.status {
            results.retain(|p| p.status == status);
        }

        // Apply gender filter
        if let Some(gender) = criteria.gender {
            results.retain(|p| p.gender == gender);
        }

        // Convert to summaries (removes PHI)
        let summaries: Vec<PatientSummary> = results.into_iter()
            .map(|p| p.summary())
            .collect();

        // Log search activity
        let activity = UserActivity::new(
            searched_by,
            practice_id,
            ActivityType::PatientManagement,
            format!("Patient search performed, {} results", summaries.len()),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Update patient information
    pub async fn update_patient(
        &self,
        mut patient: Patient,
        updated_by: Uuid,
    ) -> OctaveResult<Patient> {
        patient.updated_at = Utc::now();
        let updated_patient = self.patient_repo.update(patient).await?;

        // Log update activity
        let activity = UserActivity::new(
            updated_by,
            updated_patient.practice_id,
            ActivityType::PatientManagement,
            format!("Patient updated: MRN {}", updated_patient.mrn),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_patient)
    }

    /// Get patients by practice
    pub async fn get_practice_patients(&self, practice_id: Uuid) -> OctaveResult<Vec<PatientSummary>> {
        let summaries = self.patient_repo.get_summaries(practice_id).await?;
        Ok(summaries)
    }

    /// Find potential duplicate patients
    pub async fn find_duplicates(
        &self,
        practice_id: Uuid,
        patient_id: Uuid,
    ) -> OctaveResult<Vec<Patient>> {
        self.patient_repo.find_potential_duplicates(practice_id, patient_id).await
    }
}

/// Patient registration service for multi-step registration workflow
pub struct PatientRegistrationService<P: PatientRepository, A: UserActivityRepository> {
    patient_service: PatientService<P, A>,
}

impl<P: PatientRepository, A: UserActivityRepository> PatientRegistrationService<P, A> {
    /// Create a new patient registration service
    pub fn new(patient_repo: P, activity_repo: A) -> Self {
        Self {
            patient_service: PatientService::new(patient_repo, activity_repo),
        }
    }

    /// Start patient registration workflow
    pub async fn start_registration(
        &self,
        practice_id: Uuid,
        registration_data: PatientRegistrationData,
        registered_by: Uuid,
    ) -> OctaveResult<PatientRegistrationResult> {
        // Step 1: Validate registration data
        self.validate_registration_data(&registration_data)?;

        // Step 2: Generate MRN
        let mrn = self.generate_mrn(practice_id).await?;

        // Step 3: Check for potential duplicates
        let potential_duplicates = self.check_for_duplicates(practice_id, &registration_data).await?;

        if !potential_duplicates.is_empty() {
            return Ok(PatientRegistrationResult {
                status: RegistrationStatus::DuplicatesFound,
                patient: None,
                potential_duplicates,
                next_steps: vec![
                    "Review potential duplicate patients".to_string(),
                    "Confirm this is a new patient or merge with existing".to_string(),
                ],
            });
        }

        // Step 4: Create patient
        let patient = self.patient_service.create_patient(
            practice_id,
            mrn,
            registration_data,
            registered_by,
        ).await?;

        Ok(PatientRegistrationResult {
            status: RegistrationStatus::Completed,
            patient: Some(patient),
            potential_duplicates: Vec::new(),
            next_steps: vec![
                "Patient registration completed successfully".to_string(),
                "Add insurance information if available".to_string(),
                "Schedule initial appointment".to_string(),
            ],
        })
    }

    /// Validate registration data
    fn validate_registration_data(&self, data: &PatientRegistrationData) -> OctaveResult<()> {
        if data.first_name.trim().is_empty() {
            return Err(OctaveError::validation("first_name", "First name is required"));
        }

        if data.last_name.trim().is_empty() {
            return Err(OctaveError::validation("last_name", "Last name is required"));
        }

        // Validate date of birth format (YYYY-MM-DD)
        if chrono::NaiveDate::parse_from_str(&data.date_of_birth, "%Y-%m-%d").is_err() {
            return Err(OctaveError::validation("date_of_birth", "Invalid date format (use YYYY-MM-DD)"));
        }

        // Validate phone format if provided
        if let Some(ref phone) = data.phone {
            if !self.is_valid_phone(phone) {
                return Err(OctaveError::validation("phone", "Invalid phone number format"));
            }
        }

        // Validate email format if provided
        if let Some(ref email) = data.email {
            if !self.is_valid_email(email) {
                return Err(OctaveError::validation("email", "Invalid email format"));
            }
        }

        Ok(())
    }

    /// Generate unique MRN for practice
    async fn generate_mrn(&self, practice_id: Uuid) -> OctaveResult<String> {
        // Simple MRN generation - in production, this would be more sophisticated
        let timestamp = Utc::now().timestamp();
        let practice_suffix = practice_id.to_string().chars().take(4).collect::<String>();
        Ok(format!("MRN{}{}", practice_suffix.to_uppercase(), timestamp))
    }

    /// Check for potential duplicate patients
    async fn check_for_duplicates(
        &self,
        practice_id: Uuid,
        data: &PatientRegistrationData,
    ) -> OctaveResult<Vec<PatientSummary>> {
        let criteria = PatientSearchCriteria {
            name: Some(format!("{} {}", data.first_name, data.last_name)),
            phone: data.phone.clone(),
            date_of_birth: Some(data.date_of_birth.clone()),
            mrn: None,
            status: Some(PatientStatus::Active),
            gender: Some(data.gender.clone()),
            min_age: None,
            max_age: None,
        };

        // Search for potential duplicates
        let duplicates = self.patient_service.search_patients(
            practice_id,
            criteria,
            Uuid::new_v4(), // System search
        ).await?;

        Ok(duplicates)
    }

    /// Validate phone number format
    fn is_valid_phone(&self, phone: &str) -> bool {
        // Simple phone validation - in production, use a proper phone validation library
        let cleaned = phone.chars().filter(|c| c.is_ascii_digit()).collect::<String>();
        cleaned.len() >= 10 && cleaned.len() <= 15
    }

    /// Validate email format
    fn is_valid_email(&self, email: &str) -> bool {
        // Simple email validation - in production, use a proper email validation library
        email.contains('@') && email.contains('.') && email.len() > 5
    }
}

/// Patient registration result
#[derive(Debug, Clone)]
pub struct PatientRegistrationResult {
    pub status: RegistrationStatus,
    pub patient: Option<Patient>,
    pub potential_duplicates: Vec<PatientSummary>,
    pub next_steps: Vec<String>,
}

/// Registration status
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum RegistrationStatus {
    Completed,
    DuplicatesFound,
    ValidationFailed,
    Error,
}

/// Patient search service with privacy protection
pub struct PatientSearchService<P: PatientRepository, A: UserActivityRepository> {
    patient_repo: P,
    activity_repo: A,
}

impl<P: PatientRepository, A: UserActivityRepository> PatientSearchService<P, A> {
    /// Create a new patient search service
    pub fn new(patient_repo: P, activity_repo: A) -> Self {
        Self { patient_repo, activity_repo }
    }

    /// Advanced patient search with fuzzy matching
    pub async fn advanced_search(
        &self,
        practice_id: Uuid,
        query: &str,
        search_type: SearchType,
        searched_by: Uuid,
    ) -> OctaveResult<Vec<PatientSummary>> {
        let results = match search_type {
            SearchType::Name => self.search_by_name_fuzzy(practice_id, query).await?,
            SearchType::Phone => self.search_by_phone_fuzzy(practice_id, query).await?,
            SearchType::DateOfBirth => self.search_by_dob_exact(practice_id, query).await?,
            SearchType::Mrn => self.search_by_mrn_exact(practice_id, query).await?,
            SearchType::All => self.search_all_fields(practice_id, query).await?,
        };

        // Log search activity
        let activity = UserActivity::new(
            searched_by,
            practice_id,
            ActivityType::PatientManagement,
            format!("Patient search: {} ({:?}), {} results", query, search_type, results.len()),
        );
        self.activity_repo.create(activity).await?;

        Ok(results)
    }

    /// Search by name with fuzzy matching
    async fn search_by_name_fuzzy(
        &self,
        practice_id: Uuid,
        query: &str,
    ) -> OctaveResult<Vec<PatientSummary>> {
        // In a real implementation, this would use fuzzy matching algorithms
        // For now, we'll use exact matching
        let patients = self.patient_repo.search_by_name(practice_id, query).await?;
        Ok(patients.into_iter().map(|p| p.summary()).collect())
    }

    /// Search by phone with fuzzy matching
    async fn search_by_phone_fuzzy(
        &self,
        practice_id: Uuid,
        query: &str,
    ) -> OctaveResult<Vec<PatientSummary>> {
        // Clean phone number for search
        let cleaned_query = query.chars().filter(|c| c.is_ascii_digit()).collect::<String>();
        let patients = self.patient_repo.search_by_phone(practice_id, &cleaned_query).await?;
        Ok(patients.into_iter().map(|p| p.summary()).collect())
    }

    /// Search by date of birth (exact match)
    async fn search_by_dob_exact(
        &self,
        practice_id: Uuid,
        query: &str,
    ) -> OctaveResult<Vec<PatientSummary>> {
        let patients = self.patient_repo.search_by_dob(practice_id, query).await?;
        Ok(patients.into_iter().map(|p| p.summary()).collect())
    }

    /// Search by MRN (exact match)
    async fn search_by_mrn_exact(
        &self,
        practice_id: Uuid,
        query: &str,
    ) -> OctaveResult<Vec<PatientSummary>> {
        if let Some(patient) = self.patient_repo.get_by_mrn(practice_id, query).await? {
            Ok(vec![patient.summary()])
        } else {
            Ok(Vec::new())
        }
    }

    /// Search all fields
    async fn search_all_fields(
        &self,
        practice_id: Uuid,
        query: &str,
    ) -> OctaveResult<Vec<PatientSummary>> {
        let mut all_results = Vec::new();

        // Search by name
        let name_results = self.search_by_name_fuzzy(practice_id, query).await?;
        all_results.extend(name_results);

        // Search by phone (if query looks like a phone number)
        if query.chars().any(|c| c.is_ascii_digit()) {
            let phone_results = self.search_by_phone_fuzzy(practice_id, query).await?;
            all_results.extend(phone_results);
        }

        // Search by MRN
        let mrn_results = self.search_by_mrn_exact(practice_id, query).await?;
        all_results.extend(mrn_results);

        // Remove duplicates
        all_results.sort_by_key(|p| p.id);
        all_results.dedup_by_key(|p| p.id);

        Ok(all_results)
    }

    /// Get search suggestions based on partial input
    pub async fn get_search_suggestions(
        &self,
        practice_id: Uuid,
        partial_query: &str,
        max_suggestions: usize,
    ) -> OctaveResult<Vec<String>> {
        // In a real implementation, this would provide intelligent suggestions
        // For now, we'll return empty suggestions
        Ok(Vec::new())
    }
}

/// Search type enumeration
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum SearchType {
    Name,
    Phone,
    DateOfBirth,
    Mrn,
    All,
}

/// Patient validation service
pub struct PatientValidationService {
    // No dependencies needed for validation
}

impl PatientValidationService {
    /// Create a new patient validation service
    pub fn new() -> Self {
        Self {}
    }

    /// Validate patient data comprehensively
    pub fn validate_patient_data(&self, patient: &Patient) -> OctaveResult<ValidationResult> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();
        let mut score = 100.0;

        // Validate required fields
        if patient.mrn.trim().is_empty() {
            errors.push("MRN is required".to_string());
            score -= 20.0;
        }

        // Validate PHI fields
        if let Err(e) = patient.first_name.decrypt() {
            errors.push(format!("Invalid first name: {}", e));
            score -= 15.0;
        }

        if let Err(e) = patient.last_name.decrypt() {
            errors.push(format!("Invalid last name: {}", e));
            score -= 15.0;
        }

        if let Err(e) = patient.date_of_birth.decrypt() {
            errors.push(format!("Invalid date of birth: {}", e));
            score -= 15.0;
        }

        // Validate age reasonableness
        if let Ok(age) = patient.age() {
            if age > 150 {
                warnings.push("Patient age seems unusually high".to_string());
                score -= 5.0;
            } else if age == 0 {
                warnings.push("Patient age is 0 - please verify date of birth".to_string());
                score -= 5.0;
            }
        }

        // Validate phone format if present
        if let Some(ref phone) = patient.phone {
            if let Ok(phone_str) = phone.decrypt() {
                if !self.is_valid_phone_format(&phone_str) {
                    warnings.push("Phone number format may be invalid".to_string());
                    score -= 3.0;
                }
            }
        }

        // Validate email format if present
        if let Some(ref email) = patient.email {
            if let Ok(email_str) = email.decrypt() {
                if !self.is_valid_email_format(&email_str) {
                    warnings.push("Email format may be invalid".to_string());
                    score -= 3.0;
                }
            }
        }

        // Validate insurance information
        for (i, insurance) in patient.insurance_info.iter().enumerate() {
            if let Err(e) = insurance.policy_number.decrypt() {
                warnings.push(format!("Insurance {} policy number invalid: {}", i + 1, e));
                score -= 2.0;
            }
        }

        let quality = if score >= 90.0 {
            DataQuality::Excellent
        } else if score >= 75.0 {
            DataQuality::Good
        } else if score >= 60.0 {
            DataQuality::Fair
        } else {
            DataQuality::Poor
        };

        Ok(ValidationResult {
            is_valid: errors.is_empty(),
            quality,
            score,
            errors,
            warnings,
        })
    }

    /// Validate phone format
    fn is_valid_phone_format(&self, phone: &str) -> bool {
        let cleaned = phone.chars().filter(|c| c.is_ascii_digit()).collect::<String>();
        cleaned.len() >= 10 && cleaned.len() <= 15
    }

    /// Validate email format
    fn is_valid_email_format(&self, email: &str) -> bool {
        email.contains('@') && email.contains('.') && email.len() > 5
    }
}

/// Validation result
#[derive(Debug, Clone)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub quality: DataQuality,
    pub score: f64,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

/// Data quality enumeration
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum DataQuality {
    Excellent,
    Good,
    Fair,
    Poor,
}

// ============================================================================
// PRIOR AUTHORIZATION SERVICES
// ============================================================================

/// Prior authorization submission data
#[derive(Debug, Clone)]
pub struct PriorAuthSubmissionData {
    pub patient_id: Uuid,
    pub procedure_code: String,
    pub procedure_description: Option<String>,
    pub diagnosis_codes: Vec<String>,
    pub requesting_provider: String,
    pub insurance_company: String,
    pub priority: PriorAuthPriority,
    pub urgency_level: UrgencyLevel,
    pub clinical_justification: String,
    pub supporting_documents: Vec<Uuid>,
    pub expected_approval_date: Option<DateTime<Utc>>,
    pub notes: Option<String>,
}

/// Prior authorization search criteria
#[derive(Debug, Clone)]
pub struct PriorAuthSearchCriteria {
    pub tracking_id: Option<String>,
    pub patient_id: Option<Uuid>,
    pub status: Option<PriorAuthStatus>,
    pub priority: Option<PriorAuthPriority>,
    pub urgency_level: Option<UrgencyLevel>,
    pub insurance_company: Option<String>,
    pub procedure_code: Option<String>,
    pub date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    pub submitted_by: Option<Uuid>,
    pub is_expired: Option<bool>,
    pub is_overdue: Option<bool>,
}

/// Prior authorization service for core management
pub struct PriorAuthService<P: PriorAuthRepository, A: UserActivityRepository> {
    prior_auth_repo: P,
    activity_repo: A,
}

impl<P: PriorAuthRepository, A: UserActivityRepository> PriorAuthService<P, A> {
    /// Create a new prior authorization service
    pub fn new(prior_auth_repo: P, activity_repo: A) -> Self {
        Self { prior_auth_repo, activity_repo }
    }

    /// Submit a new prior authorization request
    pub async fn submit_request(
        &self,
        practice_id: Uuid,
        submission_data: PriorAuthSubmissionData,
        submitted_by: Uuid,
    ) -> OctaveResult<PriorAuth> {
        // Validate submission data
        self.validate_submission_data(&submission_data)?;

        // Create prior authorization
        let mut prior_auth = PriorAuth::new(
            practice_id,
            submission_data.patient_id,
            submission_data.procedure_code,
            submission_data.requesting_provider,
            submission_data.insurance_company,
            submission_data.priority,
            submitted_by,
        );

        // Set additional fields
        prior_auth.procedure_description = submission_data.procedure_description;
        prior_auth.diagnosis_codes = submission_data.diagnosis_codes;
        prior_auth.urgency_level = submission_data.urgency_level;
        prior_auth.clinical_justification = submission_data.clinical_justification;
        prior_auth.supporting_documents = submission_data.supporting_documents;
        prior_auth.expected_approval_date = submission_data.expected_approval_date;
        prior_auth.notes = submission_data.notes;

        // Save to database
        let created_prior_auth = self.prior_auth_repo.create(prior_auth).await?;

        // Log submission activity
        let activity = UserActivity::new(
            submitted_by,
            practice_id,
            ActivityType::PriorAuthAction,
            format!("Prior authorization submitted: {}", created_prior_auth.tracking_id),
        );
        self.activity_repo.create(activity).await?;

        Ok(created_prior_auth)
    }

    /// Get prior authorization by ID with access logging
    pub async fn get_prior_auth(&self, id: Uuid, accessed_by: Uuid) -> OctaveResult<Option<PriorAuth>> {
        let prior_auth = self.prior_auth_repo.get_by_id(id).await?;

        if let Some(ref pa) = prior_auth {
            // Log access activity
            let activity = UserActivity::new(
                accessed_by,
                pa.practice_id,
                ActivityType::PriorAuthAction,
                format!("Prior authorization accessed: {}", pa.tracking_id),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(prior_auth)
    }

    /// Update prior authorization status
    pub async fn update_status(
        &self,
        id: Uuid,
        new_status: PriorAuthStatus,
        updated_by: Uuid,
        reason: Option<String>,
    ) -> OctaveResult<PriorAuth> {
        let updated_prior_auth = self.prior_auth_repo.update_status(
            id,
            new_status.clone(),
            updated_by,
            reason.clone(),
        ).await?;

        // Log status update activity
        let activity = UserActivity::new(
            updated_by,
            updated_prior_auth.practice_id,
            ActivityType::PriorAuthAction,
            format!(
                "Prior authorization status updated: {} -> {:?}{}",
                updated_prior_auth.tracking_id,
                new_status,
                reason.map(|r| format!(" ({})", r)).unwrap_or_default()
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_prior_auth)
    }

    /// Search prior authorizations
    pub async fn search_prior_auths(
        &self,
        practice_id: Uuid,
        criteria: PriorAuthSearchCriteria,
        searched_by: Uuid,
    ) -> OctaveResult<Vec<PriorAuthSummary>> {
        let mut results = Vec::new();

        // Search by different criteria
        if let Some(tracking_id) = criteria.tracking_id {
            if let Some(pa) = self.prior_auth_repo.get_by_tracking_id(&tracking_id).await? {
                if pa.practice_id == practice_id {
                    results.push(pa);
                }
            }
        } else {
            // Get all for practice and filter
            let all_prior_auths = self.prior_auth_repo.get_by_practice(practice_id).await?;
            results = all_prior_auths;
        }

        // Apply filters
        if let Some(patient_id) = criteria.patient_id {
            results.retain(|pa| pa.patient_id == patient_id);
        }

        if let Some(status) = criteria.status {
            results.retain(|pa| pa.status == status);
        }

        if let Some(priority) = criteria.priority {
            results.retain(|pa| pa.priority == priority);
        }

        if let Some(urgency) = criteria.urgency_level {
            results.retain(|pa| pa.urgency_level == urgency);
        }

        if let Some(insurance) = criteria.insurance_company {
            results.retain(|pa| pa.insurance_company.contains(&insurance));
        }

        if let Some(procedure_code) = criteria.procedure_code {
            results.retain(|pa| pa.procedure_code == procedure_code);
        }

        if let Some(is_expired) = criteria.is_expired {
            results.retain(|pa| pa.is_expired() == is_expired);
        }

        if let Some(is_overdue) = criteria.is_overdue {
            results.retain(|pa| !pa.is_sla_met() == is_overdue);
        }

        // Convert to summaries
        let summaries: Vec<PriorAuthSummary> = results.into_iter()
            .map(|pa| pa.summary())
            .collect();

        // Log search activity
        let activity = UserActivity::new(
            searched_by,
            practice_id,
            ActivityType::PriorAuthAction,
            format!("Prior authorization search performed, {} results", summaries.len()),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Get practice prior authorizations summary
    pub async fn get_practice_prior_auths(&self, practice_id: Uuid) -> OctaveResult<Vec<PriorAuthSummary>> {
        let summaries = self.prior_auth_repo.get_summaries(practice_id).await?;
        Ok(summaries)
    }

    /// Validate submission data
    fn validate_submission_data(&self, data: &PriorAuthSubmissionData) -> OctaveResult<()> {
        if data.procedure_code.trim().is_empty() {
            return Err(OctaveError::validation("procedure_code", "Procedure code is required"));
        }

        if data.requesting_provider.trim().is_empty() {
            return Err(OctaveError::validation("requesting_provider", "Requesting provider is required"));
        }

        if data.insurance_company.trim().is_empty() {
            return Err(OctaveError::validation("insurance_company", "Insurance company is required"));
        }

        if data.clinical_justification.trim().is_empty() {
            return Err(OctaveError::validation("clinical_justification", "Clinical justification is required"));
        }

        if data.diagnosis_codes.is_empty() {
            return Err(OctaveError::validation("diagnosis_codes", "At least one diagnosis code is required"));
        }

        Ok(())
    }
}

/// Prior authorization status service for status management
pub struct PriorAuthStatusService<P: PriorAuthRepository, A: UserActivityRepository> {
    prior_auth_repo: P,
    activity_repo: A,
}

impl<P: PriorAuthRepository, A: UserActivityRepository> PriorAuthStatusService<P, A> {
    /// Create a new status service
    pub fn new(prior_auth_repo: P, activity_repo: A) -> Self {
        Self { prior_auth_repo, activity_repo }
    }

    /// Approve prior authorization
    pub async fn approve(
        &self,
        id: Uuid,
        approval_number: String,
        approved_by: Uuid,
        notes: Option<String>,
    ) -> OctaveResult<PriorAuth> {
        let mut prior_auth = self.prior_auth_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("id", "Prior authorization not found"))?;

        // Validate status transition
        prior_auth.update_status(PriorAuthStatus::Approved, approved_by)?;
        prior_auth.approval_number = Some(approval_number.clone());

        if let Some(notes_text) = notes {
            prior_auth.notes = Some(notes_text);
        }

        let updated_prior_auth = self.prior_auth_repo.update(prior_auth).await?;

        // Log approval activity
        let activity = UserActivity::new(
            approved_by,
            updated_prior_auth.practice_id,
            ActivityType::PriorAuthAction,
            format!(
                "Prior authorization approved: {} (Approval #: {})",
                updated_prior_auth.tracking_id,
                approval_number
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_prior_auth)
    }

    /// Deny prior authorization
    pub async fn deny(
        &self,
        id: Uuid,
        denial_reason: String,
        denied_by: Uuid,
        notes: Option<String>,
    ) -> OctaveResult<PriorAuth> {
        let mut prior_auth = self.prior_auth_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("id", "Prior authorization not found"))?;

        // Validate status transition
        prior_auth.update_status(PriorAuthStatus::Denied, denied_by)?;
        prior_auth.denial_reason = Some(denial_reason.clone());

        if let Some(notes_text) = notes {
            prior_auth.notes = Some(notes_text);
        }

        let updated_prior_auth = self.prior_auth_repo.update(prior_auth).await?;

        // Log denial activity
        let activity = UserActivity::new(
            denied_by,
            updated_prior_auth.practice_id,
            ActivityType::PriorAuthAction,
            format!(
                "Prior authorization denied: {} (Reason: {})",
                updated_prior_auth.tracking_id,
                denial_reason
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_prior_auth)
    }

    /// Request additional information
    pub async fn request_information(
        &self,
        id: Uuid,
        information_request: String,
        requested_by: Uuid,
    ) -> OctaveResult<PriorAuth> {
        let mut prior_auth = self.prior_auth_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("id", "Prior authorization not found"))?;

        // Validate status transition
        prior_auth.update_status(PriorAuthStatus::InformationRequired, requested_by)?;

        // Add to notes
        let current_notes = prior_auth.notes.unwrap_or_default();
        let new_notes = if current_notes.is_empty() {
            format!("Information requested: {}", information_request)
        } else {
            format!("{}\n\nInformation requested: {}", current_notes, information_request)
        };
        prior_auth.notes = Some(new_notes);

        let updated_prior_auth = self.prior_auth_repo.update(prior_auth).await?;

        // Log information request activity
        let activity = UserActivity::new(
            requested_by,
            updated_prior_auth.practice_id,
            ActivityType::PriorAuthAction,
            format!(
                "Additional information requested for: {}",
                updated_prior_auth.tracking_id
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_prior_auth)
    }

    /// Cancel prior authorization
    pub async fn cancel(
        &self,
        id: Uuid,
        cancellation_reason: String,
        cancelled_by: Uuid,
    ) -> OctaveResult<PriorAuth> {
        let mut prior_auth = self.prior_auth_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("id", "Prior authorization not found"))?;

        // Validate status transition
        prior_auth.update_status(PriorAuthStatus::Cancelled, cancelled_by)?;

        // Add cancellation reason to notes
        let current_notes = prior_auth.notes.unwrap_or_default();
        let new_notes = if current_notes.is_empty() {
            format!("Cancelled: {}", cancellation_reason)
        } else {
            format!("{}\n\nCancelled: {}", current_notes, cancellation_reason)
        };
        prior_auth.notes = Some(new_notes);

        let updated_prior_auth = self.prior_auth_repo.update(prior_auth).await?;

        // Log cancellation activity
        let activity = UserActivity::new(
            cancelled_by,
            updated_prior_auth.practice_id,
            ActivityType::PriorAuthAction,
            format!(
                "Prior authorization cancelled: {} (Reason: {})",
                updated_prior_auth.tracking_id,
                cancellation_reason
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_prior_auth)
    }

    /// Get status history for prior authorization
    pub async fn get_status_history(&self, id: Uuid) -> OctaveResult<Vec<UserActivity>> {
        let prior_auth = self.prior_auth_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("id", "Prior authorization not found"))?;

        // Get all activities related to this prior auth
        let activities = self.activity_repo.get_by_practice(prior_auth.practice_id).await?;

        // Filter activities related to this specific prior auth
        let status_activities: Vec<UserActivity> = activities.into_iter()
            .filter(|activity| {
                activity.activity_type == ActivityType::PriorAuthAction &&
                activity.description.contains(&prior_auth.tracking_id)
            })
            .collect();

        Ok(status_activities)
    }
}

/// Prior authorization expiration service
pub struct PriorAuthExpirationService<P: PriorAuthRepository, A: UserActivityRepository> {
    prior_auth_repo: P,
    activity_repo: A,
}

impl<P: PriorAuthRepository, A: UserActivityRepository> PriorAuthExpirationService<P, A> {
    /// Create a new expiration service
    pub fn new(prior_auth_repo: P, activity_repo: A) -> Self {
        Self { prior_auth_repo, activity_repo }
    }

    /// Check and mark expired authorizations
    pub async fn process_expirations(&self, practice_id: Uuid) -> OctaveResult<ExpirationResult> {
        let expired_count = self.prior_auth_repo.mark_expired(practice_id).await?;

        // Log expiration processing
        let activity = UserActivity::new(
            Uuid::new_v4(), // System user
            practice_id,
            ActivityType::PriorAuthAction,
            format!("Processed {} expired prior authorizations", expired_count),
        );
        self.activity_repo.create(activity).await?;

        Ok(ExpirationResult {
            expired_count,
            processed_at: Utc::now(),
        })
    }

    /// Get expiring authorizations
    pub async fn get_expiring_soon(
        &self,
        practice_id: Uuid,
        days: u32,
    ) -> OctaveResult<Vec<PriorAuth>> {
        self.prior_auth_repo.get_expiring_soon(practice_id, days).await
    }

    /// Get expired authorizations
    pub async fn get_expired(&self, practice_id: Uuid) -> OctaveResult<Vec<PriorAuth>> {
        self.prior_auth_repo.get_expired(practice_id).await
    }

    /// Extend authorization expiration
    pub async fn extend_expiration(
        &self,
        id: Uuid,
        new_expiration_date: DateTime<Utc>,
        extended_by: Uuid,
        reason: String,
    ) -> OctaveResult<PriorAuth> {
        let mut prior_auth = self.prior_auth_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("id", "Prior authorization not found"))?;

        // Validate that authorization is approved
        if prior_auth.status != PriorAuthStatus::Approved {
            return Err(OctaveError::validation("status", "Can only extend approved authorizations"));
        }

        // Update expiration date
        prior_auth.expiration_date = Some(new_expiration_date);
        prior_auth.last_updated_by = extended_by;
        prior_auth.updated_at = Utc::now();

        // Add extension note
        let current_notes = prior_auth.notes.unwrap_or_default();
        let new_notes = if current_notes.is_empty() {
            format!("Expiration extended to {} - Reason: {}", new_expiration_date.format("%Y-%m-%d"), reason)
        } else {
            format!("{}\n\nExpiration extended to {} - Reason: {}", current_notes, new_expiration_date.format("%Y-%m-%d"), reason)
        };
        prior_auth.notes = Some(new_notes);

        let updated_prior_auth = self.prior_auth_repo.update(prior_auth).await?;

        // Log extension activity
        let activity = UserActivity::new(
            extended_by,
            updated_prior_auth.practice_id,
            ActivityType::PriorAuthAction,
            format!(
                "Prior authorization expiration extended: {} (New expiration: {})",
                updated_prior_auth.tracking_id,
                new_expiration_date.format("%Y-%m-%d")
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_prior_auth)
    }

    /// Generate expiration report
    pub async fn generate_expiration_report(&self, practice_id: Uuid) -> OctaveResult<ExpirationReport> {
        let expired = self.get_expired(practice_id).await?;
        let expiring_30_days = self.get_expiring_soon(practice_id, 30).await?;
        let expiring_7_days = self.get_expiring_soon(practice_id, 7).await?;

        Ok(ExpirationReport {
            practice_id,
            expired_count: expired.len(),
            expiring_30_days_count: expiring_30_days.len(),
            expiring_7_days_count: expiring_7_days.len(),
            expired_authorizations: expired.into_iter().map(|pa| pa.summary()).collect(),
            expiring_30_days: expiring_30_days.into_iter().map(|pa| pa.summary()).collect(),
            expiring_7_days: expiring_7_days.into_iter().map(|pa| pa.summary()).collect(),
            generated_at: Utc::now(),
        })
    }
}

/// Expiration processing result
#[derive(Debug, Clone)]
pub struct ExpirationResult {
    pub expired_count: usize,
    pub processed_at: DateTime<Utc>,
}

/// Expiration report
#[derive(Debug, Clone)]
pub struct ExpirationReport {
    pub practice_id: Uuid,
    pub expired_count: usize,
    pub expiring_30_days_count: usize,
    pub expiring_7_days_count: usize,
    pub expired_authorizations: Vec<PriorAuthSummary>,
    pub expiring_30_days: Vec<PriorAuthSummary>,
    pub expiring_7_days: Vec<PriorAuthSummary>,
    pub generated_at: DateTime<Utc>,
}

// ============================================================================
// DOCUMENT MANAGEMENT SERVICES
// ============================================================================

/// Document upload data
#[derive(Debug, Clone)]
pub struct DocumentUploadData {
    pub file_name: String,
    pub file_type: String,
    pub file_size: u64,
    pub file_content: Vec<u8>,
    pub document_type: DocumentType,
    pub classification: Option<DocumentClassification>,
    pub prior_auth_id: Option<Uuid>,
    pub patient_id: Option<Uuid>,
    pub tags: Vec<String>,
    pub metadata: HashMap<String, String>,
}

/// Document search criteria
#[derive(Debug, Clone)]
pub struct DocumentSearchCriteria {
    pub filename_pattern: Option<String>,
    pub document_type: Option<DocumentType>,
    pub classification: Option<DocumentClassification>,
    pub tags: Vec<String>,
    pub prior_auth_id: Option<Uuid>,
    pub patient_id: Option<Uuid>,
    pub uploaded_by: Option<Uuid>,
    pub date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    pub virus_scan_status: Option<VirusScanStatus>,
    pub is_current_version: Option<bool>,
    pub full_text_query: Option<String>,
}

/// File validation result
#[derive(Debug, Clone)]
pub struct FileValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub detected_type: Option<String>,
    pub is_safe: bool,
}

/// Document service for core document management
pub struct DocumentService<D: DocumentRepository, A: UserActivityRepository> {
    document_repo: D,
    activity_repo: A,
}

impl<D: DocumentRepository, A: UserActivityRepository> DocumentService<D, A> {
    /// Create a new document service
    pub fn new(document_repo: D, activity_repo: A) -> Self {
        Self { document_repo, activity_repo }
    }

    /// Upload a new document
    pub async fn upload_document(
        &self,
        practice_id: Uuid,
        upload_data: DocumentUploadData,
        uploaded_by: Uuid,
    ) -> OctaveResult<Document> {
        // Validate file
        let validation_result = self.validate_file(&upload_data)?;
        if !validation_result.is_valid {
            return Err(OctaveError::validation("file", &validation_result.errors.join(", ")));
        }

        // Generate storage path
        let storage_path = self.generate_storage_path(&upload_data.file_name, &upload_data.file_type);

        // Create document record
        let mut document = Document::new(
            practice_id,
            upload_data.file_name,
            upload_data.file_type,
            upload_data.file_size,
            storage_path,
            upload_data.document_type,
            uploaded_by,
        );

        // Set optional fields
        if let Some(classification) = upload_data.classification {
            document.classification = classification;
        }
        document.prior_auth_id = upload_data.prior_auth_id;
        document.patient_id = upload_data.patient_id;
        document.tags = upload_data.tags;
        document.set_metadata(upload_data.metadata);

        // Calculate checksum
        document.checksum = self.calculate_checksum(&upload_data.file_content);

        // TODO: Upload file to S3/MinIO storage
        // TODO: Encrypt file with document.encryption_key_id

        // Save document record
        let created_document = self.document_repo.create(document).await?;

        // TODO: Initiate virus scan
        self.initiate_virus_scan(&created_document).await?;

        // Log upload activity
        let activity = UserActivity::new(
            uploaded_by,
            practice_id,
            ActivityType::DocumentAccess,
            format!("Document uploaded: {} ({})", created_document.file_name.masked(), created_document.id),
        );
        self.activity_repo.create(activity).await?;

        Ok(created_document)
    }

    /// Get document by ID with access logging
    pub async fn get_document(&self, id: Uuid, accessed_by: Uuid) -> OctaveResult<Option<Document>> {
        let document = self.document_repo.get_by_id(id).await?;

        if let Some(ref doc) = document {
            // Check if document is safe to access
            if !doc.is_safe_to_access() {
                return Err(OctaveError::validation("document", "Document is not safe to access"));
            }

            // Update last accessed timestamp
            self.document_repo.update_last_accessed(id).await?;

            // Log access activity
            let activity = UserActivity::new(
                accessed_by,
                doc.practice_id,
                ActivityType::DocumentAccess,
                format!("Document accessed: {} ({})", doc.file_name.masked(), doc.id),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(document)
    }

    /// Search documents
    pub async fn search_documents(
        &self,
        practice_id: Uuid,
        criteria: DocumentSearchCriteria,
        searched_by: Uuid,
    ) -> OctaveResult<Vec<DocumentSummary>> {
        let mut results = Vec::new();

        // Search by different criteria
        if let Some(filename_pattern) = criteria.filename_pattern {
            let documents = self.document_repo.search_by_filename(practice_id, &filename_pattern).await?;
            results.extend(documents);
        } else if !criteria.tags.is_empty() {
            let documents = self.document_repo.search_by_tags(practice_id, criteria.tags).await?;
            results.extend(documents);
        } else if let Some(full_text_query) = criteria.full_text_query {
            let documents = self.document_repo.full_text_search(practice_id, &full_text_query).await?;
            results.extend(documents);
        } else {
            // Get all documents for practice and filter
            let all_documents = self.document_repo.get_by_practice(practice_id).await?;
            results = all_documents;
        }

        // Apply filters
        if let Some(doc_type) = criteria.document_type {
            results.retain(|doc| doc.document_type == doc_type);
        }

        if let Some(classification) = criteria.classification {
            results.retain(|doc| doc.classification == classification);
        }

        if let Some(prior_auth_id) = criteria.prior_auth_id {
            results.retain(|doc| doc.prior_auth_id == Some(prior_auth_id));
        }

        if let Some(patient_id) = criteria.patient_id {
            results.retain(|doc| doc.patient_id == Some(patient_id));
        }

        if let Some(uploaded_by) = criteria.uploaded_by {
            results.retain(|doc| doc.uploaded_by == uploaded_by);
        }

        if let Some(virus_status) = criteria.virus_scan_status {
            results.retain(|doc| doc.virus_scan_status == virus_status);
        }

        if let Some(is_current) = criteria.is_current_version {
            results.retain(|doc| doc.is_current == is_current);
        }

        // Convert to summaries
        let summaries: Vec<DocumentSummary> = results.into_iter()
            .map(|doc| doc.summary())
            .collect();

        // Log search activity
        let activity = UserActivity::new(
            searched_by,
            practice_id,
            ActivityType::DocumentAccess,
            format!("Document search performed, {} results", summaries.len()),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Get practice documents summary
    pub async fn get_practice_documents(&self, practice_id: Uuid) -> OctaveResult<Vec<DocumentSummary>> {
        let summaries = self.document_repo.get_summaries(practice_id).await?;
        Ok(summaries)
    }

    /// Validate uploaded file
    fn validate_file(&self, upload_data: &DocumentUploadData) -> OctaveResult<FileValidationResult> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();

        // Check file size (max 100MB)
        const MAX_FILE_SIZE: u64 = 100 * 1024 * 1024;
        if upload_data.file_size > MAX_FILE_SIZE {
            errors.push("File size exceeds maximum limit (100MB)".to_string());
        }

        // Check file name
        if upload_data.file_name.trim().is_empty() {
            errors.push("File name is required".to_string());
        }

        // Check file type
        let allowed_types = vec![
            "application/pdf",
            "image/jpeg",
            "image/png",
            "image/tiff",
            "text/plain",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        ];

        if !allowed_types.contains(&upload_data.file_type.as_str()) {
            errors.push(format!("File type '{}' is not allowed", upload_data.file_type));
        }

        // Check for suspicious file extensions
        let suspicious_extensions = vec!["exe", "bat", "cmd", "scr", "vbs", "js"];
        if let Some(ext) = upload_data.file_name.split('.').last() {
            if suspicious_extensions.contains(&ext.to_lowercase().as_str()) {
                errors.push("Suspicious file extension detected".to_string());
            }
        }

        // Basic content validation
        let is_safe = self.basic_content_scan(&upload_data.file_content);
        if !is_safe {
            errors.push("File content appears to be malicious".to_string());
        }

        Ok(FileValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
            detected_type: Some(upload_data.file_type.clone()),
            is_safe,
        })
    }

    /// Generate storage path for file
    fn generate_storage_path(&self, file_name: &str, file_type: &str) -> String {
        let timestamp = Utc::now().timestamp();
        let random_id = Uuid::new_v4();
        let extension = file_name.split('.').last().unwrap_or("bin");

        format!("documents/{}/{}/{}.{}",
            timestamp / 86400, // Day-based partitioning
            random_id,
            timestamp,
            extension
        )
    }

    /// Calculate file checksum
    fn calculate_checksum(&self, content: &[u8]) -> String {
        use sha2::{Sha256, Digest};
        let mut hasher = Sha256::new();
        hasher.update(content);
        format!("{:x}", hasher.finalize())
    }

    /// Basic content scan for malicious patterns
    fn basic_content_scan(&self, content: &[u8]) -> bool {
        // Check for common malicious patterns
        let malicious_patterns: &[&[u8]] = &[
            b"<script",
            b"javascript:",
            b"vbscript:",
            b"onload=",
            b"onerror=",
        ];

        for pattern in malicious_patterns {
            if content.windows(pattern.len()).any(|window| window == *pattern) {
                return false;
            }
        }

        true
    }

    /// Initiate virus scan (placeholder)
    async fn initiate_virus_scan(&self, document: &Document) -> OctaveResult<()> {
        // TODO: Integrate with actual virus scanning service
        // For now, mark as clean after a delay
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        self.document_repo.update_virus_scan_status(
            document.id,
            VirusScanStatus::Clean,
        ).await?;

        Ok(())
    }
}

/// File upload service for secure file handling
pub struct FileUploadService<D: DocumentRepository, A: UserActivityRepository> {
    document_service: DocumentService<D, A>,
}

impl<D: DocumentRepository, A: UserActivityRepository> FileUploadService<D, A> {
    /// Create a new file upload service
    pub fn new(document_repo: D, activity_repo: A) -> Self {
        Self {
            document_service: DocumentService::new(document_repo, activity_repo),
        }
    }

    /// Upload file with comprehensive validation
    pub async fn upload_file(
        &self,
        practice_id: Uuid,
        upload_data: DocumentUploadData,
        uploaded_by: Uuid,
    ) -> OctaveResult<UploadResult> {
        // Pre-upload validation
        let validation_result = self.validate_upload(&upload_data)?;
        if !validation_result.is_valid {
            return Ok(UploadResult {
                success: false,
                document: None,
                errors: validation_result.errors,
                warnings: validation_result.warnings,
                upload_id: Uuid::new_v4(),
            });
        }

        // Upload document
        match self.document_service.upload_document(practice_id, upload_data, uploaded_by).await {
            Ok(document) => Ok(UploadResult {
                success: true,
                document: Some(document),
                errors: Vec::new(),
                warnings: validation_result.warnings,
                upload_id: Uuid::new_v4(),
            }),
            Err(error) => Ok(UploadResult {
                success: false,
                document: None,
                errors: vec![error.to_string()],
                warnings: validation_result.warnings,
                upload_id: Uuid::new_v4(),
            }),
        }
    }

    /// Validate upload data
    fn validate_upload(&self, upload_data: &DocumentUploadData) -> OctaveResult<FileValidationResult> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();

        // File size validation
        if upload_data.file_size == 0 {
            errors.push("File is empty".to_string());
        }

        // File name validation
        if upload_data.file_name.contains("..") || upload_data.file_name.contains("/") {
            errors.push("Invalid file name".to_string());
        }

        // Content type validation
        if !self.is_allowed_content_type(&upload_data.file_type) {
            errors.push(format!("Content type '{}' is not allowed", upload_data.file_type));
        }

        // PHI detection warning
        if upload_data.document_type.contains_phi() && upload_data.classification.is_none() {
            warnings.push("Document may contain PHI - ensure proper classification".to_string());
        }

        Ok(FileValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
            detected_type: Some(upload_data.file_type.clone()),
            is_safe: true,
        })
    }

    /// Check if content type is allowed
    fn is_allowed_content_type(&self, content_type: &str) -> bool {
        let allowed_types = [
            "application/pdf",
            "image/jpeg",
            "image/jpg",
            "image/png",
            "image/tiff",
            "image/gif",
            "text/plain",
            "text/csv",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        ];

        allowed_types.contains(&content_type)
    }
}

/// Upload result
#[derive(Debug, Clone)]
pub struct UploadResult {
    pub success: bool,
    pub document: Option<Document>,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
    pub upload_id: Uuid,
}

/// Document retention service
pub struct DocumentRetentionService<D: DocumentRepository, A: UserActivityRepository> {
    document_repo: D,
    activity_repo: A,
}

impl<D: DocumentRepository, A: UserActivityRepository> DocumentRetentionService<D, A> {
    /// Create a new retention service
    pub fn new(document_repo: D, activity_repo: A) -> Self {
        Self { document_repo, activity_repo }
    }

    /// Process retention policies
    pub async fn process_retention_policies(&self, practice_id: Uuid) -> OctaveResult<RetentionResult> {
        let candidates = self.document_repo.get_retention_candidates(practice_id).await?;
        let mut processed = 0;
        let mut errors = Vec::new();

        for document in candidates {
            if !document.should_be_retained() && !document.retention_policy.legal_hold {
                match self.archive_document(&document).await {
                    Ok(_) => processed += 1,
                    Err(e) => errors.push(format!("Failed to archive {}: {}", document.id, e)),
                }
            }
        }

        // Log retention processing
        let activity = UserActivity::new(
            Uuid::new_v4(), // System user
            practice_id,
            ActivityType::DocumentAccess,
            format!("Processed {} documents for retention", processed),
        );
        self.activity_repo.create(activity).await?;

        Ok(RetentionResult {
            processed_count: processed,
            errors,
            processed_at: Utc::now(),
        })
    }

    /// Archive document
    async fn archive_document(&self, document: &Document) -> OctaveResult<()> {
        // TODO: Move document to archive storage
        // TODO: Update document status to archived
        // For now, just mark as processed
        Ok(())
    }

    /// Generate retention report
    pub async fn generate_retention_report(&self, practice_id: Uuid) -> OctaveResult<RetentionReport> {
        let candidates = self.document_repo.get_retention_candidates(practice_id).await?;
        let expired = self.document_repo.get_expired(practice_id).await?;

        Ok(RetentionReport {
            practice_id,
            retention_candidates_count: candidates.len(),
            expired_documents_count: expired.len(),
            candidates: candidates.into_iter().map(|d| d.summary()).collect(),
            expired: expired.into_iter().map(|d| d.summary()).collect(),
            generated_at: Utc::now(),
        })
    }
}

/// Retention processing result
#[derive(Debug, Clone)]
pub struct RetentionResult {
    pub processed_count: usize,
    pub errors: Vec<String>,
    pub processed_at: DateTime<Utc>,
}

/// Retention report
#[derive(Debug, Clone)]
pub struct RetentionReport {
    pub practice_id: Uuid,
    pub retention_candidates_count: usize,
    pub expired_documents_count: usize,
    pub candidates: Vec<DocumentSummary>,
    pub expired: Vec<DocumentSummary>,
    pub generated_at: DateTime<Utc>,
}

// ============================================================================
// COMMUNICATION TRACKING SERVICES
// ============================================================================

/// Communication logging data for quick entry
#[derive(Debug, Clone)]
pub struct CommunicationLogData {
    pub communication_type: CommunicationType,
    pub direction: CommunicationDirection,
    pub subject: String,
    pub content: String,
    pub participants: Vec<CommunicationParticipant>,
    pub prior_auth_id: Option<Uuid>,
    pub patient_id: Option<Uuid>,
    pub insurance_company: Option<String>,
    pub insurance_rep_id: Option<Uuid>,
    pub outcome: CommunicationOutcome,
    pub priority: CommunicationPriority,
    pub tags: Vec<String>,
    pub duration_minutes: Option<u32>,
    pub follow_up_required: bool,
    pub follow_up_date: Option<DateTime<Utc>>,
    pub template_used: Option<Uuid>,
    pub communication_at: Option<DateTime<Utc>>,
}

/// Communication search criteria
#[derive(Debug, Clone)]
pub struct CommunicationSearchCriteria {
    pub communication_type: Option<CommunicationType>,
    pub direction: Option<CommunicationDirection>,
    pub outcome: Option<CommunicationOutcome>,
    pub priority: Option<CommunicationPriority>,
    pub prior_auth_id: Option<Uuid>,
    pub patient_id: Option<Uuid>,
    pub insurance_company: Option<String>,
    pub insurance_rep_id: Option<Uuid>,
    pub created_by: Option<Uuid>,
    pub date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    pub tags: Vec<String>,
    pub content_query: Option<String>,
    pub follow_up_required: Option<bool>,
    pub is_overdue: Option<bool>,
}

/// Communication service for core communication management
pub struct CommunicationService<C: CommunicationRepository, A: UserActivityRepository> {
    communication_repo: C,
    activity_repo: A,
}

impl<C: CommunicationRepository, A: UserActivityRepository> CommunicationService<C, A> {
    /// Create a new communication service
    pub fn new(communication_repo: C, activity_repo: A) -> Self {
        Self { communication_repo, activity_repo }
    }

    /// Log a new communication
    pub async fn log_communication(
        &self,
        practice_id: Uuid,
        log_data: CommunicationLogData,
        logged_by: Uuid,
    ) -> OctaveResult<Communication> {
        // Create communication record
        let mut communication = Communication::new(
            practice_id,
            log_data.communication_type,
            log_data.direction,
            log_data.subject,
            log_data.content,
            logged_by,
        );

        // Set additional fields
        communication.participants = log_data.participants;
        communication.prior_auth_id = log_data.prior_auth_id;
        communication.patient_id = log_data.patient_id;
        communication.insurance_company = log_data.insurance_company;
        communication.insurance_rep_id = log_data.insurance_rep_id;
        communication.set_outcome(log_data.outcome);
        communication.priority = log_data.priority;
        communication.tags = log_data.tags;
        communication.duration_minutes = log_data.duration_minutes;
        communication.template_used = log_data.template_used;

        if let Some(comm_at) = log_data.communication_at {
            communication.communication_at = comm_at;
        }

        if log_data.follow_up_required {
            communication.follow_up_required = true;
            if let Some(follow_up_date) = log_data.follow_up_date {
                communication.follow_up_date = Some(follow_up_date);
            }
        }

        // Save communication
        let created_communication = self.communication_repo.create(communication).await?;

        // Log activity
        let activity = UserActivity::new(
            logged_by,
            practice_id,
            ActivityType::DocumentAccess, // Using DocumentAccess for communication logging
            format!("Communication logged: {} - {}",
                created_communication.communication_type.as_str(),
                created_communication.subject
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(created_communication)
    }

    /// Get communication by ID with access logging
    pub async fn get_communication(&self, id: Uuid, accessed_by: Uuid) -> OctaveResult<Option<Communication>> {
        let communication = self.communication_repo.get_by_id(id).await?;

        if let Some(ref comm) = communication {
            // Log access activity
            let activity = UserActivity::new(
                accessed_by,
                comm.practice_id,
                ActivityType::DocumentAccess,
                format!("Communication accessed: {} ({})", comm.subject, comm.id),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(communication)
    }

    /// Search communications
    pub async fn search_communications(
        &self,
        practice_id: Uuid,
        criteria: CommunicationSearchCriteria,
        searched_by: Uuid,
    ) -> OctaveResult<Vec<CommunicationSummary>> {
        let mut results = Vec::new();

        // Search by different criteria
        if let Some(content_query) = criteria.content_query {
            let communications = self.communication_repo.search_content(practice_id, &content_query).await?;
            results.extend(communications);
        } else if !criteria.tags.is_empty() {
            let communications = self.communication_repo.search_by_tags(practice_id, criteria.tags).await?;
            results.extend(communications);
        } else {
            // Get all communications for practice and filter
            let all_communications = self.communication_repo.get_by_practice(practice_id).await?;
            results = all_communications;
        }

        // Apply filters
        if let Some(comm_type) = criteria.communication_type {
            results.retain(|comm| comm.communication_type == comm_type);
        }

        if let Some(direction) = criteria.direction {
            results.retain(|comm| comm.direction == direction);
        }

        if let Some(outcome) = criteria.outcome {
            results.retain(|comm| comm.outcome == outcome);
        }

        if let Some(priority) = criteria.priority {
            results.retain(|comm| comm.priority == priority);
        }

        if let Some(prior_auth_id) = criteria.prior_auth_id {
            results.retain(|comm| comm.prior_auth_id == Some(prior_auth_id));
        }

        if let Some(patient_id) = criteria.patient_id {
            results.retain(|comm| comm.patient_id == Some(patient_id));
        }

        if let Some(insurance_company) = criteria.insurance_company {
            results.retain(|comm| {
                comm.insurance_company.as_ref()
                    .map(|ic| ic.contains(&insurance_company))
                    .unwrap_or(false)
            });
        }

        if let Some(created_by) = criteria.created_by {
            results.retain(|comm| comm.created_by == created_by);
        }

        if let Some(follow_up_required) = criteria.follow_up_required {
            results.retain(|comm| comm.follow_up_required == follow_up_required);
        }

        if let Some(is_overdue) = criteria.is_overdue {
            results.retain(|comm| comm.is_overdue() == is_overdue);
        }

        // Convert to summaries
        let summaries: Vec<CommunicationSummary> = results.into_iter()
            .map(|comm| comm.summary())
            .collect();

        // Log search activity
        let activity = UserActivity::new(
            searched_by,
            practice_id,
            ActivityType::DocumentAccess,
            format!("Communication search performed, {} results", summaries.len()),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Get practice communications summary
    pub async fn get_practice_communications(&self, practice_id: Uuid) -> OctaveResult<Vec<CommunicationSummary>> {
        let summaries = self.communication_repo.get_summaries(practice_id).await?;
        Ok(summaries)
    }

    /// Update communication outcome
    pub async fn update_outcome(
        &self,
        id: Uuid,
        outcome: CommunicationOutcome,
        updated_by: Uuid,
    ) -> OctaveResult<Communication> {
        let updated_communication = self.communication_repo.update_outcome(id, outcome.clone()).await?;

        // Log outcome update activity
        let activity = UserActivity::new(
            updated_by,
            updated_communication.practice_id,
            ActivityType::DocumentAccess,
            format!(
                "Communication outcome updated: {} -> {:?}",
                updated_communication.subject,
                outcome
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_communication)
    }

    /// Get overdue communications
    pub async fn get_overdue_communications(&self, practice_id: Uuid) -> OctaveResult<Vec<CommunicationSummary>> {
        let overdue = self.communication_repo.get_overdue(practice_id).await?;
        let summaries = overdue.into_iter().map(|comm| comm.summary()).collect();
        Ok(summaries)
    }

    /// Get communications requiring follow-up
    pub async fn get_follow_up_required(&self, practice_id: Uuid) -> OctaveResult<Vec<CommunicationSummary>> {
        let follow_ups = self.communication_repo.get_requiring_follow_up(practice_id).await?;
        let summaries = follow_ups.into_iter().map(|comm| comm.summary()).collect();
        Ok(summaries)
    }

    /// Complete follow-up for communication
    pub async fn complete_follow_up(
        &self,
        id: Uuid,
        completed_by: Uuid,
    ) -> OctaveResult<bool> {
        let success = self.communication_repo.complete_follow_up(id).await?;

        if success {
            if let Some(communication) = self.communication_repo.get_by_id(id).await? {
                // Log follow-up completion activity
                let activity = UserActivity::new(
                    completed_by,
                    communication.practice_id,
                    ActivityType::DocumentAccess,
                    format!("Follow-up completed for communication: {}", communication.subject),
                );
                self.activity_repo.create(activity).await?;
            }
        }

        Ok(success)
    }
}

/// Insurance representative service for contact management
pub struct InsuranceRepService<I: InsuranceRepRepository, A: UserActivityRepository> {
    rep_repo: I,
    activity_repo: A,
}

impl<I: InsuranceRepRepository, A: UserActivityRepository> InsuranceRepService<I, A> {
    /// Create a new insurance representative service
    pub fn new(rep_repo: I, activity_repo: A) -> Self {
        Self { rep_repo, activity_repo }
    }

    /// Create a new insurance representative
    pub async fn create_representative(
        &self,
        practice_id: Uuid,
        name: String,
        insurance_company: String,
        created_by: Uuid,
    ) -> OctaveResult<InsuranceRepresentative> {
        let rep = InsuranceRepresentative::new(practice_id, name, insurance_company, created_by);
        let created_rep = self.rep_repo.create(rep).await?;

        // Log creation activity
        let activity = UserActivity::new(
            created_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Insurance representative created: {} ({})",
                created_rep.name.masked(),
                created_rep.insurance_company
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(created_rep)
    }

    /// Get representative by ID
    pub async fn get_representative(&self, id: Uuid, accessed_by: Uuid) -> OctaveResult<Option<InsuranceRepresentative>> {
        let rep = self.rep_repo.get_by_id(id).await?;

        if let Some(ref representative) = rep {
            // Log access activity
            let activity = UserActivity::new(
                accessed_by,
                representative.practice_id,
                ActivityType::UserManagement,
                format!("Insurance representative accessed: {} ({})",
                    representative.name.masked(),
                    representative.id
                ),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(rep)
    }

    /// Update representative performance
    pub async fn update_performance(
        &self,
        id: Uuid,
        response_time_hours: f32,
        helpfulness_rating: f32,
        updated_by: Uuid,
    ) -> OctaveResult<InsuranceRepresentative> {
        let updated_rep = self.rep_repo.update_performance(id, response_time_hours, helpfulness_rating).await?;

        // Log performance update activity
        let activity = UserActivity::new(
            updated_by,
            updated_rep.practice_id,
            ActivityType::UserManagement,
            format!("Performance updated for representative: {} (Rating: {:.1})",
                updated_rep.name.masked(),
                helpfulness_rating
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_rep)
    }

    /// Search representatives by insurance company
    pub async fn search_by_company(
        &self,
        practice_id: Uuid,
        company: &str,
        searched_by: Uuid,
    ) -> OctaveResult<Vec<InsuranceRepSummary>> {
        let reps = self.rep_repo.get_by_insurance_company(practice_id, company).await?;
        let summaries = reps.into_iter().map(|rep| rep.summary()).collect();

        // Log search activity
        let activity = UserActivity::new(
            searched_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Insurance representative search: {}", company),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Get top performing representatives
    pub async fn get_top_performers(&self, practice_id: Uuid, limit: u32) -> OctaveResult<Vec<InsuranceRepSummary>> {
        let reps = self.rep_repo.get_top_performers(practice_id, limit).await?;
        let summaries = reps.into_iter().map(|rep| rep.summary()).collect();
        Ok(summaries)
    }

    /// Get practice representatives summary
    pub async fn get_practice_representatives(&self, practice_id: Uuid) -> OctaveResult<Vec<InsuranceRepSummary>> {
        let summaries = self.rep_repo.get_summaries(practice_id).await?;
        Ok(summaries)
    }
}

/// Communication template service for template management
pub struct CommunicationTemplateService<T: CommunicationTemplateRepository, A: UserActivityRepository> {
    template_repo: T,
    activity_repo: A,
}

impl<T: CommunicationTemplateRepository, A: UserActivityRepository> CommunicationTemplateService<T, A> {
    /// Create a new template service
    pub fn new(template_repo: T, activity_repo: A) -> Self {
        Self { template_repo, activity_repo }
    }

    /// Create a new communication template
    pub async fn create_template(
        &self,
        practice_id: Uuid,
        name: String,
        category: TemplateCategory,
        communication_type: CommunicationType,
        subject_template: String,
        content_template: String,
        created_by: Uuid,
    ) -> OctaveResult<CommunicationTemplate> {
        let template = CommunicationTemplate::new(
            practice_id,
            name,
            category,
            communication_type,
            subject_template,
            content_template,
            created_by,
        );

        let created_template = self.template_repo.create(template).await?;

        // Log creation activity
        let activity = UserActivity::new(
            created_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Communication template created: {}", created_template.name),
        );
        self.activity_repo.create(activity).await?;

        Ok(created_template)
    }

    /// Get template by ID
    pub async fn get_template(&self, id: Uuid, accessed_by: Uuid) -> OctaveResult<Option<CommunicationTemplate>> {
        let template = self.template_repo.get_by_id(id).await?;

        if let Some(ref tmpl) = template {
            // Log access activity
            let activity = UserActivity::new(
                accessed_by,
                tmpl.practice_id,
                ActivityType::UserManagement,
                format!("Communication template accessed: {} ({})", tmpl.name, tmpl.id),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(template)
    }

    /// Render template with values
    pub async fn render_template(
        &self,
        id: Uuid,
        values: HashMap<String, String>,
        used_by: Uuid,
    ) -> OctaveResult<(String, String)> {
        let mut template = self.template_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("id", "Template not found"))?;

        let rendered = template.render(&values)?;

        // Record template usage
        template.record_usage(None);
        self.template_repo.update(template.clone()).await?;

        // Log template usage activity
        let activity = UserActivity::new(
            used_by,
            template.practice_id,
            ActivityType::UserManagement,
            format!("Communication template used: {}", template.name),
        );
        self.activity_repo.create(activity).await?;

        Ok(rendered)
    }

    /// Get templates by category
    pub async fn get_by_category(
        &self,
        practice_id: Uuid,
        category: TemplateCategory,
        accessed_by: Uuid,
    ) -> OctaveResult<Vec<CommunicationTemplateSummary>> {
        let templates = self.template_repo.get_by_category(practice_id, category.clone()).await?;
        let summaries = templates.into_iter().map(|tmpl| tmpl.summary()).collect();

        // Log category access activity
        let activity = UserActivity::new(
            accessed_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Templates accessed by category: {:?}", category),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Get most used templates
    pub async fn get_most_used(&self, practice_id: Uuid, limit: u32) -> OctaveResult<Vec<CommunicationTemplateSummary>> {
        let templates = self.template_repo.get_most_used(practice_id, limit).await?;
        let summaries = templates.into_iter().map(|tmpl| tmpl.summary()).collect();
        Ok(summaries)
    }

    /// Get practice templates summary
    pub async fn get_practice_templates(&self, practice_id: Uuid) -> OctaveResult<Vec<CommunicationTemplateSummary>> {
        let summaries = self.template_repo.get_summaries(practice_id).await?;
        Ok(summaries)
    }

    /// Record template effectiveness
    pub async fn record_effectiveness(
        &self,
        id: Uuid,
        effectiveness_rating: f32,
        rated_by: Uuid,
    ) -> OctaveResult<CommunicationTemplate> {
        let updated_template = self.template_repo.record_usage(id, Some(effectiveness_rating)).await?;

        // Log effectiveness rating activity
        let activity = UserActivity::new(
            rated_by,
            updated_template.practice_id,
            ActivityType::UserManagement,
            format!("Template effectiveness rated: {} (Rating: {:.1})",
                updated_template.name,
                effectiveness_rating
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_template)
    }
}

// ============================================================================
// REMINDER & NOTIFICATION SERVICES
// ============================================================================

/// Reminder creation data
#[derive(Debug, Clone)]
pub struct ReminderCreationData {
    pub reminder_type: ReminderType,
    pub title: String,
    pub description: String,
    pub scheduled_for: DateTime<Utc>,
    pub priority: ReminderPriority,
    pub assigned_to: Uuid,
    pub related_entity_type: EntityType,
    pub related_entity_id: Uuid,
    pub tags: Vec<String>,
    pub recurrence_pattern: Option<RecurrencePattern>,
    pub notification_preferences: Option<NotificationPreferences>,
}

/// Reminder search criteria
#[derive(Debug, Clone)]
pub struct ReminderSearchCriteria {
    pub reminder_type: Option<ReminderType>,
    pub status: Option<ReminderStatus>,
    pub priority: Option<ReminderPriority>,
    pub assigned_to: Option<Uuid>,
    pub related_entity_type: Option<EntityType>,
    pub related_entity_id: Option<Uuid>,
    pub date_range: Option<(DateTime<Utc>, DateTime<Utc>)>,
    pub tags: Vec<String>,
    pub is_overdue: Option<bool>,
    pub query: Option<String>,
}

/// Reminder service for comprehensive reminder management
pub struct ReminderService<R: ReminderRepository, A: UserActivityRepository> {
    reminder_repo: R,
    activity_repo: A,
}

impl<R: ReminderRepository, A: UserActivityRepository> ReminderService<R, A> {
    /// Create a new reminder service
    pub fn new(reminder_repo: R, activity_repo: A) -> Self {
        Self { reminder_repo, activity_repo }
    }

    /// Create a new reminder
    pub async fn create_reminder(
        &self,
        practice_id: Uuid,
        creation_data: ReminderCreationData,
        created_by: Uuid,
    ) -> OctaveResult<Reminder> {
        // Create reminder
        let mut reminder = Reminder::new(
            practice_id,
            creation_data.reminder_type,
            creation_data.title,
            creation_data.description,
            creation_data.scheduled_for,
            creation_data.assigned_to,
            created_by,
            creation_data.related_entity_type,
            creation_data.related_entity_id,
        );

        // Set additional fields
        reminder.priority = creation_data.priority;
        reminder.tags = creation_data.tags;
        reminder.recurrence_pattern = creation_data.recurrence_pattern;

        if let Some(prefs) = creation_data.notification_preferences {
            reminder.notification_preferences = prefs;
        }

        // Save reminder
        let created_reminder = self.reminder_repo.create(reminder).await?;

        // Log creation activity
        let activity = UserActivity::new(
            created_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Reminder created: {} ({})", created_reminder.title, created_reminder.id),
        );
        self.activity_repo.create(activity).await?;

        Ok(created_reminder)
    }

    /// Get reminder by ID with access logging
    pub async fn get_reminder(&self, id: Uuid, accessed_by: Uuid) -> OctaveResult<Option<Reminder>> {
        let reminder = self.reminder_repo.get_by_id(id).await?;

        if let Some(ref rem) = reminder {
            // Log access activity
            let activity = UserActivity::new(
                accessed_by,
                rem.practice_id,
                ActivityType::UserManagement,
                format!("Reminder accessed: {} ({})", rem.title, rem.id),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(reminder)
    }

    /// Search reminders
    pub async fn search_reminders(
        &self,
        practice_id: Uuid,
        criteria: ReminderSearchCriteria,
        searched_by: Uuid,
    ) -> OctaveResult<Vec<ReminderSummary>> {
        let mut results = Vec::new();

        // Search by different criteria
        if let Some(query) = criteria.query {
            let reminders = self.reminder_repo.search_reminders(practice_id, &query).await?;
            results.extend(reminders);
        } else {
            // Get all reminders for practice and filter
            let all_reminders = self.reminder_repo.get_by_practice(practice_id).await?;
            results = all_reminders;
        }

        // Apply filters
        if let Some(reminder_type) = criteria.reminder_type {
            results.retain(|rem| rem.reminder_type == reminder_type);
        }

        if let Some(status) = criteria.status {
            results.retain(|rem| rem.status == status);
        }

        if let Some(priority) = criteria.priority {
            results.retain(|rem| rem.priority == priority);
        }

        if let Some(assigned_to) = criteria.assigned_to {
            results.retain(|rem| rem.assigned_to == assigned_to);
        }

        if let Some(entity_type) = criteria.related_entity_type {
            results.retain(|rem| rem.related_entity_type == entity_type);
        }

        if let Some(entity_id) = criteria.related_entity_id {
            results.retain(|rem| rem.related_entity_id == entity_id);
        }

        if let Some(is_overdue) = criteria.is_overdue {
            results.retain(|rem| rem.is_overdue() == is_overdue);
        }

        // Convert to summaries
        let summaries: Vec<ReminderSummary> = results.into_iter()
            .map(|rem| rem.summary())
            .collect();

        // Log search activity
        let activity = UserActivity::new(
            searched_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Reminder search performed, {} results", summaries.len()),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Complete reminder
    pub async fn complete_reminder(
        &self,
        id: Uuid,
        completion_notes: Option<String>,
        completed_by: Uuid,
    ) -> OctaveResult<Reminder> {
        let completed_reminder = self.reminder_repo.complete_reminder(id, completion_notes).await?;

        // Log completion activity
        let activity = UserActivity::new(
            completed_by,
            completed_reminder.practice_id,
            ActivityType::UserManagement,
            format!("Reminder completed: {} ({})", completed_reminder.title, completed_reminder.id),
        );
        self.activity_repo.create(activity).await?;

        Ok(completed_reminder)
    }

    /// Snooze reminder
    pub async fn snooze_reminder(
        &self,
        id: Uuid,
        snooze_duration: chrono::Duration,
        snoozed_by: Uuid,
    ) -> OctaveResult<Reminder> {
        let new_due_time = Utc::now() + snooze_duration;
        let snoozed_reminder = self.reminder_repo.snooze_reminder(id, new_due_time).await?;

        // Log snooze activity
        let activity = UserActivity::new(
            snoozed_by,
            snoozed_reminder.practice_id,
            ActivityType::UserManagement,
            format!("Reminder snoozed: {} ({})", snoozed_reminder.title, snoozed_reminder.id),
        );
        self.activity_repo.create(activity).await?;

        Ok(snoozed_reminder)
    }

    /// Get due reminders
    pub async fn get_due_reminders(&self, practice_id: Uuid) -> OctaveResult<Vec<ReminderSummary>> {
        let due_reminders = self.reminder_repo.get_due_reminders(practice_id).await?;
        let summaries = due_reminders.into_iter().map(|rem| rem.summary()).collect();
        Ok(summaries)
    }

    /// Get overdue reminders
    pub async fn get_overdue_reminders(&self, practice_id: Uuid) -> OctaveResult<Vec<ReminderSummary>> {
        let overdue_reminders = self.reminder_repo.get_overdue_reminders(practice_id).await?;
        let summaries = overdue_reminders.into_iter().map(|rem| rem.summary()).collect();
        Ok(summaries)
    }

    /// Get upcoming reminders
    pub async fn get_upcoming_reminders(&self, practice_id: Uuid, hours: u32) -> OctaveResult<Vec<ReminderSummary>> {
        let upcoming_reminders = self.reminder_repo.get_upcoming_reminders(practice_id, hours).await?;
        let summaries = upcoming_reminders.into_iter().map(|rem| rem.summary()).collect();
        Ok(summaries)
    }

    /// Process escalations for overdue reminders
    pub async fn process_escalations(&self, practice_id: Uuid) -> OctaveResult<EscalationResult> {
        let escalation_candidates = self.reminder_repo.get_escalation_candidates(practice_id).await?;
        let mut escalated_count = 0;
        let mut errors = Vec::new();

        for mut reminder in escalation_candidates {
            let reminder_id = reminder.id;
            let triggered_rules = reminder.check_escalations();
            if !triggered_rules.is_empty() {
                match self.reminder_repo.update(reminder).await {
                    Ok(_) => escalated_count += 1,
                    Err(e) => errors.push(format!("Failed to escalate reminder {}: {}", reminder_id, e)),
                }
            }
        }

        // Log escalation processing
        let activity = UserActivity::new(
            Uuid::new_v4(), // System user
            practice_id,
            ActivityType::UserManagement,
            format!("Processed {} reminder escalations", escalated_count),
        );
        self.activity_repo.create(activity).await?;

        Ok(EscalationResult {
            escalated_count,
            errors,
            processed_at: Utc::now(),
        })
    }
}

/// Escalation processing result
#[derive(Debug, Clone)]
pub struct EscalationResult {
    pub escalated_count: usize,
    pub errors: Vec<String>,
    pub processed_at: DateTime<Utc>,
}

/// Notification service for multi-channel delivery
pub struct NotificationService<N: NotificationRepository, A: UserActivityRepository> {
    notification_repo: N,
    activity_repo: A,
}

impl<N: NotificationRepository, A: UserActivityRepository> NotificationService<N, A> {
    /// Create a new notification service
    pub fn new(notification_repo: N, activity_repo: A) -> Self {
        Self { notification_repo, activity_repo }
    }

    /// Send notification
    pub async fn send_notification(
        &self,
        practice_id: Uuid,
        notification_type: NotificationType,
        channel: NotificationChannel,
        recipient_id: Uuid,
        subject: String,
        content: String,
        related_entity_type: Option<EntityType>,
        related_entity_id: Option<Uuid>,
        sent_by: Uuid,
    ) -> OctaveResult<Notification> {
        // Create notification record
        let notification = Notification {
            id: Uuid::new_v4(),
            practice_id,
            notification_type,
            channel,
            recipient_id,
            subject,
            content: EncryptedPhi::new(content, PhiType::Other("notification_content".to_string())),
            delivery_status: DeliveryStatus::Pending,
            related_entity_type,
            related_entity_id,
            external_message_id: None,
            delivery_attempts: 0,
            last_attempt_at: None,
            delivered_at: None,
            read_at: None,
            error_message: None,
            metadata: "{}".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        // Save notification record
        let created_notification = self.notification_repo.create(notification).await?;

        // TODO: Integrate with actual notification delivery services
        // For now, mark as delivered after a delay
        tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

        let delivered_notification = self.notification_repo.update_delivery_status(
            created_notification.id,
            DeliveryStatus::Delivered,
            None,
        ).await?;

        // Log notification activity
        let activity = UserActivity::new(
            sent_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Notification sent: {} via {:?}", delivered_notification.subject, delivered_notification.channel),
        );
        self.activity_repo.create(activity).await?;

        Ok(delivered_notification)
    }

    /// Get notifications for user
    pub async fn get_user_notifications(
        &self,
        practice_id: Uuid,
        user_id: Uuid,
        accessed_by: Uuid,
    ) -> OctaveResult<Vec<Notification>> {
        let notifications = self.notification_repo.get_by_recipient(practice_id, user_id).await?;

        // Log access activity
        let activity = UserActivity::new(
            accessed_by,
            practice_id,
            ActivityType::UserManagement,
            format!("User notifications accessed for user: {}", user_id),
        );
        self.activity_repo.create(activity).await?;

        Ok(notifications)
    }

    /// Mark notification as read
    pub async fn mark_as_read(
        &self,
        id: Uuid,
        read_by: Uuid,
    ) -> OctaveResult<Notification> {
        let read_notification = self.notification_repo.mark_as_read(id).await?;

        // Log read activity
        let activity = UserActivity::new(
            read_by,
            read_notification.practice_id,
            ActivityType::UserManagement,
            format!("Notification read: {} ({})", read_notification.subject, read_notification.id),
        );
        self.activity_repo.create(activity).await?;

        Ok(read_notification)
    }

    /// Get pending notifications for delivery
    pub async fn get_pending_notifications(&self, practice_id: Uuid) -> OctaveResult<Vec<Notification>> {
        self.notification_repo.get_pending_notifications(practice_id).await
    }

    /// Process failed notifications for retry
    pub async fn process_failed_notifications(&self, practice_id: Uuid) -> OctaveResult<RetryResult> {
        let failed_notifications = self.notification_repo.get_failed_notifications(practice_id).await?;
        let mut retried_count = 0;
        let mut errors = Vec::new();

        for notification in failed_notifications {
            if notification.delivery_attempts < 3 {
                // TODO: Implement actual retry logic
                match self.notification_repo.update_delivery_status(
                    notification.id,
                    DeliveryStatus::Pending,
                    None,
                ).await {
                    Ok(_) => retried_count += 1,
                    Err(e) => errors.push(format!("Failed to retry notification {}: {}", notification.id, e)),
                }
            }
        }

        Ok(RetryResult {
            retried_count,
            errors,
            processed_at: Utc::now(),
        })
    }
}

/// Retry processing result
#[derive(Debug, Clone)]
pub struct RetryResult {
    pub retried_count: usize,
    pub errors: Vec<String>,
    pub processed_at: DateTime<Utc>,
}

/// Workflow engine service for state management
pub struct WorkflowEngineService<W: WorkflowRepository, A: UserActivityRepository> {
    workflow_repo: W,
    activity_repo: A,
}

impl<W: WorkflowRepository, A: UserActivityRepository> WorkflowEngineService<W, A> {
    /// Create a new workflow engine service
    pub fn new(workflow_repo: W, activity_repo: A) -> Self {
        Self { workflow_repo, activity_repo }
    }

    /// Initialize workflow for entity
    pub async fn initialize_workflow(
        &self,
        practice_id: Uuid,
        entity_type: EntityType,
        entity_id: Uuid,
        initial_status: String,
        workflow_definition_id: Uuid,
        initialized_by: Uuid,
    ) -> OctaveResult<WorkflowState> {
        let workflow_state = WorkflowState {
            id: Uuid::new_v4(),
            practice_id,
            entity_type: entity_type.clone(),
            entity_id,
            current_status: initial_status.clone(),
            previous_status: None,
            workflow_definition_id,
            state_data: "{}".to_string(),
            transition_history: Vec::new(),
            pending_actions: Vec::new(),
            metadata: "{}".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
        };

        let created_workflow = self.workflow_repo.create_workflow_state(workflow_state).await?;

        // Log workflow initialization
        let activity = UserActivity::new(
            initialized_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Workflow initialized: {:?} {} -> {}", entity_type, entity_id, initial_status),
        );
        self.activity_repo.create(activity).await?;

        Ok(created_workflow)
    }

    /// Transition workflow state
    pub async fn transition_state(
        &self,
        workflow_state_id: Uuid,
        to_status: String,
        reason: Option<String>,
        notes: Option<String>,
        triggered_by: Uuid,
        is_automatic: bool,
    ) -> OctaveResult<WorkflowState> {
        let mut workflow_state = self.workflow_repo.get_workflow_state_by_id(workflow_state_id).await?
            .ok_or_else(|| OctaveError::validation("id", "Workflow state not found"))?;

        // Create transition record
        let transition = StateTransition {
            id: Uuid::new_v4(),
            from_status: workflow_state.current_status.clone(),
            to_status: to_status.clone(),
            triggered_by,
            reason,
            notes,
            transitioned_at: Utc::now(),
            is_automatic,
            metadata: "{}".to_string(),
        };

        // Update workflow state
        workflow_state.previous_status = Some(workflow_state.current_status.clone());
        workflow_state.current_status = to_status.clone();
        workflow_state.updated_at = Utc::now();

        // Add transition to history
        let updated_workflow = self.workflow_repo.add_state_transition(workflow_state_id, transition).await?;

        // Log state transition
        let activity = UserActivity::new(
            triggered_by,
            updated_workflow.practice_id,
            ActivityType::UserManagement,
            format!("Workflow transition: {:?} {} -> {}",
                updated_workflow.entity_type,
                updated_workflow.entity_id,
                to_status
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_workflow)
    }

    /// Add pending action to workflow
    pub async fn add_pending_action(
        &self,
        workflow_state_id: Uuid,
        action_type: ActionType,
        assigned_to: Uuid,
        description: String,
        due_date: Option<DateTime<Utc>>,
        added_by: Uuid,
    ) -> OctaveResult<WorkflowState> {
        let action = PendingAction {
            id: Uuid::new_v4(),
            action_type: action_type.clone(),
            assigned_to,
            due_date,
            description,
            status: ActionStatus::Pending,
            created_at: Utc::now(),
            completed_at: None,
        };

        let updated_workflow = self.workflow_repo.add_pending_action(workflow_state_id, action).await?;

        // Log action addition
        let activity = UserActivity::new(
            added_by,
            updated_workflow.practice_id,
            ActivityType::UserManagement,
            format!("Pending action added: {:?} for {:?} {}",
                action_type,
                updated_workflow.entity_type,
                updated_workflow.entity_id
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_workflow)
    }

    /// Complete pending action
    pub async fn complete_pending_action(
        &self,
        workflow_state_id: Uuid,
        action_id: Uuid,
        completed_by: Uuid,
    ) -> OctaveResult<WorkflowState> {
        let updated_workflow = self.workflow_repo.complete_pending_action(workflow_state_id, action_id).await?;

        // Log action completion
        let activity = UserActivity::new(
            completed_by,
            updated_workflow.practice_id,
            ActivityType::UserManagement,
            format!("Pending action completed: {} for {:?} {}",
                action_id,
                updated_workflow.entity_type,
                updated_workflow.entity_id
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_workflow)
    }

    /// Get workflow state by entity
    pub async fn get_workflow_by_entity(
        &self,
        practice_id: Uuid,
        entity_type: EntityType,
        entity_id: Uuid,
        accessed_by: Uuid,
    ) -> OctaveResult<Option<WorkflowState>> {
        let workflow_state = self.workflow_repo.get_workflow_state_by_entity(practice_id, entity_type.clone(), entity_id).await?;

        if workflow_state.is_some() {
            // Log access activity
            let activity = UserActivity::new(
                accessed_by,
                practice_id,
                ActivityType::UserManagement,
                format!("Workflow accessed: {:?} {}", entity_type, entity_id),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(workflow_state)
    }

    /// Get workflows with pending actions
    pub async fn get_workflows_with_pending_actions(&self, practice_id: Uuid) -> OctaveResult<Vec<WorkflowState>> {
        self.workflow_repo.get_states_with_pending_actions(practice_id).await
    }
}

// ============================================================================
// TEMPLATE & AUTOMATION SERVICES
// ============================================================================

/// Template creation data
#[derive(Debug, Clone)]
pub struct TemplateCreationData {
    pub template_type: TemplateType,
    pub name: String,
    pub description: Option<String>,
    pub category: TemplateCategory,
    pub subject: Option<String>,
    pub content: String,
    pub tags: Vec<String>,
    pub is_shared: bool,
}

/// Template search criteria
#[derive(Debug, Clone)]
pub struct TemplateSearchCriteria {
    pub template_type: Option<TemplateType>,
    pub category: Option<TemplateCategory>,
    pub approval_status: Option<ApprovalStatus>,
    pub tags: Vec<String>,
    pub created_by: Option<Uuid>,
    pub is_active: Option<bool>,
    pub is_shared: Option<bool>,
    pub min_effectiveness: Option<f32>,
    pub query: Option<String>,
}

/// Template engine service for comprehensive template management
pub struct TemplateEngineService<T: TemplateRepository, V: TemplateVersionRepository, U: TemplateUsageRepository, A: UserActivityRepository> {
    template_repo: T,
    version_repo: V,
    usage_repo: U,
    activity_repo: A,
}

impl<T: TemplateRepository, V: TemplateVersionRepository, U: TemplateUsageRepository, A: UserActivityRepository>
    TemplateEngineService<T, V, U, A> {

    /// Create a new template engine service
    pub fn new(template_repo: T, version_repo: V, usage_repo: U, activity_repo: A) -> Self {
        Self { template_repo, version_repo, usage_repo, activity_repo }
    }

    /// Create a new template
    pub async fn create_template(
        &self,
        practice_id: Uuid,
        creation_data: TemplateCreationData,
        created_by: Uuid,
    ) -> OctaveResult<Template> {
        // Create template
        let mut template = Template::new(
            practice_id,
            creation_data.template_type,
            creation_data.name,
            creation_data.content.clone(),
            creation_data.category,
            created_by,
        );

        // Set additional fields
        template.description = creation_data.description;
        template.subject = creation_data.subject;
        template.tags = creation_data.tags;
        template.is_shared = creation_data.is_shared;

        // Save template
        let created_template = self.template_repo.create(template).await?;

        // Create initial version
        let version = TemplateVersion {
            id: Uuid::new_v4(),
            template_id: created_template.id,
            version: 1,
            content: EncryptedPhi::new(creation_data.content, PhiType::Other("template_content".to_string())),
            merge_fields: created_template.merge_fields.clone(),
            conditional_logic: created_template.conditional_logic.clone(),
            change_description: Some("Initial version".to_string()),
            created_by,
            created_at: Utc::now(),
        };
        self.version_repo.create_version(version).await?;

        // Log creation activity
        let activity = UserActivity::new(
            created_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Template created: {} ({})", created_template.name, created_template.id),
        );
        self.activity_repo.create(activity).await?;

        Ok(created_template)
    }

    /// Get template by ID with access logging
    pub async fn get_template(&self, id: Uuid, accessed_by: Uuid) -> OctaveResult<Option<Template>> {
        let template = self.template_repo.get_by_id(id).await?;

        if let Some(ref tmpl) = template {
            // Log access activity
            let activity = UserActivity::new(
                accessed_by,
                tmpl.practice_id,
                ActivityType::UserManagement,
                format!("Template accessed: {} ({})", tmpl.name, tmpl.id),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(template)
    }

    /// Render template with merge values
    pub async fn render_template(
        &self,
        id: Uuid,
        merge_values: HashMap<String, String>,
        context: UsageContext,
        used_by: Uuid,
    ) -> OctaveResult<String> {
        let mut template = self.template_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("id", "Template not found"))?;

        // Check if template is approved for use
        if !template.approval_status.is_usable() {
            return Err(OctaveError::validation("approval_status", "Template is not approved for use"));
        }

        // Render template
        let rendered_content = template.render(&merge_values)?;

        // Record usage
        let usage = TemplateUsage {
            id: Uuid::new_v4(),
            template_id: id,
            practice_id: template.practice_id,
            used_by,
            usage_context: context,
            related_entity_type: None,
            related_entity_id: None,
            effectiveness_score: None,
            notes: None,
            used_at: Utc::now(),
        };
        self.usage_repo.record_usage(usage).await?;

        // Update template usage count
        template.record_usage(None);
        self.template_repo.update(template.clone()).await?;

        // Log template usage activity
        let activity = UserActivity::new(
            used_by,
            template.practice_id,
            ActivityType::UserManagement,
            format!("Template rendered: {} ({})", template.name, template.id),
        );
        self.activity_repo.create(activity).await?;

        Ok(rendered_content)
    }

    /// Get template suggestions based on context
    pub async fn get_template_suggestions(
        &self,
        practice_id: Uuid,
        context_type: ContextType,
        entity_type: Option<EntityType>,
        limit: u32,
    ) -> OctaveResult<Vec<TemplateSummary>> {
        // Get templates by effectiveness for the given context
        let effective_templates = self.template_repo.get_by_effectiveness(practice_id, 0.7).await?;

        // Filter by context relevance (simplified logic)
        let relevant_templates: Vec<Template> = effective_templates.into_iter()
            .filter(|tmpl| {
                match context_type {
                    ContextType::Email => tmpl.template_type == TemplateType::Email,
                    ContextType::SMS => tmpl.template_type == TemplateType::SMS,
                    ContextType::Document => tmpl.template_type == TemplateType::Document,
                    ContextType::PriorAuth => tmpl.template_type == TemplateType::PriorAuth,
                    ContextType::Patient => tmpl.template_type == TemplateType::Patient,
                    ContextType::Insurance => tmpl.template_type == TemplateType::Insurance,
                    _ => true,
                }
            })
            .take(limit as usize)
            .collect();

        let summaries = relevant_templates.into_iter()
            .map(|tmpl| tmpl.summary())
            .collect();

        Ok(summaries)
    }

    /// Record template effectiveness
    pub async fn record_effectiveness(
        &self,
        template_id: Uuid,
        effectiveness_score: f32,
        context: UsageContext,
        rated_by: Uuid,
    ) -> OctaveResult<Template> {
        let updated_template = self.template_repo.record_usage(template_id, Some(effectiveness_score)).await?;

        // Update usage record with effectiveness score
        let usage = TemplateUsage {
            id: Uuid::new_v4(),
            template_id,
            practice_id: updated_template.practice_id,
            used_by: rated_by,
            usage_context: context,
            related_entity_type: None,
            related_entity_id: None,
            effectiveness_score: Some(effectiveness_score),
            notes: Some("Effectiveness rating".to_string()),
            used_at: Utc::now(),
        };
        self.usage_repo.record_usage(usage).await?;

        // Log effectiveness rating activity
        let activity = UserActivity::new(
            rated_by,
            updated_template.practice_id,
            ActivityType::UserManagement,
            format!("Template effectiveness rated: {} (Score: {:.1})",
                updated_template.name,
                effectiveness_score
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_template)
    }
}

// ============================================================================
// ANALYTICS & REPORTING SERVICES
// ============================================================================

/// Analytics data collection criteria
#[derive(Debug, Clone)]
pub struct AnalyticsCollectionCriteria {
    pub metric_types: Vec<MetricType>,
    pub aggregation_periods: Vec<AggregationPeriod>,
    pub entity_types: Vec<EntityType>,
    pub start_time: DateTime<Utc>,
    pub end_time: DateTime<Utc>,
    pub include_phi: bool,
}

/// Report generation request
#[derive(Debug, Clone)]
pub struct ReportGenerationRequest {
    pub report_type: ReportType,
    pub name: String,
    pub description: Option<String>,
    pub format: ReportFormat,
    pub parameters: HashMap<String, String>,
    pub schedule: Option<ReportSchedule>,
}

/// Report schedule configuration
#[derive(Debug, Clone)]
pub struct ReportSchedule {
    pub frequency: ScheduleFrequency,
    pub start_date: DateTime<Utc>,
    pub end_date: Option<DateTime<Utc>>,
    pub delivery_method: DeliveryMethod,
    pub recipients: Vec<Uuid>,
}

/// Schedule frequency enumeration
#[derive(Debug, Clone)]
pub enum ScheduleFrequency {
    Daily,
    Weekly,
    Monthly,
    Quarterly,
    Custom(chrono::Duration),
}

/// Delivery method enumeration
#[derive(Debug, Clone)]
pub enum DeliveryMethod {
    Email,
    Portal,
    API,
    FileSystem,
}

/// Analytics engine service for comprehensive data collection and analysis
pub struct AnalyticsEngineService<A: AnalyticsRepository, P: PerformanceRepository, U: UserActivityRepository> {
    analytics_repo: A,
    performance_repo: P,
    activity_repo: U,
}

impl<A: AnalyticsRepository, P: PerformanceRepository, U: UserActivityRepository>
    AnalyticsEngineService<A, P, U> {

    /// Create a new analytics engine service
    pub fn new(analytics_repo: A, performance_repo: P, activity_repo: U) -> Self {
        Self { analytics_repo, performance_repo, activity_repo }
    }

    /// Record analytics metric
    pub async fn record_metric(
        &self,
        practice_id: Uuid,
        metric_type: MetricType,
        metric_name: String,
        metric_value: f64,
        dimensions: HashMap<String, String>,
        aggregation_period: AggregationPeriod,
        recorded_by: Option<Uuid>,
    ) -> OctaveResult<AnalyticsMetric> {
        let mut metric = AnalyticsMetric::new(
            practice_id,
            metric_type,
            metric_name,
            metric_value,
            aggregation_period,
        );

        // Add dimensions
        for (key, value) in dimensions {
            metric.add_dimension(key, value);
        }

        if let Some(user_id) = recorded_by {
            metric.created_by = Some(user_id);
        }

        // Record metric
        let recorded_metric = self.analytics_repo.record_metric(metric).await?;

        // Log analytics activity (if user provided)
        if let Some(user_id) = recorded_by {
            let activity = UserActivity::new(
                user_id,
                practice_id,
                ActivityType::UserManagement,
                format!("Analytics metric recorded: {} = {}", recorded_metric.metric_name, recorded_metric.metric_value),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(recorded_metric)
    }

    /// Get approval rate analytics by insurance
    pub async fn get_approval_rate_by_insurance(
        &self,
        practice_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        accessed_by: Uuid,
    ) -> OctaveResult<Vec<AnalyticsMetricSummary>> {
        let metrics = self.analytics_repo.get_metrics_by_type(
            practice_id,
            MetricType::ApprovalRate,
            start_time,
            end_time,
        ).await?;

        // Convert to summaries to remove PHI
        let summaries = metrics.into_iter()
            .map(|metric| metric.summary())
            .collect();

        // Log access activity
        let activity = UserActivity::new(
            accessed_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Approval rate analytics accessed for period {} to {}",
                start_time.format("%Y-%m-%d"),
                end_time.format("%Y-%m-%d")
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Get processing time analytics
    pub async fn get_processing_time_analytics(
        &self,
        practice_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        accessed_by: Uuid,
    ) -> OctaveResult<Vec<AnalyticsMetricSummary>> {
        let metrics = self.analytics_repo.get_metrics_by_type(
            practice_id,
            MetricType::ProcessingTime,
            start_time,
            end_time,
        ).await?;

        let summaries = metrics.into_iter()
            .map(|metric| metric.summary())
            .collect();

        // Log access activity
        let activity = UserActivity::new(
            accessed_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Processing time analytics accessed for period {} to {}",
                start_time.format("%Y-%m-%d"),
                end_time.format("%Y-%m-%d")
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Get provider performance metrics
    pub async fn get_provider_performance_metrics(
        &self,
        practice_id: Uuid,
        provider_id: Option<Uuid>,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        accessed_by: Uuid,
    ) -> OctaveResult<Vec<AnalyticsMetricSummary>> {
        let mut metrics = self.analytics_repo.get_metrics_by_type(
            practice_id,
            MetricType::ProviderPerformance,
            start_time,
            end_time,
        ).await?;

        // Filter by provider if specified
        if let Some(provider_id) = provider_id {
            metrics.retain(|metric| {
                metric.dimensions.get("provider_id")
                    .map(|id| id == &provider_id.to_string())
                    .unwrap_or(false)
            });
        }

        let summaries = metrics.into_iter()
            .map(|metric| metric.summary())
            .collect();

        // Log access activity
        let activity = UserActivity::new(
            accessed_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Provider performance metrics accessed for period {} to {}",
                start_time.format("%Y-%m-%d"),
                end_time.format("%Y-%m-%d")
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Get real-time dashboard metrics
    pub async fn get_real_time_dashboard_metrics(
        &self,
        practice_id: Uuid,
        accessed_by: Uuid,
    ) -> OctaveResult<Vec<AnalyticsMetricSummary>> {
        let metrics = self.analytics_repo.get_real_time_metrics(practice_id).await?;

        let summaries = metrics.into_iter()
            .map(|metric| metric.summary())
            .collect();

        // Log access activity
        let activity = UserActivity::new(
            accessed_by,
            practice_id,
            ActivityType::UserManagement,
            "Real-time dashboard metrics accessed".to_string(),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Record performance metric
    pub async fn record_performance_metric(
        &self,
        practice_id: Uuid,
        metric_type: PerformanceMetricType,
        metric_name: String,
        metric_value: f64,
        threshold: Option<f64>,
        dimensions: HashMap<String, String>,
    ) -> OctaveResult<PerformanceMetric> {
        let mut metric = PerformanceMetric::new(
            practice_id,
            metric_type,
            metric_name,
            metric_value,
        );

        // Set threshold if provided
        if let Some(threshold) = threshold {
            metric.set_threshold(threshold);
        }

        // Add dimensions
        for (key, value) in dimensions {
            metric.add_dimension(key, value);
        }

        // Record metric
        let recorded_metric = self.performance_repo.record_metric(metric).await?;

        Ok(recorded_metric)
    }

    /// Get system health metrics
    pub async fn get_system_health_metrics(
        &self,
        practice_id: Uuid,
        accessed_by: Uuid,
    ) -> OctaveResult<Vec<PerformanceMetricSummary>> {
        let metrics = self.performance_repo.get_system_health_metrics(practice_id).await?;

        let summaries = metrics.into_iter()
            .map(|metric| metric.summary())
            .collect();

        // Log access activity
        let activity = UserActivity::new(
            accessed_by,
            practice_id,
            ActivityType::UserManagement,
            "System health metrics accessed".to_string(),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }
}

/// Reporting service for comprehensive report generation and management
pub struct ReportingService<R: ReportRepository, A: AnalyticsRepository, U: UserActivityRepository> {
    report_repo: R,
    analytics_repo: A,
    activity_repo: U,
}

impl<R: ReportRepository, A: AnalyticsRepository, U: UserActivityRepository>
    ReportingService<R, A, U> {

    /// Create a new reporting service
    pub fn new(report_repo: R, analytics_repo: A, activity_repo: U) -> Self {
        Self { report_repo, analytics_repo, activity_repo }
    }

    /// Generate report
    pub async fn generate_report(
        &self,
        practice_id: Uuid,
        request: ReportGenerationRequest,
        requested_by: Uuid,
    ) -> OctaveResult<Report> {
        // Create report record
        let mut report = Report::new(
            practice_id,
            request.report_type,
            request.name,
            request.format,
            request.parameters,
            requested_by,
        );

        report.description = request.description;

        // Save report record
        let created_report = self.report_repo.create(report).await?;

        // Start generation process (in real implementation, this would be async)
        let mut report_to_update = created_report.clone();
        report_to_update.start_generation();
        let updated_report = self.report_repo.update(report_to_update).await?;

        // Log report generation activity
        let activity = UserActivity::new(
            requested_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Report generation started: {} ({})", updated_report.name, updated_report.id),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_report)
    }

    /// Get report by ID with access logging
    pub async fn get_report(
        &self,
        id: Uuid,
        accessed_by: Uuid,
    ) -> OctaveResult<Option<Report>> {
        let mut report = self.report_repo.get_by_id(id).await?;

        if let Some(ref mut report) = report {
            // Record access
            report.record_access();
            let updated_report = self.report_repo.update(report.clone()).await?;

            // Log access activity
            let activity = UserActivity::new(
                accessed_by,
                report.practice_id,
                ActivityType::UserManagement,
                format!("Report accessed: {} ({})", report.name, report.id),
            );
            self.activity_repo.create(activity).await?;

            return Ok(Some(updated_report));
        }

        Ok(None)
    }

    /// Get practice reports
    pub async fn get_practice_reports(
        &self,
        practice_id: Uuid,
        accessed_by: Uuid,
    ) -> OctaveResult<Vec<ReportSummary>> {
        let summaries = self.report_repo.get_summaries(practice_id).await?;

        // Log access activity
        let activity = UserActivity::new(
            accessed_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Practice reports accessed, {} reports", summaries.len()),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Export report in specified format
    pub async fn export_report(
        &self,
        id: Uuid,
        format: ReportFormat,
        exported_by: Uuid,
    ) -> OctaveResult<Vec<u8>> {
        let report = self.report_repo.get_by_id(id).await?
            .ok_or_else(|| OctaveError::validation("id", "Report not found"))?;

        // Check if report is completed
        if report.status != ReportStatus::Completed {
            return Err(OctaveError::validation("status", "Report is not completed"));
        }

        // Get report content
        let content = report.content
            .ok_or_else(|| OctaveError::validation("content", "Report content not available"))?
            .decrypt()?;

        // Convert to requested format (simplified implementation)
        let exported_data = match format {
            ReportFormat::PDF => self.convert_to_pdf(&content).await?,
            ReportFormat::CSV => self.convert_to_csv(&content).await?,
            ReportFormat::Excel => self.convert_to_excel(&content).await?,
            ReportFormat::JSON => content.into_bytes(),
            ReportFormat::HTML => self.convert_to_html(&content).await?,
        };

        // Log export activity
        let activity = UserActivity::new(
            exported_by,
            report.practice_id,
            ActivityType::UserManagement,
            format!("Report exported: {} as {}", report.name, format.as_str()),
        );
        self.activity_repo.create(activity).await?;

        Ok(exported_data)
    }

    /// Convert content to PDF format
    async fn convert_to_pdf(&self, _content: &str) -> OctaveResult<Vec<u8>> {
        // TODO: Implement PDF conversion
        Ok(b"PDF content placeholder".to_vec())
    }

    /// Convert content to CSV format
    async fn convert_to_csv(&self, content: &str) -> OctaveResult<Vec<u8>> {
        // TODO: Implement CSV conversion
        Ok(content.as_bytes().to_vec())
    }

    /// Convert content to Excel format
    async fn convert_to_excel(&self, _content: &str) -> OctaveResult<Vec<u8>> {
        // TODO: Implement Excel conversion
        Ok(b"Excel content placeholder".to_vec())
    }

    /// Convert content to HTML format
    async fn convert_to_html(&self, content: &str) -> OctaveResult<Vec<u8>> {
        let html = format!(
            r#"<!DOCTYPE html>
<html>
<head>
    <title>Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .report-content {{ white-space: pre-wrap; }}
    </style>
</head>
<body>
    <div class="report-content">{}</div>
</body>
</html>"#,
            content
        );
        Ok(html.into_bytes())
    }
}

/// Compliance monitoring service for HIPAA compliance and violation tracking
pub struct ComplianceMonitoringService<C: ComplianceRepository, U: UserActivityRepository> {
    compliance_repo: C,
    activity_repo: U,
}

impl<C: ComplianceRepository, U: UserActivityRepository>
    ComplianceMonitoringService<C, U> {

    /// Create a new compliance monitoring service
    pub fn new(compliance_repo: C, activity_repo: U) -> Self {
        Self { compliance_repo, activity_repo }
    }

    /// Record compliance violation
    pub async fn record_violation(
        &self,
        practice_id: Uuid,
        violation_type: ComplianceViolationType,
        title: String,
        description: String,
        detection_method: String,
        detected_by: Option<Uuid>,
    ) -> OctaveResult<ComplianceViolation> {
        let mut violation = ComplianceViolation::new(
            practice_id,
            violation_type,
            title,
            description,
            detection_method,
        );

        violation.detected_by = detected_by;

        // Record violation
        let recorded_violation = self.compliance_repo.record_violation(violation).await?;

        // Log violation detection activity
        if let Some(user_id) = detected_by {
            let activity = UserActivity::new(
                user_id,
                practice_id,
                ActivityType::UserManagement,
                format!("Compliance violation detected: {} ({})",
                    recorded_violation.title,
                    recorded_violation.id
                ),
            );
            self.activity_repo.create(activity).await?;
        }

        Ok(recorded_violation)
    }

    /// Get HIPAA compliance report
    pub async fn get_hipaa_compliance_report(
        &self,
        practice_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
        accessed_by: Uuid,
    ) -> OctaveResult<Vec<ComplianceViolationSummary>> {
        let violations = self.compliance_repo.get_violations_by_date_range(
            practice_id,
            start_date,
            end_date,
        ).await?;

        let summaries = violations.into_iter()
            .map(|violation| violation.summary())
            .collect();

        // Log access activity
        let activity = UserActivity::new(
            accessed_by,
            practice_id,
            ActivityType::UserManagement,
            format!("HIPAA compliance report accessed for period {} to {}",
                start_date.format("%Y-%m-%d"),
                end_date.format("%Y-%m-%d")
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(summaries)
    }

    /// Get compliance score
    pub async fn get_compliance_score(
        &self,
        practice_id: Uuid,
        accessed_by: Uuid,
    ) -> OctaveResult<f64> {
        let score = self.compliance_repo.get_compliance_score(practice_id).await?;

        // Log access activity
        let activity = UserActivity::new(
            accessed_by,
            practice_id,
            ActivityType::UserManagement,
            format!("Compliance score accessed: {:.2}", score),
        );
        self.activity_repo.create(activity).await?;

        Ok(score)
    }

    /// Resolve compliance violation
    pub async fn resolve_violation(
        &self,
        violation_id: Uuid,
        resolution_notes: String,
        resolved_by: Uuid,
    ) -> OctaveResult<ComplianceViolation> {
        let mut violation = self.compliance_repo.get_violation_by_id(violation_id).await?
            .ok_or_else(|| OctaveError::validation("id", "Violation not found"))?;

        violation.resolve(resolved_by, resolution_notes);
        let updated_violation = self.compliance_repo.update_violation(violation).await?;

        // Log resolution activity
        let activity = UserActivity::new(
            resolved_by,
            updated_violation.practice_id,
            ActivityType::UserManagement,
            format!("Compliance violation resolved: {} ({})",
                updated_violation.title,
                updated_violation.id
            ),
        );
        self.activity_repo.create(activity).await?;

        Ok(updated_violation)
    }
}
