[package]
name = "octave-database"
version = "0.1.0"
edition = "2021"
description = "OCTAVE skeleton database for threat patterns, learning data, and metrics storage"
authors = ["OCTAVE Team"]

[dependencies]
# Core dependencies
octave-core = { path = "../octave-core" }

# Async runtime
tokio = { workspace = true }
async-trait = { workspace = true }

# Serialization
serde = { workspace = true }
serde_json = { workspace = true }

# Time handling
chrono = { workspace = true }

# Logging
tracing = { workspace = true }

# Error handling
thiserror = { workspace = true }

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"], default-features = false }

# UUID generation
uuid = { workspace = true }

# Collections and utilities
indexmap = "2.0"

# Encoding
base64 = "0.21"

# Random number generation
rand = "0.8"

# Cryptographic hashing
sha2 = "0.10"

[dev-dependencies]
tokio-test = "0.4"
tempfile = "3.8"
