# OCTAVE Healthcare Development Documentation

## Overview

This documentation provides comprehensive information for developers working on the OCTAVE Healthcare system. It covers architecture, development setup, coding standards, testing procedures, and deployment processes.

## System Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Backend       │
│   (React/TS)    │◄──►│   (NGINX)       │◄──►│   (Rust)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CDN           │    │   Load Balancer │    │   Database      │
│   (CloudFlare)  │    │   (Kubernetes)  │    │   (PostgreSQL)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Technology Stack

#### Backend
- **Language**: Rust 1.75+
- **Framework**: Axum (async web framework)
- **Database**: PostgreSQL 15+
- **ORM**: SQLx (async SQL toolkit)
- **Authentication**: JWT with RS256
- **Caching**: Redis
- **Message Queue**: RabbitMQ

#### Frontend
- **Language**: TypeScript 5.0+
- **Framework**: React 18+
- **State Management**: Redux Toolkit
- **UI Library**: Material-UI (MUI)
- **Build Tool**: Vite
- **Testing**: Jest + React Testing Library

#### Infrastructure
- **Container Runtime**: Docker
- **Orchestration**: Kubernetes
- **Service Mesh**: Istio
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack (Elasticsearch, Logstash, Kibana)
- **CI/CD**: GitHub Actions

### Microservices Architecture

```
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Auth Service  │  │   Patient       │  │   Prior Auth    │
│   (octave-auth) │  │   Service       │  │   Service       │
│                 │  │   (octave-      │  │   (octave-      │
│                 │  │   healthcare)   │  │   core)         │
└─────────────────┘  └─────────────────┘  └─────────────────┘
         │                     │                     │
         └─────────────────────┼─────────────────────┘
                               │
┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐
│   Semantic      │  │   Database      │  │   Compliance    │
│   Protection    │  │   Service       │  │   Service       │
│   (octave-      │  │   (octave-db)   │  │   (octave-      │
│   semantic)     │  │                 │  │   compliance)   │
└─────────────────┘  └─────────────────┘  └─────────────────┘
```

## Development Environment Setup

### Prerequisites

#### Required Software
```bash
# Rust toolchain
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
rustup update stable

# Node.js and npm
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Docker and Docker Compose
sudo apt-get update
sudo apt-get install docker.io docker-compose

# Kubernetes tools
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# PostgreSQL client
sudo apt-get install postgresql-client
```

#### Development Tools
```bash
# Rust development tools
cargo install cargo-watch
cargo install sqlx-cli
cargo install cargo-audit
cargo install cargo-tarpaulin

# Frontend development tools
npm install -g @typescript-eslint/parser
npm install -g prettier
npm install -g @storybook/cli
```

### Local Development Setup

#### 1. Clone Repository
```bash
git clone https://github.com/octave-healthcare/octave-system.git
cd octave-system
```

#### 2. Environment Configuration
```bash
# Copy environment template
cp .env.development.template .env.development

# Edit environment variables
nano .env.development
```

Required environment variables:
```env
# Database
DATABASE_URL=postgresql://octave_dev:dev_password@localhost:5432/octave_development

# JWT Configuration
JWT_SECRET=your-development-jwt-secret-key-minimum-64-characters
JWT_REFRESH_SECRET=your-development-refresh-secret-key-minimum-64-characters

# Redis
REDIS_URL=redis://localhost:6379

# External APIs
INSURANCE_API_BASE_URL=https://sandbox-insurance-api.com
MEDICAL_CODING_API_KEY=your-development-api-key

# Logging
RUST_LOG=debug
OCTAVE_LOG_LEVEL=debug
```

#### 3. Database Setup
```bash
# Start PostgreSQL with Docker
docker-compose up -d postgres redis

# Run database migrations
cd backend
sqlx migrate run --database-url $DATABASE_URL

# Seed development data
cargo run --bin seed-dev-data
```

#### 4. Backend Development
```bash
cd backend

# Install dependencies and build
cargo build

# Run development server with auto-reload
cargo watch -x "run --bin octave-server"

# Run tests
cargo test

# Run with specific log level
RUST_LOG=debug cargo run --bin octave-server
```

#### 5. Frontend Development
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Run Storybook for component development
npm run storybook
```

### Development Workflow

#### Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature description"

# Push and create pull request
git push origin feature/new-feature-name
```

#### Commit Message Convention
```
feat: add new feature
fix: bug fix
docs: documentation changes
style: formatting changes
refactor: code refactoring
test: adding tests
chore: maintenance tasks
```

## Coding Standards

### Rust Code Standards

#### Project Structure
```
backend/
├── Cargo.toml
├── src/
│   ├── main.rs
│   ├── lib.rs
│   ├── config/
│   ├── handlers/
│   ├── models/
│   ├── services/
│   ├── middleware/
│   └── utils/
├── migrations/
├── tests/
└── benches/
```

#### Naming Conventions
```rust
// Modules: snake_case
mod prior_authorization;

// Structs: PascalCase
struct PriorAuthorization {
    // Fields: snake_case
    patient_id: String,
    procedure_code: String,
}

// Functions: snake_case
fn create_prior_authorization() -> Result<PriorAuthorization, Error> {
    // Constants: SCREAMING_SNAKE_CASE
    const MAX_RETRY_ATTEMPTS: u32 = 3;
    
    // Variables: snake_case
    let auth_request = PriorAuthorization::new();
    
    Ok(auth_request)
}

// Enums: PascalCase
enum AuthorizationStatus {
    Pending,
    Approved,
    Denied,
}
```

#### Error Handling
```rust
use anyhow::{Context, Result};
use thiserror::Error;

#[derive(Error, Debug)]
pub enum AuthError {
    #[error("Invalid credentials")]
    InvalidCredentials,
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    #[error("Network error: {0}")]
    Network(String),
}

// Use Result<T, E> for fallible operations
pub async fn authenticate_user(credentials: &Credentials) -> Result<User, AuthError> {
    let user = database::find_user(&credentials.email)
        .await
        .context("Failed to query user database")?;
    
    if !verify_password(&credentials.password, &user.password_hash) {
        return Err(AuthError::InvalidCredentials);
    }
    
    Ok(user)
}
```

#### Documentation
```rust
/// Creates a new prior authorization request.
/// 
/// # Arguments
/// 
/// * `patient_id` - The unique identifier for the patient
/// * `procedure_code` - The CPT/HCPCS procedure code
/// * `diagnosis_code` - The ICD-10 diagnosis code
/// 
/// # Returns
/// 
/// Returns a `Result` containing the created `PriorAuthorization` or an error.
/// 
/// # Examples
/// 
/// ```
/// let auth = create_prior_authorization(
///     "patient_123",
///     "99213",
///     "Z00.00"
/// ).await?;
/// ```
pub async fn create_prior_authorization(
    patient_id: &str,
    procedure_code: &str,
    diagnosis_code: &str,
) -> Result<PriorAuthorization, AuthError> {
    // Implementation
}
```

### TypeScript/React Standards

#### Project Structure
```
frontend/
├── package.json
├── src/
│   ├── components/
│   │   ├── common/
│   │   ├── forms/
│   │   └── pages/
│   ├── hooks/
│   ├── services/
│   ├── store/
│   ├── types/
│   ├── utils/
│   └── App.tsx
├── public/
├── tests/
└── stories/
```

#### Component Standards
```typescript
// Use functional components with TypeScript
interface PriorAuthFormProps {
  patientId: string;
  onSubmit: (data: PriorAuthData) => Promise<void>;
  onCancel: () => void;
}

export const PriorAuthForm: React.FC<PriorAuthFormProps> = ({
  patientId,
  onSubmit,
  onCancel,
}) => {
  const [formData, setFormData] = useState<PriorAuthData>({
    procedureCode: '',
    diagnosisCode: '',
    clinicalNotes: '',
  });

  const handleSubmit = useCallback(async (event: React.FormEvent) => {
    event.preventDefault();
    
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Failed to submit prior authorization:', error);
    }
  }, [formData, onSubmit]);

  return (
    <form onSubmit={handleSubmit}>
      {/* Form implementation */}
    </form>
  );
};
```

#### Type Definitions
```typescript
// API response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
}

// Domain models
export interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female' | 'other';
  insurance: InsuranceInfo;
  createdAt: string;
  updatedAt: string;
}

export interface PriorAuthorization {
  id: string;
  patientId: string;
  procedureCode: string;
  diagnosisCode: string;
  status: 'pending' | 'approved' | 'denied';
  submittedAt: string;
  decidedAt?: string;
}
```

## Testing

### Backend Testing

#### Unit Tests
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;

    #[tokio::test]
    async fn test_create_prior_authorization() {
        let auth = create_prior_authorization(
            "patient_123",
            "99213",
            "Z00.00"
        ).await;

        assert!(auth.is_ok());
        let auth = auth.unwrap();
        assert_eq!(auth.patient_id, "patient_123");
        assert_eq!(auth.procedure_code, "99213");
    }

    #[tokio::test]
    async fn test_invalid_procedure_code() {
        let result = create_prior_authorization(
            "patient_123",
            "invalid",
            "Z00.00"
        ).await;

        assert!(result.is_err());
    }
}
```

#### Integration Tests
```rust
// tests/integration_test.rs
use octave_healthcare::*;
use sqlx::PgPool;

#[tokio::test]
async fn test_full_prior_auth_workflow() {
    let pool = setup_test_database().await;
    
    // Create test patient
    let patient = create_test_patient(&pool).await;
    
    // Create prior authorization
    let auth = create_prior_authorization(
        &patient.id,
        "99213",
        "Z00.00"
    ).await.unwrap();
    
    // Submit for approval
    let result = submit_prior_authorization(&auth.id).await;
    assert!(result.is_ok());
    
    cleanup_test_data(&pool).await;
}
```

### Frontend Testing

#### Component Tests
```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PriorAuthForm } from './PriorAuthForm';

describe('PriorAuthForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders form fields correctly', () => {
    render(
      <PriorAuthForm
        patientId="patient_123"
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByLabelText(/procedure code/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/diagnosis code/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/clinical notes/i)).toBeInTheDocument();
  });

  test('submits form with correct data', async () => {
    render(
      <PriorAuthForm
        patientId="patient_123"
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    fireEvent.change(screen.getByLabelText(/procedure code/i), {
      target: { value: '99213' }
    });
    
    fireEvent.change(screen.getByLabelText(/diagnosis code/i), {
      target: { value: 'Z00.00' }
    });

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        procedureCode: '99213',
        diagnosisCode: 'Z00.00',
        clinicalNotes: '',
      });
    });
  });
});
```

### Running Tests

#### Backend Tests
```bash
# Run all tests
cargo test

# Run tests with coverage
cargo tarpaulin --out Html

# Run specific test
cargo test test_create_prior_authorization

# Run integration tests
cargo test --test integration_test
```

#### Frontend Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm test -- --watch

# Run tests with coverage
npm test -- --coverage

# Run specific test file
npm test PriorAuthForm.test.tsx
```

## Deployment

### CI/CD Pipeline

#### GitHub Actions Workflow
```yaml
# .github/workflows/ci.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          
      - name: Run backend tests
        run: |
          cd backend
          cargo test
          
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          
      - name: Run frontend tests
        run: |
          cd frontend
          npm ci
          npm test

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to production
        run: |
          # Deployment steps
```

### Production Deployment

#### Docker Build
```bash
# Build backend image
docker build -f deployment/Dockerfile.rust.production -t octave-healthcare/api:latest .

# Build frontend image
docker build -f deployment/Dockerfile.frontend -t octave-healthcare/frontend:latest .

# Push to registry
docker push octave-healthcare/api:latest
docker push octave-healthcare/frontend:latest
```

#### Kubernetes Deployment
```bash
# Apply configurations
kubectl apply -f deployment/k8s/

# Check deployment status
kubectl rollout status deployment/octave-api -n octave-healthcare

# View logs
kubectl logs -f deployment/octave-api -n octave-healthcare
```

## Security Considerations

### HIPAA Compliance
- All PHI must be encrypted at rest and in transit
- Implement comprehensive audit logging
- Use role-based access controls
- Regular security assessments and penetration testing

### Authentication & Authorization
- Use JWT tokens with short expiration times
- Implement refresh token rotation
- Multi-factor authentication for all users
- Regular password policy enforcement

### Data Protection
- Input validation and sanitization
- SQL injection prevention with parameterized queries
- XSS protection with Content Security Policy
- Rate limiting to prevent abuse

## Contributing

### Pull Request Process
1. Create feature branch from `develop`
2. Implement changes with tests
3. Update documentation
4. Submit pull request
5. Code review and approval
6. Merge to `develop`
7. Deploy to staging for testing
8. Merge to `main` for production

### Code Review Guidelines
- All code must be reviewed by at least one other developer
- Tests must pass and coverage must not decrease
- Documentation must be updated for new features
- Security implications must be considered
- Performance impact must be evaluated

## Support and Resources

### Internal Resources
- **Architecture Documentation**: `/docs/architecture/`
- **API Documentation**: `/docs/api/`
- **Deployment Guides**: `/docs/deployment/`
- **Troubleshooting**: `/docs/troubleshooting/`

### External Resources
- **Rust Documentation**: https://doc.rust-lang.org/
- **React Documentation**: https://reactjs.org/docs/
- **Kubernetes Documentation**: https://kubernetes.io/docs/
- **PostgreSQL Documentation**: https://www.postgresql.org/docs/

### Getting Help
- **Development Team**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **Architecture Questions**: <EMAIL>
- **Security Questions**: <EMAIL>
