# System Health Monitoring Runbook

## Overview

This runbook provides step-by-step procedures for monitoring and maintaining the health of the OCTAVE Healthcare system. It covers routine monitoring tasks, alert response procedures, and system maintenance activities.

## Monitoring Dashboard Access

### Primary Monitoring Systems

1. **Grafana Dashboard**: https://monitoring.octave-healthcare.com
   - Username: <EMAIL>
   - MFA required for access
   - Primary system health overview

2. **Prometheus**: https://prometheus.octave-healthcare.com
   - Raw metrics and alerting rules
   - Advanced query interface
   - Historical data analysis

3. **Alertmanager**: https://alerts.octave-healthcare.com
   - Alert management and routing
   - Notification configuration
   - Alert suppression and grouping

4. **Kubernetes Dashboard**: https://k8s.octave-healthcare.com
   - Container and pod status
   - Resource utilization
   - Deployment management

## Daily Health Checks

### Morning Health Check (8:00 AM EST)

#### 1. System Overview Check
```bash
# Check overall system status
curl -f https://octave-healthcare.com/health
curl -f https://api.octave-healthcare.com/health

# Expected response: HTTP 200 with "healthy" status
```

#### 2. Application Performance Review
- **Response Time**: < 2 seconds for 95% of requests
- **Error Rate**: < 1% of total requests
- **Throughput**: Monitor request volume trends
- **Database Performance**: Query response times < 100ms

#### 3. Infrastructure Health
- **CPU Usage**: < 80% average across all nodes
- **Memory Usage**: < 85% across all nodes
- **Disk Usage**: < 90% on all volumes
- **Network**: No packet loss or high latency

#### 4. Security Status
- **Failed Login Attempts**: Monitor for unusual patterns
- **Security Alerts**: Review overnight security incidents
- **Certificate Status**: Verify SSL certificates are valid
- **Vulnerability Scans**: Check for new security issues

### Health Check Checklist

```
□ API endpoints responding (< 2s response time)
□ Database connections healthy (< 100ms query time)
□ All Kubernetes pods running
□ No critical alerts in Alertmanager
□ SSL certificates valid (> 30 days remaining)
□ Backup jobs completed successfully
□ Log aggregation functioning
□ Monitoring systems operational
□ Security scans completed
□ No unusual error patterns
```

## Alert Response Procedures

### Critical Alerts (P1)

#### System Down Alert
**Alert**: `SystemDown`
**Severity**: Critical
**Response Time**: Immediate (< 5 minutes)

**Investigation Steps**:
1. Check system status dashboard
2. Verify load balancer health
3. Check Kubernetes cluster status
4. Review recent deployments
5. Examine application logs

**Resolution Steps**:
```bash
# Check pod status
kubectl get pods -n octave-healthcare

# Check service status
kubectl get services -n octave-healthcare

# Check ingress status
kubectl get ingress -n octave-healthcare

# Restart failed pods if necessary
kubectl delete pod <pod-name> -n octave-healthcare

# Scale deployment if needed
kubectl scale deployment octave-api --replicas=5 -n octave-healthcare
```

**Escalation**: If not resolved in 15 minutes, escalate to Senior DevOps Engineer

#### Database Connection Issues
**Alert**: `DatabaseConnectionHigh` or `DatabaseDown`
**Severity**: Critical
**Response Time**: Immediate (< 5 minutes)

**Investigation Steps**:
1. Check database pod status
2. Review database logs
3. Check connection pool metrics
4. Verify database disk space
5. Monitor query performance

**Resolution Steps**:
```bash
# Check PostgreSQL pod status
kubectl get pods -l component=database -n octave-healthcare

# Check database logs
kubectl logs postgres-0 -n octave-healthcare --tail=100

# Check database connections
kubectl exec -it postgres-0 -n octave-healthcare -- psql -U octave_user -d octave_production -c "SELECT count(*) FROM pg_stat_activity;"

# Restart database if necessary (LAST RESORT)
kubectl delete pod postgres-0 -n octave-healthcare
```

**Escalation**: If not resolved in 10 minutes, escalate to Database Administrator

### High Priority Alerts (P2)

#### High Error Rate
**Alert**: `HighErrorRate`
**Severity**: Warning
**Response Time**: < 30 minutes

**Investigation Steps**:
1. Identify error patterns in logs
2. Check recent deployments
3. Review application metrics
4. Analyze error types and frequencies

**Resolution Steps**:
```bash
# Check application logs for errors
kubectl logs -l app=octave-healthcare -n octave-healthcare --tail=500 | grep ERROR

# Check recent deployments
kubectl rollout history deployment/octave-api -n octave-healthcare

# Rollback if necessary
kubectl rollout undo deployment/octave-api -n octave-healthcare
```

#### High Response Time
**Alert**: `HighResponseTime`
**Severity**: Warning
**Response Time**: < 1 hour

**Investigation Steps**:
1. Check database query performance
2. Review application performance metrics
3. Analyze resource utilization
4. Check for memory leaks

**Resolution Steps**:
```bash
# Check resource usage
kubectl top pods -n octave-healthcare

# Check database performance
kubectl exec -it postgres-0 -n octave-healthcare -- psql -U octave_user -d octave_production -c "SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;"

# Scale application if needed
kubectl scale deployment octave-api --replicas=6 -n octave-healthcare
```

### Security Alerts

#### HIPAA Compliance Violation
**Alert**: `HIPAAComplianceViolation`
**Severity**: Critical
**Response Time**: Immediate

**Investigation Steps**:
1. Identify the specific violation
2. Determine scope of potential PHI exposure
3. Review audit logs
4. Assess impact on patient data

**Resolution Steps**:
1. Immediately contain the issue
2. Document all findings
3. Notify HIPAA Officer and Legal team
4. Implement corrective measures
5. Follow incident response procedures

**Escalation**: Immediate escalation to HIPAA Officer and Security Team

#### Security Incident
**Alert**: `SecurityIncident`
**Severity**: Critical
**Response Time**: Immediate

**Investigation Steps**:
1. Identify attack vector
2. Assess system compromise
3. Review security logs
4. Determine affected systems

**Resolution Steps**:
1. Isolate affected systems
2. Block malicious traffic
3. Preserve evidence
4. Follow security incident response plan
5. Notify security team and management

## Performance Monitoring

### Key Performance Indicators (KPIs)

#### Application Performance
- **Response Time**: 95th percentile < 2 seconds
- **Throughput**: Requests per second
- **Error Rate**: < 1% of total requests
- **Availability**: > 99.9% uptime

#### Database Performance
- **Query Response Time**: Average < 100ms
- **Connection Pool Usage**: < 80% of max connections
- **Lock Wait Time**: < 10ms average
- **Cache Hit Ratio**: > 95%

#### Infrastructure Performance
- **CPU Utilization**: < 80% average
- **Memory Usage**: < 85% of available
- **Disk I/O**: < 80% utilization
- **Network Latency**: < 50ms between services

### Performance Optimization

#### Database Optimization
```sql
-- Check slow queries
SELECT query, mean_time, calls, total_time 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Check index usage
SELECT schemaname, tablename, attname, n_distinct, correlation 
FROM pg_stats 
WHERE schemaname = 'public' 
ORDER BY n_distinct DESC;

-- Analyze table statistics
ANALYZE;

-- Vacuum tables
VACUUM ANALYZE;
```

#### Application Optimization
```bash
# Check memory usage
kubectl top pods -n octave-healthcare

# Check CPU usage
kubectl top nodes

# Review resource requests and limits
kubectl describe deployment octave-api -n octave-healthcare

# Adjust resources if needed
kubectl patch deployment octave-api -n octave-healthcare -p '{"spec":{"template":{"spec":{"containers":[{"name":"octave-api","resources":{"requests":{"memory":"2Gi","cpu":"1"},"limits":{"memory":"4Gi","cpu":"2"}}}]}}}}'
```

## Maintenance Procedures

### Weekly Maintenance (Sundays 2:00 AM EST)

#### 1. System Updates
```bash
# Update Kubernetes cluster
kubectl get nodes
kubectl drain <node-name> --ignore-daemonsets
# Perform node updates
kubectl uncordon <node-name>

# Update container images
kubectl set image deployment/octave-api octave-api=octave-healthcare/api:latest -n octave-healthcare
kubectl rollout status deployment/octave-api -n octave-healthcare
```

#### 2. Database Maintenance
```bash
# Connect to database
kubectl exec -it postgres-0 -n octave-healthcare -- psql -U octave_user -d octave_production

# Run maintenance queries
VACUUM ANALYZE;
REINDEX DATABASE octave_production;

# Check database size
SELECT pg_size_pretty(pg_database_size('octave_production'));

# Check table sizes
SELECT schemaname, tablename, pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

#### 3. Log Rotation and Cleanup
```bash
# Clean up old logs
kubectl exec -it fluentd-xxx -n octave-healthcare -- find /var/log -name "*.log" -mtime +30 -delete

# Rotate application logs
kubectl exec -it octave-api-xxx -n octave-healthcare -- logrotate /etc/logrotate.conf

# Clean up old container images
docker system prune -f
```

#### 4. Backup Verification
```bash
# Verify latest backup
aws s3 ls s3://octave-healthcare-backups/database/ --recursive | tail -5

# Test backup restore (to test database)
pg_restore --clean --if-exists -d octave_test backups/latest_backup.dump

# Verify backup integrity
pg_dump octave_test | md5sum
```

### Monthly Maintenance (First Sunday of month)

#### 1. Security Updates
- Review and apply security patches
- Update SSL certificates if needed
- Rotate API keys and secrets
- Review user access permissions

#### 2. Performance Review
- Analyze monthly performance trends
- Review capacity planning metrics
- Optimize database queries
- Update monitoring thresholds

#### 3. Compliance Audit
- Review HIPAA compliance metrics
- Audit user access logs
- Verify backup procedures
- Update security documentation

## Troubleshooting Guide

### Common Issues

#### Pod Stuck in Pending State
```bash
# Check pod events
kubectl describe pod <pod-name> -n octave-healthcare

# Check node resources
kubectl top nodes

# Check persistent volume claims
kubectl get pvc -n octave-healthcare

# Common solutions:
# 1. Insufficient resources - scale cluster or adjust requests
# 2. PVC issues - check storage class and availability
# 3. Node selector issues - verify node labels
```

#### Service Unavailable
```bash
# Check service endpoints
kubectl get endpoints -n octave-healthcare

# Check service configuration
kubectl describe service octave-api -n octave-healthcare

# Check ingress configuration
kubectl describe ingress octave-ingress -n octave-healthcare

# Test service connectivity
kubectl run test-pod --image=busybox -it --rm -- wget -qO- http://octave-api:3000/health
```

#### Database Connection Issues
```bash
# Check database pod status
kubectl get pods -l component=database -n octave-healthcare

# Check database service
kubectl get service postgres -n octave-healthcare

# Test database connectivity
kubectl run test-db --image=postgres:15-alpine -it --rm -- psql -h postgres -U octave_user -d octave_production -c "SELECT 1;"

# Check connection pool
kubectl logs -l app=octave-healthcare -n octave-healthcare | grep "connection pool"
```

## Emergency Contacts

### On-Call Rotation
- **Primary**: Current on-call engineer (PagerDuty)
- **Secondary**: Senior DevOps Engineer
- **Escalation**: Engineering Manager

### Contact Information
- **DevOps Team**: <EMAIL>
- **Database Team**: <EMAIL>
- **Security Team**: <EMAIL>
- **Emergency Hotline**: +1-555-OCTAVE-911

### Escalation Matrix
1. **Level 1**: On-call engineer (0-15 minutes)
2. **Level 2**: Senior engineer (15-30 minutes)
3. **Level 3**: Engineering manager (30-60 minutes)
4. **Level 4**: CTO (60+ minutes or business impact)

## Documentation Updates

This runbook should be reviewed and updated:
- **Monthly**: Review procedures and contact information
- **After incidents**: Update based on lessons learned
- **After system changes**: Update procedures for new components
- **Quarterly**: Full review and validation of all procedures

**Last Updated**: 2024-01-15
**Next Review**: 2024-02-15
**Owner**: DevOps Team
