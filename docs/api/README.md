# OCTAVE Healthcare API Documentation

## Overview

The OCTAVE Healthcare API provides a comprehensive RESTful interface for managing prior authorization requests, patient data, and healthcare workflows. This API is designed with HIPAA compliance, security, and semantic protection at its core.

## Base URL

```
Production: https://api.octave-healthcare.com/v1
Staging: https://staging-api.octave-healthcare.com/v1
```

## Authentication

The OCTAVE API uses JWT (JSON Web Tokens) for authentication. All API requests must include a valid JWT token in the Authorization header.

### Obtaining an Access Token

```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "secure_password",
  "practice_id": "practice_123"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 3600,
  "token_type": "Bearer",
  "user": {
    "id": "user_123",
    "email": "<EMAIL>",
    "role": "provider",
    "practice_id": "practice_123"
  }
}
```

### Using the Access Token

Include the access token in the Authorization header for all API requests:

```http
Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Rate Limiting

The API implements rate limiting to ensure fair usage and system stability:

- **Standard endpoints**: 1000 requests per hour per user
- **Authentication endpoints**: 10 requests per minute per IP
- **Bulk operations**: 100 requests per hour per user

Rate limit headers are included in all responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

## Error Handling

The API uses standard HTTP status codes and returns detailed error information in JSON format.

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The request contains invalid data",
    "details": [
      {
        "field": "patient.date_of_birth",
        "message": "Date of birth is required"
      }
    ],
    "request_id": "req_123456789"
  }
}
```

### Common Error Codes

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 400 | VALIDATION_ERROR | Request validation failed |
| 401 | UNAUTHORIZED | Invalid or missing authentication |
| 403 | FORBIDDEN | Insufficient permissions |
| 404 | NOT_FOUND | Resource not found |
| 409 | CONFLICT | Resource conflict |
| 429 | RATE_LIMITED | Rate limit exceeded |
| 500 | INTERNAL_ERROR | Internal server error |

## Data Formats

### Date and Time

All dates and times are in ISO 8601 format with UTC timezone:

```json
{
  "created_at": "2024-01-15T10:30:00Z",
  "date_of_birth": "1990-05-15"
}
```

### Pagination

List endpoints support pagination using cursor-based pagination:

```http
GET /patients?limit=20&cursor=eyJpZCI6IjEyMyJ9
```

**Response:**
```json
{
  "data": [...],
  "pagination": {
    "has_more": true,
    "next_cursor": "eyJpZCI6IjE0MyJ9",
    "limit": 20
  }
}
```

### Filtering and Sorting

Most list endpoints support filtering and sorting:

```http
GET /prior-authorizations?status=pending&sort=created_at:desc&practice_id=practice_123
```

## Core Resources

### Patients

Manage patient information with full HIPAA compliance and PHI protection.

#### Patient Object

```json
{
  "id": "patient_123",
  "first_name": "John",
  "last_name": "Doe",
  "date_of_birth": "1990-05-15",
  "gender": "male",
  "phone": "******-0123",
  "email": "<EMAIL>",
  "address": {
    "street": "123 Main St",
    "city": "Anytown",
    "state": "CA",
    "zip_code": "12345"
  },
  "insurance": {
    "primary": {
      "company": "Blue Cross Blue Shield",
      "policy_number": "BC123456789",
      "group_number": "GRP001"
    }
  },
  "practice_id": "practice_123",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### Endpoints

- `GET /patients` - List patients
- `POST /patients` - Create a new patient
- `GET /patients/{id}` - Get patient details
- `PUT /patients/{id}` - Update patient
- `DELETE /patients/{id}` - Delete patient (soft delete)

### Prior Authorizations

Manage prior authorization requests throughout their lifecycle.

#### Prior Authorization Object

```json
{
  "id": "pa_123",
  "patient_id": "patient_123",
  "practice_id": "practice_123",
  "provider_id": "provider_123",
  "insurance_company": "Blue Cross Blue Shield",
  "procedure": {
    "code": "99213",
    "description": "Office visit, established patient",
    "modifier": "25"
  },
  "diagnosis": {
    "primary": {
      "code": "Z00.00",
      "description": "Encounter for general adult medical examination"
    }
  },
  "status": "pending",
  "priority": "routine",
  "requested_date": "2024-01-20",
  "clinical_notes": "Patient requires follow-up examination",
  "supporting_documents": [
    {
      "id": "doc_123",
      "type": "medical_record",
      "filename": "patient_chart.pdf",
      "uploaded_at": "2024-01-15T10:30:00Z"
    }
  ],
  "decision": {
    "status": "approved",
    "reason": "Medical necessity established",
    "decided_at": "2024-01-16T14:20:00Z",
    "decided_by": "insurance_reviewer_456"
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-16T14:20:00Z"
}
```

#### Endpoints

- `GET /prior-authorizations` - List prior authorizations
- `POST /prior-authorizations` - Create new prior authorization
- `GET /prior-authorizations/{id}` - Get prior authorization details
- `PUT /prior-authorizations/{id}` - Update prior authorization
- `POST /prior-authorizations/{id}/submit` - Submit for review
- `POST /prior-authorizations/{id}/approve` - Approve (insurance only)
- `POST /prior-authorizations/{id}/deny` - Deny (insurance only)

### Practices

Manage healthcare practice information and settings.

#### Practice Object

```json
{
  "id": "practice_123",
  "name": "Anytown Medical Center",
  "npi": "**********",
  "tax_id": "12-3456789",
  "address": {
    "street": "456 Healthcare Blvd",
    "city": "Anytown",
    "state": "CA",
    "zip_code": "12345"
  },
  "contact": {
    "phone": "******-0199",
    "fax": "******-0198",
    "email": "<EMAIL>"
  },
  "settings": {
    "auto_submit_routine": true,
    "notification_preferences": {
      "email": true,
      "sms": false
    }
  },
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### Endpoints

- `GET /practices` - List practices (admin only)
- `POST /practices` - Create new practice (admin only)
- `GET /practices/{id}` - Get practice details
- `PUT /practices/{id}` - Update practice
- `GET /practices/{id}/users` - List practice users
- `POST /practices/{id}/users` - Add user to practice

### Users

Manage user accounts and permissions.

#### User Object

```json
{
  "id": "user_123",
  "email": "<EMAIL>",
  "first_name": "Dr. Jane",
  "last_name": "Smith",
  "role": "provider",
  "practice_id": "practice_123",
  "npi": "**********",
  "specialties": ["internal_medicine", "cardiology"],
  "permissions": [
    "create_prior_auth",
    "view_patients",
    "manage_practice_settings"
  ],
  "last_login": "2024-01-15T09:15:00Z",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

#### Endpoints

- `GET /users` - List users
- `POST /users` - Create new user
- `GET /users/{id}` - Get user details
- `PUT /users/{id}` - Update user
- `DELETE /users/{id}` - Deactivate user
- `POST /users/{id}/reset-password` - Reset user password

## Webhooks

OCTAVE supports webhooks to notify your application of important events.

### Webhook Events

- `prior_authorization.created`
- `prior_authorization.submitted`
- `prior_authorization.approved`
- `prior_authorization.denied`
- `patient.created`
- `patient.updated`
- `user.login`
- `security.incident`

### Webhook Payload

```json
{
  "id": "evt_123",
  "type": "prior_authorization.approved",
  "created": "2024-01-16T14:20:00Z",
  "data": {
    "object": {
      "id": "pa_123",
      "status": "approved",
      ...
    }
  }
}
```

### Webhook Security

All webhook payloads are signed using HMAC SHA-256. Verify the signature using the `X-OCTAVE-Signature` header.

## SDKs and Libraries

Official SDKs are available for popular programming languages:

- **JavaScript/Node.js**: `npm install @octave/healthcare-api`
- **Python**: `pip install octave-healthcare`
- **PHP**: `composer require octave/healthcare-api`
- **C#/.NET**: `Install-Package Octave.Healthcare.Api`

### JavaScript Example

```javascript
const { OctaveClient } = require('@octave/healthcare-api');

const client = new OctaveClient({
  apiKey: 'your-api-key',
  environment: 'production'
});

// Create a new patient
const patient = await client.patients.create({
  first_name: 'John',
  last_name: 'Doe',
  date_of_birth: '1990-05-15',
  practice_id: 'practice_123'
});

// Create a prior authorization
const priorAuth = await client.priorAuthorizations.create({
  patient_id: patient.id,
  procedure_code: '99213',
  diagnosis_code: 'Z00.00',
  requested_date: '2024-01-20'
});
```

## Testing

### Sandbox Environment

Use the sandbox environment for testing:

```
Sandbox API: https://sandbox-api.octave-healthcare.com/v1
```

### Test Data

The sandbox includes pre-populated test data:

- Test practices, patients, and users
- Sample prior authorization requests
- Mock insurance company responses

### Test Cards and Scenarios

Use these test scenarios to verify your integration:

- **Approved PA**: Use procedure code `99213` with diagnosis `Z00.00`
- **Denied PA**: Use procedure code `99999` with any diagnosis
- **Pending PA**: Use procedure code `99214` with diagnosis `Z00.01`

## Support

### Documentation

- **API Reference**: https://docs.octave-healthcare.com/api
- **Guides**: https://docs.octave-healthcare.com/guides
- **SDKs**: https://docs.octave-healthcare.com/sdks

### Support Channels

- **Email**: <EMAIL>
- **Developer Portal**: https://developers.octave-healthcare.com
- **Status Page**: https://status.octave-healthcare.com

### SLA

- **Uptime**: 99.9% availability
- **Response Time**: < 200ms for 95% of requests
- **Support Response**: < 24 hours for standard issues, < 4 hours for critical issues
