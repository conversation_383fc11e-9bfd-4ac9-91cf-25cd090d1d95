[workspace]
members = [
    "octave-core",
    "octave-healthcare",
    "octave-auth",
    "octave-api",
    "octave-db",
    "octave-semantic",
    "octave-database",
    "octave-compliance"
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["OCTAVE Development Team <<EMAIL>>"]
license = "Proprietary"
repository = "https://github.com/company/octave-healthcare"
homepage = "https://octave-healthcare.com"
documentation = "https://docs.octave-healthcare.com"
description = "OCTAVE - Healthcare Prior Authorization System with Semantic Protection"
keywords = ["healthcare", "hipaa", "prior-authorization", "semantic-protection"]
categories = ["healthcare", "web-programming", "authentication"]

[workspace.dependencies]
# Web Framework
axum = "0.7"
tower = "0.4"
tower-http = "0.5"
hyper = "1.0"
tokio = { version = "1.0", features = ["full"] }

# Database
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "chrono", "uuid", "json"] }
sea-orm = { version = "0.12", features = ["sqlx-postgres", "runtime-tokio-rustls", "macros"] }

# Serialization
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"

# Error Handling
thiserror = "1.0"
anyhow = "1.0"
color-eyre = "0.6"

# Logging and Tracing
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
tracing-appender = "0.2"

# Authentication & Security
jsonwebtoken = "9.0"
argon2 = "0.5"
rand = "0.8"
uuid = { version = "1.0", features = ["v4", "serde"] }

# Cryptography
ring = "0.17"
aes-gcm = "0.10"
chacha20poly1305 = "0.10"

# HTTP Client
reqwest = { version = "0.11", features = ["json", "rustls-tls"] }

# Configuration
config = "0.14"
dotenvy = "0.15"

# Validation
validator = { version = "0.18", features = ["derive"] }
regex = "1.0"

# Date and Time
chrono = { version = "0.4", features = ["serde"] }
time = { version = "0.3", features = ["serde", "macros"] }

# Async Runtime
futures = "0.3"
async-trait = "0.1"

# Testing
mockall = "0.12"
wiremock = "0.6"
proptest = "1.0"
criterion = { version = "0.5", features = ["html_reports"] }
fake = { version = "2.9", features = ["derive", "chrono", "uuid"] }
rstest = "0.18"
serial_test = "3.0"
tempfile = "3.8"
test-case = "3.3"
pretty_assertions = "1.4"
insta = "1.34"
quickcheck = "1.0"
quickcheck_macros = "1.0"

# Performance
rayon = "1.0"
dashmap = "5.0"

# Healthcare Specific
# Note: Most healthcare libraries will be built in-house due to Rust ecosystem immaturity
lazy_static = "1.4"

[profile.dev]
opt-level = 0
debug = true
split-debuginfo = "unpacked"
debug-assertions = true
overflow-checks = true
lto = false
panic = "unwind"
incremental = true
codegen-units = 256

[profile.release]
opt-level = 3
debug = false
split-debuginfo = "off"
debug-assertions = false
overflow-checks = false
lto = "thin"
panic = "abort"
incremental = false
codegen-units = 1

[profile.test]
opt-level = 0
debug = true
debug-assertions = true
overflow-checks = true
incremental = true

# Healthcare-specific profile for maximum security and performance
[profile.healthcare]
inherits = "release"
opt-level = 3
lto = "fat"
codegen-units = 1
panic = "abort"
strip = true
