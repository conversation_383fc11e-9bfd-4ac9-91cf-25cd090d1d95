//! Unit tests for OCTAVE Healthcare module
//! 
//! Comprehensive unit tests covering:
//! - PHI protection and sanitization
//! - Medical coding validation
//! - HIPAA compliance utilities
//! - Clinical data validation
//! - Healthcare interoperability

use octave_healthcare::*;
use octave_core::{OctaveResult, OctaveError};
use proptest::prelude::*;
use rstest::*;
use pretty_assertions::assert_eq;
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use uuid::Uuid;

mod common;
use common::{TestDataFactory, HipaaAssertions};

/// Test PHI protection functionality
#[cfg(test)]
mod phi_protection_tests {
    use super::*;

    #[test]
    fn test_ssn_detection() {
        let phi_detector = PhiDetector::new();
        
        // Test valid SSN patterns
        assert!(phi_detector.contains_ssn("***********"));
        assert!(phi_detector.contains_ssn("Patient SSN: ***********"));
        assert!(phi_detector.contains_ssn("SSN 111223333"));
        
        // Test invalid patterns
        assert!(!phi_detector.contains_ssn("123-45-678")); // Too short
        assert!(!phi_detector.contains_ssn("abc-de-fghi")); // Non-numeric
        assert!(!phi_detector.contains_ssn("***********")); // Invalid SSN
    }

    #[test]
    fn test_phi_sanitization() {
        let sanitizer = PhiSanitizer::new();
        
        let input = "Patient John Doe, SSN: ***********, Phone: (*************";
        let sanitized = sanitizer.sanitize(input, SanitizationMode::Redact).unwrap();
        
        // Should not contain original PHI
        assert!(!sanitized.contains("***********"));
        assert!(!sanitized.contains("(*************"));
        
        // Should contain redaction markers
        assert!(sanitized.contains("[REDACTED_SSN]"));
        assert!(sanitized.contains("[REDACTED_PHONE]"));
        
        // Should preserve non-PHI data
        assert!(sanitized.contains("Patient"));
    }

    #[test]
    fn test_phi_encryption_roundtrip() {
        let encryptor = PhiEncryptor::new("test-key-32-bytes-long-for-aes256").unwrap();
        
        let original = "Sensitive patient data: SSN ***********";
        let encrypted = encryptor.encrypt(original.as_bytes()).unwrap();
        let decrypted = encryptor.decrypt(&encrypted).unwrap();
        
        assert_eq!(original.as_bytes(), decrypted);
        
        // Encrypted data should not contain original
        let encrypted_str = String::from_utf8_lossy(&encrypted);
        assert!(!encrypted_str.contains("***********"));
    }

    #[proptest]
    fn test_phi_sanitization_preserves_structure(
        #[strategy(r"[A-Za-z ]{10,50}")] name: String,
        #[strategy(r"\d{3}-\d{2}-\d{4}")] ssn: String
    ) {
        let sanitizer = PhiSanitizer::new();
        let input = format!("Patient: {}, SSN: {}", name, ssn);
        let sanitized = sanitizer.sanitize(&input, SanitizationMode::Redact).unwrap();
        
        // Should preserve name but redact SSN
        prop_assert!(sanitized.contains(&name));
        prop_assert!(!sanitized.contains(&ssn));
        prop_assert!(sanitized.contains("[REDACTED_SSN]"));
    }

    #[rstest]
    #[case("***********", true)]
    #[case("***********", false)]
    #[case("***********", false)]
    #[case("***********", false)]
    #[case("***********", false)]
    fn test_ssn_validation(#[case] ssn: &str, #[case] expected_valid: bool) {
        let validator = SsnValidator::new();
        assert_eq!(validator.is_valid(ssn), expected_valid);
    }
}

/// Test medical coding validation
#[cfg(test)]
mod medical_coding_tests {
    use super::*;

    #[test]
    fn test_icd10_validation() {
        let validator = Icd10Validator::new();
        
        // Valid ICD-10 codes
        assert!(validator.is_valid("A00.0"));
        assert!(validator.is_valid("Z99.89"));
        assert!(validator.is_valid("S72.001A"));
        
        // Invalid ICD-10 codes
        assert!(!validator.is_valid(""));
        assert!(!validator.is_valid("A"));
        assert!(!validator.is_valid("123.45"));
        assert!(!validator.is_valid("A00.X"));
    }

    #[test]
    fn test_cpt_validation() {
        let validator = CptValidator::new();
        
        // Valid CPT codes
        assert!(validator.is_valid("99213"));
        assert!(validator.is_valid("99214"));
        assert!(validator.is_valid("00100"));
        
        // Invalid CPT codes
        assert!(!validator.is_valid(""));
        assert!(!validator.is_valid("9921"));
        assert!(!validator.is_valid("992130"));
        assert!(!validator.is_valid("ABCDE"));
    }

    #[test]
    fn test_hcpcs_validation() {
        let validator = HcpcsValidator::new();
        
        // Valid HCPCS codes
        assert!(validator.is_valid("A0021"));
        assert!(validator.is_valid("E0100"));
        assert!(validator.is_valid("J0120"));
        
        // Invalid HCPCS codes
        assert!(!validator.is_valid(""));
        assert!(!validator.is_valid("A002"));
        assert!(!validator.is_valid("12345"));
        assert!(!validator.is_valid("AA021"));
    }

    #[test]
    fn test_diagnosis_procedure_compatibility() {
        let checker = CompatibilityChecker::new();
        
        // Compatible combinations
        assert!(checker.is_compatible("Z00.00", "99213")); // Routine exam with office visit
        assert!(checker.is_compatible("S72.001A", "27245")); // Fracture with surgical repair
        
        // Incompatible combinations
        assert!(!checker.is_compatible("Z00.00", "27245")); // Routine exam with surgery
        assert!(!checker.is_compatible("F32.9", "99213")); // Mental health with regular office visit
    }

    #[proptest]
    fn test_medical_code_format_validation(
        #[strategy(r"[A-Z]\d{2}\.\d{1,2}[A-Z]?")] icd10: String
    ) {
        let validator = Icd10Validator::new();
        // All generated codes should have valid format
        prop_assert!(validator.has_valid_format(&icd10));
    }
}

/// Test clinical data validation
#[cfg(test)]
mod clinical_validation_tests {
    use super::*;

    #[test]
    fn test_vital_signs_validation() {
        let validator = VitalSignsValidator::new();
        
        // Valid vital signs
        let valid_vitals = VitalSigns {
            temperature: 98.6,
            blood_pressure_systolic: 120,
            blood_pressure_diastolic: 80,
            heart_rate: 72,
            respiratory_rate: 16,
            oxygen_saturation: 98,
        };
        assert!(validator.validate(&valid_vitals).is_ok());
        
        // Invalid vital signs
        let invalid_vitals = VitalSigns {
            temperature: 150.0, // Too high
            blood_pressure_systolic: 300, // Too high
            blood_pressure_diastolic: 200, // Too high
            heart_rate: 300, // Too high
            respiratory_rate: 100, // Too high
            oxygen_saturation: 150, // Invalid percentage
        };
        assert!(validator.validate(&invalid_vitals).is_err());
    }

    #[test]
    fn test_medication_dosage_validation() {
        let validator = MedicationValidator::new();
        
        // Valid medications
        assert!(validator.validate_dosage("Aspirin", "81mg", "daily").is_ok());
        assert!(validator.validate_dosage("Metformin", "500mg", "twice daily").is_ok());
        
        // Invalid medications
        assert!(validator.validate_dosage("", "81mg", "daily").is_err()); // Empty name
        assert!(validator.validate_dosage("Aspirin", "", "daily").is_err()); // Empty dosage
        assert!(validator.validate_dosage("Aspirin", "81mg", "").is_err()); // Empty frequency
    }

    #[test]
    fn test_allergy_contraindication_detection() {
        let detector = AllergyDetector::new();
        
        let allergies = vec!["Penicillin".to_string(), "Sulfa".to_string()];
        let medications = vec!["Amoxicillin".to_string(), "Sulfamethoxazole".to_string()];
        
        let contraindications = detector.check_contraindications(&allergies, &medications);
        assert!(!contraindications.is_empty());
        assert!(contraindications.iter().any(|c| c.contains("Penicillin")));
        assert!(contraindications.iter().any(|c| c.contains("Sulfa")));
    }

    #[rstest]
    #[case(95.0, 105.0, true)]  // Normal temperature range
    #[case(98.6, 98.6, true)]   // Exact normal
    #[case(85.0, 85.0, false)]  // Too low
    #[case(110.0, 110.0, false)] // Too high
    fn test_temperature_validation(
        #[case] temp: f64, 
        #[case] _expected_temp: f64, 
        #[case] expected_valid: bool
    ) {
        let validator = VitalSignsValidator::new();
        assert_eq!(validator.is_valid_temperature(temp), expected_valid);
    }
}

/// Test HIPAA compliance utilities
#[cfg(test)]
mod hipaa_compliance_tests {
    use super::*;

    #[test]
    fn test_minimum_necessary_access() {
        let enforcer = MinimumNecessaryEnforcer::new();
        
        let patient_data = TestDataFactory::create_test_patient();
        let user_role = "billing_staff";
        let operation = "view_billing_info";
        
        let filtered_data = enforcer.filter_data(&patient_data, user_role, operation).unwrap();
        
        // Billing staff should see billing-relevant fields
        assert!(filtered_data.contains("insurance_info"));
        assert!(filtered_data.contains("patient_id"));
        
        // But not sensitive medical details
        assert!(!filtered_data.contains("medical_history"));
        assert!(!filtered_data.contains("allergies"));
    }

    #[test]
    fn test_audit_trail_generation() {
        let auditor = AuditTrailGenerator::new();
        
        let event = AuditEvent {
            user_id: Uuid::new_v4(),
            action: "VIEW_PATIENT".to_string(),
            resource_type: "Patient".to_string(),
            resource_id: Uuid::new_v4(),
            timestamp: Utc::now(),
            ip_address: "*************".to_string(),
            user_agent: "Test Browser".to_string(),
        };
        
        let audit_entry = auditor.generate_entry(&event).unwrap();
        
        // Verify audit entry contains required fields
        assert!(audit_entry.contains(&event.user_id.to_string()));
        assert!(audit_entry.contains(&event.action));
        assert!(audit_entry.contains(&event.resource_type));
        assert!(audit_entry.contains(&event.ip_address));
    }

    #[test]
    fn test_data_retention_policy() {
        let policy = DataRetentionPolicy::new();
        
        // Test different data types have correct retention periods
        assert_eq!(policy.get_retention_period("audit_log"), chrono::Duration::days(7 * 365)); // 7 years
        assert_eq!(policy.get_retention_period("patient_data"), chrono::Duration::days(7 * 365)); // 7 years
        assert_eq!(policy.get_retention_period("session_data"), chrono::Duration::hours(24)); // 24 hours
        assert_eq!(policy.get_retention_period("temp_files"), chrono::Duration::hours(1)); // 1 hour
    }

    #[test]
    fn test_breach_detection() {
        let detector = BreachDetector::new();
        
        // Simulate suspicious activity
        let events = vec![
            create_access_event("user1", "*************", Utc::now()),
            create_access_event("user1", "*************", Utc::now()),
            create_access_event("user1", "*************", Utc::now()),
            create_access_event("user1", "*************", Utc::now()),
            create_access_event("user1", "*************", Utc::now()),
        ];
        
        let breach_indicators = detector.analyze_events(&events);
        
        // Should detect rapid successive access as potential breach
        assert!(!breach_indicators.is_empty());
        assert!(breach_indicators.iter().any(|b| b.indicator_type == "rapid_access"));
    }
}

// Helper functions for tests

fn create_access_event(user_id: &str, ip: &str, timestamp: DateTime<Utc>) -> AccessEvent {
    AccessEvent {
        user_id: user_id.to_string(),
        ip_address: ip.to_string(),
        timestamp,
        action: "VIEW_PATIENT".to_string(),
        resource_id: Uuid::new_v4(),
    }
}

// Test data structures

#[derive(Debug)]
struct VitalSigns {
    temperature: f64,
    blood_pressure_systolic: u32,
    blood_pressure_diastolic: u32,
    heart_rate: u32,
    respiratory_rate: u32,
    oxygen_saturation: u32,
}

#[derive(Debug)]
struct AuditEvent {
    user_id: Uuid,
    action: String,
    resource_type: String,
    resource_id: Uuid,
    timestamp: DateTime<Utc>,
    ip_address: String,
    user_agent: String,
}

#[derive(Debug)]
struct AccessEvent {
    user_id: String,
    ip_address: String,
    timestamp: DateTime<Utc>,
    action: String,
    resource_id: Uuid,
}

#[derive(Debug)]
struct BreachIndicator {
    indicator_type: String,
    severity: String,
    description: String,
}
