# OCTAVE Healthcare System - Rust Testing Configuration
# Comprehensive testing configuration for all test types

[unit_tests]
# Unit test configuration
coverage_threshold = 85
critical_modules_threshold = 95
parallel_execution = true
timeout_seconds = 30

# Critical modules requiring higher coverage
critical_modules = [
    "octave-healthcare/src/phi_protection.rs",
    "octave-healthcare/src/hipaa_compliance.rs", 
    "octave-compliance/src/audit.rs",
    "octave-compliance/src/encryption.rs",
    "octave-auth/src/authentication.rs",
    "octave-semantic/src/immune_system.rs",
    "octave-semantic/src/threat_detection.rs"
]

[integration_tests]
# Integration test configuration
database_url = "postgresql://test_user:test_pass@localhost:5432/octave_test"
test_data_cleanup = true
parallel_execution = false
timeout_seconds = 120

# Test environments
environments = ["test", "integration", "staging"]

[security_tests]
# Security testing configuration
vulnerability_scanning = true
penetration_testing = true
hipaa_compliance_validation = true
phi_protection_testing = true
audit_trail_validation = true

# Security test categories
categories = [
    "authentication",
    "authorization", 
    "data_encryption",
    "input_validation",
    "audit_logging",
    "phi_protection",
    "semantic_protection"
]

[performance_tests]
# Performance testing configuration
benchmark_iterations = 1000
load_test_duration_seconds = 300
memory_leak_detection = true
cpu_profiling = true

# Performance targets
targets = [
    { name = "api_response_time_p95", threshold = "100ms" },
    { name = "database_query_time_p95", threshold = "50ms" },
    { name = "semantic_protection_overhead", threshold = "5ms" },
    { name = "memory_usage_per_request", threshold = "1MB" },
    { name = "concurrent_users", threshold = 1000 },
    { name = "requests_per_second", threshold = 5000 }
]

[property_based_tests]
# Property-based testing with proptest
test_cases_per_property = 1000
shrinking_enabled = true
max_shrink_iters = 10000

# Property test categories
categories = [
    "medical_coding_validation",
    "phi_sanitization",
    "encryption_roundtrip",
    "audit_trail_integrity",
    "semantic_protection_consistency"
]

[mutation_tests]
# Mutation testing configuration
enabled = true
timeout_multiplier = 2.0
minimum_score_threshold = 80.0

# Modules to exclude from mutation testing
exclude_modules = [
    "tests/",
    "examples/",
    "benches/"
]

[test_data]
# Test data generation and management
factory_enabled = true
realistic_data = true
phi_safe_data_only = true

# Test data categories
categories = [
    "patients",
    "prior_authorizations", 
    "medical_records",
    "insurance_companies",
    "healthcare_providers",
    "audit_events",
    "threat_patterns"
]

[reporting]
# Test reporting configuration
html_reports = true
junit_xml = true
coverage_reports = ["html", "lcov", "json"]
performance_reports = true
security_reports = true

# Report output directories
output_dir = "target/test-reports"
coverage_dir = "target/coverage"
benchmark_dir = "target/benchmarks"

[ci_cd]
# Continuous Integration configuration
fail_on_coverage_drop = true
fail_on_security_issues = true
fail_on_performance_regression = true
parallel_test_execution = true

# Test execution order
execution_order = [
    "unit_tests",
    "integration_tests", 
    "security_tests",
    "performance_tests"
]

[healthcare_specific]
# Healthcare-specific testing requirements
hipaa_compliance_required = true
phi_protection_required = true
audit_trail_required = true
medical_coding_validation = true

# Healthcare test scenarios
scenarios = [
    "patient_data_access",
    "prior_auth_workflow",
    "phi_sanitization",
    "audit_trail_generation",
    "medical_coding_validation",
    "insurance_integration",
    "semantic_threat_detection"
]

[mock_services]
# Mock service configuration
external_apis = true
database_mocking = true
file_system_mocking = true

# Mock service endpoints
services = [
    { name = "insurance_api", port = 8081 },
    { name = "ehr_system", port = 8082 },
    { name = "philhealth_api", port = 8083 },
    { name = "notification_service", port = 8084 }
]
