//! Unit tests for OCTAVE Compliance module
//! 
//! Comprehensive unit tests covering:
//! - Audit logging and trail generation
//! - Data encryption and key management
//! - Access control and authorization
//! - Compliance reporting and monitoring
//! - Data retention and disposal

use octave_compliance::*;
use octave_core::{OctaveResult, OctaveError};
use proptest::prelude::*;
use rstest::*;
use pretty_assertions::assert_eq;
use std::collections::HashMap;
use chrono::{DateTime, Utc, Duration};
use uuid::Uuid;
use serde_json::Value;

mod common;
use common::{TestDataFactory, HipaaAssertions};

/// Test audit logging functionality
#[cfg(test)]
mod audit_logging_tests {
    use super::*;

    #[test]
    fn test_audit_logger_initialization() {
        let logger = AuditLogger::new("test_system".to_string());
        assert_eq!(logger.get_system_name(), "test_system");
        assert!(logger.is_initialized());
    }

    #[test]
    fn test_audit_event_creation() {
        let logger = AuditLogger::new("test_system".to_string());
        
        let event = AuditEvent::new(
            AuditEventType::PatientDataAccess,
            Uuid::new_v4(),
            "Patient".to_string(),
            Uuid::new_v4(),
            "READ".to_string(),
            AuditOutcome::Success,
        );
        
        let result = logger.log_event(event).unwrap();
        assert!(result.event_id.is_some());
        assert!(result.timestamp <= Utc::now());
    }

    #[test]
    fn test_audit_trail_integrity() {
        let logger = AuditLogger::new("test_system".to_string());
        
        // Create multiple audit events
        let events = vec![
            create_test_audit_event("USER_LOGIN", "user1"),
            create_test_audit_event("PATIENT_ACCESS", "user1"),
            create_test_audit_event("DATA_EXPORT", "user1"),
            create_test_audit_event("USER_LOGOUT", "user1"),
        ];
        
        let mut logged_events = Vec::new();
        for event in events {
            let result = logger.log_event(event).unwrap();
            logged_events.push(result);
        }
        
        // Verify audit trail integrity
        let trail = logger.get_audit_trail("user1", None, None).unwrap();
        assert_eq!(trail.len(), 4);
        
        // Events should be in chronological order
        for i in 1..trail.len() {
            assert!(trail[i].timestamp >= trail[i-1].timestamp);
        }
        
        // Verify tamper detection
        assert!(logger.verify_trail_integrity(&trail).unwrap());
    }

    #[test]
    fn test_audit_event_filtering() {
        let logger = AuditLogger::new("test_system".to_string());
        
        let user1 = Uuid::new_v4();
        let user2 = Uuid::new_v4();
        
        // Log events for different users
        logger.log_event(create_audit_event_for_user(user1, "PATIENT_ACCESS")).unwrap();
        logger.log_event(create_audit_event_for_user(user1, "DATA_EXPORT")).unwrap();
        logger.log_event(create_audit_event_for_user(user2, "USER_LOGIN")).unwrap();
        
        // Filter by user
        let user1_events = logger.get_events_by_user(user1).unwrap();
        assert_eq!(user1_events.len(), 2);
        
        let user2_events = logger.get_events_by_user(user2).unwrap();
        assert_eq!(user2_events.len(), 1);
        
        // Filter by event type
        let access_events = logger.get_events_by_type(AuditEventType::PatientDataAccess).unwrap();
        assert_eq!(access_events.len(), 1);
    }

    #[test]
    fn test_audit_retention_policy() {
        let logger = AuditLogger::new("test_system".to_string());
        
        // Create old audit event (8 years ago)
        let old_event = AuditEvent {
            event_type: AuditEventType::PatientDataAccess,
            user_id: Uuid::new_v4(),
            resource_type: "Patient".to_string(),
            resource_id: Uuid::new_v4(),
            action: "READ".to_string(),
            outcome: AuditOutcome::Success,
            timestamp: Utc::now() - Duration::days(8 * 365),
            ip_address: "*************".to_string(),
            user_agent: "Test Browser".to_string(),
            details: serde_json::json!({}),
            event_id: None,
        };
        
        logger.log_event(old_event).unwrap();
        
        // Apply retention policy (7 years for HIPAA)
        let retention_result = logger.apply_retention_policy().unwrap();
        assert!(retention_result.deleted_count > 0);
        
        // Verify old events are removed
        let remaining_events = logger.get_all_events().unwrap();
        assert!(remaining_events.iter().all(|e| 
            Utc::now().signed_duration_since(e.timestamp).num_days() < 7 * 365
        ));
    }

    #[proptest]
    fn test_audit_logger_handles_various_inputs(
        #[strategy(r"[A-Za-z0-9_]{1,50}")] action: String,
        #[strategy(r"[A-Za-z0-9_]{1,50}")] resource_type: String
    ) {
        let logger = AuditLogger::new("test_system".to_string());
        
        let event = AuditEvent::new(
            AuditEventType::SystemAccess,
            Uuid::new_v4(),
            resource_type,
            Uuid::new_v4(),
            action,
            AuditOutcome::Success,
        );
        
        let result = logger.log_event(event);
        prop_assert!(result.is_ok());
    }
}

/// Test data encryption functionality
#[cfg(test)]
mod encryption_tests {
    use super::*;

    #[test]
    fn test_encryption_key_generation() {
        let key_manager = EncryptionKeyManager::new();
        
        let key = key_manager.generate_key(EncryptionAlgorithm::Aes256Gcm).unwrap();
        assert_eq!(key.algorithm, EncryptionAlgorithm::Aes256Gcm);
        assert_eq!(key.key_data.len(), 32); // 256 bits
        assert!(key.created_at <= Utc::now());
    }

    #[test]
    fn test_data_encryption_roundtrip() {
        let encryptor = DataEncryptor::new();
        
        let plaintext = b"Sensitive patient data: John Doe, SSN: ***********";
        let encrypted = encryptor.encrypt(plaintext, EncryptionAlgorithm::Aes256Gcm).unwrap();
        let decrypted = encryptor.decrypt(&encrypted).unwrap();
        
        assert_eq!(plaintext, decrypted.as_slice());
        
        // Encrypted data should be different from plaintext
        assert_ne!(plaintext.to_vec(), encrypted.ciphertext);
        
        // Verify encryption metadata
        assert_eq!(encrypted.algorithm, EncryptionAlgorithm::Aes256Gcm);
        assert!(!encrypted.nonce.is_empty());
        assert!(!encrypted.tag.is_empty());
    }

    #[test]
    fn test_key_rotation() {
        let mut key_manager = EncryptionKeyManager::new();
        
        // Generate initial key
        let key1 = key_manager.generate_key(EncryptionAlgorithm::Aes256Gcm).unwrap();
        let key1_id = key1.key_id;
        
        // Rotate key
        let key2 = key_manager.rotate_key(key1_id).unwrap();
        assert_ne!(key1.key_id, key2.key_id);
        assert_ne!(key1.key_data, key2.key_data);
        
        // Old key should be marked as rotated
        let old_key = key_manager.get_key(key1_id).unwrap();
        assert_eq!(old_key.status, KeyStatus::Rotated);
        
        // New key should be active
        let new_key = key_manager.get_key(key2.key_id).unwrap();
        assert_eq!(new_key.status, KeyStatus::Active);
    }

    #[test]
    fn test_encryption_with_different_algorithms() {
        let encryptor = DataEncryptor::new();
        let plaintext = b"Test data for encryption";
        
        let algorithms = vec![
            EncryptionAlgorithm::Aes256Gcm,
            EncryptionAlgorithm::ChaCha20Poly1305,
        ];
        
        for algorithm in algorithms {
            let encrypted = encryptor.encrypt(plaintext, algorithm).unwrap();
            let decrypted = encryptor.decrypt(&encrypted).unwrap();
            
            assert_eq!(plaintext, decrypted.as_slice());
            assert_eq!(encrypted.algorithm, algorithm);
        }
    }

    #[test]
    fn test_encryption_performance() {
        let encryptor = DataEncryptor::new();
        let large_data = vec![0u8; 1024 * 1024]; // 1MB of data
        
        let start = std::time::Instant::now();
        let encrypted = encryptor.encrypt(&large_data, EncryptionAlgorithm::Aes256Gcm).unwrap();
        let encryption_time = start.elapsed();
        
        let start = std::time::Instant::now();
        let decrypted = encryptor.decrypt(&encrypted).unwrap();
        let decryption_time = start.elapsed();
        
        // Performance should be reasonable (< 100ms for 1MB)
        assert!(encryption_time.as_millis() < 100);
        assert!(decryption_time.as_millis() < 100);
        assert_eq!(large_data, decrypted);
    }

    #[rstest]
    #[case(b"", true)]  // Empty data
    #[case(b"a", true)] // Single byte
    #[case(b"Hello, World!", true)] // Small text
    #[case(&vec![0u8; 1000], true)] // Larger data
    fn test_encryption_various_data_sizes(#[case] data: &[u8], #[case] should_succeed: bool) {
        let encryptor = DataEncryptor::new();
        
        let result = encryptor.encrypt(data, EncryptionAlgorithm::Aes256Gcm);
        assert_eq!(result.is_ok(), should_succeed);
        
        if should_succeed {
            let encrypted = result.unwrap();
            let decrypted = encryptor.decrypt(&encrypted).unwrap();
            assert_eq!(data, decrypted.as_slice());
        }
    }
}

/// Test access control functionality
#[cfg(test)]
mod access_control_tests {
    use super::*;

    #[test]
    fn test_role_based_access_control() {
        let access_controller = AccessController::new();
        
        // Define roles and permissions
        let doctor_role = Role::new("doctor".to_string(), vec![
            Permission::ReadPatientData,
            Permission::WritePatientData,
            Permission::ReadMedicalRecords,
            Permission::WriteMedicalRecords,
        ]);
        
        let nurse_role = Role::new("nurse".to_string(), vec![
            Permission::ReadPatientData,
            Permission::ReadMedicalRecords,
        ]);
        
        let billing_role = Role::new("billing".to_string(), vec![
            Permission::ReadPatientData,
            Permission::ReadBillingData,
            Permission::WriteBillingData,
        ]);
        
        access_controller.register_role(doctor_role).unwrap();
        access_controller.register_role(nurse_role).unwrap();
        access_controller.register_role(billing_role).unwrap();
        
        // Test access permissions
        assert!(access_controller.check_permission("doctor", Permission::WritePatientData).unwrap());
        assert!(!access_controller.check_permission("nurse", Permission::WritePatientData).unwrap());
        assert!(!access_controller.check_permission("billing", Permission::WriteMedicalRecords).unwrap());
    }

    #[test]
    fn test_minimum_necessary_access() {
        let access_controller = AccessController::new();
        
        let patient_data = TestDataFactory::create_test_patient();
        let user_context = UserContext {
            user_id: Uuid::new_v4(),
            role: "billing".to_string(),
            purpose: "billing_inquiry".to_string(),
            ip_address: "*************".to_string(),
        };
        
        let filtered_data = access_controller.apply_minimum_necessary(&patient_data, &user_context).unwrap();
        
        // Billing staff should only see billing-relevant data
        assert!(filtered_data.contains("insurance_info"));
        assert!(filtered_data.contains("patient_id"));
        assert!(!filtered_data.contains("medical_history"));
        assert!(!filtered_data.contains("allergies"));
    }

    #[test]
    fn test_access_logging() {
        let access_controller = AccessController::new();
        
        let access_request = AccessRequest {
            user_id: Uuid::new_v4(),
            resource_type: "Patient".to_string(),
            resource_id: Uuid::new_v4(),
            action: "READ".to_string(),
            context: AccessContext {
                ip_address: "*************".to_string(),
                user_agent: "Test Browser".to_string(),
                timestamp: Utc::now(),
            },
        };
        
        let result = access_controller.process_access_request(access_request).unwrap();
        
        // Should generate audit log entry
        assert!(result.audit_logged);
        assert!(result.access_granted); // Assuming valid request
        
        // Verify audit trail
        let audit_events = access_controller.get_access_audit_trail(result.user_id).unwrap();
        assert!(!audit_events.is_empty());
    }

    #[test]
    fn test_session_management() {
        let session_manager = SessionManager::new();
        
        let user_id = Uuid::new_v4();
        let session = session_manager.create_session(user_id, "doctor".to_string()).unwrap();
        
        assert_eq!(session.user_id, user_id);
        assert_eq!(session.role, "doctor");
        assert!(session.is_active);
        assert!(session.expires_at > Utc::now());
        
        // Test session validation
        assert!(session_manager.validate_session(&session.session_id).unwrap());
        
        // Test session expiration
        session_manager.expire_session(&session.session_id).unwrap();
        assert!(!session_manager.validate_session(&session.session_id).unwrap());
    }
}

/// Test compliance reporting functionality
#[cfg(test)]
mod compliance_reporting_tests {
    use super::*;

    #[test]
    fn test_hipaa_compliance_report_generation() {
        let reporter = ComplianceReporter::new();
        
        // Generate sample audit data
        let audit_events = vec![
            create_test_audit_event("PATIENT_ACCESS", "user1"),
            create_test_audit_event("PHI_ACCESS", "user1"),
            create_test_audit_event("DATA_EXPORT", "user2"),
        ];
        
        let report = reporter.generate_hipaa_report(&audit_events, Utc::now() - Duration::days(30), Utc::now()).unwrap();
        
        assert_eq!(report.report_type, ReportType::HipaaCompliance);
        assert_eq!(report.total_events, 3);
        assert!(report.phi_access_events > 0);
        assert!(report.data_export_events > 0);
        assert!(!report.violations.is_empty() || report.violations.is_empty()); // May or may not have violations
    }

    #[test]
    fn test_breach_detection_report() {
        let reporter = ComplianceReporter::new();
        
        // Simulate potential breach scenario
        let suspicious_events = vec![
            create_rapid_access_events("user1", 10), // Rapid successive access
            create_unusual_time_access("user2"), // Access at unusual hours
            create_bulk_export_event("user3"), // Large data export
        ].into_iter().flatten().collect();
        
        let breach_report = reporter.analyze_for_breaches(&suspicious_events).unwrap();
        
        assert!(!breach_report.potential_breaches.is_empty());
        assert!(breach_report.risk_score > 0.0);
        
        // Should identify different types of suspicious activity
        let breach_types: Vec<_> = breach_report.potential_breaches.iter()
            .map(|b| &b.breach_type)
            .collect();
        
        assert!(breach_types.contains(&&"rapid_access".to_string()));
    }

    #[test]
    fn test_compliance_metrics_calculation() {
        let metrics_calculator = ComplianceMetricsCalculator::new();
        
        let audit_data = ComplianceAuditData {
            total_access_events: 1000,
            phi_access_events: 200,
            failed_access_attempts: 5,
            data_export_events: 10,
            user_sessions: 150,
            average_session_duration: Duration::minutes(45),
        };
        
        let metrics = metrics_calculator.calculate_metrics(&audit_data).unwrap();
        
        assert_eq!(metrics.phi_access_ratio, 0.2); // 200/1000
        assert_eq!(metrics.failed_access_ratio, 0.005); // 5/1000
        assert!(metrics.compliance_score >= 0.0 && metrics.compliance_score <= 1.0);
    }
}

// Helper functions for tests

fn create_test_audit_event(action: &str, user_id: &str) -> AuditEvent {
    AuditEvent::new(
        AuditEventType::PatientDataAccess,
        Uuid::parse_str(&format!("{:0<32}", user_id)).unwrap_or_else(|_| Uuid::new_v4()),
        "Patient".to_string(),
        Uuid::new_v4(),
        action.to_string(),
        AuditOutcome::Success,
    )
}

fn create_audit_event_for_user(user_id: Uuid, action: &str) -> AuditEvent {
    AuditEvent::new(
        AuditEventType::PatientDataAccess,
        user_id,
        "Patient".to_string(),
        Uuid::new_v4(),
        action.to_string(),
        AuditOutcome::Success,
    )
}

fn create_rapid_access_events(user_id: &str, count: usize) -> Vec<AuditEvent> {
    (0..count).map(|_| create_test_audit_event("RAPID_ACCESS", user_id)).collect()
}

fn create_unusual_time_access(user_id: &str) -> Vec<AuditEvent> {
    vec![create_test_audit_event("UNUSUAL_TIME_ACCESS", user_id)]
}

fn create_bulk_export_event(user_id: &str) -> Vec<AuditEvent> {
    vec![create_test_audit_event("BULK_EXPORT", user_id)]
}

// Test data structures

#[derive(Debug, Clone, PartialEq)]
enum AuditEventType {
    PatientDataAccess,
    PhiAccess,
    UserLogin,
    UserLogout,
    DataModification,
    SystemAccess,
    DataExport,
}

#[derive(Debug, Clone, PartialEq)]
enum AuditOutcome {
    Success,
    Failure,
    Warning,
}

#[derive(Debug, Clone, PartialEq)]
enum EncryptionAlgorithm {
    Aes256Gcm,
    ChaCha20Poly1305,
}

#[derive(Debug, Clone, PartialEq)]
enum KeyStatus {
    Active,
    Rotated,
    Revoked,
}

#[derive(Debug, Clone, PartialEq)]
enum Permission {
    ReadPatientData,
    WritePatientData,
    ReadMedicalRecords,
    WriteMedicalRecords,
    ReadBillingData,
    WriteBillingData,
}

#[derive(Debug, Clone, PartialEq)]
enum ReportType {
    HipaaCompliance,
    BreachDetection,
    AccessControl,
    DataRetention,
}
