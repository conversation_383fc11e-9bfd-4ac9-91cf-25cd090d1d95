apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: logging
data:
  fluent.conf: |
    # Fluentd configuration for OCTAVE Healthcare logging
    
    <system>
      log_level info
      suppress_repeated_stacktrace true
      emit_error_log_interval 30s
      suppress_config_dump
      without_source
    </system>
    
    # Input from Kubernetes containers
    <source>
      @type tail
      @id in_tail_container_logs
      path /var/log/containers/*.log
      pos_file /var/log/fluentd-containers.log.pos
      tag kubernetes.*
      read_from_head true
      <parse>
        @type json
        time_format %Y-%m-%dT%H:%M:%S.%NZ
        time_key time
        keep_time_key true
      </parse>
    </source>
    
    # Input from OCTAVE application logs
    <source>
      @type tail
      @id in_tail_octave_logs
      path /app/logs/*.log
      pos_file /var/log/fluentd-octave.log.pos
      tag octave.application
      <parse>
        @type json
        time_format %Y-%m-%dT%H:%M:%S.%NZ
        time_key timestamp
        keep_time_key true
      </parse>
    </source>
    
    # Input from OCTAVE audit logs (HIPAA compliance)
    <source>
      @type tail
      @id in_tail_audit_logs
      path /app/audit-logs/*.log
      pos_file /var/log/fluentd-audit.log.pos
      tag octave.audit
      <parse>
        @type json
        time_format %Y-%m-%dT%H:%M:%S.%NZ
        time_key timestamp
        keep_time_key true
      </parse>
    </source>
    
    # Filter to add Kubernetes metadata
    <filter kubernetes.**>
      @type kubernetes_metadata
      @id filter_kube_metadata
      kubernetes_url "#{ENV['KUBERNETES_SERVICE_HOST']}:#{ENV['KUBERNETES_SERVICE_PORT_HTTPS']}"
      verify_ssl "#{ENV['KUBERNETES_VERIFY_SSL'] || true}"
      ca_file "#{ENV['KUBERNETES_CA_FILE']}"
      skip_labels false
      skip_container_metadata false
      skip_master_url false
      skip_namespace_metadata false
    </filter>
    
    # Filter to parse OCTAVE application logs
    <filter octave.application>
      @type parser
      key_name message
      reserve_data true
      <parse>
        @type json
      </parse>
    </filter>
    
    # Filter for PHI detection and sanitization
    <filter octave.**>
      @type record_transformer
      enable_ruby true
      <record>
        # Add environment and service information
        environment production
        service octave-healthcare
        cluster octave-prod
        
        # Sanitize potential PHI in log messages
        message ${record["message"].to_s.gsub(/\b\d{3}-\d{2}-\d{4}\b/, "***-**-****")}  # SSN
        message ${record["message"].to_s.gsub(/\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/, "****-****-****-****")}  # Credit card
        message ${record["message"].to_s.gsub(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/, "***@***.***")}  # Email
      </record>
    </filter>
    
    # Filter for audit log compliance
    <filter octave.audit>
      @type record_transformer
      <record>
        log_type audit
        compliance_level hipaa
        retention_required true
        encryption_required true
      </record>
    </filter>
    
    # Output to Elasticsearch for application logs
    <match octave.application>
      @type elasticsearch
      @id out_es_application
      host elasticsearch
      port 9200
      logstash_format true
      logstash_prefix octave-application
      logstash_dateformat %Y.%m.%d
      include_tag_key true
      type_name _doc
      tag_key @log_name
      flush_interval 10s
      <buffer>
        @type file
        path /var/log/fluentd-buffers/application.buffer
        flush_mode interval
        retry_type exponential_backoff
        flush_thread_count 2
        flush_interval 5s
        retry_forever
        retry_max_interval 30
        chunk_limit_size 2M
        queue_limit_length 8
        overflow_action block
      </buffer>
    </match>
    
    # Output to secure storage for audit logs (HIPAA compliance)
    <match octave.audit>
      @type s3
      @id out_s3_audit
      aws_key_id "#{ENV['AWS_ACCESS_KEY_ID']}"
      aws_sec_key "#{ENV['AWS_SECRET_ACCESS_KEY']}"
      s3_bucket octave-audit-logs
      s3_region us-east-1
      path audit-logs/%Y/%m/%d/
      s3_object_key_format %{path}%{time_slice}_%{index}.%{file_extension}
      time_slice_format %Y%m%d-%H
      time_slice_wait 10m
      utc
      <format>
        @type json
      </format>
      <buffer time>
        @type file
        path /var/log/fluentd-buffers/audit.buffer
        timekey 3600  # 1 hour
        timekey_wait 600  # 10 minutes
        timekey_use_utc true
        chunk_limit_size 256m
      </buffer>
    </match>
    
    # Output to Elasticsearch for Kubernetes logs
    <match kubernetes.**>
      @type elasticsearch
      @id out_es_kubernetes
      host elasticsearch
      port 9200
      logstash_format true
      logstash_prefix kubernetes
      logstash_dateformat %Y.%m.%d
      include_tag_key true
      type_name _doc
      tag_key @log_name
      <buffer>
        @type file
        path /var/log/fluentd-buffers/kubernetes.buffer
        flush_mode interval
        retry_type exponential_backoff
        flush_thread_count 2
        flush_interval 5s
        retry_forever
        retry_max_interval 30
        chunk_limit_size 2M
        queue_limit_length 8
        overflow_action block
      </buffer>
    </match>
    
    # Catch-all output
    <match **>
      @type stdout
      @id out_stdout
    </match>
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluentd
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: logging
spec:
  selector:
    matchLabels:
      app: octave-healthcare
      component: fluentd
  template:
    metadata:
      labels:
        app: octave-healthcare
        component: fluentd
    spec:
      serviceAccountName: octave-service-account
      tolerations:
      - key: node-role.kubernetes.io/master
        effect: NoSchedule
      containers:
      - name: fluentd
        image: fluent/fluentd-kubernetes-daemonset:v1.16-debian-elasticsearch7-1
        env:
        - name: FLUENT_CONF
          value: "fluent.conf"
        - name: FLUENT_ELASTICSEARCH_HOST
          value: "elasticsearch"
        - name: FLUENT_ELASTICSEARCH_PORT
          value: "9200"
        - name: FLUENT_ELASTICSEARCH_SCHEME
          value: "http"
        - name: FLUENTD_SYSTEMD_CONF
          value: disable
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: octave-external-api-secret
              key: AWS_ACCESS_KEY_ID
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: octave-external-api-secret
              key: AWS_SECRET_ACCESS_KEY
        resources:
          limits:
            memory: 512Mi
            cpu: 500m
          requests:
            memory: 200Mi
            cpu: 100m
        volumeMounts:
        - name: fluentd-config
          mountPath: /fluentd/etc/fluent.conf
          subPath: fluent.conf
        - name: varlog
          mountPath: /var/log
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        - name: octave-logs
          mountPath: /app/logs
          readOnly: true
        - name: octave-audit-logs
          mountPath: /app/audit-logs
          readOnly: true
      terminationGracePeriodSeconds: 30
      volumes:
      - name: fluentd-config
        configMap:
          name: fluentd-config
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      - name: octave-logs
        hostPath:
          path: /var/log/octave
      - name: octave-audit-logs
        persistentVolumeClaim:
          claimName: octave-audit-logs-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: fluentd
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: logging
spec:
  type: ClusterIP
  ports:
  - port: 24224
    targetPort: 24224
    protocol: TCP
    name: fluentd
  selector:
    app: octave-healthcare
    component: fluentd
