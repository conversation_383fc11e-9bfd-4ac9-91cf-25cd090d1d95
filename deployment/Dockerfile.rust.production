# OCTAVE Healthcare Rust Production Dockerfile
# Optimized multi-stage build for production deployment with security hardening

# Build stage
FROM rust:1.75-slim as builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    pkg-config \
    libssl-dev \
    libpq-dev \
    ca-certificates \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy manifests first for better layer caching
COPY Cargo.toml Cargo.lock ./
COPY octave-*/Cargo.toml ./octave-*/

# Create dummy source files to cache dependencies
RUN mkdir -p octave-core/src octave-healthcare/src octave-auth/src \
    octave-api/src octave-db/src octave-semantic/src \
    octave-database/src octave-compliance/src && \
    echo "fn main() {}" > octave-api/src/main.rs && \
    echo "// dummy" > octave-core/src/lib.rs && \
    echo "// dummy" > octave-healthcare/src/lib.rs && \
    echo "// dummy" > octave-auth/src/lib.rs && \
    echo "// dummy" > octave-db/src/lib.rs && \
    echo "// dummy" > octave-semantic/src/lib.rs && \
    echo "// dummy" > octave-database/src/lib.rs && \
    echo "// dummy" > octave-compliance/src/lib.rs

# Build dependencies (this layer will be cached)
RUN cargo build --release --bin octave-server && \
    rm -rf target/release/deps/octave*

# Copy actual source code
COPY . .

# Build the application with healthcare profile for maximum optimization
RUN cargo build --profile healthcare --bin octave-server

# Strip binary for smaller size
RUN strip target/healthcare/octave-server

# Runtime stage - Use distroless for maximum security
FROM gcr.io/distroless/cc-debian12:latest

# Copy CA certificates for HTTPS
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/

# Copy the binary from builder stage
COPY --from=builder /app/target/healthcare/octave-server /usr/local/bin/octave-server

# Copy configuration templates and scripts
COPY --from=builder /app/deployment/config /app/config
COPY --from=builder /app/deployment/scripts /app/scripts

# Create necessary directories
USER 1001:1001
WORKDIR /app

# Expose port
EXPOSE 3000

# Health check using the binary itself
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD ["/usr/local/bin/octave-server", "--health-check"]

# Set environment variables for production
ENV RUST_LOG=info
ENV RUST_BACKTRACE=0
ENV OCTAVE_SERVER__HOST=0.0.0.0
ENV OCTAVE_SERVER__PORT=3000
ENV OCTAVE_ENVIRONMENT=production
ENV OCTAVE_HIPAA_STRICT=true
ENV OCTAVE_PHI_ENCRYPTION=true
ENV OCTAVE_SEMANTIC_ENABLED=true

# Metadata
LABEL maintainer="OCTAVE Development Team <<EMAIL>>"
LABEL version="1.0.0"
LABEL description="OCTAVE Healthcare Prior Authorization System - Rust Production"
LABEL org.opencontainers.image.source="https://github.com/company/octave-healthcare"
LABEL org.opencontainers.image.documentation="https://docs.octave-healthcare.com"
LABEL org.opencontainers.image.licenses="Proprietary"
LABEL org.opencontainers.image.title="OCTAVE Healthcare"
LABEL org.opencontainers.image.description="Healthcare Prior Authorization System with Semantic Protection"

# Start the application
ENTRYPOINT ["/usr/local/bin/octave-server"]
