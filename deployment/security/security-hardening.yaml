apiVersion: v1
kind: ConfigMap
metadata:
  name: security-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: security
data:
  # Security hardening configuration
  security.conf: |
    # OCTAVE Healthcare Security Configuration
    
    # Network Security
    network:
      # Firewall rules
      ingress_rules:
        - port: 443
          protocol: tcp
          source: "0.0.0.0/0"
          description: "HTTPS traffic"
        - port: 80
          protocol: tcp
          source: "0.0.0.0/0"
          description: "HTTP redirect to HTTPS"
        - port: 22
          protocol: tcp
          source: "10.0.0.0/8"
          description: "SSH from internal network only"
      
      # DDoS protection
      rate_limiting:
        enabled: true
        requests_per_minute: 1000
        burst_size: 100
        block_duration: 300
      
      # IP whitelisting for admin access
      admin_whitelist:
        - "10.0.0.0/8"
        - "**********/12"
        - "***********/16"
    
    # Application Security
    application:
      # Security headers
      headers:
        strict_transport_security: "max-age=31536000; includeSubDomains; preload"
        content_security_policy: "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none';"
        x_frame_options: "DENY"
        x_content_type_options: "nosniff"
        x_xss_protection: "1; mode=block"
        referrer_policy: "strict-origin-when-cross-origin"
        permissions_policy: "geolocation=(), microphone=(), camera=()"
      
      # Session security
      session:
        secure: true
        http_only: true
        same_site: "strict"
        max_age: 3600
        regenerate_on_auth: true
      
      # Input validation
      validation:
        max_request_size: 10485760  # 10MB
        max_header_size: 8192
        max_url_length: 2048
        sanitize_input: true
        validate_content_type: true
    
    # Authentication Security
    authentication:
      # Password policy
      password_policy:
        min_length: 12
        require_uppercase: true
        require_lowercase: true
        require_numbers: true
        require_special_chars: true
        max_age_days: 90
        history_count: 12
        lockout_attempts: 5
        lockout_duration: 900  # 15 minutes
      
      # Multi-factor authentication
      mfa:
        required: true
        methods: ["totp", "sms", "email"]
        backup_codes: 10
        grace_period: 86400  # 24 hours
      
      # JWT security
      jwt:
        algorithm: "RS256"
        key_rotation_days: 30
        max_age: 3600
        refresh_max_age: 604800  # 7 days
        issuer: "octave-healthcare.com"
        audience: "octave-api"
    
    # Data Protection
    data_protection:
      # Encryption
      encryption:
        algorithm: "AES-256-GCM"
        key_rotation_days: 90
        key_derivation: "PBKDF2"
        iterations: 100000
      
      # PHI protection
      phi_protection:
        detection_enabled: true
        sanitization_level: "strict"
        audit_all_access: true
        minimum_necessary: true
        access_logging: true
      
      # Data masking
      data_masking:
        enabled: true
        mask_ssn: true
        mask_credit_card: true
        mask_phone: true
        mask_email: true
    
    # Audit and Compliance
    audit:
      # Logging requirements
      logging:
        level: "info"
        format: "json"
        include_request_body: false
        include_response_body: false
        sanitize_phi: true
        retention_days: 2555  # 7 years
      
      # HIPAA compliance
      hipaa:
        strict_mode: true
        audit_all_phi_access: true
        breach_detection: true
        risk_assessment: true
        compliance_reporting: true
      
      # Security monitoring
      monitoring:
        failed_login_threshold: 5
        suspicious_activity_detection: true
        anomaly_detection: true
        threat_intelligence: true
---
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: octave-network-policy
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: security
spec:
  podSelector:
    matchLabels:
      app: octave-healthcare
  policyTypes:
  - Ingress
  - Egress
  ingress:
  # Allow ingress from nginx ingress controller
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 3000
  # Allow ingress from monitoring
  - from:
    - podSelector:
        matchLabels:
          component: monitoring
    ports:
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 9090
  # Allow database access
  - from:
    - podSelector:
        matchLabels:
          component: api
    - podSelector:
        matchLabels:
          component: database
    ports:
    - protocol: TCP
      port: 5432
  egress:
  # Allow egress to database
  - to:
    - podSelector:
        matchLabels:
          component: database
    ports:
    - protocol: TCP
      port: 5432
  # Allow egress to external APIs (HTTPS only)
  - to: []
    ports:
    - protocol: TCP
      port: 443
  # Allow DNS resolution
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
---
apiVersion: v1
kind: Secret
metadata:
  name: security-certificates
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: security
type: kubernetes.io/tls
stringData:
  # Production SSL certificate (replace with actual certificate)
  tls.crt: |
    -----BEGIN CERTIFICATE-----
    # Production SSL certificate for octave-healthcare.com
    # This should be replaced with actual certificate from CA
    -----END CERTIFICATE-----
  tls.key: |
    -----BEGIN PRIVATE KEY-----
    # Production SSL private key
    # This should be replaced with actual private key
    -----END PRIVATE KEY-----
  ca.crt: |
    -----BEGIN CERTIFICATE-----
    # Certificate Authority certificate
    # This should be replaced with actual CA certificate
    -----END CERTIFICATE-----
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-policies
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: security
data:
  pod-security-policy.yaml: |
    apiVersion: policy/v1beta1
    kind: PodSecurityPolicy
    metadata:
      name: octave-psp
      namespace: octave-healthcare
    spec:
      privileged: false
      allowPrivilegeEscalation: false
      requiredDropCapabilities:
        - ALL
      volumes:
        - 'configMap'
        - 'emptyDir'
        - 'projected'
        - 'secret'
        - 'downwardAPI'
        - 'persistentVolumeClaim'
      hostNetwork: false
      hostIPC: false
      hostPID: false
      runAsUser:
        rule: 'MustRunAsNonRoot'
      supplementalGroups:
        rule: 'MustRunAs'
        ranges:
          - min: 1
            max: 65535
      fsGroup:
        rule: 'MustRunAs'
        ranges:
          - min: 1
            max: 65535
      readOnlyRootFilesystem: false
  
  security-context-constraints.yaml: |
    apiVersion: security.openshift.io/v1
    kind: SecurityContextConstraints
    metadata:
      name: octave-scc
    allowHostDirVolumePlugin: false
    allowHostIPC: false
    allowHostNetwork: false
    allowHostPID: false
    allowHostPorts: false
    allowPrivilegedContainer: false
    allowedCapabilities: null
    defaultAddCapabilities: null
    fsGroup:
      type: MustRunAs
      ranges:
        - min: 1
          max: 65535
    readOnlyRootFilesystem: false
    requiredDropCapabilities:
    - ALL
    runAsUser:
      type: MustRunAsNonRoot
    supplementalGroups:
      type: MustRunAs
      ranges:
        - min: 1
          max: 65535
    volumes:
    - configMap
    - downwardAPI
    - emptyDir
    - persistentVolumeClaim
    - projected
    - secret
