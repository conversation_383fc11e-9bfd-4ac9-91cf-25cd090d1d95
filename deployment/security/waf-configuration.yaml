apiVersion: v1
kind: ConfigMap
metadata:
  name: waf-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: waf
data:
  modsecurity.conf: |
    # ModSecurity Configuration for OCTAVE Healthcare
    # OWASP Core Rule Set with healthcare-specific customizations
    
    # Basic configuration
    SecRuleEngine On
    SecRequestBodyAccess On
    SecResponseBodyAccess Off
    SecRequestBodyLimit 13107200
    SecRequestBodyNoFilesLimit 131072
    SecRequestBodyInMemoryLimit 131072
    SecRequestBodyLimitAction Reject
    SecPcreMatchLimit 1000
    SecPcreMatchLimitRecursion 1000
    
    # Audit logging
    SecAuditEngine RelevantOnly
    SecAuditLogRelevantStatus "^(?:5|4(?!04))"
    SecAuditLogParts ABIJDEFHZ
    SecAuditLogType Serial
    SecAuditLog /var/log/modsec_audit.log
    
    # Debug logging
    SecDebugLog /var/log/modsec_debug.log
    SecDebugLogLevel 0
    
    # Temporary directory
    SecTmpDir /tmp/
    SecDataDir /tmp/
    
    # Unicode mapping
    SecUnicodeMapFile unicode.mapping 20127
    
    # Include OWASP Core Rule Set
    Include /etc/modsecurity/owasp-crs/*.conf
    Include /etc/modsecurity/owasp-crs/rules/*.conf
    
    # Healthcare-specific rules
    Include /etc/modsecurity/healthcare-rules/*.conf
  
  healthcare-rules.conf: |
    # Healthcare-specific ModSecurity rules for OCTAVE
    
    # PHI Protection Rules
    SecRule ARGS "@detectSQLi" \
        "id:1001,\
        phase:2,\
        block,\
        msg:'SQL Injection Attack Detected in Healthcare System',\
        logdata:'Matched Data: %{MATCHED_VAR} found within %{MATCHED_VAR_NAME}',\
        tag:'application-multi',\
        tag:'language-multi',\
        tag:'platform-multi',\
        tag:'attack-sqli',\
        tag:'healthcare-phi',\
        severity:'CRITICAL',\
        setvar:'tx.sql_injection_score=+%{tx.critical_anomaly_score}',\
        setvar:'tx.anomaly_score_pl1=+%{tx.critical_anomaly_score}'"
    
    # Block potential PHI in URLs
    SecRule REQUEST_URI "@rx (?i)(?:ssn|social.security|medical.record|patient.id|mrn)" \
        "id:1002,\
        phase:1,\
        block,\
        msg:'Potential PHI in URL detected',\
        tag:'healthcare-phi',\
        tag:'privacy-violation',\
        severity:'CRITICAL'"
    
    # Block potential PHI patterns in request body
    SecRule REQUEST_BODY "@rx \b\d{3}-\d{2}-\d{4}\b" \
        "id:1003,\
        phase:2,\
        block,\
        msg:'SSN pattern detected in request body',\
        tag:'healthcare-phi',\
        tag:'ssn-detection',\
        severity:'CRITICAL'"
    
    # Medical record number pattern detection
    SecRule REQUEST_BODY "@rx \b(?:MRN|mrn|medical.record).{0,10}\d{6,}\b" \
        "id:1004,\
        phase:2,\
        block,\
        msg:'Medical record number pattern detected',\
        tag:'healthcare-phi',\
        tag:'mrn-detection',\
        severity:'CRITICAL'"
    
    # HIPAA compliance - block unauthorized access attempts
    SecRule REQUEST_URI "@rx (?i)/admin|/config|/debug|/test" \
        "id:1005,\
        phase:1,\
        deny,\
        status:403,\
        msg:'Unauthorized access to sensitive endpoints',\
        tag:'healthcare-security',\
        tag:'unauthorized-access',\
        severity:'WARNING'"
    
    # Rate limiting for authentication endpoints
    SecAction \
        "id:1006,\
        phase:1,\
        nolog,\
        pass,\
        initcol:ip=%{REMOTE_ADDR},\
        setvar:ip.auth_attempts=+1,\
        expirevar:ip.auth_attempts=300"
    
    SecRule IP:AUTH_ATTEMPTS "@gt 5" \
        "id:1007,\
        phase:1,\
        deny,\
        status:429,\
        msg:'Too many authentication attempts',\
        tag:'healthcare-security',\
        tag:'brute-force',\
        severity:'WARNING'"
    
    # Block common attack patterns
    SecRule ARGS "@detectXSS" \
        "id:1008,\
        phase:2,\
        block,\
        msg:'XSS Attack Detected in Healthcare System',\
        tag:'healthcare-security',\
        tag:'xss-attack',\
        severity:'CRITICAL'"
    
    # File upload restrictions
    SecRule FILES_TMPNAMES "@inspectFile /etc/modsecurity/file-inspection.lua" \
        "id:1009,\
        phase:2,\
        block,\
        msg:'Malicious file upload detected',\
        tag:'healthcare-security',\
        tag:'file-upload',\
        severity:'CRITICAL'"
    
    # API endpoint protection
    SecRule REQUEST_URI "@beginsWith /api/" \
        "id:1010,\
        phase:1,\
        pass,\
        nolog,\
        setvar:'tx.api_request=1'"
    
    SecRule TX:API_REQUEST "@eq 1" \
        "id:1011,\
        phase:2,\
        chain,\
        pass,\
        nolog"
        SecRule REQUEST_HEADERS:Content-Type "!@rx ^application/json" \
            "block,\
            msg:'Invalid content type for API request',\
            tag:'healthcare-api',\
            severity:'WARNING'"
  
  nginx-waf.conf: |
    # NGINX with ModSecurity configuration
    
    # Load ModSecurity module
    load_module modules/ngx_http_modsecurity_module.so;
    
    http {
        # ModSecurity configuration
        modsecurity on;
        modsecurity_rules_file /etc/modsecurity/modsecurity.conf;
        
        # Rate limiting zones
        limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=api:10m rate=20r/s;
        limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
        limit_req_zone $binary_remote_addr zone=upload:10m rate=2r/s;
        
        # Connection limiting
        limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
        limit_conn_zone $server_name zone=conn_limit_per_server:10m;
        
        # GeoIP blocking (if enabled)
        # geoip_country /usr/share/GeoIP/GeoIP.dat;
        # map $geoip_country_code $allowed_country {
        #     default yes;
        #     CN no;
        #     RU no;
        # }
        
        server {
            listen 80;
            server_name octave-healthcare.com;
            return 301 https://$server_name$request_uri;
        }
        
        server {
            listen 443 ssl http2;
            server_name octave-healthcare.com;
            
            # SSL configuration
            ssl_certificate /etc/ssl/certs/octave-healthcare.crt;
            ssl_certificate_key /etc/ssl/private/octave-healthcare.key;
            
            # Connection limits
            limit_conn conn_limit_per_ip 10;
            limit_conn conn_limit_per_server 1000;
            
            # Security headers
            add_header X-Frame-Options DENY always;
            add_header X-Content-Type-Options nosniff always;
            add_header X-XSS-Protection "1; mode=block" always;
            add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
            
            # Block suspicious user agents
            if ($http_user_agent ~* (nmap|nikto|wikto|sf|sqlmap|bsqlbf|w3af|acunetix|havij|appscan)) {
                return 403;
            }
            
            # Block requests with no user agent
            if ($http_user_agent = "") {
                return 403;
            }
            
            # API endpoints with enhanced protection
            location /api/ {
                # Rate limiting
                limit_req zone=api burst=50 nodelay;
                
                # ModSecurity enabled
                modsecurity on;
                
                # Additional security headers for API
                add_header X-API-Version "1.0" always;
                add_header X-RateLimit-Limit "20" always;
                
                # Proxy to backend
                proxy_pass http://octave-api:3000;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Hide backend server information
                proxy_hide_header X-Powered-By;
                proxy_hide_header Server;
            }
            
            # Authentication endpoints with strict rate limiting
            location /auth/ {
                limit_req zone=auth burst=10 nodelay;
                modsecurity on;
                
                proxy_pass http://octave-api:3000;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # File upload endpoints with special handling
            location /upload/ {
                limit_req zone=upload burst=5 nodelay;
                modsecurity on;
                
                # File size limit
                client_max_body_size 10M;
                
                proxy_pass http://octave-api:3000;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                
                # Timeout settings for file uploads
                proxy_read_timeout 300s;
                proxy_send_timeout 300s;
            }
            
            # Health check endpoint (no rate limiting)
            location /health {
                access_log off;
                modsecurity off;
                
                proxy_pass http://octave-api:3000;
                proxy_set_header Host $host;
            }
            
            # Block access to sensitive files
            location ~* \.(env|config|ini|log|bak|backup|old)$ {
                deny all;
                return 404;
            }
            
            # Block access to hidden files
            location ~ /\. {
                deny all;
                return 404;
            }
        }
    }
  
  file-inspection.lua: |
    -- File inspection script for ModSecurity
    -- Checks uploaded files for malicious content
    
    function main(filename)
        local file = io.open(filename, "rb")
        if not file then
            return "Unable to open file"
        end
        
        local content = file:read("*all")
        file:close()
        
        -- Check for malicious patterns
        local malicious_patterns = {
            "<%script",
            "javascript:",
            "vbscript:",
            "onload=",
            "onerror=",
            "eval%(",
            "document%.cookie",
            "window%.location",
            "<iframe",
            "<?php",
            "<%",
            "#!/bin/",
            "cmd.exe",
            "powershell"
        }
        
        for _, pattern in ipairs(malicious_patterns) do
            if string.find(content:lower(), pattern) then
                return "Malicious pattern detected: " .. pattern
            end
        end
        
        -- Check file size (max 10MB)
        if #content > 10485760 then
            return "File too large"
        end
        
        -- Check for executable file signatures
        local exe_signatures = {
            "MZ",  -- Windows executable
            "\x7fELF",  -- Linux executable
            "#!/bin/sh",  -- Shell script
            "#!/bin/bash"  -- Bash script
        }
        
        for _, sig in ipairs(exe_signatures) do
            if string.sub(content, 1, #sig) == sig then
                return "Executable file detected"
            end
        end
        
        return nil  -- File is clean
    end
