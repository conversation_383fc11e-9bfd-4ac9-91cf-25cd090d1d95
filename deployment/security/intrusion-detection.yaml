apiVersion: v1
kind: ConfigMap
metadata:
  name: suricata-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: ids
data:
  suricata.yaml: |
    # Suricata IDS Configuration for OCTAVE Healthcare
    
    # Global settings
    vars:
      address-groups:
        HOME_NET: "[10.0.0.0/8,**********/12,***********/16]"
        EXTERNAL_NET: "!$HOME_NET"
        HTTP_SERVERS: "$HOME_NET"
        SMTP_SERVERS: "$HOME_NET"
        SQL_SERVERS: "$HOME_NET"
        DNS_SERVERS: "$HOME_NET"
        TELNET_SERVERS: "$HOME_NET"
        AIM_SERVERS: "$EXTERNAL_NET"
        DC_SERVERS: "$HOME_NET"
        DNP3_SERVER: "$HOME_NET"
        DNP3_CLIENT: "$HOME_NET"
        MODBUS_CLIENT: "$HOME_NET"
        MODBUS_SERVER: "$HOME_NET"
        ENIP_CLIENT: "$HOME_NET"
        ENIP_SERVER: "$HOME_NET"
      
      port-groups:
        HTTP_PORTS: "80"
        SHELLCODE_PORTS: "!80"
        ORACLE_PORTS: 1521
        SSH_PORTS: 22
        DNP3_PORTS: 20000
        MODBUS_PORTS: 502
        FILE_DATA_PORTS: "[$HTTP_PORTS,110,143]"
        FTP_PORTS: 21
        GENEVE_PORTS: 6081
        VXLAN_PORTS: 4789
        TEREDO_PORTS: 3544
    
    # Default rule path
    default-rule-path: /var/lib/suricata/rules
    
    # Rule files
    rule-files:
      - suricata.rules
      - healthcare-custom.rules
      - emerging-threats.rules
      - botcc.rules
      - ciarmy.rules
      - compromised.rules
      - drop.rules
      - dshield.rules
      - emerging-activex.rules
      - emerging-attack_response.rules
      - emerging-chat.rules
      - emerging-current_events.rules
      - emerging-dns.rules
      - emerging-dos.rules
      - emerging-exploit.rules
      - emerging-ftp.rules
      - emerging-imap.rules
      - emerging-inappropriate.rules
      - emerging-malware.rules
      - emerging-misc.rules
      - emerging-mobile_malware.rules
      - emerging-netbios.rules
      - emerging-p2p.rules
      - emerging-policy.rules
      - emerging-pop3.rules
      - emerging-rpc.rules
      - emerging-scada.rules
      - emerging-scan.rules
      - emerging-shellcode.rules
      - emerging-smtp.rules
      - emerging-snmp.rules
      - emerging-sql.rules
      - emerging-telnet.rules
      - emerging-tftp.rules
      - emerging-trojan.rules
      - emerging-user_agents.rules
      - emerging-voip.rules
      - emerging-web_client.rules
      - emerging-web_server.rules
      - emerging-web_specific_apps.rules
      - emerging-worm.rules
    
    # Classification file
    classification-file: /etc/suricata/classification.config
    reference-config-file: /etc/suricata/reference.config
    
    # Logging configuration
    outputs:
      - fast:
          enabled: yes
          filename: fast.log
          append: yes
      
      - eve-log:
          enabled: yes
          filetype: regular
          filename: eve.json
          types:
            - alert:
                payload: yes
                payload-buffer-size: 4kb
                payload-printable: yes
                packet: yes
                metadata: yes
                http-body: yes
                http-body-printable: yes
                tagged-packets: yes
            - http:
                extended: yes
            - dns:
                query: yes
                answer: yes
            - tls:
                extended: yes
            - files:
                force-magic: no
                force-hash: [md5]
            - smtp:
            - ssh
            - stats:
                totals: yes
                threads: no
                deltas: no
            - flow
      
      - unified2-alert:
          enabled: no
          filename: unified2.alert
          limit: 32mb
          sensor-id: 0
          sensor-name: octave-healthcare-ids
      
      - http-log:
          enabled: no
          filename: http.log
          append: yes
      
      - tls-log:
          enabled: no
          filename: tls.log
          append: yes
      
      - dns-log:
          enabled: no
          filename: dns.log
          append: yes
      
      - pcap-log:
          enabled: no
          filename: log.pcap
          limit: 1000mb
          max-files: 2000
          compression: none
          mode: normal
          use-stream-depth: no
          honor-pass-rules: no
      
      - alert-debug:
          enabled: no
          filename: alert-debug.log
          append: yes
      
      - alert-prelude:
          enabled: no
          profile: suricata
          log-packet-content: no
          log-packet-header: yes
      
      - stats:
          enabled: yes
          filename: stats.log
          append: yes
          totals: yes
          threads: no
      
      - syslog:
          enabled: no
          facility: local5
          format: "[%i] <%d> -- "
          level: Info
    
    # Application layer parsers
    app-layer:
      protocols:
        tls:
          enabled: yes
          detection-ports:
            dp: 443
        http:
          enabled: yes
          libhtp:
            default-config:
              personality: IDS
              request-body-limit: 100kb
              response-body-limit: 100kb
              request-body-minimal-inspect-size: 32kb
              request-body-inspect-window: 4kb
              response-body-minimal-inspect-size: 40kb
              response-body-inspect-window: 16kb
              response-body-decompress-layer-limit: 2
              http-body-inline: auto
              swf-decompression:
                enabled: yes
                type: both
                compress-depth: 100kb
                decompress-depth: 100kb
              double-decode-path: no
              double-decode-query: no
        ftp:
          enabled: yes
        smtp:
          enabled: yes
          raw-extraction: no
          mime:
            decode-mime: yes
            decode-base64: yes
            decode-quoted-printable: yes
            header-value-depth: 2000
            extract-urls: yes
            body-md5: no
          inspected-tracker:
            content-limit: 100000
            content-inspect-min-size: 32768
            content-inspect-window: 4096
        imap:
          enabled: detection-only
        msn:
          enabled: detection-only
        smb:
          enabled: yes
          detection-ports:
            dp: 139, 445
        nfs:
          enabled: yes
        tftp:
          enabled: yes
        dns:
          tcp:
            enabled: yes
            detection-ports:
              dp: 53
          udp:
            enabled: yes
            detection-ports:
              dp: 53
        ssh:
          enabled: yes
        dcerpc:
          enabled: yes
        modbus:
          enabled: no
          detection-ports:
            dp: 502
          stream-depth: 0
        dnp3:
          enabled: no
          detection-ports:
            dp: 20000
        enip:
          enabled: no
          detection-ports:
            dp: 44818
            sp: 44818
        ntp:
          enabled: yes
        dhcp:
          enabled: yes
        sip:
          enabled: yes
    
    # Stream engine settings
    stream:
      memcap: 64mb
      checksum-validation: yes
      inline: auto
      reassembly:
        memcap: 256mb
        depth: 1mb
        toserver-chunk-size: 2560
        toclient-chunk-size: 2560
        randomize-chunk-size: yes
    
    # Host table
    host:
      hash-size: 4096
      prealloc: 1000
      memcap: 32mb
    
    # Flow settings
    flow:
      memcap: 128mb
      hash-size: 65536
      prealloc: 10000
      emergency-recovery: 30
    
    # Defrag settings
    defrag:
      memcap: 32mb
      hash-size: 65536
      trackers: 65536
      max-frags: 65536
      prealloc: yes
      timeout: 60
    
    # Flow timeouts
    flow-timeouts:
      default:
        new: 30
        established: 300
        closed: 0
        bypassed: 100
        emergency-new: 10
        emergency-established: 100
        emergency-closed: 0
        emergency-bypassed: 50
      tcp:
        new: 60
        established: 600
        closed: 60
        bypassed: 100
        emergency-new: 5
        emergency-established: 25
        emergency-closed: 5
        emergency-bypassed: 25
      udp:
        new: 30
        established: 300
        bypassed: 100
        emergency-new: 10
        emergency-established: 25
        emergency-bypassed: 25
      icmp:
        new: 30
        established: 300
        bypassed: 100
        emergency-new: 10
        emergency-established: 25
        emergency-bypassed: 25
    
    # Detection engine settings
    detect:
      profile: medium
      custom-values:
        toclient-groups: 3
        toserver-groups: 25
      sgh-mpm-context: auto
      inspection-recursion-limit: 3000
      prefilter:
        default: mpm
      grouping:
      rules:
    
    # Threading
    threading:
      set-cpu-affinity: no
      cpu-affinity:
        - management-cpu-set:
            cpu: [ 0 ]
        - receive-cpu-set:
            cpu: [ 0 ]
        - worker-cpu-set:
            cpu: [ "all" ]
            mode: "exclusive"
            prio:
              low: [ 0 ]
              medium: [ "1-2" ]
              high: [ 3 ]
              default: "medium"
      detect-thread-ratio: 1.0
    
    # Profiling
    profiling:
      rules:
        enabled: yes
        filename: rule_perf.log
        append: yes
        sort: avgticks
        limit: 10
        json: yes
      keywords:
        enabled: yes
        filename: keyword_perf.log
        append: yes
      prefilter:
        enabled: yes
        filename: prefilter_perf.log
        append: yes
      rulegroups:
        enabled: yes
        filename: rule_group_perf.log
        append: yes
      packets:
        enabled: yes
        filename: packet_stats.log
        append: yes
        csv:
          enabled: no
          filename: packet_stats.csv
      locks:
        enabled: no
        filename: lock_stats.log
        append: yes
      pcap-log:
        enabled: no
        filename: pcaplog_stats.log
        append: yes
    
    # Capture settings
    pcap:
      - interface: eth0
        threads: auto
        cluster-id: 99
        cluster-type: cluster_flow
        defrag: yes
        use-mmap: yes
        mmap-locked: yes
        tpacket-v3: yes
        ring-size: 2048
        block-size: 32768
        block-timeout: 10
        use-emergency-flush: yes
    
    # PCAP file mode
    pcap-file:
      checksum-checks: auto
    
    # Unix socket
    unix-command:
      enabled: auto
      filename: /var/run/suricata/suricata-command.socket
    
    # Magic file
    magic-file: /usr/share/file/magic
    
    # GeoIP database
    geoip-database: /usr/share/GeoIP/GeoLite2-Country.mmdb
  
  healthcare-custom.rules: |
    # Custom Suricata rules for OCTAVE Healthcare
    
    # PHI Detection Rules
    alert tcp any any -> $HOME_NET any (msg:"Potential SSN in HTTP traffic"; flow:established,to_server; content:"POST"; http_method; pcre:"/\b\d{3}-\d{2}-\d{4}\b/"; classtype:policy-violation; sid:1000001; rev:1;)
    
    alert tcp any any -> $HOME_NET any (msg:"Potential Medical Record Number"; flow:established,to_server; content:"POST"; http_method; pcre:"/\b(?:MRN|mrn|medical.record).{0,10}\d{6,}\b/"; classtype:policy-violation; sid:1000002; rev:1;)
    
    alert tcp any any -> $HOME_NET any (msg:"Potential Credit Card Number"; flow:established,to_server; content:"POST"; http_method; pcre:"/\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/"; classtype:policy-violation; sid:1000003; rev:1;)
    
    # Healthcare API Security
    alert tcp any any -> $HOME_NET 443 (msg:"Suspicious API access pattern"; flow:established,to_server; content:"GET"; http_method; content:"/api/patients"; http_uri; threshold:type both, track by_src, count 100, seconds 60; classtype:attempted-recon; sid:1000004; rev:1;)
    
    alert tcp any any -> $HOME_NET 443 (msg:"Potential API abuse - too many requests"; flow:established,to_server; content:"/api/"; http_uri; threshold:type both, track by_src, count 1000, seconds 300; classtype:attempted-dos; sid:1000005; rev:1;)
    
    # Authentication Security
    alert tcp any any -> $HOME_NET 443 (msg:"Multiple failed login attempts"; flow:established,to_server; content:"POST"; http_method; content:"/auth/login"; http_uri; content:"401"; http_stat_code; threshold:type both, track by_src, count 5, seconds 300; classtype:attempted-user; sid:1000006; rev:1;)
    
    alert tcp any any -> $HOME_NET 443 (msg:"Brute force attack detected"; flow:established,to_server; content:"POST"; http_method; content:"/auth/"; http_uri; threshold:type both, track by_src, count 20, seconds 60; classtype:attempted-user; sid:1000007; rev:1;)
    
    # Data Exfiltration Detection
    alert tcp $HOME_NET any -> any any (msg:"Large data transfer - potential exfiltration"; flow:established,from_server; dsize:>1000000; threshold:type both, track by_src, count 5, seconds 300; classtype:policy-violation; sid:1000008; rev:1;)
    
    alert tcp $HOME_NET any -> any any (msg:"Suspicious outbound connection to known bad IP"; flow:established,to_server; content:"User-Agent"; http_header; content:"curl"; http_header; classtype:trojan-activity; sid:1000009; rev:1;)
    
    # SQL Injection Detection
    alert tcp any any -> $HOME_NET any (msg:"SQL Injection attempt in healthcare system"; flow:established,to_server; content:"POST"; http_method; pcre:"/(?i)(union|select|insert|delete|update|drop|create|alter|exec|execute).*(from|into|where|order|group)/"; classtype:web-application-attack; sid:1000010; rev:1;)
    
    # XSS Detection
    alert tcp any any -> $HOME_NET any (msg:"XSS attempt in healthcare application"; flow:established,to_server; content:"<script"; http_client_body; nocase; classtype:web-application-attack; sid:1000011; rev:1;)
    
    # File Upload Security
    alert tcp any any -> $HOME_NET any (msg:"Suspicious file upload"; flow:established,to_server; content:"POST"; http_method; content:"multipart/form-data"; http_header; content:".php"; http_client_body; classtype:web-application-attack; sid:1000012; rev:1;)
    
    alert tcp any any -> $HOME_NET any (msg:"Executable file upload attempt"; flow:established,to_server; content:"POST"; http_method; content:"multipart/form-data"; http_header; content:".exe"; http_client_body; classtype:web-application-attack; sid:1000013; rev:1;)
    
    # HIPAA Compliance Monitoring
    alert tcp any any -> $HOME_NET any (msg:"Unencrypted PHI transmission detected"; flow:established,to_server; content:"HTTP/1."; content:!"https"; pcre:"/\b\d{3}-\d{2}-\d{4}\b/"; classtype:policy-violation; sid:1000014; rev:1;)
    
    # Malware Detection
    alert tcp any any -> $HOME_NET any (msg:"Potential malware communication"; flow:established,to_server; content:"User-Agent"; http_header; content:"Metasploit"; http_header; nocase; classtype:trojan-activity; sid:1000015; rev:1;)
    
    # Network Scanning Detection
    alert tcp any any -> $HOME_NET any (msg:"Port scan detected"; flags:S; threshold:type both, track by_src, count 10, seconds 60; classtype:attempted-recon; sid:1000016; rev:1;)
    
    # Insider Threat Detection
    alert tcp $HOME_NET any -> any any (msg:"Unusual data access pattern"; flow:established,to_server; content:"GET"; http_method; content:"/api/patients"; http_uri; threshold:type both, track by_src, count 500, seconds 3600; classtype:policy-violation; sid:1000017; rev:1;)
    
    # Compliance Violation
    alert tcp any any -> $HOME_NET any (msg:"Access to patient data outside business hours"; flow:established,to_server; content:"/api/patients"; http_uri; time:"<06:00,>22:00"; classtype:policy-violation; sid:1000018; rev:1;)
