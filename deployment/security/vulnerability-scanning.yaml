apiVersion: v1
kind: ConfigMap
metadata:
  name: vulnerability-scanner-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: security-scanning
data:
  trivy-config.yaml: |
    # Trivy vulnerability scanner configuration
    
    # Database settings
    db:
      repository: "ghcr.io/aquasecurity/trivy-db"
      
    # Scan settings
    scan:
      security-checks:
        - vuln
        - config
        - secret
      severity:
        - UNKNOWN
        - LOW
        - MEDIUM
        - HIGH
        - CRITICAL
      ignore-unfixed: false
      
    # Output settings
    format: json
    output: /reports/vulnerability-report.json
    
    # Cache settings
    cache:
      dir: /tmp/trivy-cache
      
    # Timeout settings
    timeout: 5m
  
  nmap-config.xml: |
    <?xml version="1.0" encoding="UTF-8"?>
    <!-- Nmap configuration for OCTAVE Healthcare network scanning -->
    <nmap-config>
      <scan-options>
        <timing>4</timing>
        <host-discovery>true</host-discovery>
        <port-scan>true</port-scan>
        <service-detection>true</service-detection>
        <os-detection>false</os-detection>
        <script-scan>true</script-scan>
      </scan-options>
      
      <scripts>
        <script>http-enum</script>
        <script>http-headers</script>
        <script>http-methods</script>
        <script>http-title</script>
        <script>ssl-cert</script>
        <script>ssl-enum-ciphers</script>
        <script>ssh-hostkey</script>
        <script>banner</script>
      </scripts>
      
      <ports>
        <port-range>1-65535</port-range>
        <common-ports>true</common-ports>
      </ports>
      
      <output>
        <format>xml</format>
        <file>/reports/network-scan.xml</file>
      </output>
    </nmap-config>
  
  owasp-zap-config.yaml: |
    # OWASP ZAP configuration for web application scanning
    
    # Global settings
    zap:
      proxy:
        host: 127.0.0.1
        port: 8080
      
      # Spider configuration
      spider:
        maxDepth: 5
        maxChildren: 10
        acceptCookies: true
        handleParameters: use_all
        parseComments: true
        parseRobotsTxt: true
        parseSitemapXml: true
        postForm: true
        processForm: true
        requestWaitTime: 200
        sendRefererHeader: true
        threadCount: 2
        userAgent: "OCTAVE-Security-Scanner/1.0"
      
      # Active scan configuration
      activeScan:
        policy: "Default Policy"
        strength: "Medium"
        threshold: "Medium"
        scanHeadersAllRequests: true
        inScopeOnly: true
        addQueryParam: false
        doNotScanAvgResponseTime: false
        delayInMs: 0
        maxRuleDurationInMs: 0
        maxScanDurationInMs: 0
        maxAlertsPerRule: 0
        scanNullJsonValues: false
      
      # Authentication
      authentication:
        method: "form"
        loginUrl: "https://octave-healthcare.com/auth/login"
        usernameParameter: "email"
        passwordParameter: "password"
        extraPostData: ""
        loggedInRegex: "\\Qdashboard\\E"
        loggedOutRegex: "\\Qlogin\\E"
      
      # Session management
      session:
        method: "cookie"
        sessionTokens:
          - "JSESSIONID"
          - "session_token"
          - "auth_token"
      
      # Context configuration
      context:
        name: "OCTAVE Healthcare"
        includePaths:
          - "https://octave-healthcare.com/.*"
          - "https://api.octave-healthcare.com/.*"
        excludePaths:
          - "https://octave-healthcare.com/logout"
          - "https://octave-healthcare.com/static/.*"
          - ".*\\.css"
          - ".*\\.js"
          - ".*\\.png"
          - ".*\\.jpg"
          - ".*\\.gif"
      
      # Report configuration
      report:
        format: "json"
        output: "/reports/web-app-scan.json"
        includePassiveAlerts: true
        includeActiveAlerts: true
        includeIgnoredAlerts: false
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: vulnerability-scan
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: security-scanning
spec:
  schedule: "0 2 * * 0"  # Weekly on Sunday at 2 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: octave-service-account
          securityContext:
            runAsNonRoot: true
            runAsUser: 1001
            fsGroup: 1001
          containers:
          - name: trivy-scanner
            image: aquasec/trivy:latest
            command:
            - /bin/sh
            - -c
            - |
              # Container vulnerability scanning
              echo "Starting vulnerability scan at $(date)"
              
              # Create reports directory
              mkdir -p /reports
              
              # Scan OCTAVE API container
              trivy image --config /config/trivy-config.yaml \
                --format json \
                --output /reports/api-vulnerabilities.json \
                octave-healthcare/api:latest
              
              # Scan database container
              trivy image --config /config/trivy-config.yaml \
                --format json \
                --output /reports/db-vulnerabilities.json \
                postgres:15-alpine
              
              # Scan for secrets in filesystem
              trivy fs --config /config/trivy-config.yaml \
                --format json \
                --output /reports/filesystem-secrets.json \
                /app
              
              # Generate summary report
              python3 /scripts/generate-summary.py /reports/
              
              echo "Vulnerability scan completed at $(date)"
            volumeMounts:
            - name: config
              mountPath: /config
            - name: reports
              mountPath: /reports
            - name: scripts
              mountPath: /scripts
            resources:
              requests:
                memory: "512Mi"
                cpu: "500m"
              limits:
                memory: "1Gi"
                cpu: "1"
          
          - name: network-scanner
            image: instrumentisto/nmap:latest
            command:
            - /bin/sh
            - -c
            - |
              # Network vulnerability scanning
              echo "Starting network scan at $(date)"
              
              # Scan internal network
              nmap -sS -sV -sC -O -A \
                --script vuln \
                --script-args=unsafe=1 \
                -oX /reports/internal-network-scan.xml \
                10.0.0.0/24
              
              # Scan external endpoints
              nmap -sS -sV -sC \
                --script http-enum,http-headers,ssl-cert,ssl-enum-ciphers \
                -oX /reports/external-scan.xml \
                octave-healthcare.com api.octave-healthcare.com
              
              # Convert XML to JSON for processing
              python3 /scripts/xml-to-json.py /reports/
              
              echo "Network scan completed at $(date)"
            volumeMounts:
            - name: reports
              mountPath: /reports
            - name: scripts
              mountPath: /scripts
            resources:
              requests:
                memory: "256Mi"
                cpu: "250m"
              limits:
                memory: "512Mi"
                cpu: "500m"
          
          - name: web-app-scanner
            image: owasp/zap2docker-stable:latest
            command:
            - /bin/sh
            - -c
            - |
              # Web application vulnerability scanning
              echo "Starting web application scan at $(date)"
              
              # Start ZAP daemon
              zap.sh -daemon -host 0.0.0.0 -port 8080 -config api.disablekey=true &
              sleep 30
              
              # Spider the application
              zap-cli --zap-url http://localhost:8080 open-url https://octave-healthcare.com
              zap-cli --zap-url http://localhost:8080 spider https://octave-healthcare.com
              
              # Active scan
              zap-cli --zap-url http://localhost:8080 active-scan https://octave-healthcare.com
              
              # Generate report
              zap-cli --zap-url http://localhost:8080 report -o /reports/web-app-scan.json -f json
              
              echo "Web application scan completed at $(date)"
            volumeMounts:
            - name: reports
              mountPath: /reports
            resources:
              requests:
                memory: "1Gi"
                cpu: "500m"
              limits:
                memory: "2Gi"
                cpu: "1"
          
          volumes:
          - name: config
            configMap:
              name: vulnerability-scanner-config
          - name: reports
            persistentVolumeClaim:
              claimName: security-reports-pvc
          - name: scripts
            configMap:
              name: scanner-scripts
          
          restartPolicy: OnFailure
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: security-reports-pvc
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: security-scanning
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: scanner-scripts
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: security-scanning
data:
  generate-summary.py: |
    #!/usr/bin/env python3
    """Generate vulnerability scan summary report"""
    
    import json
    import os
    import sys
    from datetime import datetime
    
    def load_json_report(filepath):
        """Load JSON report file"""
        try:
            with open(filepath, 'r') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading {filepath}: {e}")
            return None
    
    def analyze_vulnerabilities(reports_dir):
        """Analyze vulnerability reports and generate summary"""
        summary = {
            'scan_date': datetime.now().isoformat(),
            'total_vulnerabilities': 0,
            'critical': 0,
            'high': 0,
            'medium': 0,
            'low': 0,
            'unknown': 0,
            'components_scanned': 0,
            'reports': []
        }
        
        # Process Trivy reports
        for filename in os.listdir(reports_dir):
            if filename.endswith('-vulnerabilities.json'):
                report = load_json_report(os.path.join(reports_dir, filename))
                if report and 'Results' in report:
                    for result in report['Results']:
                        if 'Vulnerabilities' in result:
                            for vuln in result['Vulnerabilities']:
                                severity = vuln.get('Severity', 'UNKNOWN').lower()
                                summary['total_vulnerabilities'] += 1
                                summary[severity] = summary.get(severity, 0) + 1
                    
                    summary['components_scanned'] += 1
                    summary['reports'].append({
                        'file': filename,
                        'type': 'container_scan',
                        'vulnerabilities': len(report.get('Results', []))
                    })
        
        # Generate alerts for critical/high vulnerabilities
        if summary['critical'] > 0 or summary['high'] > 10:
            generate_alert(summary)
        
        # Save summary report
        with open(os.path.join(reports_dir, 'vulnerability-summary.json'), 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"Vulnerability scan summary generated: {summary['total_vulnerabilities']} total vulnerabilities found")
        return summary
    
    def generate_alert(summary):
        """Generate alert for critical vulnerabilities"""
        alert_message = f"""
        SECURITY ALERT: Critical vulnerabilities detected
        
        Scan Date: {summary['scan_date']}
        Total Vulnerabilities: {summary['total_vulnerabilities']}
        Critical: {summary['critical']}
        High: {summary['high']}
        
        Immediate action required!
        """
        
        # Send alert (implement notification mechanism)
        print("ALERT:", alert_message)
    
    if __name__ == "__main__":
        if len(sys.argv) != 2:
            print("Usage: python3 generate-summary.py <reports_directory>")
            sys.exit(1)
        
        reports_dir = sys.argv[1]
        analyze_vulnerabilities(reports_dir)
  
  xml-to-json.py: |
    #!/usr/bin/env python3
    """Convert XML scan reports to JSON format"""
    
    import json
    import os
    import sys
    import xml.etree.ElementTree as ET
    
    def xml_to_dict(element):
        """Convert XML element to dictionary"""
        result = {}
        
        # Add attributes
        if element.attrib:
            result['@attributes'] = element.attrib
        
        # Add text content
        if element.text and element.text.strip():
            if len(element) == 0:
                return element.text.strip()
            result['#text'] = element.text.strip()
        
        # Add child elements
        for child in element:
            child_data = xml_to_dict(child)
            if child.tag in result:
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data
        
        return result
    
    def convert_xml_files(reports_dir):
        """Convert all XML files in directory to JSON"""
        for filename in os.listdir(reports_dir):
            if filename.endswith('.xml'):
                xml_path = os.path.join(reports_dir, filename)
                json_path = os.path.join(reports_dir, filename.replace('.xml', '.json'))
                
                try:
                    tree = ET.parse(xml_path)
                    root = tree.getroot()
                    data = xml_to_dict(root)
                    
                    with open(json_path, 'w') as f:
                        json.dump(data, f, indent=2)
                    
                    print(f"Converted {filename} to JSON")
                except Exception as e:
                    print(f"Error converting {filename}: {e}")
    
    if __name__ == "__main__":
        if len(sys.argv) != 2:
            print("Usage: python3 xml-to-json.py <reports_directory>")
            sys.exit(1)
        
        reports_dir = sys.argv[1]
        convert_xml_files(reports_dir)
