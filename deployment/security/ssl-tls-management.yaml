apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
  labels:
    app: octave-healthcare
    component: ssl-management
spec:
  acme:
    # Production Let's Encrypt server
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod-private-key
    solvers:
    - http01:
        ingress:
          class: nginx
    - dns01:
        route53:
          region: us-east-1
          accessKeyID: AKIAIOSFODNN7EXAMPLE
          secretAccessKeySecretRef:
            name: route53-credentials
            key: secret-access-key
---
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-staging
  labels:
    app: octave-healthcare
    component: ssl-management
spec:
  acme:
    # Staging Let's Encrypt server for testing
    server: https://acme-staging-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-staging-private-key
    solvers:
    - http01:
        ingress:
          class: nginx
    - dns01:
        route53:
          region: us-east-1
          accessKeyID: AKIAIOSFODNN7EXAMPLE
          secretAccessKeySecretRef:
            name: route53-credentials
            key: secret-access-key
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: octave-healthcare-tls
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: ssl-management
spec:
  secretName: octave-healthcare-tls-secret
  issuerRef:
    name: letsencrypt-prod
    kind: ClusterIssuer
  dnsNames:
  - octave-healthcare.com
  - www.octave-healthcare.com
  - api.octave-healthcare.com
  - app.octave-healthcare.com
  - admin.octave-healthcare.com
  duration: 2160h  # 90 days
  renewBefore: 360h  # 15 days before expiry
  subject:
    organizations:
    - OCTAVE Healthcare Inc.
    countries:
    - US
    organizationalUnits:
    - IT Department
    localities:
    - San Francisco
    provinces:
    - California
  privateKey:
    algorithm: RSA
    encoding: PKCS1
    size: 2048
  usages:
  - digital signature
  - key encipherment
  - server auth
---
apiVersion: v1
kind: Secret
metadata:
  name: route53-credentials
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: ssl-management
type: Opaque
stringData:
  secret-access-key: "REPLACE_WITH_ACTUAL_AWS_SECRET_ACCESS_KEY"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: ssl-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: ssl-management
data:
  ssl.conf: |
    # SSL/TLS Configuration for OCTAVE Healthcare
    
    # SSL Protocols
    ssl_protocols TLSv1.2 TLSv1.3;
    
    # SSL Ciphers (Modern configuration)
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # SSL Session Configuration
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_session_tickets off;
    
    # OCSP Stapling
    ssl_stapling on;
    ssl_stapling_verify on;
    resolver ******* ******* valid=300s;
    resolver_timeout 5s;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none';" always;
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
    
    # Certificate Transparency
    ssl_ct on;
    ssl_ct_static_scts /etc/ssl/scts;
  
  nginx-ssl.conf: |
    # NGINX SSL Configuration for OCTAVE Healthcare
    
    server {
        listen 80;
        server_name octave-healthcare.com www.octave-healthcare.com api.octave-healthcare.com;
        return 301 https://$server_name$request_uri;
    }
    
    server {
        listen 443 ssl http2;
        server_name octave-healthcare.com www.octave-healthcare.com;
        
        # SSL Certificate
        ssl_certificate /etc/ssl/certs/octave-healthcare-tls.crt;
        ssl_certificate_key /etc/ssl/private/octave-healthcare-tls.key;
        
        # Include SSL configuration
        include /etc/nginx/ssl.conf;
        
        # Root and index
        root /var/www/html;
        index index.html index.htm;
        
        # Security headers
        include /etc/nginx/security-headers.conf;
        
        # Rate limiting
        limit_req zone=general burst=20 nodelay;
        
        location / {
            try_files $uri $uri/ =404;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
    
    server {
        listen 443 ssl http2;
        server_name api.octave-healthcare.com;
        
        # SSL Certificate
        ssl_certificate /etc/ssl/certs/octave-healthcare-tls.crt;
        ssl_certificate_key /etc/ssl/private/octave-healthcare-tls.key;
        
        # Include SSL configuration
        include /etc/nginx/ssl.conf;
        
        # Security headers
        include /etc/nginx/security-headers.conf;
        
        # Rate limiting for API
        limit_req zone=api burst=50 nodelay;
        
        # Proxy to OCTAVE API
        location / {
            proxy_pass http://octave-api:3000;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
            
            # Security headers for API
            proxy_hide_header X-Powered-By;
            proxy_hide_header Server;
        }
    }
  
  security-headers.conf: |
    # Security Headers Configuration
    
    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Prevent clickjacking
    add_header X-Frame-Options "DENY" always;
    
    # Prevent MIME type sniffing
    add_header X-Content-Type-Options "nosniff" always;
    
    # XSS Protection
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Referrer Policy
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Content Security Policy
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' https:; frame-ancestors 'none'; base-uri 'self'; form-action 'self';" always;
    
    # Permissions Policy
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()" always;
    
    # Remove server information
    server_tokens off;
    more_clear_headers Server;
    more_clear_headers X-Powered-By;
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: ssl-certificate-monitor
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: ssl-management
spec:
  schedule: "0 6 * * *"  # Daily at 6 AM
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: octave-service-account
          containers:
          - name: ssl-monitor
            image: alpine/openssl:latest
            command:
            - /bin/sh
            - -c
            - |
              # SSL Certificate Monitoring Script
              
              DOMAINS="octave-healthcare.com api.octave-healthcare.com app.octave-healthcare.com"
              ALERT_DAYS=30
              
              for domain in $DOMAINS; do
                echo "Checking SSL certificate for $domain"
                
                # Get certificate expiry date
                expiry_date=$(echo | openssl s_client -servername $domain -connect $domain:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
                
                # Convert to epoch time
                expiry_epoch=$(date -d "$expiry_date" +%s)
                current_epoch=$(date +%s)
                
                # Calculate days until expiry
                days_until_expiry=$(( (expiry_epoch - current_epoch) / 86400 ))
                
                echo "Certificate for $domain expires in $days_until_expiry days"
                
                # Alert if certificate expires soon
                if [ $days_until_expiry -lt $ALERT_DAYS ]; then
                  echo "WARNING: Certificate for $domain expires in $days_until_expiry days!"
                  # Send alert (implement notification mechanism)
                  curl -X POST -H 'Content-type: application/json' \
                    --data "{\"text\":\"SSL Certificate Alert: $domain expires in $days_until_expiry days\"}" \
                    $SLACK_WEBHOOK_URL
                fi
              done
            env:
            - name: SLACK_WEBHOOK_URL
              valueFrom:
                secretKeyRef:
                  name: octave-monitoring-secret
                  key: ALERTMANAGER_WEBHOOK_URL
          restartPolicy: OnFailure
---
apiVersion: v1
kind: Service
metadata:
  name: ssl-certificate-service
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: ssl-management
spec:
  type: ClusterIP
  ports:
  - port: 443
    targetPort: 443
    protocol: TCP
    name: https
  - port: 80
    targetPort: 80
    protocol: TCP
    name: http
  selector:
    app: octave-healthcare
    component: nginx
