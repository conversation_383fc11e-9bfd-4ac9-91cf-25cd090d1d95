# OCTAVE Healthcare Production Configuration
# This file contains production-specific settings for the OCTAVE system

[server]
host = "0.0.0.0"
port = 3000
environment = "production"
workers = 4
max_connections = 1000
request_timeout = 30
max_request_size = 10485760  # 10MB
graceful_shutdown_timeout = 30

[database]
# Database connection settings
max_connections = 20
min_connections = 5
connect_timeout = 30
idle_timeout = 600
max_lifetime = 3600
# Connection URL should be set via environment variable: DATABASE_URL

[logging]
level = "info"
format = "json"
file_rotation = "daily"
max_file_size = "100MB"
retention_days = 365  # HIPAA requires 7 years, but logs can be archived
audit_log_encryption = true

[security]
# JWT Configuration
jwt_expiry = 3600  # 1 hour
jwt_refresh_expiry = 604800  # 7 days
# JWT secrets should be set via environment variables

# Rate limiting
rate_limit_requests = 1000
rate_limit_window = 3600  # 1 hour
rate_limit_burst = 100

# CORS settings
cors_enabled = true
cors_origins = ["https://octave-healthcare.com", "https://app.octave-healthcare.com"]
cors_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
cors_headers = ["Content-Type", "Authorization", "X-Requested-With"]

# Security headers
security_headers = true
hsts_max_age = 31536000  # 1 year
content_security_policy = "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"

[healthcare]
# PHI Protection
phi_encryption = true
phi_detection_enabled = true
phi_sanitization_level = "strict"
medical_coding_validation = true
clinical_decision_support = true

# HIPAA Compliance
hipaa_strict_mode = true
audit_all_phi_access = true
minimum_necessary_enforcement = true
breach_detection_enabled = true
compliance_reporting = true

[semantic]
# Semantic Protection System
enabled = true
threat_detection = true
adaptive_learning = true
antibody_effectiveness_threshold = 0.85
threat_intelligence_updates = true

# Immune System Configuration
immune_system_sensitivity = "high"
false_positive_tolerance = 0.05
learning_rate = 0.01
pattern_update_frequency = 3600  # 1 hour

[monitoring]
# Application Monitoring
metrics_enabled = true
health_check_interval = 30
prometheus_enabled = true
prometheus_port = 9090

# Performance Monitoring
performance_tracking = true
slow_query_threshold = 1000  # 1 second
memory_usage_alerts = true
cpu_usage_alerts = true

# Business Metrics
business_metrics_enabled = true
user_activity_tracking = true
prior_auth_metrics = true
compliance_metrics = true

[alerting]
# Alert Configuration
enabled = true
email_notifications = true
slack_notifications = true
pagerduty_integration = true

# Alert Thresholds
error_rate_threshold = 0.01  # 1%
response_time_threshold = 2000  # 2 seconds
memory_usage_threshold = 0.85  # 85%
cpu_usage_threshold = 0.80  # 80%
disk_usage_threshold = 0.90  # 90%

[backup]
# Backup Configuration
enabled = true
frequency = "daily"
retention_days = 2555  # 7 years for HIPAA compliance
encryption_enabled = true
compression_enabled = true
verification_enabled = true

# Backup Storage
storage_type = "s3"
storage_region = "us-east-1"
storage_bucket = "octave-healthcare-backups"

[compliance]
# HIPAA Compliance Settings
audit_log_retention = 2555  # 7 years
phi_access_logging = true
breach_notification_enabled = true
business_associate_tracking = true

# Regulatory Reporting
automated_reporting = true
compliance_dashboard = true
violation_alerts = true
risk_assessment_enabled = true

[performance]
# Performance Optimization
connection_pooling = true
query_caching = true
response_caching = true
compression_enabled = true

# Resource Limits
max_memory_usage = "2GB"
max_cpu_usage = 80
max_disk_usage = 90
max_network_bandwidth = "1Gbps"

[external_apis]
# External API Configuration
timeout = 30
retry_attempts = 3
retry_delay = 1000  # 1 second
circuit_breaker_enabled = true

# Insurance API Settings
insurance_api_timeout = 60
insurance_api_retries = 5
insurance_api_rate_limit = 100

[notifications]
# Notification Settings
email_enabled = true
sms_enabled = true
push_notifications = false  # Not implemented yet

# SMTP Configuration (credentials via environment variables)
smtp_host = "smtp.octave-healthcare.com"
smtp_port = 587
smtp_encryption = "tls"
smtp_timeout = 30

[development]
# Development-specific settings (disabled in production)
debug_mode = false
hot_reload = false
detailed_errors = false
sql_logging = false

[features]
# Feature Flags
patient_portal = true
mobile_app_support = false  # Not implemented yet
ai_powered_insights = true
advanced_analytics = true
multi_tenant_support = false  # Single tenant for now

# Experimental Features
experimental_features = false
beta_features = false
preview_features = false
