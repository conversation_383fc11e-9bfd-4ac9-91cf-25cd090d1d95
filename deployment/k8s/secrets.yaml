# OCTAVE Healthcare Secrets Configuration
# NOTE: This is a template file. In production, secrets should be managed
# through proper secret management systems like HashiCorp Vault, AWS Secrets Manager,
# or Kubernetes External Secrets Operator.

apiVersion: v1
kind: Secret
metadata:
  name: octave-database-secret
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: database
type: Opaque
stringData:
  # Database connection (replace with actual values)
  DATABASE_URL: "**************************************************************/octave_production"
  DB_USERNAME: "octave_user"
  DB_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  DB_HOST: "postgres"
  DB_PORT: "5432"
  DB_NAME: "octave_production"
---
apiVersion: v1
kind: Secret
metadata:
  name: octave-auth-secret
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: auth
type: Opaque
stringData:
  # JWT secrets (replace with actual values - minimum 64 characters)
  JWT_SECRET: "CHAN<PERSON>_ME_IN_PRODUCTION_MINIMUM_64_CHARACTERS_FOR_SECURITY_COMPLIANCE"
  JWT_REFRESH_SECRET: "CHANGE_ME_IN_PRODUCTION_REFRESH_TOKEN_SECRET_MINIMUM_64_CHARS"
  
  # Encryption keys for PHI protection (replace with actual values)
  PHI_ENCRYPTION_KEY: "CHANGE_ME_IN_PRODUCTION_32_BYTE_KEY_FOR_PHI_ENCRYPTION"
  AUDIT_ENCRYPTION_KEY: "CHANGE_ME_IN_PRODUCTION_32_BYTE_KEY_FOR_AUDIT_LOGS"
---
apiVersion: v1
kind: Secret
metadata:
  name: octave-ssl-secret
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: ssl
type: kubernetes.io/tls
stringData:
  # SSL/TLS certificates (replace with actual certificates)
  tls.crt: |
    -----BEGIN CERTIFICATE-----
    # Replace with actual SSL certificate
    -----END CERTIFICATE-----
  tls.key: |
    -----BEGIN PRIVATE KEY-----
    # Replace with actual SSL private key
    -----END PRIVATE KEY-----
---
apiVersion: v1
kind: Secret
metadata:
  name: octave-monitoring-secret
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: monitoring
type: Opaque
stringData:
  # Monitoring and alerting credentials
  PROMETHEUS_USERNAME: "octave_monitor"
  PROMETHEUS_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  GRAFANA_ADMIN_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
  ALERTMANAGER_WEBHOOK_URL: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
---
apiVersion: v1
kind: Secret
metadata:
  name: octave-external-api-secret
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: external-apis
type: Opaque
stringData:
  # External API keys and credentials
  INSURANCE_API_KEY: "CHANGE_ME_IN_PRODUCTION"
  MEDICAL_CODING_API_KEY: "CHANGE_ME_IN_PRODUCTION"
  NOTIFICATION_SERVICE_KEY: "CHANGE_ME_IN_PRODUCTION"
  
  # SMTP configuration for notifications
  SMTP_HOST: "smtp.example.com"
  SMTP_PORT: "587"
  SMTP_USERNAME: "<EMAIL>"
  SMTP_PASSWORD: "CHANGE_ME_IN_PRODUCTION"
---
# Service Account for OCTAVE application
apiVersion: v1
kind: ServiceAccount
metadata:
  name: octave-service-account
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
automountServiceAccountToken: true
---
# Role for OCTAVE application
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: octave-healthcare
  name: octave-role
rules:
- apiGroups: [""]
  resources: ["configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
---
# Role binding for OCTAVE application
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: octave-role-binding
  namespace: octave-healthcare
subjects:
- kind: ServiceAccount
  name: octave-service-account
  namespace: octave-healthcare
roleRef:
  kind: Role
  name: octave-role
  apiGroup: rbac.authorization.k8s.io
