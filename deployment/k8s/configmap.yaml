apiVersion: v1
kind: ConfigMap
metadata:
  name: octave-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: config
data:
  # Server Configuration
  OCTAVE_SERVER__HOST: "0.0.0.0"
  OCTAVE_SERVER__PORT: "3000"
  OCTAVE_SERVER__ENVIRONMENT: "production"
  
  # Logging Configuration
  RUST_LOG: "info"
  OCTAVE_LOGGING__LEVEL: "info"
  OCTAVE_LOGGING__FORMAT: "json"
  
  # Database Configuration
  OCTAVE_DATABASE__MAX_CONNECTIONS: "20"
  OCTAVE_DATABASE__MIN_CONNECTIONS: "5"
  OCTAVE_DATABASE__CONNECT_TIMEOUT: "30"
  OCTAVE_DATABASE__IDLE_TIMEOUT: "600"
  
  # HIPAA Compliance
  OCTAVE_HIPAA_STRICT: "true"
  OCTAVE_PHI_ENCRYPTION: "true"
  OCTAVE_AUDIT_LOGGING: "true"
  
  # Semantic Protection
  OCTAVE_SEMANTIC_ENABLED: "true"
  OCTAVE_SEMANTIC__THREAT_DETECTION: "true"
  OCTAVE_SEMANTIC__ADAPTIVE_LEARNING: "true"
  
  # Performance Settings
  OCTAVE_PERFORMANCE__MAX_REQUEST_SIZE: "10485760"  # 10MB
  OCTAVE_PERFORMANCE__REQUEST_TIMEOUT: "30"
  OCTAVE_PERFORMANCE__WORKER_THREADS: "4"
  
  # Security Settings
  OCTAVE_SECURITY__RATE_LIMIT_REQUESTS: "1000"
  OCTAVE_SECURITY__RATE_LIMIT_WINDOW: "3600"
  OCTAVE_SECURITY__CORS_ENABLED: "true"
  
  # Monitoring
  OCTAVE_MONITORING__METRICS_ENABLED: "true"
  OCTAVE_MONITORING__HEALTH_CHECK_INTERVAL: "30"
  OCTAVE_MONITORING__PROMETHEUS_ENABLED: "true"
  
  # Healthcare Specific
  OCTAVE_HEALTHCARE__PHI_DETECTION: "true"
  OCTAVE_HEALTHCARE__MEDICAL_CODING_VALIDATION: "true"
  OCTAVE_HEALTHCARE__CLINICAL_DECISION_SUPPORT: "true"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: octave-nginx-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: nginx
data:
  nginx.conf: |
    user nginx;
    worker_processes auto;
    error_log /var/log/nginx/error.log warn;
    pid /var/run/nginx.pid;
    
    events {
        worker_connections 1024;
        use epoll;
        multi_accept on;
    }
    
    http {
        include /etc/nginx/mime.types;
        default_type application/octet-stream;
        
        # Security headers
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';" always;
        
        # Logging
        log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                        '$status $body_bytes_sent "$http_referer" '
                        '"$http_user_agent" "$http_x_forwarded_for"';
        
        access_log /var/log/nginx/access.log main;
        
        # Performance
        sendfile on;
        tcp_nopush on;
        tcp_nodelay on;
        keepalive_timeout 65;
        types_hash_max_size 2048;
        client_max_body_size 10M;
        
        # Gzip compression
        gzip on;
        gzip_vary on;
        gzip_min_length 1024;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
        
        # Rate limiting
        limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
        limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/s;
        
        upstream octave_backend {
            server octave-api:3000;
            keepalive 32;
        }
        
        server {
            listen 80;
            server_name _;
            return 301 https://$server_name$request_uri;
        }
        
        server {
            listen 443 ssl http2;
            server_name octave-healthcare.com;
            
            # SSL configuration
            ssl_certificate /etc/ssl/certs/octave.crt;
            ssl_certificate_key /etc/ssl/private/octave.key;
            ssl_protocols TLSv1.2 TLSv1.3;
            ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
            ssl_prefer_server_ciphers off;
            ssl_session_cache shared:SSL:10m;
            ssl_session_timeout 10m;
            
            # API endpoints
            location /api/ {
                limit_req zone=api burst=20 nodelay;
                proxy_pass http://octave_backend;
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection 'upgrade';
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
                proxy_cache_bypass $http_upgrade;
                proxy_read_timeout 300s;
                proxy_connect_timeout 75s;
            }
            
            # Authentication endpoints
            location /auth/ {
                limit_req zone=auth burst=10 nodelay;
                proxy_pass http://octave_backend;
                proxy_http_version 1.1;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
            }
            
            # Health check
            location /health {
                proxy_pass http://octave_backend;
                access_log off;
            }
            
            # Static files (if any)
            location /static/ {
                alias /var/www/static/;
                expires 1y;
                add_header Cache-Control "public, immutable";
            }
        }
    }
