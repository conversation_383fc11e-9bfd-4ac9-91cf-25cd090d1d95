apiVersion: v1
kind: Namespace
metadata:
  name: octave-healthcare
  labels:
    name: octave-healthcare
    environment: production
    compliance: hipaa
    security-level: high
  annotations:
    description: "OCTAVE Healthcare Prior Authorization System"
    contact: "<EMAIL>"
    compliance.hipaa/enabled: "true"
    security.policy/level: "strict"
---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: octave-resource-quota
  namespace: octave-healthcare
spec:
  hard:
    requests.cpu: "4"
    requests.memory: 8Gi
    limits.cpu: "8"
    limits.memory: 16Gi
    persistentvolumeclaims: "10"
    services: "10"
    secrets: "20"
    configmaps: "20"
---
apiVersion: v1
kind: LimitRange
metadata:
  name: octave-limit-range
  namespace: octave-healthcare
spec:
  limits:
  - default:
      cpu: "1"
      memory: "2Gi"
    defaultRequest:
      cpu: "500m"
      memory: "1Gi"
    type: Container
  - default:
      storage: "10Gi"
    type: PersistentVolumeClaim
