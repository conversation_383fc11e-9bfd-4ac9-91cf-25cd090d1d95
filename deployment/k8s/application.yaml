apiVersion: apps/v1
kind: Deployment
metadata:
  name: octave-api
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: api
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: octave-healthcare
      component: api
  template:
    metadata:
      labels:
        app: octave-healthcare
        component: api
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: octave-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: octave-api
        image: octave-healthcare/api:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        env:
        # Configuration from ConfigMap
        - name: OCTAVE_SERVER__HOST
          valueFrom:
            configMapKeyRef:
              name: octave-config
              key: OCTAVE_SERVER__HOST
        - name: OCTAVE_SERVER__PORT
          valueFrom:
            configMapKeyRef:
              name: octave-config
              key: OCTAVE_SERVER__PORT
        - name: OCTAVE_SERVER__ENVIRONMENT
          valueFrom:
            configMapKeyRef:
              name: octave-config
              key: OCTAVE_SERVER__ENVIRONMENT
        - name: RUST_LOG
          valueFrom:
            configMapKeyRef:
              name: octave-config
              key: RUST_LOG
        - name: OCTAVE_HIPAA_STRICT
          valueFrom:
            configMapKeyRef:
              name: octave-config
              key: OCTAVE_HIPAA_STRICT
        - name: OCTAVE_PHI_ENCRYPTION
          valueFrom:
            configMapKeyRef:
              name: octave-config
              key: OCTAVE_PHI_ENCRYPTION
        - name: OCTAVE_SEMANTIC_ENABLED
          valueFrom:
            configMapKeyRef:
              name: octave-config
              key: OCTAVE_SEMANTIC_ENABLED
        
        # Secrets
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: octave-database-secret
              key: DATABASE_URL
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: octave-auth-secret
              key: JWT_SECRET
        - name: PHI_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: octave-auth-secret
              key: PHI_ENCRYPTION_KEY
        
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1"
        
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: audit-logs
          mountPath: /app/audit-logs
        - name: config
          mountPath: /app/config
          readOnly: true
      
      volumes:
      - name: logs
        emptyDir: {}
      - name: audit-logs
        persistentVolumeClaim:
          claimName: octave-audit-logs-pvc
      - name: config
        configMap:
          name: octave-config
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - octave-healthcare
                - key: component
                  operator: In
                  values:
                  - api
              topologyKey: kubernetes.io/hostname
---
apiVersion: v1
kind: Service
metadata:
  name: octave-api
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: api
spec:
  type: ClusterIP
  ports:
  - port: 3000
    targetPort: 3000
    protocol: TCP
    name: http
  selector:
    app: octave-healthcare
    component: api
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: octave-audit-logs-pvc
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: audit
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 50Gi
  storageClassName: fast-ssd
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: octave-ingress
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - octave-healthcare.com
    - api.octave-healthcare.com
    secretName: octave-ssl-secret
  rules:
  - host: octave-healthcare.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: octave-api
            port:
              number: 3000
      - path: /health
        pathType: Prefix
        backend:
          service:
            name: octave-api
            port:
              number: 3000
  - host: api.octave-healthcare.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: octave-api
            port:
              number: 3000
