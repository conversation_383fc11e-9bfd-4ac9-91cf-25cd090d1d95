apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: database
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: database
spec:
  serviceName: postgres
  replicas: 1
  selector:
    matchLabels:
      app: octave-healthcare
      component: database
  template:
    metadata:
      labels:
        app: octave-healthcare
        component: database
    spec:
      serviceAccountName: octave-service-account
      securityContext:
        fsGroup: 999
        runAsUser: 999
        runAsNonRoot: true
      containers:
      - name: postgres
        image: postgres:15-alpine
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_DB
          valueFrom:
            secretKeyRef:
              name: octave-database-secret
              key: DB_NAME
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: octave-database-secret
              key: DB_USERNAME
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: octave-database-secret
              key: DB_PASSWORD
        - name: PGDATA
          value: /var/lib/postgresql/data/pgdata
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
            - -d
            - $(POSTGRES_DB)
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - $(POSTGRES_USER)
            - -d
            - $(POSTGRES_DB)
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
      - name: postgres-config
        configMap:
          name: postgres-config
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: database
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
    name: postgres
  selector:
    app: octave-healthcare
    component: database
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: database
data:
  postgresql.conf: |
    # PostgreSQL configuration for OCTAVE Healthcare
    # Optimized for healthcare workloads with HIPAA compliance
    
    # Connection settings
    max_connections = 200
    shared_buffers = 1GB
    effective_cache_size = 3GB
    work_mem = 16MB
    maintenance_work_mem = 256MB
    
    # WAL settings for durability
    wal_level = replica
    max_wal_size = 2GB
    min_wal_size = 80MB
    checkpoint_completion_target = 0.9
    
    # Logging for audit compliance
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_rotation_age = 1d
    log_rotation_size = 100MB
    log_min_duration_statement = 1000
    log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
    log_statement = 'mod'
    log_connections = on
    log_disconnections = on
    log_lock_waits = on
    
    # Security settings
    ssl = on
    ssl_cert_file = '/etc/ssl/certs/server.crt'
    ssl_key_file = '/etc/ssl/private/server.key'
    ssl_ca_file = '/etc/ssl/certs/ca.crt'
    ssl_ciphers = 'HIGH:MEDIUM:+3DES:!aNULL'
    ssl_prefer_server_ciphers = on
    
    # Performance tuning
    random_page_cost = 1.1
    effective_io_concurrency = 200
    default_statistics_target = 100
    
    # Autovacuum settings
    autovacuum = on
    autovacuum_max_workers = 3
    autovacuum_naptime = 1min
    
    # Memory settings
    shared_preload_libraries = 'pg_stat_statements'
    
    # Timezone
    timezone = 'UTC'
    
    # Locale
    lc_messages = 'en_US.UTF-8'
    lc_monetary = 'en_US.UTF-8'
    lc_numeric = 'en_US.UTF-8'
    lc_time = 'en_US.UTF-8'
