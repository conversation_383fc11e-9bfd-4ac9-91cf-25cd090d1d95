apiVersion: v1
kind: ConfigMap
metadata:
  name: user-experience-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: user-experience
data:
  synthetic-monitoring.js: |
    // Synthetic monitoring script for OCTAVE Healthcare
    const puppeteer = require('puppeteer');
    const prometheus = require('prom-client');
    
    // Metrics
    const pageLoadTime = new prometheus.Histogram({
      name: 'octave_page_load_duration_seconds',
      help: 'Page load duration in seconds',
      labelNames: ['page', 'environment'],
      buckets: [0.1, 0.5, 1, 2, 5, 10]
    });
    
    const userJourneySuccess = new prometheus.Counter({
      name: 'octave_user_journey_success_total',
      help: 'Successful user journey completions',
      labelNames: ['journey', 'environment']
    });
    
    const userJourneyFailure = new prometheus.Counter({
      name: 'octave_user_journey_failure_total',
      help: 'Failed user journey attempts',
      labelNames: ['journey', 'environment', 'error_type']
    });
    
    const apiResponseTime = new prometheus.Histogram({
      name: 'octave_synthetic_api_response_duration_seconds',
      help: 'API response time from synthetic tests',
      labelNames: ['endpoint', 'method', 'status_code'],
      buckets: [0.1, 0.5, 1, 2, 5]
    });
    
    // Test configurations
    const testConfig = {
      baseUrl: process.env.OCTAVE_BASE_URL || 'https://octave-healthcare.com',
      testUser: {
        email: process.env.TEST_USER_EMAIL || '<EMAIL>',
        password: process.env.TEST_USER_PASSWORD || 'test-password'
      },
      timeout: 30000,
      viewport: { width: 1920, height: 1080 }
    };
    
    // User journey tests
    async function testLoginJourney() {
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();
      await page.setViewport(testConfig.viewport);
      
      try {
        const startTime = Date.now();
        
        // Navigate to login page
        await page.goto(`${testConfig.baseUrl}/login`, { waitUntil: 'networkidle0' });
        
        // Measure page load time
        const loadTime = (Date.now() - startTime) / 1000;
        pageLoadTime.labels('login', 'production').observe(loadTime);
        
        // Fill login form
        await page.type('#email', testConfig.testUser.email);
        await page.type('#password', testConfig.testUser.password);
        
        // Submit form
        await Promise.all([
          page.waitForNavigation({ waitUntil: 'networkidle0' }),
          page.click('#login-button')
        ]);
        
        // Verify successful login
        await page.waitForSelector('#dashboard', { timeout: 10000 });
        
        userJourneySuccess.labels('login', 'production').inc();
        console.log('Login journey successful');
        
      } catch (error) {
        userJourneyFailure.labels('login', 'production', error.name).inc();
        console.error('Login journey failed:', error.message);
      } finally {
        await browser.close();
      }
    }
    
    async function testPriorAuthJourney() {
      const browser = await puppeteer.launch({ headless: true });
      const page = await browser.newPage();
      await page.setViewport(testConfig.viewport);
      
      try {
        // Login first
        await page.goto(`${testConfig.baseUrl}/login`);
        await page.type('#email', testConfig.testUser.email);
        await page.type('#password', testConfig.testUser.password);
        await Promise.all([
          page.waitForNavigation(),
          page.click('#login-button')
        ]);
        
        // Navigate to new prior auth
        const startTime = Date.now();
        await page.goto(`${testConfig.baseUrl}/prior-auth/new`, { waitUntil: 'networkidle0' });
        
        const loadTime = (Date.now() - startTime) / 1000;
        pageLoadTime.labels('prior-auth-new', 'production').observe(loadTime);
        
        // Fill prior auth form
        await page.type('#patient-name', 'Test Patient');
        await page.type('#patient-dob', '1990-01-01');
        await page.type('#procedure-code', '99213');
        await page.select('#insurance-company', 'test-insurance');
        
        // Submit form
        await Promise.all([
          page.waitForNavigation(),
          page.click('#submit-button')
        ]);
        
        // Verify submission
        await page.waitForSelector('.success-message', { timeout: 10000 });
        
        userJourneySuccess.labels('prior-auth-creation', 'production').inc();
        console.log('Prior auth creation journey successful');
        
      } catch (error) {
        userJourneyFailure.labels('prior-auth-creation', 'production', error.name).inc();
        console.error('Prior auth creation journey failed:', error.message);
      } finally {
        await browser.close();
      }
    }
    
    async function testApiEndpoints() {
      const endpoints = [
        { path: '/api/health', method: 'GET' },
        { path: '/api/practices', method: 'GET' },
        { path: '/api/patients', method: 'GET' },
        { path: '/api/prior-authorizations', method: 'GET' }
      ];
      
      for (const endpoint of endpoints) {
        try {
          const startTime = Date.now();
          const response = await fetch(`${testConfig.baseUrl}${endpoint.path}`, {
            method: endpoint.method,
            headers: {
              'Authorization': `Bearer ${process.env.TEST_API_TOKEN}`,
              'Content-Type': 'application/json'
            }
          });
          
          const responseTime = (Date.now() - startTime) / 1000;
          apiResponseTime.labels(endpoint.path, endpoint.method, response.status).observe(responseTime);
          
          console.log(`API test ${endpoint.method} ${endpoint.path}: ${response.status} (${responseTime}s)`);
          
        } catch (error) {
          apiResponseTime.labels(endpoint.path, endpoint.method, 'error').observe(30);
          console.error(`API test failed ${endpoint.method} ${endpoint.path}:`, error.message);
        }
      }
    }
    
    // Main execution
    async function runTests() {
      console.log('Starting synthetic monitoring tests...');
      
      try {
        await testLoginJourney();
        await testPriorAuthJourney();
        await testApiEndpoints();
        
        console.log('All synthetic tests completed');
      } catch (error) {
        console.error('Synthetic monitoring error:', error);
      }
    }
    
    // Export metrics
    const register = prometheus.register;
    
    module.exports = {
      runTests,
      getMetrics: () => register.metrics()
    };
    
    // Run tests if called directly
    if (require.main === module) {
      runTests();
    }
  
  real-user-monitoring.js: |
    // Real User Monitoring (RUM) configuration for OCTAVE Healthcare
    
    // Core Web Vitals tracking
    function trackCoreWebVitals() {
      // Largest Contentful Paint (LCP)
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (entry.entryType === 'largest-contentful-paint') {
            sendMetric('core_web_vitals_lcp', entry.startTime, {
              page: window.location.pathname,
              user_agent: navigator.userAgent
            });
          }
        }
      }).observe({ entryTypes: ['largest-contentful-paint'] });
      
      // First Input Delay (FID)
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (entry.entryType === 'first-input') {
            sendMetric('core_web_vitals_fid', entry.processingStart - entry.startTime, {
              page: window.location.pathname,
              input_type: entry.name
            });
          }
        }
      }).observe({ entryTypes: ['first-input'] });
      
      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        }
        sendMetric('core_web_vitals_cls', clsValue, {
          page: window.location.pathname
        });
      }).observe({ entryTypes: ['layout-shift'] });
    }
    
    // User interaction tracking
    function trackUserInteractions() {
      // Click tracking
      document.addEventListener('click', (event) => {
        const element = event.target;
        const elementInfo = {
          tag: element.tagName,
          id: element.id,
          class: element.className,
          text: element.textContent?.substring(0, 50)
        };
        
        sendMetric('user_interaction_click', 1, {
          page: window.location.pathname,
          element: JSON.stringify(elementInfo)
        });
      });
      
      // Form submission tracking
      document.addEventListener('submit', (event) => {
        const form = event.target;
        sendMetric('user_interaction_form_submit', 1, {
          page: window.location.pathname,
          form_id: form.id,
          form_action: form.action
        });
      });
      
      // Error tracking
      window.addEventListener('error', (event) => {
        sendMetric('javascript_error', 1, {
          page: window.location.pathname,
          error_message: event.message,
          error_filename: event.filename,
          error_line: event.lineno
        });
      });
    }
    
    // Performance tracking
    function trackPerformance() {
      // Page load performance
      window.addEventListener('load', () => {
        setTimeout(() => {
          const perfData = performance.getEntriesByType('navigation')[0];
          
          sendMetric('page_load_time', perfData.loadEventEnd - perfData.fetchStart, {
            page: window.location.pathname
          });
          
          sendMetric('dom_content_loaded_time', perfData.domContentLoadedEventEnd - perfData.fetchStart, {
            page: window.location.pathname
          });
          
          sendMetric('first_byte_time', perfData.responseStart - perfData.fetchStart, {
            page: window.location.pathname
          });
        }, 0);
      });
      
      // Resource loading performance
      new PerformanceObserver((entryList) => {
        for (const entry of entryList.getEntries()) {
          if (entry.entryType === 'resource') {
            sendMetric('resource_load_time', entry.duration, {
              resource_type: entry.initiatorType,
              resource_name: entry.name.split('/').pop()
            });
          }
        }
      }).observe({ entryTypes: ['resource'] });
    }
    
    // Healthcare-specific tracking
    function trackHealthcareMetrics() {
      // Prior authorization form interactions
      const priorAuthForms = document.querySelectorAll('[data-form-type="prior-auth"]');
      priorAuthForms.forEach(form => {
        form.addEventListener('submit', (event) => {
          const startTime = Date.now();
          
          // Track form completion time
          const formStartTime = sessionStorage.getItem('prior_auth_form_start');
          if (formStartTime) {
            const completionTime = startTime - parseInt(formStartTime);
            sendMetric('prior_auth_form_completion_time', completionTime, {
              form_id: form.id
            });
          }
          
          // Track form field completion
          const fields = form.querySelectorAll('input, select, textarea');
          const completedFields = Array.from(fields).filter(field => field.value).length;
          sendMetric('prior_auth_form_completion_rate', completedFields / fields.length, {
            form_id: form.id
          });
        });
        
        // Track form start
        form.addEventListener('focus', () => {
          if (!sessionStorage.getItem('prior_auth_form_start')) {
            sessionStorage.setItem('prior_auth_form_start', Date.now().toString());
          }
        }, { once: true });
      });
      
      // Patient search interactions
      const patientSearchInputs = document.querySelectorAll('[data-search-type="patient"]');
      patientSearchInputs.forEach(input => {
        let searchStartTime;
        
        input.addEventListener('input', () => {
          searchStartTime = Date.now();
        });
        
        // Assuming search results are displayed in a specific container
        const resultsContainer = document.querySelector('[data-search-results="patient"]');
        if (resultsContainer) {
          new MutationObserver(() => {
            if (searchStartTime) {
              const searchTime = Date.now() - searchStartTime;
              sendMetric('patient_search_time', searchTime, {
                query_length: input.value.length
              });
              searchStartTime = null;
            }
          }).observe(resultsContainer, { childList: true });
        }
      });
    }
    
    // Metric sending function
    function sendMetric(name, value, labels = {}) {
      const metric = {
        name: `octave_rum_${name}`,
        value: value,
        timestamp: Date.now(),
        labels: {
          ...labels,
          user_id: getCurrentUserId(),
          session_id: getSessionId(),
          practice_id: getCurrentPracticeId()
        }
      };
      
      // Send to metrics endpoint
      fetch('/api/metrics/rum', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getAuthToken()}`
        },
        body: JSON.stringify(metric)
      }).catch(error => {
        console.warn('Failed to send RUM metric:', error);
      });
    }
    
    // Helper functions
    function getCurrentUserId() {
      return window.octaveUser?.id || 'anonymous';
    }
    
    function getSessionId() {
      let sessionId = sessionStorage.getItem('octave_session_id');
      if (!sessionId) {
        sessionId = 'session_' + Math.random().toString(36).substr(2, 9);
        sessionStorage.setItem('octave_session_id', sessionId);
      }
      return sessionId;
    }
    
    function getCurrentPracticeId() {
      return window.octaveUser?.practiceId || 'unknown';
    }
    
    function getAuthToken() {
      return localStorage.getItem('octave_auth_token') || '';
    }
    
    // Initialize RUM tracking
    function initializeRUM() {
      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          trackCoreWebVitals();
          trackUserInteractions();
          trackPerformance();
          trackHealthcareMetrics();
        });
      } else {
        trackCoreWebVitals();
        trackUserInteractions();
        trackPerformance();
        trackHealthcareMetrics();
      }
    }
    
    // Auto-initialize
    initializeRUM();
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: synthetic-monitoring
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: user-experience
spec:
  schedule: "*/5 * * * *"  # Every 5 minutes
  jobTemplate:
    spec:
      template:
        spec:
          serviceAccountName: octave-service-account
          containers:
          - name: synthetic-tests
            image: node:18-alpine
            command:
            - /bin/sh
            - -c
            - |
              # Install dependencies
              npm install puppeteer prom-client
              
              # Run synthetic monitoring tests
              node /scripts/synthetic-monitoring.js
              
              # Export metrics to Prometheus pushgateway
              curl -X POST http://prometheus-pushgateway:9091/metrics/job/synthetic-monitoring \
                --data-binary @/tmp/metrics.txt
            env:
            - name: OCTAVE_BASE_URL
              value: "https://octave-healthcare.com"
            - name: TEST_USER_EMAIL
              valueFrom:
                secretKeyRef:
                  name: monitoring-secrets
                  key: TEST_USER_EMAIL
            - name: TEST_USER_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: monitoring-secrets
                  key: TEST_USER_PASSWORD
            - name: TEST_API_TOKEN
              valueFrom:
                secretKeyRef:
                  name: monitoring-secrets
                  key: TEST_API_TOKEN
            volumeMounts:
            - name: scripts
              mountPath: /scripts
            resources:
              requests:
                memory: "512Mi"
                cpu: "250m"
              limits:
                memory: "1Gi"
                cpu: "500m"
          volumes:
          - name: scripts
            configMap:
              name: user-experience-config
          restartPolicy: OnFailure
