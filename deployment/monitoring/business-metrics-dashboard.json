{"dashboard": {"id": null, "title": "OCTAVE Healthcare Business Metrics Dashboard", "description": "Business intelligence and operational metrics for healthcare prior authorization system", "tags": ["octave", "healthcare", "business", "metrics", "kpi"], "timezone": "UTC", "editable": true, "graphTooltip": 1, "links": [], "panels": [{"id": 1, "title": "Business Overview", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}}, {"id": 2, "title": "Total Prior Authorizations Today", "type": "stat", "targets": [{"expr": "increase(octave_prior_auth_requests_total[1d])", "legendFormat": "Total Requests", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 100}, {"color": "red", "value": 500}]}}}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}}, {"id": 3, "title": "Approval Rate", "type": "stat", "targets": [{"expr": "increase(octave_prior_auth_approved_total[1d]) / increase(octave_prior_auth_requests_total[1d]) * 100", "legendFormat": "Approval Rate", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 85}]}}}, "gridPos": {"h": 8, "w": 6, "x": 6, "y": 1}}, {"id": 4, "title": "Average Processing Time", "type": "stat", "targets": [{"expr": "avg(octave_prior_auth_processing_duration_seconds)", "legendFormat": "Avg Processing Time", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 3600}, {"color": "red", "value": 86400}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 1}}, {"id": 5, "title": "Active Practices", "type": "stat", "targets": [{"expr": "count(count by (practice_id) (octave_user_sessions{practice_id!=\"\"}))", "legendFormat": "Active Practices", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 1}}, {"id": 6, "title": "Prior Authorization Trends", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}}, {"id": 7, "title": "Prior Auth Requests Over Time", "type": "timeseries", "targets": [{"expr": "rate(octave_prior_auth_requests_total[1h])", "legendFormat": "Requests/hour", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqph", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}}, {"id": 8, "title": "Approval vs Denial Rates", "type": "timeseries", "targets": [{"expr": "rate(octave_prior_auth_approved_total[1h])", "legendFormat": "Approved/hour", "refId": "A"}, {"expr": "rate(octave_prior_auth_denied_total[1h])", "legendFormat": "Denied/hour", "refId": "B"}, {"expr": "rate(octave_prior_auth_pending_total[1h])", "legendFormat": "Pending/hour", "refId": "C"}], "fieldConfig": {"defaults": {"unit": "reqph", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}}, {"id": 9, "title": "User Activity Metrics", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}}, {"id": 10, "title": "Daily Active Users", "type": "timeseries", "targets": [{"expr": "count(count by (user_id) (increase(octave_user_activity_total[1d])))", "legendFormat": "Daily Active Users", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 19}}, {"id": 11, "title": "User Sessions by Role", "type": "piechart", "targets": [{"expr": "sum by (role) (octave_user_sessions)", "legendFormat": "{{ role }}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 19}}, {"id": 12, "title": "Performance Metrics", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}}, {"id": 13, "title": "Processing Time by Priority", "type": "timeseries", "targets": [{"expr": "avg by (priority) (octave_prior_auth_processing_duration_seconds)", "legendFormat": "{{ priority }} Priority", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "s", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 28}}, {"id": 14, "title": "SLA Compliance", "type": "stat", "targets": [{"expr": "sum(octave_sla_met_total) / sum(octave_sla_total) * 100", "legendFormat": "SLA Compliance", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 95}, {"color": "green", "value": 99}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 28}}, {"id": 15, "title": "Revenue Impact", "type": "stat", "targets": [{"expr": "sum(octave_revenue_impact_dollars)", "legendFormat": "Revenue Impact", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "currencyUSD", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 28}}, {"id": 16, "title": "Insurance Company Metrics", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 36}}, {"id": 17, "title": "Top Insurance Companies by Volume", "type": "table", "targets": [{"expr": "topk(10, sum by (insurance_company) (increase(octave_prior_auth_requests_total[1d])))", "legendFormat": "{{ insurance_company }}", "refId": "A", "format": "table"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 37}}, {"id": 18, "title": "Approval Rates by Insurance Company", "type": "table", "targets": [{"expr": "sum by (insurance_company) (increase(octave_prior_auth_approved_total[1d])) / sum by (insurance_company) (increase(octave_prior_auth_requests_total[1d])) * 100", "legendFormat": "{{ insurance_company }}", "refId": "A", "format": "table"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 70}, {"color": "green", "value": 85}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 37}}, {"id": 19, "title": "Compliance and Security", "type": "row", "collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 45}}, {"id": 20, "title": "HIPAA Audit Events", "type": "timeseries", "targets": [{"expr": "rate(octave_hipaa_audit_events_total[1h])", "legendFormat": "{{ event_type }}", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "reqph", "color": {"mode": "palette-classic"}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 46}}, {"id": 21, "title": "Security Incidents", "type": "stat", "targets": [{"expr": "increase(octave_security_incidents_total[1d])", "legendFormat": "Security Incidents Today", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "short", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}}}, "gridPos": {"h": 8, "w": 6, "x": 12, "y": 46}}, {"id": 22, "title": "Data Quality Score", "type": "stat", "targets": [{"expr": "avg(octave_data_quality_score)", "legendFormat": "Data Quality", "refId": "A"}], "fieldConfig": {"defaults": {"unit": "percent", "min": 0, "max": 100, "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": null}, {"color": "yellow", "value": 80}, {"color": "green", "value": 95}]}}}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 46}}], "time": {"from": "now-24h", "to": "now"}, "refresh": "1m", "schemaVersion": 30, "version": 1, "templating": {"list": [{"name": "practice", "type": "query", "query": "label_values(octave_prior_auth_requests_total, practice_id)", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}, {"name": "insurance_company", "type": "query", "query": "label_values(octave_prior_auth_requests_total, insurance_company)", "current": {"text": "All", "value": "$__all"}, "multi": true, "includeAll": true}]}}}