apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'octave-healthcare-prod'
        environment: 'production'
    
    rule_files:
      - "/etc/prometheus/rules/*.yml"
    
    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093
    
    scrape_configs:
      # OCTAVE API metrics
      - job_name: 'octave-api'
        static_configs:
          - targets: ['octave-api:3000']
        metrics_path: '/metrics'
        scrape_interval: 10s
        scrape_timeout: 5s
        honor_labels: true
        params:
          format: ['prometheus']
      
      # PostgreSQL metrics
      - job_name: 'postgres'
        static_configs:
          - targets: ['postgres-exporter:9187']
        scrape_interval: 30s
      
      # Node metrics
      - job_name: 'node-exporter'
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - source_labels: [__address__]
            regex: '(.*):10250'
            target_label: __address__
            replacement: '${1}:9100'
      
      # Kubernetes API server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
          - role: endpoints
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
            action: keep
            regex: default;kubernetes;https
      
      # Kubernetes pods
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name
  
  alert_rules.yml: |
    groups:
      - name: octave-healthcare-alerts
        rules:
          # High error rate
          - alert: HighErrorRate
            expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.01
            for: 2m
            labels:
              severity: critical
              service: octave-api
            annotations:
              summary: "High error rate detected"
              description: "Error rate is {{ $value | humanizePercentage }} for {{ $labels.instance }}"
          
          # High response time
          - alert: HighResponseTime
            expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
            for: 5m
            labels:
              severity: warning
              service: octave-api
            annotations:
              summary: "High response time detected"
              description: "95th percentile response time is {{ $value }}s for {{ $labels.instance }}"
          
          # Database connection issues
          - alert: DatabaseConnectionHigh
            expr: pg_stat_activity_count > 80
            for: 2m
            labels:
              severity: warning
              service: postgres
            annotations:
              summary: "High database connections"
              description: "Database has {{ $value }} active connections"
          
          # Memory usage high
          - alert: HighMemoryUsage
            expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.85
            for: 5m
            labels:
              severity: warning
              service: system
            annotations:
              summary: "High memory usage"
              description: "Memory usage is {{ $value | humanizePercentage }} on {{ $labels.instance }}"
          
          # CPU usage high
          - alert: HighCPUUsage
            expr: 100 - (avg by(instance) (rate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
            for: 5m
            labels:
              severity: warning
              service: system
            annotations:
              summary: "High CPU usage"
              description: "CPU usage is {{ $value }}% on {{ $labels.instance }}"
          
          # Disk space low
          - alert: LowDiskSpace
            expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
            for: 2m
            labels:
              severity: critical
              service: system
            annotations:
              summary: "Low disk space"
              description: "Disk space is {{ $value | humanizePercentage }} available on {{ $labels.instance }}"
          
          # HIPAA compliance alerts
          - alert: PHIAccessWithoutAudit
            expr: increase(phi_access_total[5m]) > increase(audit_log_entries_total[5m])
            for: 1m
            labels:
              severity: critical
              service: compliance
            annotations:
              summary: "PHI access without proper audit logging"
              description: "Detected PHI access that may not be properly audited"
          
          # Semantic protection alerts
          - alert: SemanticThreatDetected
            expr: increase(semantic_threats_detected_total[1m]) > 0
            for: 0s
            labels:
              severity: critical
              service: security
            annotations:
              summary: "Semantic threat detected"
              description: "{{ $value }} semantic threats detected in the last minute"
          
          # Application down
          - alert: ApplicationDown
            expr: up{job="octave-api"} == 0
            for: 1m
            labels:
              severity: critical
              service: octave-api
            annotations:
              summary: "OCTAVE API is down"
              description: "OCTAVE API instance {{ $labels.instance }} is down"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: octave-healthcare
      component: prometheus
  template:
    metadata:
      labels:
        app: octave-healthcare
        component: prometheus
    spec:
      serviceAccountName: octave-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
      containers:
      - name: prometheus
        image: prom/prometheus:v2.45.0
        args:
          - '--config.file=/etc/prometheus/prometheus.yml'
          - '--storage.tsdb.path=/prometheus/'
          - '--web.console.libraries=/etc/prometheus/console_libraries'
          - '--web.console.templates=/etc/prometheus/consoles'
          - '--storage.tsdb.retention.time=30d'
          - '--web.enable-lifecycle'
          - '--web.enable-admin-api'
        ports:
        - containerPort: 9090
          name: prometheus
        volumeMounts:
        - name: prometheus-config
          mountPath: /etc/prometheus/
        - name: prometheus-storage
          mountPath: /prometheus/
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9090
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: prometheus-config
        configMap:
          name: prometheus-config
      - name: prometheus-storage
        persistentVolumeClaim:
          claimName: prometheus-storage-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: prometheus
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: monitoring
spec:
  type: ClusterIP
  ports:
  - port: 9090
    targetPort: 9090
    protocol: TCP
    name: prometheus
  selector:
    app: octave-healthcare
    component: prometheus
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-storage-pvc
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: monitoring
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
