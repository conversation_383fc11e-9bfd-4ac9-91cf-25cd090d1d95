apiVersion: v1
kind: ConfigMap
metadata:
  name: alertmanager-config
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: alerting
data:
  alertmanager.yml: |
    global:
      smtp_smarthost: 'smtp.octave-healthcare.com:587'
      smtp_from: '<EMAIL>'
      smtp_auth_username: '<EMAIL>'
      smtp_auth_password: 'SMTP_PASSWORD_FROM_SECRET'
      slack_api_url: 'SLACK_WEBHOOK_URL_FROM_SECRET'
      pagerduty_url: 'https://events.pagerduty.com/v2/enqueue'
    
    # Templates for notifications
    templates:
      - '/etc/alertmanager/templates/*.tmpl'
    
    # Route configuration
    route:
      group_by: ['alertname', 'cluster', 'service']
      group_wait: 10s
      group_interval: 10s
      repeat_interval: 1h
      receiver: 'default'
      routes:
        # Critical alerts - immediate notification
        - match:
            severity: critical
          receiver: 'critical-alerts'
          group_wait: 0s
          repeat_interval: 5m
          routes:
            # HIPAA compliance violations
            - match:
                alertname: 'HIPAAComplianceViolation'
              receiver: 'hipaa-compliance-team'
            # Security incidents
            - match:
                alertname: 'SecurityIncident'
              receiver: 'security-team'
            # System down
            - match:
                alertname: 'SystemDown'
              receiver: 'ops-team'
        
        # High priority alerts
        - match:
            severity: warning
          receiver: 'warning-alerts'
          group_wait: 30s
          repeat_interval: 30m
        
        # Business alerts
        - match:
            category: business
          receiver: 'business-team'
          group_wait: 1m
          repeat_interval: 2h
        
        # Healthcare specific alerts
        - match:
            category: healthcare
          receiver: 'healthcare-team'
          group_wait: 30s
          repeat_interval: 1h
    
    # Inhibition rules
    inhibit_rules:
      # Inhibit warning alerts if critical alert is firing
      - source_match:
          severity: 'critical'
        target_match:
          severity: 'warning'
        equal: ['alertname', 'instance']
      
      # Inhibit individual service alerts if entire system is down
      - source_match:
          alertname: 'SystemDown'
        target_match_re:
          alertname: '.*ServiceDown'
        equal: ['instance']
    
    # Receivers configuration
    receivers:
      - name: 'default'
        email_configs:
          - to: '<EMAIL>'
            subject: 'OCTAVE Alert: {{ .GroupLabels.alertname }}'
            body: |
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Severity: {{ .Labels.severity }}
              Instance: {{ .Labels.instance }}
              Time: {{ .StartsAt }}
              {{ end }}
      
      - name: 'critical-alerts'
        email_configs:
          - to: '<EMAIL>'
            subject: 'CRITICAL ALERT: {{ .GroupLabels.alertname }}'
            body: |
              🚨 CRITICAL ALERT 🚨
              
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Severity: {{ .Labels.severity }}
              Instance: {{ .Labels.instance }}
              Time: {{ .StartsAt }}
              
              Runbook: {{ .Annotations.runbook_url }}
              {{ end }}
        slack_configs:
          - api_url: 'SLACK_WEBHOOK_URL_FROM_SECRET'
            channel: '#critical-alerts'
            title: 'CRITICAL: {{ .GroupLabels.alertname }}'
            text: |
              {{ range .Alerts }}
              🚨 *{{ .Annotations.summary }}*
              
              *Description:* {{ .Annotations.description }}
              *Severity:* {{ .Labels.severity }}
              *Instance:* {{ .Labels.instance }}
              *Time:* {{ .StartsAt }}
              
              <{{ .Annotations.runbook_url }}|Runbook>
              {{ end }}
            send_resolved: true
        pagerduty_configs:
          - routing_key: 'PAGERDUTY_INTEGRATION_KEY_FROM_SECRET'
            description: '{{ .GroupLabels.alertname }}: {{ .Annotations.summary }}'
            severity: 'critical'
            details:
              summary: '{{ .Annotations.summary }}'
              description: '{{ .Annotations.description }}'
              instance: '{{ .Labels.instance }}'
              runbook: '{{ .Annotations.runbook_url }}'
      
      - name: 'warning-alerts'
        email_configs:
          - to: '<EMAIL>'
            subject: 'Warning: {{ .GroupLabels.alertname }}'
            body: |
              ⚠️ WARNING ALERT ⚠️
              
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Severity: {{ .Labels.severity }}
              Instance: {{ .Labels.instance }}
              Time: {{ .StartsAt }}
              {{ end }}
        slack_configs:
          - api_url: 'SLACK_WEBHOOK_URL_FROM_SECRET'
            channel: '#alerts'
            title: 'Warning: {{ .GroupLabels.alertname }}'
            text: |
              {{ range .Alerts }}
              ⚠️ *{{ .Annotations.summary }}*
              
              *Description:* {{ .Annotations.description }}
              *Instance:* {{ .Labels.instance }}
              {{ end }}
            send_resolved: true
      
      - name: 'hipaa-compliance-team'
        email_configs:
          - to: '<EMAIL>,<EMAIL>,<EMAIL>'
            subject: 'HIPAA COMPLIANCE ALERT: {{ .GroupLabels.alertname }}'
            body: |
              🏥 HIPAA COMPLIANCE VIOLATION DETECTED 🏥
              
              This alert indicates a potential HIPAA compliance violation that requires immediate attention.
              
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Severity: {{ .Labels.severity }}
              Instance: {{ .Labels.instance }}
              Time: {{ .StartsAt }}
              
              Compliance Impact: {{ .Annotations.compliance_impact }}
              Required Actions: {{ .Annotations.required_actions }}
              {{ end }}
              
              Please review immediately and take appropriate action according to HIPAA incident response procedures.
        slack_configs:
          - api_url: 'SLACK_WEBHOOK_URL_FROM_SECRET'
            channel: '#hipaa-compliance'
            title: '🏥 HIPAA COMPLIANCE ALERT'
            text: |
              {{ range .Alerts }}
              🚨 *HIPAA COMPLIANCE VIOLATION*
              
              *Alert:* {{ .Annotations.summary }}
              *Description:* {{ .Annotations.description }}
              *Instance:* {{ .Labels.instance }}
              *Time:* {{ .StartsAt }}
              
              *Compliance Impact:* {{ .Annotations.compliance_impact }}
              *Required Actions:* {{ .Annotations.required_actions }}
              {{ end }}
            send_resolved: true
      
      - name: 'security-team'
        email_configs:
          - to: '<EMAIL>,<EMAIL>'
            subject: 'SECURITY INCIDENT: {{ .GroupLabels.alertname }}'
            body: |
              🔒 SECURITY INCIDENT DETECTED 🔒
              
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Severity: {{ .Labels.severity }}
              Instance: {{ .Labels.instance }}
              Time: {{ .StartsAt }}
              
              Threat Level: {{ .Labels.threat_level }}
              Attack Vector: {{ .Annotations.attack_vector }}
              Affected Systems: {{ .Annotations.affected_systems }}
              {{ end }}
        slack_configs:
          - api_url: 'SLACK_WEBHOOK_URL_FROM_SECRET'
            channel: '#security-incidents'
            title: '🔒 SECURITY INCIDENT'
            text: |
              {{ range .Alerts }}
              🚨 *SECURITY INCIDENT DETECTED*
              
              *Alert:* {{ .Annotations.summary }}
              *Description:* {{ .Annotations.description }}
              *Threat Level:* {{ .Labels.threat_level }}
              *Instance:* {{ .Labels.instance }}
              *Time:* {{ .StartsAt }}
              {{ end }}
            send_resolved: true
        pagerduty_configs:
          - routing_key: 'PAGERDUTY_SECURITY_KEY_FROM_SECRET'
            description: 'Security Incident: {{ .Annotations.summary }}'
            severity: 'critical'
      
      - name: 'ops-team'
        email_configs:
          - to: '<EMAIL>,<EMAIL>'
            subject: 'Operations Alert: {{ .GroupLabels.alertname }}'
            body: |
              🔧 OPERATIONS ALERT 🔧
              
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Severity: {{ .Labels.severity }}
              Instance: {{ .Labels.instance }}
              Time: {{ .StartsAt }}
              
              Runbook: {{ .Annotations.runbook_url }}
              {{ end }}
        slack_configs:
          - api_url: 'SLACK_WEBHOOK_URL_FROM_SECRET'
            channel: '#ops-alerts'
            title: 'Ops Alert: {{ .GroupLabels.alertname }}'
            send_resolved: true
      
      - name: 'business-team'
        email_configs:
          - to: '<EMAIL>,<EMAIL>'
            subject: 'Business Metrics Alert: {{ .GroupLabels.alertname }}'
            body: |
              📊 BUSINESS METRICS ALERT 📊
              
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Business Impact: {{ .Annotations.business_impact }}
              Time: {{ .StartsAt }}
              {{ end }}
        slack_configs:
          - api_url: 'SLACK_WEBHOOK_URL_FROM_SECRET'
            channel: '#business-alerts'
            title: 'Business Alert: {{ .GroupLabels.alertname }}'
            send_resolved: true
      
      - name: 'healthcare-team'
        email_configs:
          - to: '<EMAIL>,<EMAIL>'
            subject: 'Healthcare System Alert: {{ .GroupLabels.alertname }}'
            body: |
              🏥 HEALTHCARE SYSTEM ALERT 🏥
              
              {{ range .Alerts }}
              Alert: {{ .Annotations.summary }}
              Description: {{ .Annotations.description }}
              Patient Impact: {{ .Annotations.patient_impact }}
              Clinical Impact: {{ .Annotations.clinical_impact }}
              Time: {{ .StartsAt }}
              {{ end }}
        slack_configs:
          - api_url: 'SLACK_WEBHOOK_URL_FROM_SECRET'
            channel: '#healthcare-alerts'
            title: 'Healthcare Alert: {{ .GroupLabels.alertname }}'
            send_resolved: true
  
  notification-templates.tmpl: |
    {{ define "slack.default.title" }}
    {{ if eq .Status "firing" }}🔥{{ else }}✅{{ end }} {{ .GroupLabels.alertname }}
    {{ end }}
    
    {{ define "slack.default.text" }}
    {{ range .Alerts }}
    {{ if eq .Status "firing" }}🔥{{ else }}✅{{ end }} *{{ .Annotations.summary }}*
    
    *Description:* {{ .Annotations.description }}
    *Severity:* {{ .Labels.severity }}
    *Instance:* {{ .Labels.instance }}
    {{ if .Annotations.runbook_url }}*Runbook:* <{{ .Annotations.runbook_url }}|View Runbook>{{ end }}
    {{ end }}
    {{ end }}
    
    {{ define "email.default.subject" }}
    [{{ .Status | toUpper }}] OCTAVE Healthcare Alert: {{ .GroupLabels.alertname }}
    {{ end }}
    
    {{ define "email.default.html" }}
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; }
            .alert { padding: 10px; margin: 10px 0; border-radius: 5px; }
            .critical { background-color: #ffebee; border-left: 5px solid #f44336; }
            .warning { background-color: #fff3e0; border-left: 5px solid #ff9800; }
            .resolved { background-color: #e8f5e8; border-left: 5px solid #4caf50; }
        </style>
    </head>
    <body>
        <h2>OCTAVE Healthcare Alert</h2>
        {{ range .Alerts }}
        <div class="alert {{ if eq .Status "firing" }}{{ .Labels.severity }}{{ else }}resolved{{ end }}">
            <h3>{{ .Annotations.summary }}</h3>
            <p><strong>Description:</strong> {{ .Annotations.description }}</p>
            <p><strong>Severity:</strong> {{ .Labels.severity }}</p>
            <p><strong>Instance:</strong> {{ .Labels.instance }}</p>
            <p><strong>Time:</strong> {{ .StartsAt }}</p>
            {{ if .Annotations.runbook_url }}
            <p><strong>Runbook:</strong> <a href="{{ .Annotations.runbook_url }}">View Runbook</a></p>
            {{ end }}
        </div>
        {{ end }}
    </body>
    </html>
    {{ end }}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: alertmanager
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: alerting
spec:
  replicas: 2
  selector:
    matchLabels:
      app: octave-healthcare
      component: alertmanager
  template:
    metadata:
      labels:
        app: octave-healthcare
        component: alertmanager
    spec:
      serviceAccountName: octave-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
      containers:
      - name: alertmanager
        image: prom/alertmanager:v0.25.0
        args:
          - '--config.file=/etc/alertmanager/alertmanager.yml'
          - '--storage.path=/alertmanager'
          - '--web.external-url=https://alertmanager.octave-healthcare.com'
          - '--cluster.listen-address=0.0.0.0:9094'
          - '--cluster.peer=alertmanager-0.alertmanager:9094'
          - '--cluster.peer=alertmanager-1.alertmanager:9094'
        ports:
        - containerPort: 9093
          name: web
        - containerPort: 9094
          name: cluster
        volumeMounts:
        - name: config
          mountPath: /etc/alertmanager
        - name: storage
          mountPath: /alertmanager
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9093
          initialDelaySeconds: 30
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9093
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: config
        configMap:
          name: alertmanager-config
      - name: storage
        persistentVolumeClaim:
          claimName: alertmanager-storage-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: alertmanager
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: alerting
spec:
  type: ClusterIP
  ports:
  - port: 9093
    targetPort: 9093
    protocol: TCP
    name: web
  - port: 9094
    targetPort: 9094
    protocol: TCP
    name: cluster
  selector:
    app: octave-healthcare
    component: alertmanager
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: alertmanager-storage-pvc
  namespace: octave-healthcare
  labels:
    app: octave-healthcare
    component: alerting
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
