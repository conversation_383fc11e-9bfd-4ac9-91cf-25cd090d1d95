{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 14830594861098857722, "profile": 14037508935055259071, "path": 11697586848262071942, "deps": [[6493259146304816786, "indexmap", false, 8675018843838240423], [8008191657135824715, "thiserror", false, 2277826180583068115], [8319709847752024821, "uuid", false, 14643814017276253492], [8569119365930580996, "serde_json", false, 12579596638386407579], [8606274917505247608, "tracing", false, 16836446493881906328], [9689903380558560274, "serde", false, 12653071505652221058], [9897246384292347999, "chrono", false, 1791920998076811337], [10632374999838431203, "sqlx", false, 17702492558861932113], [11946729385090170470, "async_trait", false, 171698127956791669], [12944427623413450645, "tokio", false, 241206205182144427], [13208667028893622512, "rand", false, 8106312600059402251], [14368513111871041592, "octave_core", false, 7694855249914396817], [18066890886671768183, "base64", false, 11938147575978701726]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/octave-database-a22d7c0a603095d4/dep-lib-octave_database", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}