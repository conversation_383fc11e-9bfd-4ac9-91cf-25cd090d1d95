{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 447770816760783640, "profile": 14037508935055259071, "path": 5016918586472941476, "deps": [[421255656520196549, "validator", false, 6412650893361535343], [3611029251930514425, "aes_gcm", false, 10682930149226304499], [5364813825765636762, "dashmap", false, 17619256701471794395], [5491919304041016563, "ring", false, 16321708667716325546], [7244058819997729774, "reqwest", false, 14079688469763944320], [8008191657135824715, "thiserror", false, 2277826180583068115], [8319709847752024821, "uuid", false, 14643814017276253492], [8569119365930580996, "serde_json", false, 12671385349869589111], [8606274917505247608, "tracing", false, 13928539558090020533], [9451456094439810778, "regex", false, 5883726391141801346], [9689903380558560274, "serde", false, 12653071505652221058], [9897246384292347999, "chrono", false, 11103001270638778896], [10697383615564341592, "rayon", false, 11459965096019564104], [11946729385090170470, "async_trait", false, 171698127956791669], [12944427623413450645, "tokio", false, 5020253171583244933], [13625485746686963219, "anyhow", false, 2101887136625042253], [14368513111871041592, "octave_core", false, 5986724580713165598], [17917672826516349275, "lazy_static", false, 9104387480568886006], [18066890886671768183, "base64", false, 11938147575978701726]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/octave-healthcare-95586ebbf0743730/dep-lib-octave_healthcare", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}