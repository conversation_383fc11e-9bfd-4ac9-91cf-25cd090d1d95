{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 14830594861098857722, "profile": 14037508935055259071, "path": 11697586848262071942, "deps": [[6493259146304816786, "indexmap", false, 8675018843838240423], [8008191657135824715, "thiserror", false, 2277826180583068115], [8319709847752024821, "uuid", false, 14643814017276253492], [8569119365930580996, "serde_json", false, 12671385349869589111], [8606274917505247608, "tracing", false, 13928539558090020533], [9689903380558560274, "serde", false, 12653071505652221058], [9857275760291862238, "sha2", false, 17508248844471908809], [9897246384292347999, "chrono", false, 11103001270638778896], [10632374999838431203, "sqlx", false, 15803981767658084411], [11946729385090170470, "async_trait", false, 171698127956791669], [12944427623413450645, "tokio", false, 5020253171583244933], [13208667028893622512, "rand", false, 8106312600059402251], [14368513111871041592, "octave_core", false, 5986724580713165598], [18066890886671768183, "base64", false, 11938147575978701726]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/octave-database-a70157f3a6b2b9a0/dep-lib-octave_database", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}