{"$message_type":"diagnostic","message":"unused imports: `AuthResponse` and `octave_error_to_response`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":160,"byte_end":172,"line_start":5,"line_end":5,"column_start":33,"column_end":45,"is_primary":true,"text":[{"text":"use crate::responses::{helpers, AuthResponse, UserResponse, octave_error_to_response};","highlight_start":33,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":188,"byte_end":212,"line_start":5,"line_end":5,"column_start":61,"column_end":85,"is_primary":true,"text":[{"text":"use crate::responses::{help<PERSON>, <PERSON>th<PERSON><PERSON>po<PERSON>, UserResponse, octave_error_to_response};","highlight_start":61,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":158,"byte_end":172,"line_start":5,"line_end":5,"column_start":31,"column_end":45,"is_primary":true,"text":[{"text":"use crate::responses::{helpers, AuthResponse, UserResponse, octave_error_to_response};","highlight_start":31,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":186,"byte_end":212,"line_start":5,"line_end":5,"column_start":59,"column_end":85,"is_primary":true,"text":[{"text":"use crate::responses::{helpers, AuthResponse, UserResponse, octave_error_to_response};","highlight_start":59,"highlight_end":85}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `AuthResponse` and `octave_error_to_response`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-api/src/handlers.rs:5:33\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m5\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse crate::responses::{helpers, AuthResponse, UserResponse, octave_error_to_response};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `CreateSessionRequest`, `UserInfo`, and `utils as jwt_utils`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":372,"byte_end":390,"line_start":13,"line_end":13,"column_start":33,"column_end":51,"is_primary":true,"text":[{"text":"    jwt::{JwtHandler, UserRole, utils as jwt_utils},","highlight_start":33,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":425,"byte_end":433,"line_start":14,"line_end":14,"column_start":33,"column_end":41,"is_primary":true,"text":[{"text":"    password::{PasswordManager, UserInfo},","highlight_start":33,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":474,"byte_end":494,"line_start":15,"line_end":15,"column_start":39,"column_end":59,"is_primary":true,"text":[{"text":"    session_manager::{SessionManager, CreateSessionRequest},","highlight_start":39,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":370,"byte_end":390,"line_start":13,"line_end":13,"column_start":31,"column_end":51,"is_primary":true,"text":[{"text":"    jwt::{JwtHandler, UserRole, utils as jwt_utils},","highlight_start":31,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":423,"byte_end":433,"line_start":14,"line_end":14,"column_start":31,"column_end":41,"is_primary":true,"text":[{"text":"    password::{PasswordManager, UserInfo},","highlight_start":31,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":407,"byte_end":408,"line_start":14,"line_end":14,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    password::{PasswordManager, UserInfo},","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":433,"byte_end":434,"line_start":14,"line_end":14,"column_start":41,"column_end":42,"is_primary":true,"text":[{"text":"    password::{PasswordManager, UserInfo},","highlight_start":41,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":472,"byte_end":494,"line_start":15,"line_end":15,"column_start":37,"column_end":59,"is_primary":true,"text":[{"text":"    session_manager::{SessionManager, CreateSessionRequest},","highlight_start":37,"highlight_end":59}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":457,"byte_end":458,"line_start":15,"line_end":15,"column_start":22,"column_end":23,"is_primary":true,"text":[{"text":"    session_manager::{SessionManager, CreateSessionRequest},","highlight_start":22,"highlight_end":23}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":494,"byte_end":495,"line_start":15,"line_end":15,"column_start":59,"column_end":60,"is_primary":true,"text":[{"text":"    session_manager::{SessionManager, CreateSessionRequest},","highlight_start":59,"highlight_end":60}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `CreateSessionRequest`, `UserInfo`, and `utils as jwt_utils`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-api/src/handlers.rs:13:33\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    jwt::{JwtHandler, UserRole, utils as jwt_utils},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    password::{PasswordManager, UserInfo},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    session_manager::{SessionManager, CreateSessionRequest},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `CreateUser`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":530,"byte_end":540,"line_start":18,"line_end":18,"column_start":14,"column_end":24,"is_primary":true,"text":[{"text":"    models::{CreateUser, User},","highlight_start":14,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":530,"byte_end":542,"line_start":18,"line_end":18,"column_start":14,"column_end":26,"is_primary":true,"text":[{"text":"    models::{CreateUser, User},","highlight_start":14,"highlight_end":26}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":529,"byte_end":530,"line_start":18,"line_end":18,"column_start":13,"column_end":14,"is_primary":true,"text":[{"text":"    models::{CreateUser, User},","highlight_start":13,"highlight_end":14}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":546,"byte_end":547,"line_start":18,"line_end":18,"column_start":30,"column_end":31,"is_primary":true,"text":[{"text":"    models::{CreateUser, User},","highlight_start":30,"highlight_end":31}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `CreateUser`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-api/src/handlers.rs:18:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m18\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    models::{CreateUser, User},\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `error`, `info`, and `warn`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":664,"byte_end":669,"line_start":23,"line_end":23,"column_start":22,"column_end":27,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":22,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":671,"byte_end":675,"line_start":23,"line_end":23,"column_start":29,"column_end":33,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":29,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":677,"byte_end":681,"line_start":23,"line_end":23,"column_start":35,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":35,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":662,"byte_end":681,"line_start":23,"line_end":23,"column_start":20,"column_end":39,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":20,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":656,"byte_end":657,"line_start":23,"line_end":23,"column_start":14,"column_end":15,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":14,"highlight_end":15}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/handlers.rs","byte_start":681,"byte_end":682,"line_start":23,"line_end":23,"column_start":39,"column_end":40,"is_primary":true,"text":[{"text":"use tracing::{debug, error, info, warn};","highlight_start":39,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `error`, `info`, and `warn`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-api/src/handlers.rs:23:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m23\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse tracing::{debug, error, info, warn};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `Response`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"octave-api/src/responses.rs","byte_start":244,"byte_end":252,"line_start":8,"line_end":8,"column_start":30,"column_end":38,"is_primary":true,"text":[{"text":"    response::{IntoResponse, Response},","highlight_start":30,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused import","code":null,"level":"help","spans":[{"file_name":"octave-api/src/responses.rs","byte_start":242,"byte_end":252,"line_start":8,"line_end":8,"column_start":28,"column_end":38,"is_primary":true,"text":[{"text":"    response::{IntoResponse, Response},","highlight_start":28,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/responses.rs","byte_start":229,"byte_end":230,"line_start":8,"line_end":8,"column_start":15,"column_end":16,"is_primary":true,"text":[{"text":"    response::{IntoResponse, Response},","highlight_start":15,"highlight_end":16}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-api/src/responses.rs","byte_start":252,"byte_end":253,"line_start":8,"line_end":8,"column_start":38,"column_end":39,"is_primary":true,"text":[{"text":"    response::{IntoResponse, Response},","highlight_start":38,"highlight_end":39}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `Response`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-api/src/responses.rs:8:30\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    response::{IntoResponse, Response},\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `extract_client_info` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":3340,"byte_end":3359,"line_start":116,"line_end":116,"column_start":4,"column_end":23,"is_primary":true,"text":[{"text":"fn extract_client_info(headers: &HeaderMap) -> (Option<String>, Option<String>) {","highlight_start":4,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `extract_client_info` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-api/src/handlers.rs:116:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m116\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn extract_client_info(headers: &HeaderMap) -> (Option<String>, Option<String>) {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `user_to_response` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":3838,"byte_end":3854,"line_start":132,"line_end":132,"column_start":4,"column_end":20,"is_primary":true,"text":[{"text":"fn user_to_response(user: &User) -> UserResponse {","highlight_start":4,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `user_to_response` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-api/src/handlers.rs:132:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m132\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn user_to_response(user: &User) -> UserResponse {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `session_to_response` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"octave-api/src/handlers.rs","byte_start":4310,"byte_end":4329,"line_start":147,"line_end":147,"column_start":4,"column_end":23,"is_primary":true,"text":[{"text":"fn session_to_response(session: &octave_auth::SessionInfo) -> crate::responses::SessionInfo {","highlight_start":4,"highlight_end":23}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: function `session_to_response` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-api/src/handlers.rs:147:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn session_to_response(session: &octave_auth::SessionInfo) -> crate::responses::SessionInfo {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"8 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 8 warnings emitted\u001b[0m\n\n"}
