{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 383880775870265714, "profile": 14037508935055259071, "path": 12806249359002099184, "deps": [[421255656520196549, "validator", false, 6412650893361535343], [1837536900705529536, "octave_semantic", false, 2596562726477275197], [2706460456408817945, "futures", false, 15511237988625537755], [2770272378949170607, "octave_healthcare", false, 8967106226988812636], [3405707034081185165, "dotenvy", false, 17028695507916154925], [3601586811267292532, "tower", false, 6473814060406845868], [4891297352905791595, "axum", false, 8151928920701268402], [7244058819997729774, "reqwest", false, 14079688469763944320], [8008191657135824715, "thiserror", false, 2277826180583068115], [8319709847752024821, "uuid", false, 14643814017276253492], [8349969753706539021, "octave_db", false, 5569010214436985760], [8569119365930580996, "serde_json", false, 12671385349869589111], [8606274917505247608, "tracing", false, 13928539558090020533], [9689903380558560274, "serde", false, 12653071505652221058], [9802318499724879310, "octave_compliance", false, 4010473283782904740], [9897246384292347999, "chrono", false, 11103001270638778896], [11594979262886006466, "tracing_appender", false, 11950575385778015903], [11957360342995674422, "hyper", false, 10714985658513807764], [12382237672615274180, "config", false, 16153830478445132003], [12944427623413450645, "tokio", false, 5020253171583244933], [13625485746686963219, "anyhow", false, 2101887136625042253], [14368513111871041592, "octave_core", false, 5986724580713165598], [14435908599267459652, "tower_http", false, 17811361450932532060], [15289459058370084791, "octave_auth", false, 14693714726860854531], [16230660778393187092, "tracing_subscriber", false, 13799057318935022312], [16661324460137237341, "color_eyre", false, 204691495091345834]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/octave-api-267cb9e09d8a97df/dep-lib-octave_api", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}