{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 12807555375832890853, "profile": 14037508935055259071, "path": 5832858206054351684, "deps": [[421255656520196549, "validator", false, 6412650893361535343], [1837536900705529536, "octave_semantic", false, 2596562726477275197], [1996688857878793156, "<PERSON><PERSON><PERSON><PERSON>", false, 5487028771177369146], [2737927996754702673, "jsonwebtoken", false, 3281196298580252259], [8008191657135824715, "thiserror", false, 2277826180583068115], [8319709847752024821, "uuid", false, 14643814017276253492], [8569119365930580996, "serde_json", false, 12671385349869589111], [8606274917505247608, "tracing", false, 13928539558090020533], [9689903380558560274, "serde", false, 12653071505652221058], [9897246384292347999, "chrono", false, 11103001270638778896], [11946729385090170470, "async_trait", false, 171698127956791669], [12944427623413450645, "tokio", false, 5020253171583244933], [13208667028893622512, "rand", false, 8106312600059402251], [13625485746686963219, "anyhow", false, 2101887136625042253], [14368513111871041592, "octave_core", false, 5986724580713165598], [18112009879309521262, "argon2", false, 17445152037679885641]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/octave-auth-4cc4d22046e6a2ee/dep-lib-octave_auth", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}