{"rustc": 15497389221046826682, "features": "[\"default\", \"std\"]", "declared_features": "[\"approx\", \"approx-0_5\", \"blas\", \"cblas-sys\", \"default\", \"docs\", \"libc\", \"matrixmultiply-threading\", \"rayon\", \"rayon_\", \"serde\", \"serde-1\", \"std\", \"test\"]", "target": 2233090415856294416, "profile": 10026849059110840599, "path": 11721092990821572398, "deps": [[5157631553186200874, "num_traits", false, 6322401797271343029], [12319020793864570031, "num_complex", false, 17604441086893960669], [15709748443193639506, "rawpointer", false, 11781222676046654327], [15826188163127377936, "matrixmultiply", false, 4199419509251330616], [16795989132585092538, "num_integer", false, 1608408015354497273]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ndarray-1073793c38e5e912/dep-lib-ndarray", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}