{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 5023181784348339394, "profile": 14037508935055259071, "path": 5917801919312541665, "deps": [[421255656520196549, "validator", false, 6412650893361535343], [530211389790465181, "hex", false, 9548336177561867720], [2706460456408817945, "futures", false, 15511237988625537755], [2770272378949170607, "octave_healthcare", false, 8967106226988812636], [3008854931152362171, "n<PERSON><PERSON>", false, 2219732500965756333], [4801984952432540513, "metrics", false, 14491445896176701259], [5364813825765636762, "dashmap", false, 17619256701471794395], [8008191657135824715, "thiserror", false, 2277826180583068115], [8319709847752024821, "uuid", false, 14643814017276253492], [8569119365930580996, "serde_json", false, 12671385349869589111], [8606274917505247608, "tracing", false, 13928539558090020533], [9241925498456048256, "blake3", false, 3971939177266416218], [9451456094439810778, "regex", false, 5883726391141801346], [9689903380558560274, "serde", false, 12653071505652221058], [9897246384292347999, "chrono", false, 11103001270638778896], [10697383615564341592, "rayon", false, 11459965096019564104], [11946729385090170470, "async_trait", false, 171698127956791669], [12944427623413450645, "tokio", false, 5020253171583244933], [13208667028893622512, "rand", false, 8106312600059402251], [13625485746686963219, "anyhow", false, 2101887136625042253], [14368513111871041592, "octave_core", false, 5986724580713165598]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/octave-semantic-2aa6930c894f5af0/dep-lib-octave_semantic", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}