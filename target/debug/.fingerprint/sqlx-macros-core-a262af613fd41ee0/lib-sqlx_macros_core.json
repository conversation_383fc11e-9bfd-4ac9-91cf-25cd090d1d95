{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"postgres\", \"sqlite\", \"sqlx-postgres\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 3033921117576893, "path": 12556255233731516675, "deps": [[530211389790465181, "hex", false, 5054411887327788366], [996810380461694889, "sqlx_core", false, 8056234364025612879], [1441306149310335789, "tempfile", false, 5727964765223210821], [2713742371683562785, "syn", false, 8444015993336014493], [3060637413840920116, "proc_macro2", false, 9926504638400894714], [3150220818285335163, "url", false, 3990778450837123107], [3405707034081185165, "dotenvy", false, 3440354807194926672], [3722963349756955755, "once_cell", false, 5712462848953866967], [8045585743974080694, "heck", false, 8715977571542734295], [8569119365930580996, "serde_json", false, 1801090118967120299], [9689903380558560274, "serde", false, 18102743518911559075], [9857275760291862238, "sha2", false, 8932467216796164334], [11838249260056359578, "sqlx_sqlite", false, 13880302615950857594], [12170264697963848012, "either", false, 7038415179184165291], [12944427623413450645, "tokio", false, 15413336003428659345], [15634168271133386882, "sqlx_postgres", false, 3907383272576161901], [17990358020177143287, "quote", false, 4307053779554051676]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-core-a262af613fd41ee0/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}