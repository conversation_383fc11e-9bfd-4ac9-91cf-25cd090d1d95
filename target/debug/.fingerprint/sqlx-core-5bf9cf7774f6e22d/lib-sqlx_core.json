{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"any\", \"chrono\", \"crc\", \"default\", \"json\", \"migrate\", \"offline\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha2\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-none\", \"_tls-rustls\", \"any\", \"async-io\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"bstr\", \"chrono\", \"crc\", \"default\", \"digest\", \"encoding_rs\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"native-tls\", \"num-bigint\", \"offline\", \"regex\", \"rust_decimal\", \"rustls\", \"rustls-pemfile\", \"serde\", \"serde_json\", \"sha1\", \"sha2\", \"time\", \"tokio\", \"tokio-stream\", \"uuid\", \"webpki-roots\"]", "target": 2042750936636613814, "profile": 10026849059110840599, "path": 17041482572124269129, "deps": [[5103565458935487, "futures_io", false, 4982948420848256852], [40386456601120721, "percent_encoding", false, 2073616645132551519], [530211389790465181, "hex", false, 9548336177561867720], [788558663644978524, "crossbeam_queue", false, 11634511427950304801], [966925859616469517, "ahash", false, 16069066661948049005], [1162433738665300155, "crc", false, 9641325451069349110], [1464803193346256239, "event_listener", false, 8076387858375057815], [1811549171721445101, "futures_channel", false, 7557334594128369032], [3150220818285335163, "url", false, 14079305954469411054], [3405817021026194662, "hashlink", false, 8132770702401516430], [3646857438214563691, "futures_intrusive", false, 10063424361595350279], [3666196340704888985, "smallvec", false, 2222458209388727332], [3712811570531045576, "byteorder", false, 12088055468461873290], [3722963349756955755, "once_cell", false, 7052867773189121993], [5986029879202738730, "log", false, 6428386159164090333], [6493259146304816786, "indexmap", false, 8675018843838240423], [7620660491849607393, "futures_core", false, 11688346835187461130], [8008191657135824715, "thiserror", false, 2277826180583068115], [8319709847752024821, "uuid", false, 14643814017276253492], [8569119365930580996, "serde_json", false, 12671385349869589111], [8606274917505247608, "tracing", false, 13928539558090020533], [9689903380558560274, "serde", false, 12653071505652221058], [9857275760291862238, "sha2", false, 17508248844471908809], [9897246384292347999, "chrono", false, 11103001270638778896], [10629569228670356391, "futures_util", false, 783775132576063577], [10862088793507253106, "sqlformat", false, 14258301572425461616], [11295624341523567602, "rustls", false, 4826365681778025433], [12170264697963848012, "either", false, 431840101674482809], [12944427623413450645, "tokio", false, 5020253171583244933], [15932120279885307830, "memchr", false, 17986694318735245992], [16066129441945555748, "bytes", false, 12520424362261250584], [16311359161338405624, "rustls_pemfile", false, 16065575178176447600], [16973251432615581304, "tokio_stream", false, 2782899373603604736], [17106256174509013259, "atoi", false, 3724805989772546779], [17605717126308396068, "paste", false, 10476848073614706140], [17652733826348741533, "webpki_roots", false, 937087320041686781]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-core-5bf9cf7774f6e22d/dep-lib-sqlx_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}