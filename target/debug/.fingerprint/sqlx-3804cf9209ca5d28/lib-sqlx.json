{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"any\", \"chrono\", \"default\", \"json\", \"macros\", \"migrate\", \"postgres\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlite\", \"sqlx-macros\", \"sqlx-postgres\", \"sqlx-sqlite\", \"tls-rustls\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 10026849059110840599, "path": 11330134083678863168, "deps": [[228475551920078470, "sqlx_macros", false, 8467528945784714646], [996810380461694889, "sqlx_core", false, 9839848766475732654], [11838249260056359578, "sqlx_sqlite", false, 13449191241071311972], [15634168271133386882, "sqlx_postgres", false, 14047651492487179307]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-3804cf9209ca5d28/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}