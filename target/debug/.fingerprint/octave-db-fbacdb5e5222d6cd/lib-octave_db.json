{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[]", "target": 11727109872663735752, "profile": 14037508935055259071, "path": 6727736368443162469, "deps": [[421255656520196549, "validator", false, 6412650893361535343], [1837536900705529536, "octave_semantic", false, 2596562726477275197], [8008191657135824715, "thiserror", false, 2277826180583068115], [8319709847752024821, "uuid", false, 14643814017276253492], [8569119365930580996, "serde_json", false, 12671385349869589111], [8606274917505247608, "tracing", false, 13928539558090020533], [9689903380558560274, "serde", false, 12653071505652221058], [9897246384292347999, "chrono", false, 11103001270638778896], [10632374999838431203, "sqlx", false, 15803981767658084411], [11946729385090170470, "async_trait", false, 171698127956791669], [12944427623413450645, "tokio", false, 5020253171583244933], [13625485746686963219, "anyhow", false, 2101887136625042253], [14368513111871041592, "octave_core", false, 5986724580713165598], [15289459058370084791, "octave_auth", false, 14693714726860854531]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/octave-db-fbacdb5e5222d6cd/dep-lib-octave_db", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}