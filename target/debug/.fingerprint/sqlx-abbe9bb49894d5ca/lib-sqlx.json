{"rustc": 15497389221046826682, "features": "[\"_rt-tokio\", \"chrono\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sqlite\", \"sqlx-sqlite\", \"tls-rustls\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_unstable-all-types\", \"all-databases\", \"any\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"macros\", \"migrate\", \"mysql\", \"postgres\", \"regexp\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sqlite\", \"sqlx-macros\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tls-native-tls\", \"tls-none\", \"tls-rustls\", \"uuid\"]", "target": 3003836824758849296, "profile": 10026849059110840599, "path": 11330134083678863168, "deps": [[996810380461694889, "sqlx_core", false, 17363101229029423145], [11838249260056359578, "sqlx_sqlite", false, 14092784513544236394]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-abbe9bb49894d5ca/dep-lib-sqlx", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}