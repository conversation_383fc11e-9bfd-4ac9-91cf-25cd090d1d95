{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"password-hash\", \"rand\"]", "declared_features": "[\"alloc\", \"default\", \"password-hash\", \"rand\", \"simple\", \"std\", \"zeroize\"]", "target": 5931530492013982456, "profile": 10026849059110840599, "path": 15936990262107675990, "deps": [[6742268975477224606, "password_hash", false, 9689085104763769046], [8700459469608572718, "blake2", false, 3151106665391010100], [13036989088902834928, "base64ct", false, 15552132547307582120]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/argon2-fa7ccc28f3fa1776/dep-lib-argon2", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}