{"$message_type":"diagnostic","message":"unused imports: `ActionStatus`, `ActionType`, `CommunicationDirection`, `ReportFormat`, and `UsageContext`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/repositories.rs","byte_start":468,"byte_end":490,"line_start":8,"line_end":8,"column_start":39,"column_end":61,"is_primary":true,"text":[{"text":"    Communication, CommunicationType, CommunicationDirection, CommunicationOutcome,","highlight_start":39,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/repositories.rs","byte_start":919,"byte_end":929,"line_start":13,"line_end":13,"column_start":20,"column_end":30,"is_primary":true,"text":[{"text":"    PendingAction, ActionType, ActionStatus, Template, TemplateType, TemplateSummary, ApprovalStatus,","highlight_start":20,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/repositories.rs","byte_start":931,"byte_end":943,"line_start":13,"line_end":13,"column_start":32,"column_end":44,"is_primary":true,"text":[{"text":"    PendingAction, ActionType, ActionStatus, Template, TemplateType, TemplateSummary, ApprovalStatus,","highlight_start":32,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/repositories.rs","byte_start":1038,"byte_end":1050,"line_start":14,"line_end":14,"column_start":37,"column_end":49,"is_primary":true,"text":[{"text":"    TemplateVersion, TemplateUsage, UsageContext, ContextType, AnalyticsMetric, MetricType,","highlight_start":37,"highlight_end":49}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/repositories.rs","byte_start":1161,"byte_end":1173,"line_start":15,"line_end":15,"column_start":68,"column_end":80,"is_primary":true,"text":[{"text":"    AggregationPeriod, AnalyticsMetricSummary, Report, ReportType, ReportFormat, ReportStatus,","highlight_start":68,"highlight_end":80}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"octave-database/src/repositories.rs","byte_start":466,"byte_end":490,"line_start":8,"line_end":8,"column_start":37,"column_end":61,"is_primary":true,"text":[{"text":"    Communication, CommunicationType, CommunicationDirection, CommunicationOutcome,","highlight_start":37,"highlight_end":61}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-database/src/repositories.rs","byte_start":917,"byte_end":943,"line_start":13,"line_end":13,"column_start":18,"column_end":44,"is_primary":true,"text":[{"text":"    PendingAction, ActionType, ActionStatus, Template, TemplateType, TemplateSummary, ApprovalStatus,","highlight_start":18,"highlight_end":44}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-database/src/repositories.rs","byte_start":1036,"byte_end":1050,"line_start":14,"line_end":14,"column_start":35,"column_end":49,"is_primary":true,"text":[{"text":"    TemplateVersion, TemplateUsage, UsageContext, ContextType, AnalyticsMetric, MetricType,","highlight_start":35,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-database/src/repositories.rs","byte_start":1159,"byte_end":1173,"line_start":15,"line_end":15,"column_start":66,"column_end":80,"is_primary":true,"text":[{"text":"    AggregationPeriod, AnalyticsMetricSummary, Report, ReportType, ReportFormat, ReportStatus,","highlight_start":66,"highlight_end":80}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ActionStatus`, `ActionType`, `CommunicationDirection`, `ReportFormat`, and `UsageContext`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/repositories.rs:8:39\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m8\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Communication, CommunicationType, CommunicationDirection, CommunicationOutcome,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    PendingAction, ActionType, ActionStatus, Template, TemplateType, TemplateSummary, ApprovalStatus,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    TemplateVersion, TemplateUsage, UsageContext, ContextType, AnalyticsMetric, MetricType,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    AggregationPeriod, AnalyticsMetricSummary, Report, ReportType, ReportFormat, ReportStatus,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ComplianceSeverity`, `ComplianceStatus`, `DashboardSummary`, `Dashboard`, and `WorkflowDefinition`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/services.rs","byte_start":850,"byte_end":868,"line_start":11,"line_end":11,"column_start":20,"column_end":38,"is_primary":true,"text":[{"text":"    WorkflowState, WorkflowDefinition, StateTransition, PendingAction, ActionType, ActionStatus,","highlight_start":20,"highlight_end":38}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/services.rs","byte_start":1285,"byte_end":1303,"line_start":15,"line_end":15,"column_start":66,"column_end":84,"is_primary":true,"text":[{"text":"    ReportSummary, ComplianceViolation, ComplianceViolationType, ComplianceSeverity, ComplianceStatus,","highlight_start":66,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/services.rs","byte_start":1305,"byte_end":1321,"line_start":15,"line_end":15,"column_start":86,"column_end":102,"is_primary":true,"text":[{"text":"    ReportSummary, ComplianceViolation, ComplianceViolationType, ComplianceSeverity, ComplianceStatus,","highlight_start":86,"highlight_end":102}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/services.rs","byte_start":1427,"byte_end":1436,"line_start":17,"line_end":17,"column_start":5,"column_end":14,"is_primary":true,"text":[{"text":"    Dashboard, DashboardSummary};","highlight_start":5,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/services.rs","byte_start":1438,"byte_end":1454,"line_start":17,"line_end":17,"column_start":16,"column_end":32,"is_primary":true,"text":[{"text":"    Dashboard, DashboardSummary};","highlight_start":16,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"octave-database/src/services.rs","byte_start":848,"byte_end":868,"line_start":11,"line_end":11,"column_start":18,"column_end":38,"is_primary":true,"text":[{"text":"    WorkflowState, WorkflowDefinition, StateTransition, PendingAction, ActionType, ActionStatus,","highlight_start":18,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-database/src/services.rs","byte_start":1283,"byte_end":1321,"line_start":15,"line_end":15,"column_start":64,"column_end":102,"is_primary":true,"text":[{"text":"    ReportSummary, ComplianceViolation, ComplianceViolationType, ComplianceSeverity, ComplianceStatus,","highlight_start":64,"highlight_end":102}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"octave-database/src/services.rs","byte_start":1421,"byte_end":1454,"line_start":16,"line_end":17,"column_start":99,"column_end":32,"is_primary":true,"text":[{"text":"    ComplianceViolationSummary, PerformanceMetric, PerformanceMetricType, PerformanceMetricSummary,","highlight_start":99,"highlight_end":100},{"text":"    Dashboard, DashboardSummary};","highlight_start":1,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `ComplianceSeverity`, `ComplianceStatus`, `DashboardSummary`, `Dashboard`, and `WorkflowDefinition`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/services.rs:11:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    WorkflowState, WorkflowDefinition, StateTransition, PendingAction, ActionType, ActionStatus,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ReportSummary, ComplianceViolation, ComplianceViolationType, ComplianceSeverity, ComplianceStatus,\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ComplianceViolationSummary, PerformanceMetric, PerformanceMetricType, PerformanceMetricSummary,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m17\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Dashboard, DashboardSummary};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `practice_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/services.rs","byte_start":57893,"byte_end":57904,"line_start":1651,"line_end":1651,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        practice_id: Uuid,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/services.rs","byte_start":57893,"byte_end":57904,"line_start":1651,"line_end":1651,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        practice_id: Uuid,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":"_practice_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `practice_id`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/services.rs:1651:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1651\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        practice_id: Uuid,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_practice_id`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `partial_query`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/services.rs","byte_start":57920,"byte_end":57933,"line_start":1652,"line_end":1652,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"        partial_query: &str,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/services.rs","byte_start":57920,"byte_end":57933,"line_start":1652,"line_end":1652,"column_start":9,"column_end":22,"is_primary":true,"text":[{"text":"        partial_query: &str,","highlight_start":9,"highlight_end":22}],"label":null,"suggested_replacement":"_partial_query","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `partial_query`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/services.rs:1652:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1652\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        partial_query: &str,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_partial_query`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `max_suggestions`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/services.rs","byte_start":57949,"byte_end":57964,"line_start":1653,"line_end":1653,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        max_suggestions: usize,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/services.rs","byte_start":57949,"byte_end":57964,"line_start":1653,"line_end":1653,"column_start":9,"column_end":24,"is_primary":true,"text":[{"text":"        max_suggestions: usize,","highlight_start":9,"highlight_end":24}],"label":null,"suggested_replacement":"_max_suggestions","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `max_suggestions`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/services.rs:1653:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m1653\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        max_suggestions: usize,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_max_suggestions`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `document`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/services.rs","byte_start":99138,"byte_end":99146,"line_start":2855,"line_end":2855,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn archive_document(&self, document: &Document) -> OctaveResult<()> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/services.rs","byte_start":99138,"byte_end":99146,"line_start":2855,"line_end":2855,"column_start":38,"column_end":46,"is_primary":true,"text":[{"text":"    async fn archive_document(&self, document: &Document) -> OctaveResult<()> {","highlight_start":38,"highlight_end":46}],"label":null,"suggested_replacement":"_document","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `document`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/services.rs:2855:38\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2855\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn archive_document(&self, document: &Document) -> OctaveResult<()> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_document`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `entity_type`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/services.rs","byte_start":147819,"byte_end":147830,"line_start":4260,"line_end":4260,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        entity_type: Option<EntityType>,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/services.rs","byte_start":147819,"byte_end":147830,"line_start":4260,"line_end":4260,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        entity_type: Option<EntityType>,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":"_entity_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `entity_type`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/services.rs:4260:9\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4260\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        entity_type: Option<EntityType>,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_entity_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `DashboardRepository`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/services.rs","byte_start":1921,"byte_end":1940,"line_start":22,"line_end":22,"column_start":68,"column_end":87,"is_primary":true,"text":[{"text":"    ReportRepository, ComplianceRepository, PerformanceRepository, DashboardRepository};","highlight_start":68,"highlight_end":87}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `DashboardRepository`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/services.rs:22:68\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m22\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    ReportRepository, ComplianceRepository, PerformanceRepository, DashboardRepository};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variable does not need to be mutable","code":{"code":"unused_mut","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/services.rs","byte_start":90190,"byte_end":90202,"line_start":2587,"line_end":2587,"column_start":13,"column_end":25,"is_primary":true,"text":[{"text":"        let mut warnings = Vec::new();","highlight_start":13,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_mut)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove this `mut`","code":null,"level":"help","spans":[{"file_name":"octave-database/src/services.rs","byte_start":90190,"byte_end":90194,"line_start":2587,"line_end":2587,"column_start":13,"column_end":17,"is_primary":true,"text":[{"text":"        let mut warnings = Vec::new();","highlight_start":13,"highlight_end":17}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variable does not need to be mutable\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/services.rs:2587:13\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2587\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        let mut warnings = Vec::new();\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----\u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mhelp: remove this `mut`\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_mut)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `file_type`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/services.rs","byte_start":92019,"byte_end":92028,"line_start":2639,"line_end":2639,"column_start":54,"column_end":63,"is_primary":true,"text":[{"text":"    fn generate_storage_path(&self, file_name: &str, file_type: &str) -> String {","highlight_start":54,"highlight_end":63}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/services.rs","byte_start":92019,"byte_end":92028,"line_start":2639,"line_end":2639,"column_start":54,"column_end":63,"is_primary":true,"text":[{"text":"    fn generate_storage_path(&self, file_name: &str, file_type: &str) -> String {","highlight_start":54,"highlight_end":63}],"label":null,"suggested_replacement":"_file_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `file_type`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/services.rs:2639:54\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2639\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    fn generate_storage_path(&self, file_name: &str, file_type: &str) -> String {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_file_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":5735,"byte_end":5737,"line_start":185,"line_end":185,"column_start":31,"column_end":33,"is_primary":true,"text":[{"text":"    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredThreatPattern>> {","highlight_start":31,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":5735,"byte_end":5737,"line_start":185,"line_end":185,"column_start":31,"column_end":33,"is_primary":true,"text":[{"text":"    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredThreatPattern>> {","highlight_start":31,"highlight_end":33}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:185:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m185\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredThreatPattern>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":5916,"byte_end":5920,"line_start":190,"line_end":190,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"    async fn get_by_name(&self, name: &str) -> OctaveResult<Option<StoredThreatPattern>> {","highlight_start":33,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":5916,"byte_end":5920,"line_start":190,"line_end":190,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"    async fn get_by_name(&self, name: &str) -> OctaveResult<Option<StoredThreatPattern>> {","highlight_start":33,"highlight_end":37}],"label":null,"suggested_replacement":"_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `name`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:190:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m190\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_by_name(&self, name: &str) -> OctaveResult<Option<StoredThreatPattern>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6149,"byte_end":6151,"line_start":198,"line_end":198,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"    async fn delete(&self, id: &str) -> OctaveResult<bool> {","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6149,"byte_end":6151,"line_start":198,"line_end":198,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"    async fn delete(&self, id: &str) -> OctaveResult<bool> {","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:198:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m198\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn delete(&self, id: &str) -> OctaveResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `filter`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6233,"byte_end":6239,"line_start":202,"line_end":202,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredThreatPattern>> {","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6233,"byte_end":6239,"line_start":202,"line_end":202,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredThreatPattern>> {","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"_filter","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `filter`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:202:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m202\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredThreatPattern>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_filter`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `threat_type`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6367,"byte_end":6378,"line_start":206,"line_end":206,"column_start":40,"column_end":51,"is_primary":true,"text":[{"text":"    async fn get_by_threat_type(&self, threat_type: &str) -> OctaveResult<Vec<StoredThreatPattern>> {","highlight_start":40,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6367,"byte_end":6378,"line_start":206,"line_end":206,"column_start":40,"column_end":51,"is_primary":true,"text":[{"text":"    async fn get_by_threat_type(&self, threat_type: &str) -> OctaveResult<Vec<StoredThreatPattern>> {","highlight_start":40,"highlight_end":51}],"label":null,"suggested_replacement":"_threat_type","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `threat_type`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:206:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m206\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_by_threat_type(&self, threat_type: &str) -> OctaveResult<Vec<StoredThreatPattern>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_threat_type`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6607,"byte_end":6609,"line_start":214,"line_end":214,"column_start":34,"column_end":36,"is_primary":true,"text":[{"text":"    async fn update_stats(&self, id: &str, was_false_positive: bool) -> OctaveResult<()> {","highlight_start":34,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6607,"byte_end":6609,"line_start":214,"line_end":214,"column_start":34,"column_end":36,"is_primary":true,"text":[{"text":"    async fn update_stats(&self, id: &str, was_false_positive: bool) -> OctaveResult<()> {","highlight_start":34,"highlight_end":36}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:214:34\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn update_stats(&self, id: &str, was_false_positive: bool) -> OctaveResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `was_false_positive`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6617,"byte_end":6635,"line_start":214,"line_end":214,"column_start":44,"column_end":62,"is_primary":true,"text":[{"text":"    async fn update_stats(&self, id: &str, was_false_positive: bool) -> OctaveResult<()> {","highlight_start":44,"highlight_end":62}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6617,"byte_end":6635,"line_start":214,"line_end":214,"column_start":44,"column_end":62,"is_primary":true,"text":[{"text":"    async fn update_stats(&self, id: &str, was_false_positive: bool) -> OctaveResult<()> {","highlight_start":44,"highlight_end":62}],"label":null,"suggested_replacement":"_was_false_positive","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `was_false_positive`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:214:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m214\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn update_stats(&self, id: &str, was_false_positive: bool) -> OctaveResult<()> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_was_false_positive`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `threshold`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6738,"byte_end":6747,"line_start":218,"line_end":218,"column_start":52,"column_end":61,"is_primary":true,"text":[{"text":"    async fn get_low_effectiveness_patterns(&self, threshold: f64) -> OctaveResult<Vec<StoredThreatPattern>> {","highlight_start":52,"highlight_end":61}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":6738,"byte_end":6747,"line_start":218,"line_end":218,"column_start":52,"column_end":61,"is_primary":true,"text":[{"text":"    async fn get_low_effectiveness_patterns(&self, threshold: f64) -> OctaveResult<Vec<StoredThreatPattern>> {","highlight_start":52,"highlight_end":61}],"label":null,"suggested_replacement":"_threshold","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `threshold`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:218:52\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m218\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_low_effectiveness_patterns(&self, threshold: f64) -> OctaveResult<Vec<StoredThreatPattern>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_threshold`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":7605,"byte_end":7607,"line_start":251,"line_end":251,"column_start":31,"column_end":33,"is_primary":true,"text":[{"text":"    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredLearningEvent>> {","highlight_start":31,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":7605,"byte_end":7607,"line_start":251,"line_end":251,"column_start":31,"column_end":33,"is_primary":true,"text":[{"text":"    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredLearningEvent>> {","highlight_start":31,"highlight_end":33}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:251:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m251\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredLearningEvent>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":7832,"byte_end":7834,"line_start":259,"line_end":259,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"    async fn delete(&self, id: &str) -> OctaveResult<bool> {","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":7832,"byte_end":7834,"line_start":259,"line_end":259,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"    async fn delete(&self, id: &str) -> OctaveResult<bool> {","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:259:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m259\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn delete(&self, id: &str) -> OctaveResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `filter`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":7916,"byte_end":7922,"line_start":263,"line_end":263,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredLearningEvent>> {","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":7916,"byte_end":7922,"line_start":263,"line_end":263,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredLearningEvent>> {","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"_filter","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `filter`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:263:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m263\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredLearningEvent>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_filter`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `fingerprint`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":8046,"byte_end":8057,"line_start":267,"line_end":267,"column_start":36,"column_end":47,"is_primary":true,"text":[{"text":"    async fn get_by_context(&self, fingerprint: &str) -> OctaveResult<Vec<StoredLearningEvent>> {","highlight_start":36,"highlight_end":47}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":8046,"byte_end":8057,"line_start":267,"line_end":267,"column_start":36,"column_end":47,"is_primary":true,"text":[{"text":"    async fn get_by_context(&self, fingerprint: &str) -> OctaveResult<Vec<StoredLearningEvent>> {","highlight_start":36,"highlight_end":47}],"label":null,"suggested_replacement":"_fingerprint","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `fingerprint`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:267:36\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m267\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_by_context(&self, fingerprint: &str) -> OctaveResult<Vec<StoredLearningEvent>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_fingerprint`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":8171,"byte_end":8178,"line_start":271,"line_end":271,"column_start":33,"column_end":40,"is_primary":true,"text":[{"text":"    async fn get_by_user(&self, user_id: i32) -> OctaveResult<Vec<StoredLearningEvent>> {","highlight_start":33,"highlight_end":40}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":8171,"byte_end":8178,"line_start":271,"line_end":271,"column_start":33,"column_end":40,"is_primary":true,"text":[{"text":"    async fn get_by_user(&self, user_id: i32) -> OctaveResult<Vec<StoredLearningEvent>> {","highlight_start":33,"highlight_end":40}],"label":null,"suggested_replacement":"_user_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `user_id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:271:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m271\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_by_user(&self, user_id: i32) -> OctaveResult<Vec<StoredLearningEvent>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_user_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `threat_level`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":8299,"byte_end":8311,"line_start":275,"line_end":275,"column_start":41,"column_end":53,"is_primary":true,"text":[{"text":"    async fn get_by_threat_level(&self, threat_level: &str) -> OctaveResult<Vec<StoredLearningEvent>> {","highlight_start":41,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":8299,"byte_end":8311,"line_start":275,"line_end":275,"column_start":41,"column_end":53,"is_primary":true,"text":[{"text":"    async fn get_by_threat_level(&self, threat_level: &str) -> OctaveResult<Vec<StoredLearningEvent>> {","highlight_start":41,"highlight_end":53}],"label":null,"suggested_replacement":"_threat_level","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `threat_level`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:275:41\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m275\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_by_threat_level(&self, threat_level: &str) -> OctaveResult<Vec<StoredLearningEvent>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_threat_level`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `hours`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":8431,"byte_end":8436,"line_start":279,"line_end":279,"column_start":39,"column_end":44,"is_primary":true,"text":[{"text":"    async fn get_recent_events(&self, hours: u32) -> OctaveResult<Vec<StoredLearningEvent>> {","highlight_start":39,"highlight_end":44}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":8431,"byte_end":8436,"line_start":279,"line_end":279,"column_start":39,"column_end":44,"is_primary":true,"text":[{"text":"    async fn get_recent_events(&self, hours: u32) -> OctaveResult<Vec<StoredLearningEvent>> {","highlight_start":39,"highlight_end":44}],"label":null,"suggested_replacement":"_hours","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `hours`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:279:39\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m279\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_recent_events(&self, hours: u32) -> OctaveResult<Vec<StoredLearningEvent>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                       \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_hours`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `retention_days`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":8670,"byte_end":8684,"line_start":287,"line_end":287,"column_start":40,"column_end":54,"is_primary":true,"text":[{"text":"    async fn cleanup_old_events(&self, retention_days: u32) -> OctaveResult<u64> {","highlight_start":40,"highlight_end":54}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":8670,"byte_end":8684,"line_start":287,"line_end":287,"column_start":40,"column_end":54,"is_primary":true,"text":[{"text":"    async fn cleanup_old_events(&self, retention_days: u32) -> OctaveResult<u64> {","highlight_start":40,"highlight_end":54}],"label":null,"suggested_replacement":"_retention_days","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `retention_days`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:287:40\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m287\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn cleanup_old_events(&self, retention_days: u32) -> OctaveResult<u64> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_retention_days`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":9483,"byte_end":9485,"line_start":320,"line_end":320,"column_start":31,"column_end":33,"is_primary":true,"text":[{"text":"    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredMetric>> {","highlight_start":31,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":9483,"byte_end":9485,"line_start":320,"line_end":320,"column_start":31,"column_end":33,"is_primary":true,"text":[{"text":"    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredMetric>> {","highlight_start":31,"highlight_end":33}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:320:31\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m320\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_by_id(&self, id: &str) -> OctaveResult<Option<StoredMetric>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                               \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":9691,"byte_end":9693,"line_start":328,"line_end":328,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"    async fn delete(&self, id: &str) -> OctaveResult<bool> {","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":9691,"byte_end":9693,"line_start":328,"line_end":328,"column_start":28,"column_end":30,"is_primary":true,"text":[{"text":"    async fn delete(&self, id: &str) -> OctaveResult<bool> {","highlight_start":28,"highlight_end":30}],"label":null,"suggested_replacement":"_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `id`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:328:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m328\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn delete(&self, id: &str) -> OctaveResult<bool> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `filter`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":9775,"byte_end":9781,"line_start":332,"line_end":332,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredMetric>> {","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":9775,"byte_end":9781,"line_start":332,"line_end":332,"column_start":26,"column_end":32,"is_primary":true,"text":[{"text":"    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredMetric>> {","highlight_start":26,"highlight_end":32}],"label":null,"suggested_replacement":"_filter","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `filter`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:332:26\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m332\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn list(&self, filter: QueryFilter) -> OctaveResult<Vec<StoredMetric>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                          \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_filter`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":9895,"byte_end":9899,"line_start":336,"line_end":336,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"    async fn get_by_name(&self, name: &str) -> OctaveResult<Vec<StoredMetric>> {","highlight_start":33,"highlight_end":37}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":9895,"byte_end":9899,"line_start":336,"line_end":336,"column_start":33,"column_end":37,"is_primary":true,"text":[{"text":"    async fn get_by_name(&self, name: &str) -> OctaveResult<Vec<StoredMetric>> {","highlight_start":33,"highlight_end":37}],"label":null,"suggested_replacement":"_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `name`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:336:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m336\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_by_name(&self, name: &str) -> OctaveResult<Vec<StoredMetric>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `category`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10010,"byte_end":10018,"line_start":340,"line_end":340,"column_start":37,"column_end":45,"is_primary":true,"text":[{"text":"    async fn get_by_category(&self, category: &str) -> OctaveResult<Vec<StoredMetric>> {","highlight_start":37,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10010,"byte_end":10018,"line_start":340,"line_end":340,"column_start":37,"column_end":45,"is_primary":true,"text":[{"text":"    async fn get_by_category(&self, category: &str) -> OctaveResult<Vec<StoredMetric>> {","highlight_start":37,"highlight_end":45}],"label":null,"suggested_replacement":"_category","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `category`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:340:37\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m340\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_by_category(&self, category: &str) -> OctaveResult<Vec<StoredMetric>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_category`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10145,"byte_end":10149,"line_start":346,"line_end":346,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        name: &str,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10145,"byte_end":10149,"line_start":346,"line_end":346,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        name: &str,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `name`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:346:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m346\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        name: &str,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `from`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10165,"byte_end":10169,"line_start":347,"line_end":347,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        from: DateTime<Utc>,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10165,"byte_end":10169,"line_start":347,"line_end":347,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        from: DateTime<Utc>,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_from","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `from`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:347:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m347\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        from: DateTime<Utc>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_from`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `to`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10194,"byte_end":10196,"line_start":348,"line_end":348,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"        to: DateTime<Utc>,","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10194,"byte_end":10196,"line_start":348,"line_end":348,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"        to: DateTime<Utc>,","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":"_to","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `to`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:348:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m348\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        to: DateTime<Utc>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_to`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10323,"byte_end":10327,"line_start":353,"line_end":353,"column_start":38,"column_end":42,"is_primary":true,"text":[{"text":"    async fn get_latest_value(&self, name: &str) -> OctaveResult<Option<f64>> {","highlight_start":38,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10323,"byte_end":10327,"line_start":353,"line_end":353,"column_start":38,"column_end":42,"is_primary":true,"text":[{"text":"    async fn get_latest_value(&self, name: &str) -> OctaveResult<Option<f64>> {","highlight_start":38,"highlight_end":42}],"label":null,"suggested_replacement":"_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `name`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:353:38\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m353\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    async fn get_latest_value(&self, name: &str) -> OctaveResult<Option<f64>> {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `name`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10442,"byte_end":10446,"line_start":359,"line_end":359,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        name: &str,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10442,"byte_end":10446,"line_start":359,"line_end":359,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        name: &str,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_name","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `name`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:359:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m359\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        name: &str,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_name`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `aggregation`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10462,"byte_end":10473,"line_start":360,"line_end":360,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        aggregation: MetricAggregation,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10462,"byte_end":10473,"line_start":360,"line_end":360,"column_start":9,"column_end":20,"is_primary":true,"text":[{"text":"        aggregation: MetricAggregation,","highlight_start":9,"highlight_end":20}],"label":null,"suggested_replacement":"_aggregation","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `aggregation`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:360:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m360\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        aggregation: MetricAggregation,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_aggregation`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `from`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10502,"byte_end":10506,"line_start":361,"line_end":361,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        from: DateTime<Utc>,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10502,"byte_end":10506,"line_start":361,"line_end":361,"column_start":9,"column_end":13,"is_primary":true,"text":[{"text":"        from: DateTime<Utc>,","highlight_start":9,"highlight_end":13}],"label":null,"suggested_replacement":"_from","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `from`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:361:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m361\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        from: DateTime<Utc>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_from`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `to`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10531,"byte_end":10533,"line_start":362,"line_end":362,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"        to: DateTime<Utc>,","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":10531,"byte_end":10533,"line_start":362,"line_end":362,"column_start":9,"column_end":11,"is_primary":true,"text":[{"text":"        to: DateTime<Utc>,","highlight_start":9,"highlight_end":11}],"label":null,"suggested_replacement":"_to","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused variable: `to`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:362:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m362\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        to: DateTime<Utc>,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: if this is intentional, prefix it with an underscore: `_to`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `analytics_repo` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/services.rs","byte_start":159770,"byte_end":159786,"line_start":4641,"line_end":4641,"column_start":12,"column_end":28,"is_primary":false,"text":[{"text":"pub struct ReportingService<R: ReportRepository, A: AnalyticsRepository, U: UserActivityRepository> {","highlight_start":12,"highlight_end":28}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/services.rs","byte_start":159885,"byte_end":159899,"line_start":4643,"line_end":4643,"column_start":5,"column_end":19,"is_primary":true,"text":[{"text":"    analytics_repo: A,","highlight_start":5,"highlight_end":19}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `analytics_repo` is never read\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/services.rs:4643:5\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4641\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct ReportingService<R: ReportRepository, A: AnalyticsRepository, U: UserActivityRepository> {\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4642\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    report_repo: R,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m4643\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    analytics_repo: A,\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `pool` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":7212,"byte_end":7240,"line_start":235,"line_end":235,"column_start":12,"column_end":40,"is_primary":false,"text":[{"text":"pub struct SimpleLearningDataRepository {","highlight_start":12,"highlight_end":40}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":7247,"byte_end":7251,"line_start":236,"line_end":236,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    pool: SqlitePool,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `pool` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:236:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m235\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct SimpleLearningDataRepository {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m----------------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m236\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pool: SqlitePool,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"field `pool` is never read","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":9122,"byte_end":9145,"line_start":304,"line_end":304,"column_start":12,"column_end":35,"is_primary":false,"text":[{"text":"pub struct SimpleMetricsRepository {","highlight_start":12,"highlight_end":35}],"label":"field in this struct","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"octave-database/src/simple_sqlite.rs","byte_start":9152,"byte_end":9156,"line_start":305,"line_end":305,"column_start":5,"column_end":9,"is_primary":true,"text":[{"text":"    pool: SqlitePool,","highlight_start":5,"highlight_end":9}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: field `pool` is never read\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0moctave-database/src/simple_sqlite.rs:305:5\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m304\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub struct SimpleMetricsRepository {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------------------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12mfield in this struct\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m305\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pool: SqlitePool,\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"42 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 42 warnings emitted\u001b[0m\n\n"}
