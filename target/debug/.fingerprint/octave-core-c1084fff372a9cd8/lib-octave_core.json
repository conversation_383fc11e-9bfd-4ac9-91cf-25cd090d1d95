{"rustc": 15497389221046826682, "features": "[\"sqlx\"]", "declared_features": "[\"sqlx\"]", "target": 15893417513613468538, "profile": 14037508935055259071, "path": 7932309667469858559, "deps": [[421255656520196549, "validator", false, 6412650893361535343], [2706460456408817945, "futures", false, 15511237988625537755], [8008191657135824715, "thiserror", false, 2277826180583068115], [8319709847752024821, "uuid", false, 14643814017276253492], [8569119365930580996, "serde_json", false, 12671385349869589111], [8606274917505247608, "tracing", false, 13928539558090020533], [9451456094439810778, "regex", false, 5883726391141801346], [9689903380558560274, "serde", false, 12653071505652221058], [9897246384292347999, "chrono", false, 11103001270638778896], [10632374999838431203, "sqlx", false, 15803981767658084411], [11946729385090170470, "async_trait", false, 171698127956791669], [12382237672615274180, "config", false, 16153830478445132003], [12944427623413450645, "tokio", false, 5020253171583244933], [13208667028893622512, "rand", false, 8106312600059402251], [13625485746686963219, "anyhow", false, 2101887136625042253], [17917672826516349275, "lazy_static", false, 9104387480568886006]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/octave-core-c1084fff372a9cd8/dep-lib-octave_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}