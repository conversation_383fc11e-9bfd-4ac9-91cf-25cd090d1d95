//! Unit tests for OCTAVE Semantic Protection System
//! 
//! Comprehensive unit tests covering:
//! - Semantic immune system functionality
//! - Threat detection and pattern matching
//! - Adaptive learning mechanisms
//! - Healthcare-specific antibodies
//! - Mythological archetype system

use octave_semantic::*;
use octave_core::{OctaveResult, OctaveError};
use proptest::prelude::*;
use rstest::*;
use pretty_assertions::assert_eq;
use std::collections::HashMap;
use chrono::{DateTime, Utc};
use uuid::Uuid;

mod common;
use common::{TestDataFactory, TestThreatPattern};

/// Test semantic immune system core functionality
#[cfg(test)]
mod immune_system_tests {
    use super::*;

    #[test]
    fn test_immune_system_initialization() {
        let immune_system = ImmuneSystem::new();
        
        assert!(immune_system.is_initialized());
        assert_eq!(immune_system.get_antibody_count(), 0);
        assert_eq!(immune_system.get_threat_patterns_count(), 0);
    }

    #[test]
    fn test_antibody_registration() {
        let mut immune_system = ImmuneSystem::new();
        
        let antibody = HealthcareAntibody::new(
            "phi_protection".to_string(),
            AntibodyType::PhiProtection,
            vec![r"(?i)\d{3}-\d{2}-\d{4}".to_string()], // SSN pattern
        );
        
        immune_system.register_antibody(antibody).unwrap();
        assert_eq!(immune_system.get_antibody_count(), 1);
    }

    #[test]
    fn test_threat_detection() {
        let mut immune_system = ImmuneSystem::new();
        
        // Register SQL injection antibody
        let sql_antibody = HealthcareAntibody::new(
            "sql_injection".to_string(),
            AntibodyType::SqlInjection,
            vec![r"(?i)union\s+select".to_string(), r"(?i)drop\s+table".to_string()],
        );
        immune_system.register_antibody(sql_antibody).unwrap();
        
        // Test threat detection
        let malicious_input = "'; DROP TABLE patients; --";
        let detection_result = immune_system.analyze_input(malicious_input).unwrap();
        
        assert!(detection_result.is_threat);
        assert!(detection_result.confidence > 0.8);
        assert_eq!(detection_result.threat_type, ThreatType::SqlInjection);
    }

    #[test]
    fn test_phi_exposure_detection() {
        let mut immune_system = ImmuneSystem::new();
        
        // Register PHI protection antibody
        let phi_antibody = HealthcareAntibody::new(
            "phi_exposure".to_string(),
            AntibodyType::PhiProtection,
            vec![
                r"(?i)\d{3}-\d{2}-\d{4}".to_string(), // SSN
                r"(?i)\(\d{3}\)\s?\d{3}-\d{4}".to_string(), // Phone
            ],
        );
        immune_system.register_antibody(phi_antibody).unwrap();
        
        // Test PHI detection
        let phi_input = "Patient SSN: ***********, Phone: (*************";
        let detection_result = immune_system.analyze_input(phi_input).unwrap();
        
        assert!(detection_result.is_threat);
        assert_eq!(detection_result.threat_type, ThreatType::PhiExposure);
        assert!(detection_result.detected_patterns.len() >= 2); // SSN and phone
    }

    #[proptest]
    fn test_immune_system_handles_various_inputs(
        #[strategy(r"[A-Za-z0-9 ]{1,100}")] input: String
    ) {
        let immune_system = ImmuneSystem::new();
        
        // Should not panic on any input
        let result = immune_system.analyze_input(&input);
        prop_assert!(result.is_ok());
        
        // Result should have valid structure
        let detection = result.unwrap();
        prop_assert!(detection.confidence >= 0.0 && detection.confidence <= 1.0);
    }
}

/// Test adaptive learning mechanisms
#[cfg(test)]
mod adaptive_learning_tests {
    use super::*;

    #[test]
    fn test_learning_from_false_positives() {
        let mut learning_system = AdaptiveLearningSystem::new();
        
        // Simulate false positive feedback
        let false_positive = LearningEvent {
            input: "SELECT * FROM patients WHERE id = 1".to_string(),
            detected_threat: ThreatType::SqlInjection,
            actual_threat: None, // False positive
            confidence: 0.9,
            timestamp: Utc::now(),
            user_feedback: Some(UserFeedback::FalsePositive),
        };
        
        learning_system.learn_from_event(false_positive).unwrap();
        
        // System should reduce confidence for similar patterns
        let similar_input = "SELECT * FROM patients WHERE id = 2";
        let updated_result = learning_system.analyze_with_learning(similar_input).unwrap();
        
        assert!(updated_result.confidence < 0.9);
    }

    #[test]
    fn test_learning_from_missed_threats() {
        let mut learning_system = AdaptiveLearningSystem::new();
        
        // Simulate missed threat feedback
        let missed_threat = LearningEvent {
            input: "1' OR '1'='1".to_string(),
            detected_threat: ThreatType::None,
            actual_threat: Some(ThreatType::SqlInjection),
            confidence: 0.1,
            timestamp: Utc::now(),
            user_feedback: Some(UserFeedback::MissedThreat),
        };
        
        learning_system.learn_from_event(missed_threat).unwrap();
        
        // System should increase sensitivity for similar patterns
        let similar_input = "1' OR '1'='2";
        let updated_result = learning_system.analyze_with_learning(similar_input).unwrap();
        
        assert!(updated_result.confidence > 0.1);
        assert!(updated_result.is_threat);
    }

    #[test]
    fn test_pattern_evolution() {
        let mut learning_system = AdaptiveLearningSystem::new();
        
        // Add multiple learning events for pattern evolution
        let events = vec![
            create_learning_event("admin' --", ThreatType::SqlInjection, true),
            create_learning_event("root' --", ThreatType::SqlInjection, true),
            create_learning_event("user' --", ThreatType::SqlInjection, true),
        ];
        
        for event in events {
            learning_system.learn_from_event(event).unwrap();
        }
        
        // System should evolve to detect the pattern
        let evolved_patterns = learning_system.get_evolved_patterns();
        assert!(!evolved_patterns.is_empty());
        
        // Should detect new variant
        let new_variant = "guest' --";
        let result = learning_system.analyze_with_learning(new_variant).unwrap();
        assert!(result.is_threat);
    }
}

/// Test healthcare-specific antibodies
#[cfg(test)]
mod healthcare_antibodies_tests {
    use super::*;

    #[test]
    fn test_medical_record_access_antibody() {
        let antibody = MedicalRecordAccessAntibody::new();
        
        // Test legitimate access
        let legitimate_query = "SELECT patient_id, visit_date FROM visits WHERE patient_id = ?";
        let result = antibody.analyze(legitimate_query).unwrap();
        assert!(!result.is_threat);
        
        // Test suspicious access
        let suspicious_query = "SELECT * FROM patients"; // No WHERE clause
        let result = antibody.analyze(suspicious_query).unwrap();
        assert!(result.is_threat);
        assert_eq!(result.threat_type, ThreatType::UnauthorizedAccess);
    }

    #[test]
    fn test_phi_exposure_antibody() {
        let antibody = PhiExposureAntibody::new();
        
        // Test clean data
        let clean_data = "Patient visit scheduled for tomorrow";
        let result = antibody.analyze(clean_data).unwrap();
        assert!(!result.is_threat);
        
        // Test PHI exposure
        let phi_data = "Patient John Doe, SSN: ***********";
        let result = antibody.analyze(phi_data).unwrap();
        assert!(result.is_threat);
        assert_eq!(result.threat_type, ThreatType::PhiExposure);
    }

    #[test]
    fn test_medication_safety_antibody() {
        let antibody = MedicationSafetyAntibody::new();
        
        // Test safe medication order
        let safe_order = "Aspirin 81mg daily";
        let result = antibody.analyze(safe_order).unwrap();
        assert!(!result.is_threat);
        
        // Test dangerous dosage
        let dangerous_order = "Warfarin 50mg daily"; // Extremely high dose
        let result = antibody.analyze(dangerous_order).unwrap();
        assert!(result.is_threat);
        assert_eq!(result.threat_type, ThreatType::MedicationSafety);
    }

    #[rstest]
    #[case("***********", true)]  // Valid SSN format
    #[case("(*************", true)] // Valid phone format
    #[case("<EMAIL>", false)] // Email not PHI in this context
    #[case("Patient ID: 12345", false)] // Patient ID not PHI pattern
    fn test_phi_pattern_detection(#[case] input: &str, #[case] expected_phi: bool) {
        let antibody = PhiExposureAntibody::new();
        let result = antibody.analyze(input).unwrap();
        assert_eq!(result.is_threat, expected_phi);
    }
}

/// Test mythological archetype system
#[cfg(test)]
mod archetype_tests {
    use super::*;

    #[test]
    fn test_apollo_archetype() {
        let apollo = ApolloArchetype::new(); // Healing and protection
        
        // Test healing context
        let healing_context = "Patient recovery protocol initiated";
        let result = apollo.analyze_context(healing_context).unwrap();
        assert_eq!(result.archetype_match, ArchetypeType::Apollo);
        assert!(result.confidence > 0.7);
    }

    #[test]
    fn test_artemis_archetype() {
        let artemis = ArtemisArchetype::new(); // Protection and boundaries
        
        // Test protection context
        let protection_context = "Access denied: insufficient privileges";
        let result = artemis.analyze_context(protection_context).unwrap();
        assert_eq!(result.archetype_match, ArchetypeType::Artemis);
        assert!(result.confidence > 0.7);
    }

    #[test]
    fn test_hermes_archetype() {
        let hermes = HermesArchetype::new(); // Communication and guidance
        
        // Test communication context
        let communication_context = "Notification sent to healthcare provider";
        let result = hermes.analyze_context(communication_context).unwrap();
        assert_eq!(result.archetype_match, ArchetypeType::Hermes);
        assert!(result.confidence > 0.7);
    }

    #[test]
    fn test_archetype_combination() {
        let archetype_system = ArchetypeSystem::new();
        
        // Test complex scenario requiring multiple archetypes
        let complex_scenario = "Patient data accessed for emergency treatment";
        let results = archetype_system.analyze_multi_archetype(complex_scenario).unwrap();
        
        // Should match both Apollo (healing) and Artemis (protection)
        assert!(results.len() >= 2);
        assert!(results.iter().any(|r| r.archetype_match == ArchetypeType::Apollo));
        assert!(results.iter().any(|r| r.archetype_match == ArchetypeType::Artemis));
    }
}

/// Test threat pattern matching
#[cfg(test)]
mod threat_pattern_tests {
    use super::*;

    #[test]
    fn test_sql_injection_patterns() {
        let pattern_matcher = ThreatPatternMatcher::new();
        
        let sql_patterns = vec![
            "'; DROP TABLE users; --",
            "1' OR '1'='1",
            "UNION SELECT * FROM passwords",
            "admin'/**/OR/**/1=1",
        ];
        
        for pattern in sql_patterns {
            let result = pattern_matcher.match_pattern(pattern).unwrap();
            assert!(result.is_match);
            assert_eq!(result.threat_type, ThreatType::SqlInjection);
            assert!(result.confidence > 0.8);
        }
    }

    #[test]
    fn test_xss_patterns() {
        let pattern_matcher = ThreatPatternMatcher::new();
        
        let xss_patterns = vec![
            "<script>alert('xss')</script>",
            "javascript:alert(1)",
            "<img src=x onerror=alert(1)>",
            "onmouseover=\"alert('xss')\"",
        ];
        
        for pattern in xss_patterns {
            let result = pattern_matcher.match_pattern(pattern).unwrap();
            assert!(result.is_match);
            assert_eq!(result.threat_type, ThreatType::XssAttack);
            assert!(result.confidence > 0.8);
        }
    }

    #[test]
    fn test_data_exfiltration_patterns() {
        let pattern_matcher = ThreatPatternMatcher::new();
        
        let exfiltration_patterns = vec![
            "SELECT * FROM patients WHERE 1=1",
            "BACKUP DATABASE TO 'external_location'",
            "COPY patients TO '/tmp/export.csv'",
        ];
        
        for pattern in exfiltration_patterns {
            let result = pattern_matcher.match_pattern(pattern).unwrap();
            assert!(result.is_match);
            assert_eq!(result.threat_type, ThreatType::DataExfiltration);
        }
    }

    #[proptest]
    fn test_pattern_matcher_performance(
        #[strategy(r"[A-Za-z0-9 ]{1,1000}")] input: String
    ) {
        let pattern_matcher = ThreatPatternMatcher::new();
        
        let start = std::time::Instant::now();
        let result = pattern_matcher.match_pattern(&input);
        let duration = start.elapsed();
        
        // Should complete within reasonable time
        prop_assert!(duration.as_millis() < 100);
        prop_assert!(result.is_ok());
    }
}

// Helper functions for tests

fn create_learning_event(input: &str, threat_type: ThreatType, is_actual_threat: bool) -> LearningEvent {
    LearningEvent {
        input: input.to_string(),
        detected_threat: threat_type.clone(),
        actual_threat: if is_actual_threat { Some(threat_type) } else { None },
        confidence: 0.8,
        timestamp: Utc::now(),
        user_feedback: Some(if is_actual_threat { 
            UserFeedback::TruePositive 
        } else { 
            UserFeedback::FalsePositive 
        }),
    }
}

// Test data structures

#[derive(Debug, Clone, PartialEq)]
enum ThreatType {
    None,
    SqlInjection,
    XssAttack,
    DataExfiltration,
    PhiExposure,
    UnauthorizedAccess,
    MedicationSafety,
}

#[derive(Debug, Clone)]
enum AntibodyType {
    PhiProtection,
    SqlInjection,
    XssProtection,
    DataExfiltration,
    MedicalRecordAccess,
    MedicationSafety,
}

#[derive(Debug, Clone, PartialEq)]
enum ArchetypeType {
    Apollo,
    Artemis,
    Hermes,
    Athena,
}

#[derive(Debug, Clone)]
enum UserFeedback {
    TruePositive,
    FalsePositive,
    MissedThreat,
}

#[derive(Debug)]
struct DetectionResult {
    is_threat: bool,
    threat_type: ThreatType,
    confidence: f64,
    detected_patterns: Vec<String>,
}

#[derive(Debug)]
struct LearningEvent {
    input: String,
    detected_threat: ThreatType,
    actual_threat: Option<ThreatType>,
    confidence: f64,
    timestamp: DateTime<Utc>,
    user_feedback: Option<UserFeedback>,
}

#[derive(Debug)]
struct ArchetypeResult {
    archetype_match: ArchetypeType,
    confidence: f64,
    context_factors: Vec<String>,
}

#[derive(Debug)]
struct PatternMatchResult {
    is_match: bool,
    threat_type: ThreatType,
    confidence: f64,
    matched_patterns: Vec<String>,
}
